drop view if exists "vnpt_dev"."feature_view_all_pricing_plan_price";
create or replace view "vnpt_dev"."feature_view_all_pricing_plan_price" as (
select
        pricing_id,
        pricing_draft_id,
        pricing_multi_plan_id,
        customer_type_code,
        service_id,
        pricing_plan,
        default_circle,
        unit_price,
        greatest(0, min_quantity - free_quantity) as cal_quantity,
        total_tax_percent,
        case
           when pricing_plan = 0 then (unit_price)
           when pricing_plan = 1 then (unit_price * greatest(0, min_quantity - free_quantity))
           when pricing_plan = 2 then (unit_price * greatest(0, min_quantity - free_quantity))
           when pricing_plan = 3 then (unit_price * greatest(0, min_quantity - free_quantity))
           when pricing_plan = 4 then (unit_price)
           else 0.0
        end as default_plan_price,
        case
           when pricing_plan = 0 then (unit_price) / (1 + (total_tax_percent / 100))
           when pricing_plan = 1 then (unit_price * greatest(0, min_quantity - free_quantity)) / (1 + (total_tax_percent / 100))
           when pricing_plan = 2 then (unit_price * greatest(0, min_quantity - free_quantity)) / (1 + (total_tax_percent / 100))
           when pricing_plan = 3 then (unit_price * greatest(0, min_quantity - free_quantity)) / (1 + (total_tax_percent / 100))
           when pricing_plan = 4 then (unit_price) / (1 + (total_tax_percent / 100))
           else 0.0
        end as default_plan_price_pre_tax
from (
    select
        pricing.id as pricing_id,
        pricing.pricing_draft_id,
        pricing.service_id as service_id,
        pmp.id as pricing_multi_plan_id,
        pmp.pricing_plan,
        case
            when pmp.pricing_plan = 0 then 1
            when pmp.pricing_plan = 1 then coalesce(pmp.minimum_quantity, 1)
            else firstPlanDetail.unit_from
            end as min_quantity,
        coalesce(pmp.free_quantity, 0) as free_quantity,
        case
            when pmp.pricing_plan in (0,1) then pmp.price
            when pmp.pricing_plan in (2,3,4) then firstPlanDetail.unit_price
            end as unit_price,
        coalesce(pricingTax.total_tax_percent, 0) as total_tax_percent,
        ((pricing.is_one_time = 0 and coalesce(pmp.default_circle, 0) = 0) or pmp.default_circle = 1) as default_circle,
        pmp.customer_type_code
    from vnpt_dev.pricing_multi_plan as pmp
             left join vnpt_dev.pricing on pmp.pricing_id = pricing.id
             left join vnpt_dev.services on services.id = pricing.service_id
             left join (
        select * from (
          select
              row_number() over (partition by pricing_multi_plan_id order by coalesce(unit_from, -1)) as first_range,
              pricing_multi_plan_id,
              case
                  when unit_from is null or unit_from = -1 then 1
                  else unit_from
                  end as unit_from,
              unit_to,
              price as unit_price
          from vnpt_dev.pricing_plan_detail
      ) as planDetail where planDetail.first_range = 1
    ) as firstPlanDetail on firstPlanDetail.pricing_multi_plan_id = pmp.id
             left join (
        select pricing_id, sum(coalesce(percent, 0)) filter ( where has_tax = 1) as total_tax_percent from vnpt_dev.pricing_tax group by pricing_id
    ) as pricingTax on pricingTax.pricing_id = pricing.id
) as allPricingPlanPrice
);