
DELETE FROM vnpt_dev.apis WHERE api_code IN ('XUAT_DU_LIEU_KHACH_HANG', 'XUAT_DU_LIEU_TAI_KHOAN');
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XUAT_DU_LIEU_KHACH_HANG', 'XUAT_DU_LIEU_TAI_KHOAN')));

DELETE FROM vnpt_dev.permission WHERE code IN ('XUAT_DU_LIEU_KHACH_HANG', 'XUAT_DU_LIEU_TAI_KHOAN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code IN ('XUAT_DU_LIEU_KHACH_HANG', 'XUAT_DU_LIEU_TAI_KHOAN'));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code IN ('XUAT_DU_LIEU_KHACH_HANG', 'XUAT_DU_LIEU_TAI_KHOAN'));


-- them vao bang permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
values ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Xuất dữ liệu', 'XUAT_DU_LIEU_KHACH_HANG',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
values ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Xuất dữ liệu', 'XUAT_DU_LIEU_TAI_KHOAN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_TAI_KHOAN_1' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));
-- them vao bang permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XUAT_DU_LIEU_KHACH_HANG'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XUAT_DU_LIEU_TAI_KHOAN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));
-- them vao bang roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_DU_LIEU_KHACH_HANG'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_DU_LIEU_TAI_KHOAN'), 1);
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/export-current-enterprise',
        'XUAT_DU_LIEU_KHACH_HANG',
        'POST');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/export-enterprise-have-account',
        'XUAT_DU_LIEU_TAI_KHOAN',
        'POST');

INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'XUAT_DU_LIEU_KHACH_HANG' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XUAT_DU_LIEU_KHACH_HANG' AND pp.portal_id = 1 LIMIT 1),
    1,1
    );

INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'XUAT_DU_LIEU_TAI_KHOAN' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XUAT_DU_LIEU_TAI_KHOAN' AND pp.portal_id = 1 LIMIT 1),
    1,1
    );

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;


DELETE FROM vnpt_dev.apis WHERE api_code IN ('XUAT_DU_LIEU_LIEN_HE');
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XUAT_DU_LIEU_LIEN_HE')));

DELETE FROM vnpt_dev.permission WHERE code IN ('XUAT_DU_LIEU_LIEN_HE');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code IN ('XUAT_DU_LIEU_LIEN_HE'));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code IN ('XUAT_DU_LIEU_LIEN_HE'));


-- them vao bang permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
values ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Xuất dữ liệu', 'XUAT_DU_LIEU_LIEN_HE',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));


INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XUAT_DU_LIEU_LIEN_HE'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));


-- them vao bang roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_DU_LIEU_LIEN_HE'), 1);


INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/export-get-lst-contact',
        'XUAT_DU_LIEU_LIEN_HE',
        'GET');


INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'XUAT_DU_LIEU_LIEN_HE' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XUAT_DU_LIEU_LIEN_HE' AND pp.portal_id = 1 LIMIT 1),
    1,1
    );


REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;
