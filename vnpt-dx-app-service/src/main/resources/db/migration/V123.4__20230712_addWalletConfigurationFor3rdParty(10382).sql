DELETE FROM "vnpt_dev"."system_params" WHERE param_type = 'WALLET_3RD_PARTY';
INSERT INTO "vnpt_dev"."system_params" ("param_name", "param_type") VALUES ('<PERSON><PERSON><PERSON> hình ví thanh toán của ThirdParty', 'WALLET_3RD_PARTY');
ALTER TABLE "vnpt_dev"."subscriptions" DROP COLUMN IF EXISTS "os_3rd_status";
ALTER TABLE "vnpt_dev"."subscriptions" ADD COLUMN "os_3rd_status" int8 DEFAULT 5;

COMMENT ON COLUMN "vnpt_dev"."subscriptions"."os_3rd_status" IS 'Trạng thái đơn hàng OS của 3rd party (do 3rd party tự quản lý, không qua DHSXKD) (ID bảng sme_progress)';
COMMENT ON TABLE "vnpt_dev"."order_service_status" IS 'Trạng thái đơn hàng trên hệ thống DHSXKD';
COMMENT ON COLUMN "vnpt_dev"."order_service_status"."sme_progress_id" IS 'Id của trạng thái tiến trình (ID của bảng sme_progress)';
COMMENT ON COLUMN "vnpt_dev"."order_service_receive"."order_status" IS 'Trạng thái đơn hàng trên hệ thống DHSXKD (ID của bảng order_service_status). Có giá trị -1 nếu là đơn hàng của đơn vị phát triển bên thứ 3 (không xử lý trên DHSXKD)';
COMMENT ON COLUMN "vnpt_dev"."services"."service_owner" IS 'Loại dịch vụ (ứng với nhà phát triển VNPT và 3rd party)
0 - SaaS: Online service + 3rd party
1 - VNPT: Online service + VNPT
2 - NONE: Order service + 3rd party
3 - OTHER: Order service + VNPT
(đăng ký dịch vụ loại 3 - OTHER cần tích hợp DHSXKD)';
COMMENT ON COLUMN "vnpt_dev"."services"."service_owner_partner" IS 'Loại dịch vụ (ứng với nhà phát triển Partner)
0 - ON: Online service + Partner
1 - OS: Order servce + Partner';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."service_id" IS 'ID dịch vụ/combo được đăng ký';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."quantity" IS 'Số người đang sử dụng, nếu subscription_plan là unlimited quantity = -1';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."deleted_flag" IS 'Trạng thái xóa: 0 - Đã xóa, 1 - Chưa xóa';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."created_by" IS 'Người tạo';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."modified_by" IS 'Người sửa';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."created_at" IS 'Ngày tạo';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."modified_at" IS 'Ngày sửa';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."user_id" IS 'ID admin doanh nghiệp (ID bảng user)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."pricing_id" IS 'ID gói dịch vụ được đăng ký';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."sme_subscription_id" IS '<deprecated>';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."installed" IS 'Trạng thái cài đặt: 0 - Đang cài đặt, 1 - Đã cài đặt, 2 - Thất bại';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."registed_by" IS 'ID của dev/admin đăng ký hộ sme (ID bảng user)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."sub_registration_id" IS '<deprecated>';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."trial_day" IS 'Số ngày dùng thử. Nếu reg_type = 0 thì trial_day <> null';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."reg_type" IS 'Loại đăng ký: 0 - Dùng thử, 1 - Chính thức';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."payment_method" IS '0: Chuyển khoản (P2P), 1: VNPT Pay, 2: Tiền mặt (by_cash)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."confirm_status" IS 'Trạng thái xác nhận: 0 - Waiting, 1 - Confirmed';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."current_cycle" IS 'Số thứ tự của chu kỳ hiện tại';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."sub_code" IS 'Mã khi tạo thuê bao mới';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."cycle_type" IS 'Đơn vị thời gian của chu kỳ thanh toán: 0 - ngày, 1 - tuần, 2 - tháng, 3 - năm';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."traffic_id" IS 'ID postback cho masoffer';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."combo_plan_id" IS 'ID gói combo được đăng ký';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."change_date" IS 'Ngày đổi gói';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."change_status" IS 'Null: không đổi, 0: chờ đổi, 1: đã đổi';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."update_date" IS 'Ngày cập nhật gói';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."update_status" IS 'Null: không cập nhật, 0: chờ cập nhật, 1: đã cập nhật';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."portal_type" IS 'Portal tạo thuê bao: 1 - admin, 2 - dev, 3 - sme';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."pricing_multi_plan_id" IS 'ID pricing_multi_plan của gói dịch vụ được đăng ký';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."refer_subscription" IS 'ID của subscription dùng thử';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."started_at_swap" IS 'Ngày bắt đầu sử dụng sau khi đổi gói';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."current_cycle_swap" IS 'Chu kì hiện tại sau khi đổi gói';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."reference_id" IS 'ID thuê bao tham chiếu (thuê bao hiện tại là thuê bao được tặng kèm)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."give_away_main_sub_id" IS 'ID của thuê bao chính của thuê bao được tặng';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."created_source_migration" IS 'Nguồn tạo: 0 - OneSME, 1 - Đồng bộ';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."service_owner_migration" IS 'Loại đồng bộ: 0 - ON, 1 - OS';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."current_cycle_reactive" IS 'Chu kỳ sử dụng khi kích hoạt lại (với admin/dev)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."reactive_date" IS 'Ngày kích hoạt lại (với admin/dev)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."current_cycle_renew" IS 'Chu kỳ hiện tại sau khi gia hạn thuê bao';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."type_reactive" IS 'Kiểu kích hoạt lại thuê bao';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."portal_type_cancel" IS 'Đối tượng hủy (phục vụ cho batch hủy)';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."reactive_status" IS 'Trạng thái reactive của sub: 0 - Không thành công, 1 - Thành công';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."province_id_setup" IS 'ID tỉnh thành của địa chỉ lắp đặt';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."district_id_setup" IS 'ID quận, huyện của địa chỉ lắp đặt';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."ward_id_setup" IS 'ID xã, phường của địa chỉ lắp đặt';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."street_id_setup" IS 'ID đường của địa chỉ lắp đặt';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."auto_payment" IS '0: không tự động thanh toán qua Pay, 1: tự động thanh toán quay Pay';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."total_amount_after_refund" IS 'Tổng tiền sau hoàn trả';