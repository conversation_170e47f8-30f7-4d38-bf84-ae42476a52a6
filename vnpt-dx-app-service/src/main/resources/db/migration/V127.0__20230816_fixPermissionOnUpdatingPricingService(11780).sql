-- <PERSON><PERSON><PERSON> nhật api_path cho api cập nhật dịch vụ
update vnpt_dev.apis set api_path = '/api/dev-portal/services/{id}' where api_code = 'ROLE_ADMIN_UPDATE_SERVICE' and method = 'PUT';



-- <PERSON><PERSON> sung phân quyền api chỉnh sửa gói dịch vụ
INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES 
    ((select max(id) + 1 from vnpt_dev.apis), '/api/dev-portal/pricing/setting/{id}', 'API_DEV_UPDATE_PRICING_SETTING', 'PUT');

delete from vnpt_dev.api_permission
where
    permission_portal_id in (
        select permission_portal.id
        from vnpt_dev.permission_portal
            join vnpt_dev.permission on permission.id = permission_portal.permission_id
            join vnpt_dev.portal on portal.id = permission_portal.portal_id
        where permission.code = 'SUA_GOI_DICH_VU_1' and portal.name = 'DEV'
    ) and
    api_id in (select id from vnpt_dev.apis where api_code = 'API_DEV_UPDATE_PRICING_SETTING');

insert into vnpt_dev.api_permission(id,api_id, permission_portal_id, map_permission_portal, delete_flag) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.api_permission),
        (select id from vnpt_dev.apis where api_code = 'API_DEV_UPDATE_PRICING_SETTING'),
        (select permission_portal.id
            from vnpt_dev.permission_portal
                join vnpt_dev.permission on permission.id = permission_portal.permission_id
                join vnpt_dev.portal on portal.id = permission_portal.portal_id
            where permission.code = 'SUA_GOI_DICH_VU_1' and portal.name = 'DEV'
            limit 1
        ), 1, 1
    );


REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;