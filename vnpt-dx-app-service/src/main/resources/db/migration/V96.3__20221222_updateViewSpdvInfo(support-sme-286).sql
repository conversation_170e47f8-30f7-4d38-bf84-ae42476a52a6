DROP VIEW "vnpt_dev"."feature_view_shopping_cart_get_spdv_info";
CREATE VIEW "vnpt_dev"."feature_view_shopping_cart_get_spdv_info" AS

WITH lastest_pricing AS (
    SELECT DISTINCT ON (pricing.pricing_draft_id) pricing.id
        FROM vnpt_dev.pricing
        ORDER BY pricing.pricing_draft_id DESC, pricing.id DESC
        ), lastest_combo_plan AS (
        SELECT DISTINCT ON (combo_plan.combo_plan_draft_id) combo_plan.id
        FROM vnpt_dev.combo_plan
        ORDER BY combo_plan.combo_plan_draft_id DESC, combo_plan.id DESC
        ), lastest_service AS (
        SELECT DISTINCT ON (COALESCE(services.services_draft_id, services.id)) services.id
        FROM vnpt_dev.services
        ORDER BY COALESCE(services.services_draft_id, services.id) DESC, services.id DESC
        ), lastest_combo AS (
        SELECT DISTINCT ON (COALESCE(combo.combo_draft_id, combo.id)) combo.id
        FROM vnpt_dev.combo
        ORDER BY COALESCE(combo.combo_draft_id, combo.id) DESC, combo.id DESC
        )
        ( SELECT 0 AS calculate_type,
        (concat(services.id, '0000'))::bigint AS service_unique_id,
        services.id AS services_id,
        pricing.id AS pricing_id,
        (concat(pricing.id, '0000'))::bigint AS pricing_unique_id,
        pricing_multi_plan.id AS pricing_multi_plan_id,
        services.service_name,
        ARRAY[services.categories_id] AS lst_category,
        (COALESCE((services.service_owner)::integer, 3) = ANY (ARRAY[0, 1])) AS is_on,
        services.user_id AS provider_id,
        pricing.pricing_name,
        COALESCE((pricing.number_of_trial)::integer, 0) AS number_of_trial,
        COALESCE((pricing.trial_type)::integer, 0) AS trial_type,
        CASE
        WHEN (COALESCE((pricing.number_of_trial)::integer, 0) > 0) THEN 1
        ELSE 0
        END AS is_trial,
        services.allow_multi_sub,
        string_to_array(translate((pricing.customer_type_code)::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
        CASE
        WHEN (pricing_multi_plan.circle_type = 0) THEN concat(pricing_multi_plan.payment_cycle, ' ngày')
        WHEN (pricing_multi_plan.circle_type = 1) THEN concat(pricing_multi_plan.payment_cycle, ' tuần')
        WHEN (pricing_multi_plan.circle_type = 2) THEN concat(pricing_multi_plan.payment_cycle, ' tháng')
        WHEN (pricing_multi_plan.circle_type = 3) THEN concat(pricing_multi_plan.payment_cycle, ' năm')
        WHEN (pricing.cycle_type = 0) THEN concat(pricing.payment_cycle, ' ngày')
        WHEN (pricing.cycle_type = 1) THEN concat(pricing.payment_cycle, ' tuần')
        WHEN (pricing.cycle_type = 2) THEN concat(pricing.payment_cycle, ' tháng')
        WHEN (pricing.cycle_type = 3) THEN concat(pricing.payment_cycle, ' năm')
        ELSE NULL::text
        END AS payment_cycle,
        COALESCE(pricing.is_one_time, 1) as is_one_time
        FROM (((((vnpt_dev.pricing
        JOIN lastest_pricing ON ((lastest_pricing.id = pricing.id)))
        JOIN vnpt_dev.services ON ((services.id = pricing.service_id)))
        JOIN lastest_service ON ((lastest_service.id = services.id)))
        JOIN vnpt_dev.users ON ((users.id = services.user_id)))
        LEFT JOIN vnpt_dev.pricing_multi_plan ON ((pricing_multi_plan.pricing_id = pricing.id)))
        WHERE ((pricing.deleted_flag = 1) AND (pricing.status = 1) AND (services.deleted_flag = 1) AND (services.status = 1) AND ((pricing_multi_plan.id IS NULL) OR ((pricing_multi_plan.deleted_flag = 1) AND (pricing_multi_plan.display_status = 1))))
        ORDER BY services.id, pricing.id, pricing_multi_plan.id)
        UNION ALL
        ( SELECT 1 AS calculate_type,
        (concat(combo.id, '0001'))::bigint AS service_unique_id,
        combo.id AS services_id,
        combo_plan.id AS pricing_id,
        (concat(combo_plan.id, '0001'))::bigint AS pricing_unique_id,
        NULL::bigint AS pricing_multi_plan_id,
        combo.combo_name AS service_name,
        (string_to_array((combo.categories_id)::text, ','::text))::bigint[] AS lst_category,
        (COALESCE((combo.combo_owner)::integer, 3) = ANY (ARRAY[0, 1])) AS is_on,
        combo.user_id AS provider_id,
        combo_plan.combo_name AS pricing_name,
        COALESCE((combo_plan.number_of_trial)::integer, 0) AS number_of_trial,
        COALESCE((combo_plan.trial_type)::integer, 0) AS trial_type,
        CASE
        WHEN (COALESCE((combo_plan.number_of_trial)::integer, 0) > 0) THEN 1
        ELSE 0
        END AS is_trial,
        combo.allow_multi_sub,
        string_to_array(translate((combo_plan.customer_type_code)::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
        CASE
        WHEN (combo_plan.cycle_type = 0) THEN concat(combo_plan.payment_cycle, ' ngày')
        WHEN (combo_plan.cycle_type = 1) THEN concat(combo_plan.payment_cycle, ' tuần')
        WHEN (combo_plan.cycle_type = 2) THEN concat(combo_plan.payment_cycle, ' tháng')
        WHEN (combo_plan.cycle_type = 3) THEN concat(combo_plan.payment_cycle, ' năm')
        ELSE NULL::text
        END AS payment_cycle,
        1 AS is_one_time
        FROM ((((vnpt_dev.combo_plan
        JOIN lastest_combo_plan ON ((lastest_combo_plan.id = combo_plan.id)))
        JOIN vnpt_dev.combo ON ((combo.id = combo_plan.combo_id)))
        JOIN vnpt_dev.users ON ((users.id = combo.user_id)))
        JOIN lastest_combo ON ((lastest_combo.id = combo.id)))
        WHERE ((combo_plan.deleted_flag = 1) AND (combo_plan.status = 1) AND (combo.deleted_flag = 1) AND (combo.status = 1))
        ORDER BY combo.id, combo_plan.id)