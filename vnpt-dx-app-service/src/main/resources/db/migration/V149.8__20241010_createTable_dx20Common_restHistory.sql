CREATE SCHEMA IF NOT EXISTS "dx20_common";

-- <PERSON><PERSON><PERSON> bảng dữ liệu lưu lịch sử gọi REST API
DROP TABLE IF EXISTS "dx20_common"."rest_history";
CREATE TABLE "dx20_common"."rest_history" (
  "id" bigserial,
  "partition_id" date NOT NULL DEFAULT CAST(now() AS DATE),
  "created_at" timestamp NOT NULL,
  "http_method" varchar(10) NOT NULL,
  "url" varchar(512) NOT NULL,
  "http_header" jsonb,
  "request" jsonb,
  "response" jsonb,
  "response_time" int8,
  "exception" text,
  PRIMARY KEY ("id", "partition_id")
) PARTITION BY RANGE (partition_id);

COMMENT ON COLUMN "dx20_common"."rest_history"."partition_id" IS 'ID sử dụng để partition bảng history';
COMMENT ON COLUMN "dx20_common"."rest_history"."created_at" IS 'Thời gian gọi REST API';
COMMENT ON COLUMN "dx20_common"."rest_history"."http_method" IS 'HTTP method';
COMMENT ON COLUMN "dx20_common"."rest_history"."url" IS 'URL của bản tin yêu cầu';
COMMENT ON COLUMN "dx20_common"."rest_history"."http_header" IS 'HTTP header của bản tin yêu cầu';
COMMENT ON COLUMN "dx20_common"."rest_history"."request" IS 'HTTP body của bản tin yêu cầu';
COMMENT ON COLUMN "dx20_common"."rest_history"."response" IS 'HTTP body của bản tin phản hồi';
COMMENT ON COLUMN "dx20_common"."rest_history"."response_time" IS 'Thời gian phản hồi (milisecond)';
COMMENT ON COLUMN "dx20_common"."rest_history"."exception" IS 'Thông tin exception (nếu có)';
COMMENT ON TABLE "dx20_common"."rest_history" IS 'Bảng lưu lịch sử gọi REST API';

CREATE TABLE dx20_common.rest_history_2024_10 PARTITION OF dx20_common.rest_history FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');

-- Tạo function cho phép tạo partition của tháng sau cho một bảng bất kì
CREATE OR REPLACE FUNCTION dx20_common.func_create_partition_by_month(schema_name varchar, tbl_name varchar) RETURNS int2 AS $$
DECLARE
    partition_name TEXT;
    sql_query TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- Lấy thời gian là tháng sau
    EXECUTE 'SELECT cast(date_trunc(''month'', now() + INTERVAL ''1 month'') as date)' INTO start_date;
    end_date := cast(start_date + INTERVAL '1 month' as date);
    
    -- Tạo tên partition dựa trên tháng
    partition_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM');

    -- Kiểm tra nếu partition đã tồn tại
    IF NOT EXISTS (
        SELECT 1
        FROM pg_class
            JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace
        WHERE pg_class.relname = partition_name AND pg_namespace.nspname = schema_name
    ) THEN
        -- Tạo partition mới nếu chưa tồn tại
        sql_query := format(
            'CREATE TABLE %I.%I PARTITION OF %I.%I FOR VALUES FROM (%L) TO (%L)',
            schema_name,
            partition_name,
            schema_name,
            tbl_name,
            start_date,
            end_date
        );
        RAISE NOTICE 'SQL query % ', sql_query;
        EXECUTE sql_query;
        RAISE NOTICE 'Partition % created.', partition_name;
        RETURN 1;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;