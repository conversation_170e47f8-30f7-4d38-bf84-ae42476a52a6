-- <PERSON><PERSON> quyền bật tắt dịch vụ cho admin portal
DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (SELECT id FROM vnpt_dev.permission WHERE code = 'AN_HIEN_DICH_VU_1');
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
SELECT
    (SELECT max(id) FROM vnpt_dev.permission_portal) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'AN_HIEN_DICH_VU_1'),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN')
WHERE NOT EXISTS (
    SELECT 1
    FROM vnpt_dev."permission" per
        JOIN vnpt_dev.permission_portal per_portal ON per.id = per_portal.permission_id AND per.code = 'AN_HIEN_DICH_VU_1'
);
-- Cho phép FULL_ADMIN bật tắt dịch vụ
INSERT INTO vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
SELECT
    (SELECT id FROM vnpt_dev."role" WHERE name = 'FULL_ADMIN'),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'AN_HIEN_DICH_VU_1'),
    1
WHERE NOT EXISTS (
    SELECT 1
    FROM vnpt_dev.roles_permissions role_per
        JOIN vnpt_dev."role" ON role_per.role_id = vnpt_dev."role".id
        JOIN vnpt_dev."permission" per ON role_per.permission_id = per.id
    WHERE vnpt_dev."role"."name" = 'FULL_ADMIN' AND per.code = 'AN_HIEN_DICH_VU_1'
);
-- Sửa tên permission
UPDATE vnpt_dev.permission SET name = 'Bật/Tắt trạng thái dịch vụ' WHERE code = 'AN_HIEN_DICH_VU_1';



-- Gán quyền bật tắt gói dịch vụ cho admin portal
DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (SELECT id FROM vnpt_dev.permission WHERE code = 'AN_HIEN_GOI_DICH_VU_1');
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
SELECT
    (SELECT max(id) FROM vnpt_dev.permission_portal) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'AN_HIEN_GOI_DICH_VU_1'),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN')
WHERE NOT EXISTS (
    SELECT 1
    FROM vnpt_dev."permission" per
        JOIN vnpt_dev.permission_portal per_portal ON per.id = per_portal.permission_id AND per.code = 'AN_HIEN_GOI_DICH_VU_1'
);
-- Cho phép FULL_ADMIN gói bật tắt dịch vụ
INSERT INTO vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
SELECT
    (SELECT id FROM vnpt_dev."role" WHERE name = 'FULL_ADMIN'),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'AN_HIEN_GOI_DICH_VU_1'),
    1
WHERE NOT EXISTS (
    SELECT 1
    FROM vnpt_dev.roles_permissions role_per
        JOIN vnpt_dev."role" ON role_per.role_id = vnpt_dev."role".id
        JOIN vnpt_dev."permission" per ON role_per.permission_id = per.id
    WHERE vnpt_dev."role"."name" = 'FULL_ADMIN' AND per.code = 'AN_HIEN_GOI_DICH_VU_1'
);
-- Sửa tên permission
UPDATE vnpt_dev.permission SET name = 'Bật/Tắt trạng thái gói dịch vụ' WHERE code = 'AN_HIEN_GOI_DICH_VU_1';