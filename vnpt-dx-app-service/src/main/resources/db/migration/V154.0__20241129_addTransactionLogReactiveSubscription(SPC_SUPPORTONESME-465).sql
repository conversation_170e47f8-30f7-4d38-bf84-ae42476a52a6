
insert into vnpt_dev."transaction"
("name", code)
select
    '<PERSON><PERSON><PERSON> hoạt lại thuê bao',
    'REACTIVE'
where not exists (
    select 1 from vnpt_dev."transaction"
    where
    code = 'REACTIVE');


insert into vnpt_dev.activity
("name", system_name, api_name, api_url, request, response, can_evaluate, visible, system_code, api_header, transaction_code, code)
select
    'Kích hoạt lại trên SPDV', 'SPDV', 'REACTIVE_SUBSCRIPTION', NULL, NULL, NULL, false, 1, 'SC1', NULL, 'REACTIVE', 'REACTIVE_SPDV'
where not exists (
    select 1 from  vnpt_dev.activity
    where
    code = 'REACTIVE_SPDV');
