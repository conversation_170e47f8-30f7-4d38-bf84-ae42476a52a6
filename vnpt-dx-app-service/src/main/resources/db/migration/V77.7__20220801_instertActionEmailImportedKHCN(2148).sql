UPDATE "vnpt_dev"."mail_template" SET "content_html" = '<html><head>
					<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
					<link rel="preconnect" href="https://fonts.googleapis.com">
					<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
					
			<style id="ckeditor-style">
			 	:root{--ck-color-image-caption-background:hsl(0, 0%, 97%);--ck-color-image-caption-text:hsl(0, 0%, 20%);--ck-color-mention-background:hsla(341, 100%, 30%, 0.1);--ck-color-mention-text:hsl(341, 100%, 30%);--ck-color-table-caption-background:hsl(0, 0%, 97%);--ck-color-table-caption-text:hsl(0, 0%, 20%);--ck-highlight-marker-blue:hsl(201, 97%, 72%);--ck-highlight-marker-green:hsl(120, 93%, 68%);--ck-highlight-marker-pink:hsl(345, 96%, 73%);--ck-highlight-marker-yellow:hsl(60, 97%, 73%);--ck-highlight-pen-green:hsl(112, 100%, 27%);--ck-highlight-pen-red:hsl(0, 85%, 49%);--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing) / 2);--ck-todo-list-checkmark-size:16px}.ck-content .image.image_resized{max-width:100%;display:block;-webkit-box-sizing:border-box;box-sizing:border-box}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck-content .image>figcaption{display:table-caption;caption-side:bottom;word-break:break-word;color:var(--ck-color-image-caption-text);background-color:var(--ck-color-image-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .image{display:table;clear:both;text-align:center;margin:.9em auto;min-width:50px}.ck-content .image img{display:block;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;max-width:100%;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start}.ck-content .image-inline picture{display:-webkit-box;display:-ms-flexbox;display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;-ms-flex-negative:1;flex-shrink:1;max-width:100%}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-right:0;margin-left:auto}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-top:var(--ck-inline-image-style-spacing);margin-bottom:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck-content .marker-yellow{background-color:var(--ck-highlight-marker-yellow)}.ck-content .marker-green{background-color:var(--ck-highlight-marker-green)}.ck-content .marker-pink{background-color:var(--ck-highlight-marker-pink)}.ck-content .marker-blue{background-color:var(--ck-highlight-marker-blue)}.ck-content .pen-red{color:var(--ck-highlight-pen-red);background-color:transparent}.ck-content .pen-green{color:var(--ck-highlight-pen-green);background-color:transparent}.ck-content .text-tiny{font-size:.7em}.ck-content .text-small{font-size:.85em}.ck-content .text-big{font-size:1.4em}.ck-content .text-huge{font-size:1.8em}.ck-content hr{margin:15px 0;height:4px;background:#ddd;border:0}.ck-content pre{padding:1em;color:#353535;background:rgba(199,199,199,.3);border:1px solid #c4c4c4;border-radius:2px;text-align:left;direction:ltr;-moz-tab-size:4;-o-tab-size:4;tab-size:4;white-space:pre-wrap;font-style:normal;min-width:200px}.ck-content pre code{background:unset;padding:0;border-radius:0}.ck-content blockquote{overflow:hidden;padding-right:1.5em;padding-left:1.5em;margin-left:0;margin-right:0;font-style:italic;border-left:solid 5px #ccc}.ck-content[dir=rtl] blockquote{border-left:0;border-right:solid 5px #ccc}.ck-content code{background-color:rgba(199,199,199,.3);padding:.15em;border-radius:2px}.ck-content .table>figcaption{display:table-caption;caption-side:top;word-break:break-word;text-align:center;color:var(--ck-color-table-caption-text);background-color:var(--ck-color-table-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .table{margin:.9em auto;display:table}.ck-content .table table{border-collapse:collapse;border-spacing:0;width:100%;height:100%;border:1px double #b2b2b2}.ck-content .table table td,.ck-content .table table th{min-width:2em;padding:.4em;border:1px solid #bfbfbf}.ck-content .table table th{font-weight:700;background:#000}.ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-content .page-break{position:relative;clear:both;padding:5px 0;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ck-content .page-break::after{content:"";position:absolute;border-bottom:2px dashed #c4c4c4;width:100%}.ck-content .page-break__label{position:relative;z-index:1;padding:.3em .6em;display:block;text-transform:uppercase;border:1px solid #c4c4c4;border-radius:2px;font-family:Helvetica,Arial,Tahoma,Verdana,Sans-Serif;font-size:.75em;font-weight:700;color:#333;background:#fff;-webkit-box-shadow:2px 2px 1px rgba(0,0,0,.15);box-shadow:2px 2px 1px rgba(0,0,0,.15);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ck-content .media{clear:both;margin:.9em 0;display:block;min-width:15em}.ck-content .todo-list{list-style:none}.ck-content .todo-list li{margin-bottom:5px}.ck-content .todo-list li .todo-list{margin-top:5px}.ck-content .todo-list .todo-list__label>input{-webkit-appearance:none;display:inline-block;position:relative;width:var(--ck-todo-list-checkmark-size);height:var(--ck-todo-list-checkmark-size);vertical-align:middle;border:0;left:-25px;margin-right:-15px;right:0;margin-left:0}.ck-content .todo-list .todo-list__label>input::before{display:block;position:absolute;-webkit-box-sizing:border-box;box-sizing:border-box;content:"";width:100%;height:100%;border:1px solid #333;border-radius:2px;-webkit-transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border;transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border}.ck-content .todo-list .todo-list__label>input::after{display:block;position:absolute;-webkit-box-sizing:content-box;box-sizing:content-box;pointer-events:none;content:"";left:calc(var(--ck-todo-list-checkmark-size)/ 3);top:calc(var(--ck-todo-list-checkmark-size)/ 5.3);width:calc(var(--ck-todo-list-checkmark-size)/ 5.3);height:calc(var(--ck-todo-list-checkmark-size)/ 2.6);border-style:solid;border-color:transparent;border-width:0 calc(var(--ck-todo-list-checkmark-size)/ 8) calc(var(--ck-todo-list-checkmark-size)/ 8) 0;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ck-content .todo-list .todo-list__label>input[checked]::before{background:#25ab33;border-color:#25ab33}.ck-content .todo-list .todo-list__label>input[checked]::after{border-color:#fff}.ck-content .todo-list .todo-list__label .todo-list__label__description{vertical-align:middle}.ck-content span[lang]{font-style:italic}.ck-content .mention{background:var(--ck-color-mention-background);color:var(--ck-color-mention-text)}@media print{.ck-content .page-break{padding:0}.ck-content .page-break::after{display:none}}.ck-content img{max-width:100%}
			</style>
			<style id="main-style">
				:root {
					--color-primary: #2c3d94;
					--ck-border-radius: 0.25rem;
					--border-radius-base: 0.25rem;
					--select-dropdown-height: 2rem;
				}
				.ck-content a {
					color: var(--color-primary);
					text-decoration: none;
				}
				.ck-content p,
				.ck-content a {
					font-size: 1rem;
				}
				.ck-content {
					box-sizing: border-box;
				}
				.ck-content *{
					box-sizing: border-box;
					height: unset!important;
				}
			</style>
			
				<title>Xem mẫu email</title></head>
				<body>
					<div class="main ck-content" style="padding: 40px;margin: 0 auto; width: 600px;background-color: #F8F8F8; font-family: ''Montserrat'', Helvetica, sans-serif;">
						<div class="email-container" style="background-color: #FFFFFF;">
							<div class="logo-container" style="padding: 20px 20px 5px 20px;">
								$HEADER
							</div>
							<div classname="logo-container-divider" style="border-bottom: 1px solid #cdcdcd;"></div>
							<div class="content-container" style="padding: 20px 40px 10px 40px;">
								<p><img class="image_resized" style="width:428px;" src="$IMG_PATH/resources/upload/file/mail/images/icon_import_banner.PNG"></p><p>Kính gửi: Quý Khách hàng $CUSTOMER_NAME</p><p><br>Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT) kính gửi lời chào trân trọng tới Quý Khách hàng. Cảm ơn Quý Khách hàng đã tin tưởng và sử dụng sản phẩm dịch vụ của VNPT trong thời gian qua.</p><p><br>Nhằm đem tới những trải nghiệm tốt nhất cho Quý Khách hàng khi tìm kiếm sản phẩm dịch vụ viễn thông, công nghệ thông tin cho doanh nghiệp, VNPT đã lập tài khoản MIỄN PHÍ cho Quý Khách hàng trên nền tảng oneSME.vn với thông tin như sau:</p><p>Tên doanh nghiệp: $ENTERPRISE_NAME</p><p>Mã số thuế: $ENTERPRISE_TIN</p><p><br>Vui lòng ấn Kích hoạt để kích hoạt tài khoản đã được tạo cho email $CUSTOMER_EMAIL và đổi mật khẩu để sử dụng:</p><p style="text-align:center;"><a href="$ACTIVE_LINK"><strong>KÍCH HOẠT</strong></a></p><p><br>Quý Khách hàng có thể sử dụng tài khoản đã kích hoạt để đăng nhập https://onesme.vn và tận hưởng muôn vàn tiện ích:</p><p>🔹 Gia hạn gói cước dễ dàng, nhanh chóng chỉ với vài cú click chuột</p><p>🔹 Tìm kiếm, tra cứu các sản phẩm, dịch vụ thiết yếu cho doanh nghiệp</p><p>🔹 Thanh toán trực tuyến, nhận ngay tài khoản sử dụng một số dịch vụ: Hóa đơn điện tử, Bảo hiểm xã hội, Dịch vụ Máy chủ ảo Smart Cloud…</p><p>🔹 Tận hưởng nhiều chương trình giá ưu đãi chỉ có trên nền tảng https://onesme.vn</p><p><br>oneSME là nền tảng hội tụ các công nghệ hiện đại và kiến trúc tiên tiến, đảm bảo an toàn bảo mật thông tin và mang tới cho Quý Khách hàng những trải nghiệm số chỉ có trên oneSME.vn khi tìm hiểu và mua online các sản phẩm, dịch vụ số phục vụ quá trình quản trị và vận hành doanh nghiệp.</p><p><br>Vui lòng xem thêm thông tin về nền tảng oneSME.vn tại các đường dẫn dưới đây:</p><p>✅ Giới thiệu về nền tảng mua sắm oneSME</p><p>✅ Video hướng dẫn sử dụng oneSME &nbsp;</p><p>✅ Tài liệu hướng dẫn sử dụng oneSME &nbsp;</p><p>Đây là email được gửi tự động, Quý Khách hàng vui lòng không trả lời email này.&nbsp;</p>
							</div>
							<div class="footer-container" style="padding: 20px 40px 10px 40px;">
								$FOOTER
							</div>
						</div>
					</div>
				</body></html>', "content_html_default" = '<!DOCTYPE html>
<html>

<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>

<body
    style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #ffffff;">
        <div class="logo-container"
            style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER</div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon"
                    src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
                <p class="main-title"
                    style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">
                    Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
                <p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0; margin-left: 10px;">Khách hàng $CUSTOMER_COMPANY_NAME đăng kí thuê bao
                    $NAME_SERVICE - $NAME_PRICING đã gặp sự cố</p>
                <p style="margin: 0; margin-left: 10px;">Truy cập vào trang <a href="$WEB_HOST">quản lý sự cố</a> để
                    kiểm tra chi tiết</p>
                <p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">$FOOTER</div>
    </div>
</body>

</html>'  WHERE "code" = 'MIG-01';

DELETE FROM "vnpt_dev"."action_notification" WHERE action_code = 'MIG-03';
INSERT INTO "vnpt_dev"."action_notification" ("id", "name", "is_send_email", "is_send_sms", "is_notification", "parent_id", "created_by", "created_at", "modified_by", "modified_at", "receiver", "action_code", "allow_change_email", "allow_change_sms", "allow_change_notification", "priority_order", "is_send_telegram", "allow_change_telegram") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.action_notification), 'Thông báo kích hoạt tài khoản sau khi được import cho KHCN', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'MIG'), 'system', '2022-06-15 00:00:00', '1', '2022-07-21 00:00:00', 'SME', 'MIG-03', 'B', 'B', 'B', 28003, 0, 'D');

DELETE FROM "vnpt_dev"."mail_template" WHERE code = 'MIG-03';
INSERT INTO "vnpt_dev"."mail_template" ("id", "code", "name", "status", "content_html", "content_html_default", "content_text", "content_text_default", "parent_code", "title", "title_default", "priority_order", "email_type") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.mail_template), 'MIG-03', 'Thông báo mở tài khoản trên oneSME', 1, '<html><head>
					<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
					<link rel="preconnect" href="https://fonts.googleapis.com">
					<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
					
			<style id="ckeditor-style">
			 	:root{--ck-color-image-caption-background:hsl(0, 0%, 97%);--ck-color-image-caption-text:hsl(0, 0%, 20%);--ck-color-mention-background:hsla(341, 100%, 30%, 0.1);--ck-color-mention-text:hsl(341, 100%, 30%);--ck-color-table-caption-background:hsl(0, 0%, 97%);--ck-color-table-caption-text:hsl(0, 0%, 20%);--ck-highlight-marker-blue:hsl(201, 97%, 72%);--ck-highlight-marker-green:hsl(120, 93%, 68%);--ck-highlight-marker-pink:hsl(345, 96%, 73%);--ck-highlight-marker-yellow:hsl(60, 97%, 73%);--ck-highlight-pen-green:hsl(112, 100%, 27%);--ck-highlight-pen-red:hsl(0, 85%, 49%);--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing) / 2);--ck-todo-list-checkmark-size:16px}.ck-content .image.image_resized{max-width:100%;display:block;-webkit-box-sizing:border-box;box-sizing:border-box}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck-content .image>figcaption{display:table-caption;caption-side:bottom;word-break:break-word;color:var(--ck-color-image-caption-text);background-color:var(--ck-color-image-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .image{display:table;clear:both;text-align:center;margin:.9em auto;min-width:50px}.ck-content .image img{display:block;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;max-width:100%;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start}.ck-content .image-inline picture{display:-webkit-box;display:-ms-flexbox;display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;-ms-flex-negative:1;flex-shrink:1;max-width:100%}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-right:0;margin-left:auto}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-top:var(--ck-inline-image-style-spacing);margin-bottom:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck-content .marker-yellow{background-color:var(--ck-highlight-marker-yellow)}.ck-content .marker-green{background-color:var(--ck-highlight-marker-green)}.ck-content .marker-pink{background-color:var(--ck-highlight-marker-pink)}.ck-content .marker-blue{background-color:var(--ck-highlight-marker-blue)}.ck-content .pen-red{color:var(--ck-highlight-pen-red);background-color:transparent}.ck-content .pen-green{color:var(--ck-highlight-pen-green);background-color:transparent}.ck-content .text-tiny{font-size:.7em}.ck-content .text-small{font-size:.85em}.ck-content .text-big{font-size:1.4em}.ck-content .text-huge{font-size:1.8em}.ck-content hr{margin:15px 0;height:4px;background:#ddd;border:0}.ck-content pre{padding:1em;color:#353535;background:rgba(199,199,199,.3);border:1px solid #c4c4c4;border-radius:2px;text-align:left;direction:ltr;-moz-tab-size:4;-o-tab-size:4;tab-size:4;white-space:pre-wrap;font-style:normal;min-width:200px}.ck-content pre code{background:unset;padding:0;border-radius:0}.ck-content blockquote{overflow:hidden;padding-right:1.5em;padding-left:1.5em;margin-left:0;margin-right:0;font-style:italic;border-left:solid 5px #ccc}.ck-content[dir=rtl] blockquote{border-left:0;border-right:solid 5px #ccc}.ck-content code{background-color:rgba(199,199,199,.3);padding:.15em;border-radius:2px}.ck-content .table>figcaption{display:table-caption;caption-side:top;word-break:break-word;text-align:center;color:var(--ck-color-table-caption-text);background-color:var(--ck-color-table-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .table{margin:.9em auto;display:table}.ck-content .table table{border-collapse:collapse;border-spacing:0;width:100%;height:100%;border:1px double #b2b2b2}.ck-content .table table td,.ck-content .table table th{min-width:2em;padding:.4em;border:1px solid #bfbfbf}.ck-content .table table th{font-weight:700;background:#000}.ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-content .page-break{position:relative;clear:both;padding:5px 0;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ck-content .page-break::after{content:"";position:absolute;border-bottom:2px dashed #c4c4c4;width:100%}.ck-content .page-break__label{position:relative;z-index:1;padding:.3em .6em;display:block;text-transform:uppercase;border:1px solid #c4c4c4;border-radius:2px;font-family:Helvetica,Arial,Tahoma,Verdana,Sans-Serif;font-size:.75em;font-weight:700;color:#333;background:#fff;-webkit-box-shadow:2px 2px 1px rgba(0,0,0,.15);box-shadow:2px 2px 1px rgba(0,0,0,.15);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ck-content .media{clear:both;margin:.9em 0;display:block;min-width:15em}.ck-content .todo-list{list-style:none}.ck-content .todo-list li{margin-bottom:5px}.ck-content .todo-list li .todo-list{margin-top:5px}.ck-content .todo-list .todo-list__label>input{-webkit-appearance:none;display:inline-block;position:relative;width:var(--ck-todo-list-checkmark-size);height:var(--ck-todo-list-checkmark-size);vertical-align:middle;border:0;left:-25px;margin-right:-15px;right:0;margin-left:0}.ck-content .todo-list .todo-list__label>input::before{display:block;position:absolute;-webkit-box-sizing:border-box;box-sizing:border-box;content:"";width:100%;height:100%;border:1px solid #333;border-radius:2px;-webkit-transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border;transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border}.ck-content .todo-list .todo-list__label>input::after{display:block;position:absolute;-webkit-box-sizing:content-box;box-sizing:content-box;pointer-events:none;content:"";left:calc(var(--ck-todo-list-checkmark-size)/ 3);top:calc(var(--ck-todo-list-checkmark-size)/ 5.3);width:calc(var(--ck-todo-list-checkmark-size)/ 5.3);height:calc(var(--ck-todo-list-checkmark-size)/ 2.6);border-style:solid;border-color:transparent;border-width:0 calc(var(--ck-todo-list-checkmark-size)/ 8) calc(var(--ck-todo-list-checkmark-size)/ 8) 0;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ck-content .todo-list .todo-list__label>input[checked]::before{background:#25ab33;border-color:#25ab33}.ck-content .todo-list .todo-list__label>input[checked]::after{border-color:#fff}.ck-content .todo-list .todo-list__label .todo-list__label__description{vertical-align:middle}.ck-content span[lang]{font-style:italic}.ck-content .mention{background:var(--ck-color-mention-background);color:var(--ck-color-mention-text)}@media print{.ck-content .page-break{padding:0}.ck-content .page-break::after{display:none}}.ck-content img{max-width:100%}
			</style>
			<style id="main-style">
				:root {
					--color-primary: #2c3d94;
					--ck-border-radius: 0.25rem;
					--border-radius-base: 0.25rem;
					--select-dropdown-height: 2rem;
				}
				.ck-content a {
					color: var(--color-primary);
					text-decoration: none;
				}
				.ck-content p,
				.ck-content a {
					font-size: 1rem;
				}
				.ck-content {
					box-sizing: border-box;
				}
				.ck-content *{
					box-sizing: border-box;
					height: unset!important;
				}


			</style>
			
				<title>Xem mẫu email</title></head>
				<body>
					<div class="main ck-content" style="padding: 40px;margin: 0 auto; width: 600px;background-color: #F8F8F8; font-family: ''Montserrat'', Helvetica, sans-serif;">
						<div class="email-container" style="background-color: #FFFFFF;">
							<div class="logo-container" style="padding: 20px 20px 5px 20px;">
								$HEADER
							</div>
							<div classname="logo-container-divider" style="border-bottom: 1px solid #cdcdcd;"></div>
							<div class="content-container" style="padding: 20px 40px 10px 40px;">
								<p><img class="image_resized" style="width:428px;" src="$IMG_PATH/resources/upload/file/mail/images/icon_import_banner.PNG"></p><p>Kính gửi: Quý Khách hàng $CUSTOMER_NAME</p><p><br>Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT) kính gửi lời chào trân trọng tới Quý Khách hàng. Cảm ơn Quý Khách hàng đã tin tưởng và sử dụng sản phẩm dịch vụ của VNPT trong thời gian qua.</p><p><br>Nhằm đem tới những trải nghiệm tốt nhất cho Quý Khách hàng khi tìm kiếm sản phẩm dịch vụ viễn thông, công nghệ thông tin cho doanh nghiệp, VNPT đã lập tài khoản MIỄN PHÍ cho Quý Khách hàng trên nền tảng oneSME.vn với thông tin như sau:</p><p>Tên khách hàng: $ENTERPRISE_NAME</p><p>Số giấy tờ: $IDENTITY_NO</p><p><br>Vui lòng ấn Kích hoạt để kích hoạt tài khoản đã được tạo cho email $CUSTOMER_EMAIL và đổi mật khẩu để sử dụng:</p><p style="text-align:center;"><a href="$ACTIVE_LINK"><strong>KÍCH HOẠT</strong></a></p><p><br>Quý Khách hàng có thể sử dụng tài khoản đã kích hoạt để đăng nhập https://onesme.vn và tận hưởng muôn vàn tiện ích:</p><p>🔹 Gia hạn gói cước dễ dàng, nhanh chóng chỉ với vài cú click chuột</p><p>🔹 Tìm kiếm, tra cứu các sản phẩm, dịch vụ thiết yếu cho doanh nghiệp</p><p>🔹 Thanh toán trực tuyến, nhận ngay tài khoản sử dụng một số dịch vụ: Hóa đơn điện tử, Bảo hiểm xã hội, Dịch vụ Máy chủ ảo Smart Cloud…</p><p>🔹 Tận hưởng nhiều chương trình giá ưu đãi chỉ có trên nền tảng https://onesme.vn</p><p><br>oneSME là nền tảng hội tụ các công nghệ hiện đại và kiến trúc tiên tiến, đảm bảo an toàn bảo mật thông tin và mang tới cho Quý Khách hàng những trải nghiệm số chỉ có trên oneSME.vn khi tìm hiểu và mua online các sản phẩm, dịch vụ số phục vụ quá trình quản trị và vận hành doanh nghiệp.</p><p><br>Vui lòng xem thêm thông tin về nền tảng oneSME.vn tại các đường dẫn dưới đây:</p><p>✅ Giới thiệu về nền tảng mua sắm oneSME</p><p>✅ Video hướng dẫn sử dụng oneSME &nbsp;</p><p>✅ Tài liệu hướng dẫn sử dụng oneSME &nbsp;</p><p>Đây là email được gửi tự động, Quý Khách hàng vui lòng không trả lời email này.&nbsp;</p>
							</div>
							<div class="footer-container" style="padding: 20px 40px 10px 40px;">
								$FOOTER
							</div>
						</div>
					</div>
				
				
				</body></html>', '<html><head>
					<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
					<link rel="preconnect" href="https://fonts.googleapis.com">
					<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
					
			<style id="ckeditor-style">
			 	:root{--ck-color-image-caption-background:hsl(0, 0%, 97%);--ck-color-image-caption-text:hsl(0, 0%, 20%);--ck-color-mention-background:hsla(341, 100%, 30%, 0.1);--ck-color-mention-text:hsl(341, 100%, 30%);--ck-color-table-caption-background:hsl(0, 0%, 97%);--ck-color-table-caption-text:hsl(0, 0%, 20%);--ck-highlight-marker-blue:hsl(201, 97%, 72%);--ck-highlight-marker-green:hsl(120, 93%, 68%);--ck-highlight-marker-pink:hsl(345, 96%, 73%);--ck-highlight-marker-yellow:hsl(60, 97%, 73%);--ck-highlight-pen-green:hsl(112, 100%, 27%);--ck-highlight-pen-red:hsl(0, 85%, 49%);--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing) / 2);--ck-todo-list-checkmark-size:16px}.ck-content .image.image_resized{max-width:100%;display:block;-webkit-box-sizing:border-box;box-sizing:border-box}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck-content .image>figcaption{display:table-caption;caption-side:bottom;word-break:break-word;color:var(--ck-color-image-caption-text);background-color:var(--ck-color-image-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .image{display:table;clear:both;text-align:center;margin:.9em auto;min-width:50px}.ck-content .image img{display:block;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;max-width:100%;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start}.ck-content .image-inline picture{display:-webkit-box;display:-ms-flexbox;display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;-ms-flex-negative:1;flex-shrink:1;max-width:100%}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-right:0;margin-left:auto}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-top:var(--ck-inline-image-style-spacing);margin-bottom:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck-content .marker-yellow{background-color:var(--ck-highlight-marker-yellow)}.ck-content .marker-green{background-color:var(--ck-highlight-marker-green)}.ck-content .marker-pink{background-color:var(--ck-highlight-marker-pink)}.ck-content .marker-blue{background-color:var(--ck-highlight-marker-blue)}.ck-content .pen-red{color:var(--ck-highlight-pen-red);background-color:transparent}.ck-content .pen-green{color:var(--ck-highlight-pen-green);background-color:transparent}.ck-content .text-tiny{font-size:.7em}.ck-content .text-small{font-size:.85em}.ck-content .text-big{font-size:1.4em}.ck-content .text-huge{font-size:1.8em}.ck-content hr{margin:15px 0;height:4px;background:#ddd;border:0}.ck-content pre{padding:1em;color:#353535;background:rgba(199,199,199,.3);border:1px solid #c4c4c4;border-radius:2px;text-align:left;direction:ltr;-moz-tab-size:4;-o-tab-size:4;tab-size:4;white-space:pre-wrap;font-style:normal;min-width:200px}.ck-content pre code{background:unset;padding:0;border-radius:0}.ck-content blockquote{overflow:hidden;padding-right:1.5em;padding-left:1.5em;margin-left:0;margin-right:0;font-style:italic;border-left:solid 5px #ccc}.ck-content[dir=rtl] blockquote{border-left:0;border-right:solid 5px #ccc}.ck-content code{background-color:rgba(199,199,199,.3);padding:.15em;border-radius:2px}.ck-content .table>figcaption{display:table-caption;caption-side:top;word-break:break-word;text-align:center;color:var(--ck-color-table-caption-text);background-color:var(--ck-color-table-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .table{margin:.9em auto;display:table}.ck-content .table table{border-collapse:collapse;border-spacing:0;width:100%;height:100%;border:1px double #b2b2b2}.ck-content .table table td,.ck-content .table table th{min-width:2em;padding:.4em;border:1px solid #bfbfbf}.ck-content .table table th{font-weight:700;background:#000}.ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-content .page-break{position:relative;clear:both;padding:5px 0;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ck-content .page-break::after{content:"";position:absolute;border-bottom:2px dashed #c4c4c4;width:100%}.ck-content .page-break__label{position:relative;z-index:1;padding:.3em .6em;display:block;text-transform:uppercase;border:1px solid #c4c4c4;border-radius:2px;font-family:Helvetica,Arial,Tahoma,Verdana,Sans-Serif;font-size:.75em;font-weight:700;color:#333;background:#fff;-webkit-box-shadow:2px 2px 1px rgba(0,0,0,.15);box-shadow:2px 2px 1px rgba(0,0,0,.15);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ck-content .media{clear:both;margin:.9em 0;display:block;min-width:15em}.ck-content .todo-list{list-style:none}.ck-content .todo-list li{margin-bottom:5px}.ck-content .todo-list li .todo-list{margin-top:5px}.ck-content .todo-list .todo-list__label>input{-webkit-appearance:none;display:inline-block;position:relative;width:var(--ck-todo-list-checkmark-size);height:var(--ck-todo-list-checkmark-size);vertical-align:middle;border:0;left:-25px;margin-right:-15px;right:0;margin-left:0}.ck-content .todo-list .todo-list__label>input::before{display:block;position:absolute;-webkit-box-sizing:border-box;box-sizing:border-box;content:"";width:100%;height:100%;border:1px solid #333;border-radius:2px;-webkit-transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border;transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border}.ck-content .todo-list .todo-list__label>input::after{display:block;position:absolute;-webkit-box-sizing:content-box;box-sizing:content-box;pointer-events:none;content:"";left:calc(var(--ck-todo-list-checkmark-size)/ 3);top:calc(var(--ck-todo-list-checkmark-size)/ 5.3);width:calc(var(--ck-todo-list-checkmark-size)/ 5.3);height:calc(var(--ck-todo-list-checkmark-size)/ 2.6);border-style:solid;border-color:transparent;border-width:0 calc(var(--ck-todo-list-checkmark-size)/ 8) calc(var(--ck-todo-list-checkmark-size)/ 8) 0;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ck-content .todo-list .todo-list__label>input[checked]::before{background:#25ab33;border-color:#25ab33}.ck-content .todo-list .todo-list__label>input[checked]::after{border-color:#fff}.ck-content .todo-list .todo-list__label .todo-list__label__description{vertical-align:middle}.ck-content span[lang]{font-style:italic}.ck-content .mention{background:var(--ck-color-mention-background);color:var(--ck-color-mention-text)}@media print{.ck-content .page-break{padding:0}.ck-content .page-break::after{display:none}}.ck-content img{max-width:100%}
			</style>
			<style id="main-style">
				:root {
					--color-primary: #2c3d94;
					--ck-border-radius: 0.25rem;
					--border-radius-base: 0.25rem;
					--select-dropdown-height: 2rem;
				}
				.ck-content a {
					color: var(--color-primary);
					text-decoration: none;
				}
				.ck-content p,
				.ck-content a {
					font-size: 1rem;
				}
				.ck-content {
					box-sizing: border-box;
				}
				.ck-content *{
					box-sizing: border-box;
					height: unset!important;
				}
			</style>
			
				<title>Xem mẫu email</title></head>
				<body>
					<div class="main ck-content" style="padding: 40px;margin: 0 auto; width: 600px;background-color: #F8F8F8; font-family: ''Montserrat'', Helvetica, sans-serif;">
						<div class="email-container" style="background-color: #FFFFFF;">
							<div class="logo-container" style="padding: 20px 20px 5px 20px;">
								$HEADER
							</div>
							<div classname="logo-container-divider" style="border-bottom: 1px solid #cdcdcd;"></div>
							<div class="content-container" style="padding: 20px 40px 10px 40px;">
								<p><img class="image_resized" style="width:428px;" src="$IMG_PATH/resources/upload/file/mail/images/icon_import_banner.PNG"></p><p>Kính gửi: Quý Khách hàng $CUSTOMER_NAME</p><p><br>Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT) kính gửi lời chào trân trọng tới Quý Khách hàng. Cảm ơn Quý Khách hàng đã tin tưởng và sử dụng sản phẩm dịch vụ của VNPT trong thời gian qua.</p><p><br>Nhằm đem tới những trải nghiệm tốt nhất cho Quý Khách hàng khi tìm kiếm sản phẩm dịch vụ viễn thông, công nghệ thông tin cho doanh nghiệp, VNPT đã lập tài khoản MIỄN PHÍ cho Quý Khách hàng trên nền tảng oneSME.vn với thông tin như sau:</p><p>Tên khách hàng: $ENTERPRISE_NAME</p><p>Số giấy tờ: $IDENTITY_NO</p><p><br>Vui lòng ấn Kích hoạt để kích hoạt tài khoản đã được tạo cho email $CUSTOMER_EMAIL và đổi mật khẩu để sử dụng:</p><p style="text-align:center;"><a href="$ACTIVE_LINK"><strong>KÍCH HOẠT</strong></a></p><p><br>Quý Khách hàng có thể sử dụng tài khoản đã kích hoạt để đăng nhập https://onesme.vn và tận hưởng muôn vàn tiện ích:</p><p>🔹 Gia hạn gói cước dễ dàng, nhanh chóng chỉ với vài cú click chuột</p><p>🔹 Tìm kiếm, tra cứu các sản phẩm, dịch vụ thiết yếu cho doanh nghiệp</p><p>🔹 Thanh toán trực tuyến, nhận ngay tài khoản sử dụng một số dịch vụ: Hóa đơn điện tử, Bảo hiểm xã hội, Dịch vụ Máy chủ ảo Smart Cloud…</p><p>🔹 Tận hưởng nhiều chương trình giá ưu đãi chỉ có trên nền tảng https://onesme.vn</p><p><br>oneSME là nền tảng hội tụ các công nghệ hiện đại và kiến trúc tiên tiến, đảm bảo an toàn bảo mật thông tin và mang tới cho Quý Khách hàng những trải nghiệm số chỉ có trên oneSME.vn khi tìm hiểu và mua online các sản phẩm, dịch vụ số phục vụ quá trình quản trị và vận hành doanh nghiệp.</p><p><br>Vui lòng xem thêm thông tin về nền tảng oneSME.vn tại các đường dẫn dưới đây:</p><p>✅ Giới thiệu về nền tảng mua sắm oneSME</p><p>✅ Video hướng dẫn sử dụng oneSME &nbsp;</p><p>✅ Tài liệu hướng dẫn sử dụng oneSME &nbsp;</p><p>Đây là email được gửi tự động, Quý Khách hàng vui lòng không trả lời email này.&nbsp;</p>
							</div>
							<div class="footer-container" style="padding: 20px 40px 10px 40px;">
								$FOOTER
							</div>
						</div>
					</div>
				</body></html>', NULL, NULL, 'MIG', 'VNPT THÔNG BÁO MỞ TÀI KHOẢN TRÊN NỀN TẢNG ONESME.VN', 'VNPT THÔNG BÁO MỞ TÀI KHOẢN TRÊN NỀN TẢNG ONESME.VN', 28003, 1);


DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code = 'MIG-03';
INSERT INTO "vnpt_dev"."param_email" ("id", "id_mail_template", "param_name", "remark", "mail_template_code", "param_default_value") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'MIG-03'), '$CUSTOMER_NAME', '[Tên khách hàng]', 'MIG-03', NULL);
INSERT INTO "vnpt_dev"."param_email" ("id", "id_mail_template", "param_name", "remark", "mail_template_code", "param_default_value") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'MIG-03'), '$CUSTOMER_EMAIL', '[Tên đăng nhập]', 'MIG-03', NULL);
INSERT INTO "vnpt_dev"."param_email" ("id", "id_mail_template", "param_name", "remark", "mail_template_code", "param_default_value") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'MIG-03'), '$ACTIVE_LINK', '[Link kích hoạt]', 'MIG-03', NULL);
INSERT INTO "vnpt_dev"."param_email" ("id", "id_mail_template", "param_name", "remark", "mail_template_code", "param_default_value") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'MIG-03'), '$ENTERPRISE_NAME', '[Tên doanh nghiệp]', 'MIG-03', NULL);
INSERT INTO "vnpt_dev"."param_email" ("id", "id_mail_template", "param_name", "remark", "mail_template_code", "param_default_value") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'MIG-03'), '$IDENTITY_NO', '[Số giấy tờ]', 'MIG-03', NULL);

