DELETE FROM "vnpt_dev"."permission" WHERE code = 'ADMIN_XOA_TAI_KHOAN_IMPORTED';
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Xóa tài khoản đã import', 'ADMIN_XOA_TAI_KHOAN_IMPORTED', (SELECT id FROM vnpt_dev.permission WHERE code='QUAN_LY_TAI_KHOAN_1' LIMIT 1), 9997600);

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='ADMIN_XOA_TAI_KHOAN_IMPORTED' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));


INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='ADMIN_XOA_TAI_KHOAN_IMPORTED' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='ADMIN_XOA_TAI_KHOAN_IMPORTED' LIMIT 1), 1);

DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'ADMIN_XOA_TAI_KHOAN_IMPORTED' AND method = 'DELETE';
INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/admin-portal/import-migration/delete-customer', 'ADMIN_XOA_TAI_KHOAN_IMPORTED', 'DELETE');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='ADMIN_XOA_TAI_KHOAN_IMPORTED' AND method = 'DELETE' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'ADMIN_XOA_TAI_KHOAN_IMPORTED' LIMIT 1) LIMIT 1), 1, 1);

