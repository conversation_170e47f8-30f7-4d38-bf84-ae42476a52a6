-- Thê<PERSON> phân quyền bật tắt tài khoản nhà cung cấp
DELETE FROM "vnpt_dev"."permission" WHERE code = 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP';
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'TAO_TAI_KHOAN_NHA_CUNG_CAP');
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP');
DELETE FROM "vnpt_dev"."roles_permissions" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP');

INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Bật/Tắt tài khoản nhà cung cấp', 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP', (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_TAI_KHOAN_NHA_CUNG_CAP'), (SELECT max(priority) + 1 FROM vnpt_dev.permission));
INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP' LIMIT 1), 1);

DELETE  FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP' AND method = 'PUT');
DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP' AND method = 'PUT';

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/users-dev/{userId}/status/{status}', 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP', 'PUT');
INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP' AND method = 'PUT' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'BAT_TAT_TAI_KHOAN_NHA_CUNG_CAP' LIMIT 1) LIMIT 1), 1, 1);
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;