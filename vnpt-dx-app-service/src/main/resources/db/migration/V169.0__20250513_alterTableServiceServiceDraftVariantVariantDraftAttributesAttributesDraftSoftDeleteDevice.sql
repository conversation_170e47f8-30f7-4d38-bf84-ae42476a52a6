-- soft delete service tạo trước ngày 07/04/2025 và service có product_type device
UPDATE vnpt_dev.services
SET deleted_flag = 0
WHERE product_type = 1 AND CREATED_AT < DATE '2025-04-07';

-- Tạo table tạm thời chừa id service tạo trước ngày 07/04/2025 và service có product_type device
CREATE TEMP TABLE service_device AS
SELECT id FROM vnpt_dev.services
WHERE product_type = 1 AND CREATED_AT < DATE '2025-04-07';

-- soft delete service_draft của device đúng điều kiện
UPDATE vnpt_dev.services_draft
SET deleted_flag = 0
FROM service_device
WHERE services_draft.service_id = service_device.id;

-- Soft delete variant của device đúng điều kiện
UPDATE vnpt_dev.VARIANT
SET deleted_flag = 0
FROM service_device
WHERE variant.SERVICE_ID = service_device.id;

-- Soft delete Variant_draft
UPDATE vnpt_dev.VARIANT_DRAFT
SET deleted_flag = 0
FROM service_device
WHERE variant_draft.SERVICE_ID = service_device.id;

-- Tạ<PERSON> bảng attributes id của attributes mapping với device
CREATE TEMP TABLE  attribute_service_device AS
SELECT ATTRIBUTES.id
FROM ATTRIBUTES JOIN MAPPING_ATTRIBUTES_SERVICE ON ATTRIBUTES.id = MAPPING_ATTRIBUTES_SERVICE.ATTRIBUTES_id
JOIN service_device ON service_device.id = mapping_attributes_service.service_id;

-- Soft delete attributes mapping với device
UPDATE vnpt_dev."attributes"
SET deleted_flag = 0
FROM attribute_service_device
WHERE attribute_service_device.id = ATTRIBUTES.id;

-- Soft delete attributes_draft của attributes mapping với device
UPDATE vnpt_dev.ATTRIBUTES_DRAFT
SET deleted_flag = 0
FROM attribute_service_device
WHERE attribute_service_device.id = attributes_draft.ATTRIBUTES_ID;
