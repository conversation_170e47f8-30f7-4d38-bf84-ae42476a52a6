CREATE OR REPLACE VIEW vnpt_dev.view_latest_pricing
as with max_pricing_version as (
        select max(id) as latest_pricing_id, pricing_draft_id from vnpt_dev.pricing group by pricing_draft_id
    )
    select
        pricing.id as pricing_latest_id,
        max_pricing_version.pricing_draft_id,
        pricing.pricing_name as pricing_latest_name
    from vnpt_dev.pricing
        join max_pricing_version on max_pricing_version.latest_pricing_id = pricing.id;


CREATE OR REPLACE VIEW vnpt_dev.view_latest_combo_plan
as with max_combo_plan_version as (
        select max(id) as latest_combo_plan_id, combo_plan_draft_id from vnpt_dev.combo_plan group by combo_plan_draft_id
    )
    select
        combo_plan.id as combo_plan_latest_id,
        max_combo_plan_version.combo_plan_draft_id,
        combo_plan.combo_name as combo_plan_latest_name
    from vnpt_dev.combo_plan
        join max_combo_plan_version on max_combo_plan_version.latest_combo_plan_id = combo_plan.id;

CREATE OR REPLACE VIEW vnpt_dev.view_latest_combo
as with max_combo_version as (
        select max(id) as latest_combo_id, combo_draft_id from vnpt_dev.combo group by combo_draft_id
    )
    select
        combo.id as combo_latest_id,
        max_combo_version.combo_draft_id,
        combo.combo_name as combo_latest_name
    from vnpt_dev.combo
        join max_combo_version on max_combo_version.latest_combo_id = combo.id;
