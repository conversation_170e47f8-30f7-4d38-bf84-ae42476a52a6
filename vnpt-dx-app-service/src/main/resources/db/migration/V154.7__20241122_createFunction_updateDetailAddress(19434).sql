-- Tạo function cập nhật địa chỉ chi tiết
CREATE OR REPLACE FUNCTION func_update_full_address(tbl_name varchar) RETURNS int2 AS $$
DECLARE
    sql_query TEXT;
    num_updated INTEGER;
BEGIN
    sql_query :=
        'WITH new_info AS (
            SELECT
                address.id as address_id,
                street.name as new_street_name, 
                ward.code as new_ward_code,
                ward.name as new_ward_name,
                district.code as new_district_code,
                district.name as new_district_name
            FROM
                vnpt_dev.address 
                LEFT JOIN vnpt_dev.province ON province.id = address.province_id
                LEFT JOIN vnpt_dev.district ON district.id = address.district_id AND district.province_id = province.id 
                LEFT JOIN vnpt_dev.ward ON ward.id = address.ward_id AND ward.district_id = district.id AND ward.province_id = province.id 
                LEFT JOIN vnpt_dev.street ON street.id = address.street_id AND street.ward_id = ward.id AND street.province_id = province.id
            WHERE
                address.street_name IS DISTINCT FROM street.name OR
                address.ward_code IS DISTINCT FROM ward.code OR
                address.ward_name IS DISTINCT FROM ward.name OR
                address.district_code IS DISTINCT FROM district.code OR
                address.district_name IS DISTINCT FROM district.name
            LIMIT 1000
        )

        UPDATE vnpt_dev.address 
            SET district_code = new_info.new_district_code,
            district_name = new_info.new_district_name,
            ward_code = new_info.new_ward_code,
            ward_name = new_info.new_ward_name,
            street_name = new_info.new_street_name
        FROM new_info
        WHERE 
            id = new_info.address_id';

    EXECUTE REPLACE(sql_query, 'address', tbl_name);
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS num_updated = ROW_COUNT;
    RETURN num_updated;
END;
$$ LANGUAGE plpgsql;

-- Chạy job định kỳ cập nhật thông tin địa chỉ chi tiết
INSERT INTO vnpt_dev.schedules(bean_name, method_name, cron_expression, remark, job_status) VALUES
('syncing-geography', 'syncAddressDetail', '0 */30 13-22 * * ?', 'Đồng bộ thông tin địa chỉ chi tiết', 1);