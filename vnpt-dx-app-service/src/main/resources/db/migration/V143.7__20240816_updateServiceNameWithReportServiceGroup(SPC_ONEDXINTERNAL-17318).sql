DROP VIEW IF EXISTS report_view_subscription_service_group;
Create VIEW report_view_subscription_service_group as
SELECT
    subscriptions.id,
    subscriptions.service_id,
    subscriptions.quantity,
    subscriptions.total_amount,
    subscriptions.status,
    subscriptions.deleted_flag,
    subscriptions.created_by,
    subscriptions.modified_by,
    subscriptions.created_at,
    subscriptions.modified_at,
    subscriptions.user_id,
    subscriptions.from_date,
    subscriptions.cancelled_time,
    subscriptions.pricing_id,
    subscriptions.sme_subscription_id,
    subscriptions.installed,
    subscriptions.expired_time,
    subscriptions.used_quantity,
    subscriptions.registed_by,
    subscriptions.sub_registration_id,
    subscriptions.started_at,
    subscriptions.start_charge_at,
    subscriptions.trial_day,
    subscriptions.reg_type,
    subscriptions.payment_method,
    subscriptions.confirm_status,
    subscriptions.current_cycle,
    subscriptions.phone_no,
    subscriptions.contact,
    subscriptions.address,
    subscriptions.sub_code,
    subscriptions.start_current_cycle,
    subscriptions.end_current_cycle,
    subscriptions.end_current_cycle_new,
    subscriptions.current_payment_date,
    subscriptions.dhsxkd_sub_code,
    subscriptions.next_payment_time,
    subscriptions.awaiting_cancel,
    subscriptions.pre_order,
    subscriptions.number_of_cycles,
    subscriptions.cycle_type,
    subscriptions.traffic_id,
    subscriptions.combo_plan_id,
    subscriptions.canceled_by,
    subscriptions.subscription_contract_id,
    subscriptions.called_trans,
    subscriptions.change_date,
    subscriptions.change_status,
    subscriptions.update_date,
    subscriptions.update_status,
    subscriptions.portal_type,
    subscriptions.message_setup,
    subscriptions.employee_code,
    subscriptions.pricing_multi_plan_id,
    subscriptions.number_of_cycles_default,
    subscriptions.refer_subscription,
    subscriptions.traffic_user,
    null::text AS pricing_name,
        users.customer_type,
    (concat(services.service_name, ' (', service_group.name, ')'))::text AS service_name,
        services.id AS c_service_id,
    (concat(service_group.id, '0004'))::bigint AS service_unique_id,
        null::bigint AS pricing_unique_id,
        '-1'::integer AS p_payment_cycle,
        '-1'::integer AS p_cycle_type,
        null::bigint AS price,
        4 AS subscription_type,
    CASE
        WHEN (service_group.group_service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
        WHEN (service_group.group_service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
        WHEN (service_group.group_service_owner IS NULL) THEN 'OS'::text
        ELSE NULL::text
        END AS onos,
    report_view_customer.nation_id AS c_nation_id,
    report_view_customer.nation_name AS c_nation_name,
    report_view_customer.province_id AS c_province_id,
    report_view_customer.province_name AS c_province_name,
    report_view_customer.district_id AS c_district_id,
    report_view_customer.district_name AS c_district_name,
    report_view_customer.ward_id AS c_ward_id,
    report_view_customer.ward_name AS c_ward_name,
    report_view_customer.street_name,
    report_view_customer.address AS c_address,
    report_view_customer.name AS sme_name,
    report_view_customer.email AS c_email,
    report_view_customer.phone_number AS c_phone_number,
    report_view_customer.tin,
    billings.status AS payment_status,
    billings.next_total_amount,
    bill_item.quantity AS service_quantity,
    '-1'::integer AS order_status,
        ''::text AS order_status_name,
        subscriptions.status AS c_status,
    CASE
        WHEN (subscriptions.employee_code IS NOT NULL) THEN 1
        WHEN (subscriptions.traffic_id IS NOT NULL) THEN 2
        WHEN (subscriptions.portal_type = 1) THEN 3
        WHEN (subscriptions.portal_type = 2) THEN 3
        WHEN (subscriptions.portal_type = 3) THEN 0
        ELSE NULL::integer
END AS source,
    service_group.user_id AS provider_id,
    '-1'::integer AS categories_id,
    REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    1 AS is_one_time,
    0 as has_renew
FROM ((((((vnpt_dev.subscriptions
    JOIN vnpt_dev.service_group ON ((subscriptions.service_group_id = service_group.id)))
    LEFT JOIN vnpt_dev.users ON ((users.id = subscriptions.user_id)))
    LEFT JOIN vnpt_dev.services ON ((services.id = subscriptions.service_id)))
    LEFT JOIN vnpt_dev.report_view_customer ON ((subscriptions.user_id = report_view_customer.id)))
    LEFT JOIN vnpt_dev.billings ON (((billings.subscriptions_id = subscriptions.id)
        AND (billings.id IN (
            SELECT max(billings.id) AS max
            FROM vnpt_dev.billings
            GROUP BY billings.subscriptions_id)))))
    LEFT JOIN vnpt_dev.bill_item ON (((bill_item.billing_id = billings.id) AND (bill_item.object_id = subscriptions.combo_plan_id) AND (bill_item.object_type = 1))))
WHERE ((subscriptions.deleted_flag = 1) AND (subscriptions.confirm_status = 1));