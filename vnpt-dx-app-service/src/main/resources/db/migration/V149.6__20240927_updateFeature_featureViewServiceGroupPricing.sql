DROP VIEW IF EXISTS vnpt_dev.feature_view_service_group_pricing;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_service_group_pricing
            (service_group_id, service_group_pricing_id, service_group_draft_id, service_id, service_name, status,
             deleted_flag, allow_multi_sub ,pricing_id, pricing_item_id,
             pricing_name, pricing_multi_plan_id, payment_cycle, circle_type, variant_id, variant_item_id, variant_name,
             object_type_int,
             object_type, icon)
AS
WITH item_detail AS (SELECT sgp.id AS serviceGroupPricingId,
                            sgp.service_group_id_draft AS serviceGroupDraftId,
                            services.id AS serviceId,
                            services.service_name AS serviceName,
                            services.status AS status,
                            services.deleted_flag AS deletedFlag,
                            services.allow_multi_sub as allowMultiSub,
                            MAX(pricing.id) AS pricingId,
                            MAX(case when sgpi.object_type = 'PRICING' then sgpi.id end) AS pricingItemId,
                            MAX(pricing.pricing_name) AS pricingName,
                            MAX(pmp.id) AS pricingMultiPlanId,
                            MAX(pmp.payment_cycle) AS paymentCycle,
                            MAX(pmp.circle_type) AS circleType,
                            MAX(variant.id) AS variantId,
                            MAX(case when sgpi.object_type = 'DEVICE_VARIANT' then sgpi.id end) AS variantItemId,
                            MAX(variant.full_name) AS variantName,
                            MAX(CASE sgpi.object_type
                                    WHEN 'DEVICE_VARIANT' THEN 3
                                    WHEN 'DEVICE_NO_VARIANT' THEN 2
                                    ELSE 1
                                END) AS objectTypeInt
                     FROM vnpt_dev.service_group_pricing_item sgpi
                              LEFT JOIN vnpt_dev.service_group_pricing sgp ON sgpi.service_group_pricing_id = sgp.id
                              LEFT JOIN vnpt_dev.services services ON sgp.service_id = services.id
                              LEFT JOIN vnpt_dev.pricing pricing
                                        ON sgpi.object_type = 'PRICING' AND sgpi.object_id = pricing.id
                              LEFT JOIN vnpt_dev.pricing_multi_plan pmp
                                        ON sgpi.pricing_multi_plan_id = pmp.id AND sgpi.object_type = 'PRICING'
                              LEFT JOIN vnpt_dev.variant variant
                                        ON sgpi.object_type = 'DEVICE_VARIANT' AND sgpi.object_id = variant.id
                     GROUP BY sgp.id, services.id, services.service_name)
SELECT sg.id AS serviceGroupId,
       items.*,
       CASE
           WHEN items.objectTypeInt = 1 THEN 'PRICING'
           WHEN items.objectTypeInt = 2 THEN 'DEVICE_NO_VARIANT'
           WHEN items.objectTypeInt = 3 THEN 'DEVICE_VARIANT'
           ELSE 'OTHER'
           END AS objectType,
       icon.file_path as iconPath
FROM item_detail items
         JOIN vnpt_dev.service_group_pricing sgp ON items.serviceGroupPricingId = sgp.id
         LEFT JOIN vnpt_dev.service_group_draft draft ON items.serviceGroupDraftId = draft.id
         LEFT JOIN vnpt_dev.service_group sg ON sg.id = sgp.service_group_id
         LEFT JOIN vnpt_dev.file_attach icon on items.serviceId = icon.service_id and icon.object_type = 0
