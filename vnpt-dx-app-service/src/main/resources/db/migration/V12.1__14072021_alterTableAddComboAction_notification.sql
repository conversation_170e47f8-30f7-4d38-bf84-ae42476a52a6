alter table vnpt_dev.combo
add column reason_reject text;
comment on column vnpt_dev.combo.reason_reject is '<PERSON>ý do từ chối khi approve';

alter table vnpt_dev.combo_draft
add column reason_reject text;
comment on column vnpt_dev.combo_draft.reason_reject is '<PERSON><PERSON> do từ chối khi approve';

alter table vnpt_dev.combo_plan
add column reason_reject text;
comment on column vnpt_dev.combo_plan.reason_reject is '<PERSON>ý do từ chối khi approve';

alter table vnpt_dev.combo_plan_draft
add column reason_reject text;
comment on column vnpt_dev.combo_plan_draft.reason_reject is '<PERSON><PERSON> do từ chối khi approve';

alter table vnpt_dev.param_email
add column param_email_code varchar(50);
comment on column vnpt_dev.param_email.param_email_code is 'Mã của màn con';

CREATE TABLE vnpt_dev.coupon_combo_plan (
	id bigserial NOT NULL,
	combo_plan_id int8 NULL,
	coupon_id int8 NULL,
	CONSTRAINT coupon_combo_plan_pkey PRIMARY KEY (id)
);


CREATE TABLE vnpt_dev.coupon_combo_plan_apply (
	id bigserial NOT NULL,
	combo_plan_id int8 NULL,
	coupon_id int8 NULL,
	CONSTRAINT coupon_combo_plan_apply_pkey PRIMARY KEY (id)
);


TRUNCATE TABLE vnpt_dev.action_notification RESTART IDENTITY;

INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Quản lý tạo tài khoản và phân quyền',1,1,1,-1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','',NULL,NULL,NULL,NULL),
	 ('Admin tạo tài khoản nhóm Admin',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người được tạo tài khoản','AC-01','A','D','D'),
	 ('Admin tắt tài khoản nhóm Admin',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Người bị tắt tài khoản','AC-02','B','D','D'),
	 ('Admin bật tài khoản nhóm Admin',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Người được bật tài khoản','AC-03','B','D','D'),
	 ('Admin tắt tài khoản Dev/ SME',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người bị tắt tài khoản','AC-04','B','D','D'),
	 ('Admin bật tài khoản Dev/ SME',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người được bật tài khoản','AC-05','B','D','D'),
	 ('Admin chỉnh sửa thông tin tài khoản Dev/ SME',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người bị chỉnh sửa','AC-06','B','D','D'),
	 ('Dev/SME tạo mới tài khoản cho chính mình',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người tạo tài khoản','AC-07','A','D','D'),
	 ('Dev tạo mới tài khoản Dev Busniess/ Dev Operator',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người được tạo tài khoản','AC-08','A','D','D'),
	 ('SME tạo mới tài khoản SME Employee',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người được tạo tài khoản','AC-09','A','D','D');
INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Dev/ SME Admin tắt tài khoản của nhân viên',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người bị tắt tài khoản','AC-10','B','D','D'),
	 ('Dev/ SME Admin bật tài khoản của nhân viên',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Người được bật tài khoản','AC-11','B','D','D'),
	 ('Reset password thuộc nhóm Admin',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Chủ tài khoản','AC-12','A','D','D'),
	 ('Reset password thuộc nhóm Dev/ SME',1,0,0,1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Chủ tài khoản','AC-13','A','D','D'),
	 ('Quản lý phiếu hỗ trợ',1,1,1,-1,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','',NULL,NULL,NULL,NULL),
	 ('SME tạo 1 phiếu hỗ trợ mới',1,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu hỗ trợ','TK-01','B','D','D'),
	 ('SME tạo 1 phiếu hỗ trợ mới',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin (chăm sóc khách hàng)','TK-02','B','D','B'),
	 ('SME sửa nội dung phiếu hỗ trợ',1,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu hỗ trợ','TK-03','B','D','D'),
	 ('SME sửa nội dung phiếu hỗ trợ',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin (chăm sóc khách hàng)','TK-04','B','D','B'),
	 ('Admin phân công người hỗ trợ',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu','TK-05','B','D','B');
INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Admin phân công người hỗ trợ',1,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Asignee (người được phân công)','TK-06','B','D','D'),
	 ('Người dùng bất kỳ nhập phản hồi',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu','TK-07','B','D','B'),
	 ('Người dùng bất kỳ nhập phản hồi',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin (chăm sóc khách hàng)','TK-08','B','D','B'),
	 ('Người dùng bất kỳ nhập phản hồi',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Asignee (người được phân công)','TK-09','B','D','B'),
	 ('Người dùng bất kỳ sửa phản hồi',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu','TK-10','B','D','B'),
	 ('Người dùng bất kỳ sửa phản hồi',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin (chăm sóc khách hàng)','TK-11','B','D','B'),
	 ('Người dùng bất kỳ sửa phản hồi',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Asignee (người được phân công)','TK-12','B','D','B'),
	 ('Người dùng bất kỳ xóa phản hồi của mình',0,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu','TK-13','D','D','D'),
	 ('Người dùng bất kỳ xóa phản hồi của mình',0,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin (chăm sóc khách hàng)','TK-14','D','D','D'),
	 ('Người dùng bất kỳ xóa phản hồi của mình',0,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Asignee (người được phân công)','TK-15','D','D','D');
INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Admin xóa phản hồi của người khác',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Người tạo phản hồi','TK-16','B','D','B'),
	 ('Admin xóa phản hồi của người khác',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu','TK-17','B','D','B'),
	 ('Đóng phiếu hỗ trợ',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo phiếu','TK-18','B','D','B'),
	 ('Đóng phiếu hỗ trợ',1,0,1,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin (chăm sóc khách hàng)','TK-19','B','D','B'),
	 ('Đóng phiếu hỗ trợ',1,0,0,15,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Asignee (người được phân công)','TK-20','B','D','D'),
	 ('Đánh giá dịch vụ',1,1,1,-1,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','',NULL,NULL,NULL,NULL),
	 ('SME tạo đánh giá/ nhận xét',1,0,0,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo nhận xét','EV-01','B','D','D'),
	 ('SME tạo đánh giá/ nhận xét',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev admin tạo dịch vụ','EV-02','B','D','B'),
	 ('SME sửa đánh giá/ nhận xét',1,0,0,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo nhận xét','EV-03','B','D','D'),
	 ('SME sửa đánh giá/ nhận xét',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev admin tạo dịch vụ','EV-04','B','D','B');
INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Dev tạo phản hồi',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo nhận xét','EV-05','B','D','B'),
	 ('Dev tạo phản hồi',1,0,0,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev admin tạo dịch vụ','EV-06','B','D','D'),
	 ('Dev sửa phản hồi',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo nhận xét','EV-07','B','D','B'),
	 ('Dev sửa phản hồi',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev admin tạo dịch vụ','EV-08','B','D','B'),
	 ('Admin xóa nhận xét',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo nhận xét','EV-09','B','D','B'),
	 ('Admin xóa nhận xét',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev admin tạo dịch vụ','EV-10','B','D','B'),
	 ('Admin xóa phản hồi',1,0,1,39,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME tạo nhận xét','EV-11','B','D','B'),
	 ('Admin xóa phản hồi',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev admin tạo dịch vụ','EV-12','B','D','B'),
	 ('Hệ thống gửi nhắc nhở đánh giá',1,0,1,36,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','SME đã sử dụng dịch vụ','EV-13','B','D','B'),
	 ('SME Home Page',1,1,1,-1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00',NULL,NULL,NULL,NULL,NULL);
INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Khách hàng gửi thông tin liên hệ thành công',1,0,1,50,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Admin đầu mối của Tỉnh','HP-01','B','D','B'),
	 ('Quản lý dịch vụ bổ sung',1,0,1,-1,'system','2021-07-14 19:20:59.518308','<EMAIL>','2021-07-14 19:20:59.518308','','AO','B','D','B'),
	 ('Dịch vụ bổ sung được yêu cầu cập nhật',1,0,1,52,'system','2021-07-14 19:27:36.277303','<EMAIL>','2021-07-14 19:27:36.277303','Người tạo dịch vụ bổ sung','AO-05','B','D','B'),
	 ('Dịch vụ bổ sung đã bị từ chối',1,0,1,52,'system','2021-07-14 19:28:29.82307','<EMAIL>','2021-07-14 19:28:29.82307','Người tạo dịch vụ bổ sung','AO-04','B','D','B'),
	 ('Dịch vụ bổ sung được phê duyệt',1,0,1,52,'system','2021-07-14 19:28:33.260132','<EMAIL>','2021-07-14 19:28:33.260132','Người tạo dịch vụ bổ sung','AO-03','B','D','B'),
	 ('Yêu cầu phê duyệt dịch vụ bổ sung',1,0,1,52,'system','2021-07-14 19:28:36.842208','<EMAIL>','2021-07-14 19:28:36.842208','Admin, Super Admin','AO-02','B','D','B'),
	 ('Yêu cầu phê duyệt dịch vụ bổ sung',1,0,1,52,'system','2021-07-14 19:28:40.185242','<EMAIL>','2021-07-14 19:28:40.185242','Người tạo dịch vụ bổ sung','AO-01','B','D','B');


INSERT INTO vnpt_dev.action_notification ("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification) VALUES
	('Quản lý chương trình khuyến mại', 1, 1, 1, -1, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', null, null, null, null),
	('Tặng Khuyến mại khi chọn Tổng hóa đơn/Doanh nghiệp và Tổng hóa đơn', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-01', 'B', 'D', 'B'),
	('Tặng Khuyến mại khi chọn Doanh nghiệp, sản phẩm, dịch vụ/Sản phẩm, dịch vụ bổ sung', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-02', 'B', 'D', 'B'),
	('Tặng Khuyến mại sản phẩm khi chọn Doanh nghiệp, sản phẩm, dịch vụ/Sản phẩm, dịch vụ bổ sung', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-03', 'B', 'D', 'B'),
	('Tặng Khuyến mại khi chọn Doanh nghiệp/ Sản phẩm/Doanh nghiệp, sản phẩm', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-04', 'B', 'D', 'B'),
	('Tặng Khuyến mại sản phẩm khi chọn Doanh nghiệp/ Sản phẩm/Doanh nghiệp, sản phẩm', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-05', 'B', 'D', 'B'),
	('Tặng Khuyến mại khi chọn Dịch vụ bổ sung', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-06', 'B', 'D', 'B'),
	('Tặng Khuyến mại sản phẩm khi chọn Dịch vụ bổ sung', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-07', 'B', 'D', 'B'),
	('Tặng Khuyến mại khi chọn doanh nghiệp và dịch vụ bổ sung', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-08', 'B', 'D', 'B'),
	('Tặng Khuyến mại sản phẩm khi chọn doanh nghiệp và dịch vụ bổ sung', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-09', 'B', 'D', 'B'),
	('Tặng Khuyến mại khi chọn Nhà cung cấp dịch vụ', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-10', 'B', 'D', 'B'),
	('Yêu cầu phê duyệt chương trình khuyến mại do Dev tạo', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-11', 'B', 'D', 'B'),
	('Yêu cầu phê duyệt chương trình khuyến mại do admin tạo', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-12', 'B', 'D', 'B'),
	('Chương trình khuyến mại được phê duyệt', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-13', 'B', 'D', 'B'),
	('Chương trình khuyến mại đã bị từ chối', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-14', 'B', 'D', 'B'),
	('Chương trình khuyến mại được yêu cầu cập nhật', 1, 0, 1, 58, 'system', '2021-03-18 00:00:00', '<EMAIL>', '2021-06-23 00:00:00', '', 'CP-15', 'B', 'D', 'B');


INSERT INTO vnpt_dev.action_notification ("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Quản lý combo dịch vụ', 1, 1, 1, -1, 'system', now(), '<EMAIL>', now(), '', '', '', '', '');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Yêu cầu phê duyệt Combo dịch vụ', 1, 0, 1, 74, 'system', now(), '<EMAIL>', now(), 'Người tạo combo dịch vụ', 'CB-01', 'B', 'D', 'B');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Yêu cầu phê duyệt Combo dịch vụ', 1, 0, 1, 74, 'system', now(), '<EMAIL>', now(), 'Admin, Super Admin', 'CB-02', 'B', 'D', 'B');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Combo dịch vụ được phê duyệt', 1, 0, 1, 74, 'system', now(), '<EMAIL>', now(), 'Người tạo combo dịch vụ', 'CB-03', 'B', 'D', 'B');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Combo dịch vụ đã bị từ chối', 1, 0, 1, 74, 'system', now(), '<EMAIL>', now(), 'Người tạo combo dchị vụ', 'CB-04', 'B', 'D', 'B');


INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Quản lý gói combo dịch vụ', 1, 0, 1, -1, 'system', now(), '<EMAIL>', now(), '', '', '', '', '');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Yêu cầu phê duyệt Gói combo dịch vụ', 1, 0, 1, 79, 'system', now(), '<EMAIL>', now(), 'Người tạo goi combo dich vụ', 'SCP-01', 'B', 'D', 'B');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Yêu cầu phê duyệt Gói combo dịch vụ', 1, 0, 1, 79, 'system', now(), '<EMAIL>', now(), 'Admin, Super Admin phê duyêt dich vụ', 'SCP-02', 'B', 'D', 'B');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Gói combo dịch vụ được phê duyệt', 1, 0, 1, 79, 'system', now(), '<EMAIL>', now(), 'Người tạo goi combo dich vụ', 'SCP-03', 'B', 'D', 'B');
INSERT INTO vnpt_dev.action_notification
("name", is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by, modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification)
	VALUES('Gói combo dịch vụ đã bị từ chối', 1, 0, 1, 79, 'system', now(), '<EMAIL>', now(), 'Người tạo goi combo dich vụ', 'SCP-04', 'B', 'D', 'B');





INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Quản lý gói dịch vụ',1,1,1,-1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','',NULL,NULL,NULL,NULL),
	 ('Yêu cầu phê duyệt Gói dịch vụ',1,0,0,84,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Dev tạo Gói dịch vụ','PC-01','B','D','B'),
	 ('Yêu cầu phê duyệt Gói dịch vụ',1,0,0,84,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Admin','PC-02','B','D','B'),
	 ('Gói dịch vụ được phê duyệt',1,0,0,84,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Dev tạo Gói dịch vụ','PC-03','B','D','B'),
	 ('Gói dịch vụ đã bị từ chối',1,0,0,84,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Dev tạo Gói dịch vụ','PC-04','B','D','B');


INSERT INTO vnpt_dev.action_notification ("name",is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification) VALUES
	 ('Quản lý dịch vụ',1,1,1,-1,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','',NULL,NULL,NULL,NULL),
	 ('Yêu cầu phê duyệt dịch vụ',1,0,0,89,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Dev tạo dịch vụ','SV-01','B','D','D'),
	 ('Yêu cầu phê duyệt dịch vụ',1,0,0,89,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Admin','SV-02','B','D','D'),
	 ('Dịch vụ được phê duyệt',1,0,0,89,'system','2021-03-18 00:00:00','<EMAIL>','2021-06-23 00:00:00','Dev tạo dịch vụ','SV-03','B','D','D'),
	 ('Dịch vụ đã bị từ chối',1,0,0,89,'system','2021-03-18 00:00:00','<EMAIL>','2021-04-05 00:00:00','Dev tạo dịch vụ','SV-04','B','D','D');