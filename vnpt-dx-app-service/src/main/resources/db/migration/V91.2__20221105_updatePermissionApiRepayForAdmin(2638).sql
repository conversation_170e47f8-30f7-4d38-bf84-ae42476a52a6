--insert into vnpt_dev.api_permission ( api_id, permission_portal_id, map_permission_portal, delete_flag) values (400, 87, 1, 1);
insert into vnpt_dev.api_permission ( api_id, permission_portal_id, map_permission_portal, delete_flag)
select 400, 87, 1, 1
    where not exists  (
        select id FROM vnpt_dev.api_permission WHERE api_id = 400 and permission_portal_id = 87 and map_permission_portal = 1 and delete_flag = 1
    );
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;
