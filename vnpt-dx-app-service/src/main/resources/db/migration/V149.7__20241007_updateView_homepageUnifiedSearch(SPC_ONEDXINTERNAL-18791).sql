drop view if exists vnpt_dev.feature_view_homepage_unified_search cascade;

create or replace view vnpt_dev.feature_view_homepage_unified_search as
(
with latestPricingVersion as (SELECT max(pricing.id) AS latest_pricing_id,
                                     pricing.pricing_draft_id
                              FROM vnpt_dev.pricing
                                       join vnpt_dev.pricing_draft as pd
                                            on pd.id = pricing.pricing_draft_id and pd.status = 1 and
                                               pd.deleted_flag = 1
                              WHERE pricing.status = 1
                                and pricing.approve = 1
                                and pricing.deleted_flag = 1
                              GROUP BY pricing.pricing_draft_id),
     latestPricing as (SELECT pricing.id AS pricing_id,
                              latestPricingVersion.pricing_draft_id,
                              pricing.pricing_name AS pricing_name,
                              pricing.service_id as service_id,
                              pricing.description as pricing_description
                       FROM vnpt_dev.pricing
                                JOIN latestPricingVersion ON latestPricingVersion.latest_pricing_id = pricing.id),
     raw_service as (select services.id as service_id,
                            services.service_name as service_name,
                            regexp_replace(vnpt_dev.unaccent(services.description), '[ ]', '',
                                           'g') as service_description,
                            mpricing.pricing_id as pricing_id,
                            mpricing.pricing_name as pricing_name,
                            mpricing.pricing_description as pricing_description,
                            mpricing.pricing_draft_id as pricing_draft_id,
                            provider.id as provider_id,
                            case
                                when provider.id is not null then coalesce(provider.name,
                                                                           concat_ws(' ', provider.last_name, provider.first_name))
                                else services.provider
                                end as provider_name,
                            serviceCategories.categories as category_name,
                            services.product_type as product_type,
                            coalesce(services.customer_type_code, '[]') as customer_types
                     from vnpt_dev.services
                              left join vnpt_dev.users as provider on provider.id = services.user_id
                              left join (select mapServCategory.service_id as service_id,
                                                string_agg(categories.name, ',') as categories
                                         from vnpt_dev.mapping_services_categories as mapServCategory
                                                  join vnpt_dev.categories on mapServCategory.categories_id = categories.id
                                         group by mapServCategory.service_id) as serviceCategories
                                        on serviceCategories.service_id = services.id
                              left join latestPricing mpricing on mpricing.service_id = services.id
                     where services.deleted_flag = 1
                       and services.status = 1),
     raw_combo as (select combo.id as combo_id,
                          combo.combo_name as combo_name,
                          combo.description as combo_description,
                          combo.short_description as combo_short_description,
                          cbPlan.id as comboplan_id,
                          cbPlan.combo_plan_draft_id as comboplan_draft_id,
                          cbPlan.combo_name as comboplan_name,
                          cbPlan.description as comboplan_description,
                          provider.id as provider_id,
                          case
                              when provider.id is not null then coalesce(provider.name,
                                                                         concat_ws(' ', provider.last_name, provider.first_name))
                              else combo.provider
                              end as provider_name,
                          categories.name as category_name,
                          -1 as product_type,
                          coalesce(combo.customer_type_code, '[]') as customer_types
                   from vnpt_dev.combo_plan as cbPlan
                            join (select max(id) as combo_plan_id
                                  from vnpt_dev.combo_plan
                                  group by combo_plan_draft_id) as latestCbPlan
                                 on latestCbPlan.combo_plan_id = cbPlan.id
                            join vnpt_dev.combo_plan_draft on (
                       combo_plan_draft.id = cbPlan.combo_plan_draft_id and
                       combo_plan_draft.deleted_flag = 1 and
                       combo_plan_draft.status = 1 and
                       combo_plan_draft.approve = 1)
                            join vnpt_dev.combo ON combo.id = (select max(id)
                                                               from vnpt_dev.combo
                                                               where combo_draft_id = cbPlan.combo_draft_id
                                                               GROUP BY combo_draft_id)
                            left join vnpt_dev.users as provider on provider.id = combo.user_id
                            left join vnpt_dev.categories on cast(categories.id as text) = any
                                                             (string_to_array(combo.categories_id, ',')) and
                                                             categories.deleted_flag = 1
                   where combo.status = 1
                     and combo.approve = 1
                     and combo.deleted_flag = 1),
     raw_service_group AS (SELECT sg.id AS service_group_id,
                                  sgd.id as service_group_draft_id,
                                  sg.name AS service_group_name,
                                  sg.description AS service_group_description,
                                  provider.id AS provider_id,
                                  CASE
                                      WHEN ARRAY ['ROLE_AMIN', 'ROLE_ADMIN_A', 'FULL_ADMIN']::text[] &&
                                           string_to_array(role.name, ', ')::text[]
                                          THEN 'Quản trị viên'
                                      WHEN provider.id IS NOT NULL THEN COALESCE(provider.name,
                                                                                 concat_ws(' '::text, provider.last_name, provider.first_name)::character varying)
                                      ELSE sg.created_by::character varying
                                      END AS provider_name,
                                  '-1'::integer AS product_type,
                                  categories.name AS category_name,
                                  COALESCE(sg.customer_type_code, '[]'::character varying) AS customer_types
                           FROM vnpt_dev.service_group sg
                                    JOIN vnpt_dev.service_group_draft sgd
                                         ON sgd.id = sg.group_service_draft_id AND sgd.deleted_flag = 1 AND
                                            sgd.status = 1 AND sgd.approve = 1
                                    LEFT JOIN vnpt_dev.users provider ON provider.id = sg.created_by
                                    LEFT JOIN (SELECT user_role.user_id as user_id,
                                                      STRING_AGG(role.name, ', ') AS name
                                               FROM vnpt_dev.users_roles user_role
                                                        JOIN vnpt_dev.role role ON role.id = user_role.role_id
                                               GROUP BY user_role.user_id) role on role.user_id = sg.created_by
                                    LEFT JOIN vnpt_dev.categories ON (categories.id::text = ANY
                                                                      (string_to_array(sg.categories_id::text, ','::text))) AND
                                                                     categories.deleted_flag = 1
                           WHERE sg.status = 1
                             AND sg.deleted_flag = 1),
     serviceProdAddPriority as (select 0 as type, 0 as additional_priority
                                union
                                select 1 as type, 1 as additional_priority)
-- Tên dịch vụ
select distinct 1400 as priority,
                service_name as keyword,
                'SERVICE_NAME' as keywordType,
                lower(vnpt_dev.unaccent(service_name)) as standardized,
                'SERVICE' as type,
                service_id as serviceId,
                service_name as serviceName,
                cast(null as int8) as pricingId,
                cast(null as text) as pricingName,
                pricing_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                coalesce(serviceProdAddPriority.additional_priority, 0) as additionalPriority
from raw_service
         left join serviceProdAddPriority on serviceProdAddPriority.type = product_type
where service_name is not null
  and service_name <> ''
union
-- Tên combo
select distinct 1300 as priority,
                combo_name as keyword,
                'COMBO_NAME' as keywordType,
                lower(vnpt_dev.unaccent(combo_name)) as standardized,
                'COMBO' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where combo_name is not null
  and combo_name <> ''
union
SELECT DISTINCT 1200 AS priority,
                raw_service_group.service_group_name AS keyword,
                'SERVICE_GROUP_NAME'::text AS keywordtype,
                lower(vnpt_dev.unaccent(raw_service_group.service_group_name::text)) AS standardized,
                'SERVICE_GROUP'::text AS type,
                raw_service_group.service_group_id AS serviceid,
                raw_service_group.service_group_name AS servicename,
                NULL::bigint AS pricingid,
                NULL::text AS pricingname,
                NULL::bigint AS pricingdraftid,
                raw_service_group.provider_id AS providerid,
                raw_service_group.product_type AS producttype,
                raw_service_group.customer_types AS customertypes,
                0 AS additionalpriority
FROM raw_service_group
WHERE raw_service_group.service_group_name IS NOT NULL
  AND raw_service_group.service_group_name::text <> ''::text
union
-- Tên gói dịch vụ
select distinct 1100 as priority,
                pricing_name as keyword,
                'PRICING_NAME' as keywordType,
                lower(vnpt_dev.unaccent(pricing_name)) as standardized,
                'PRICING' as type,
                service_id as serviceId,
                service_name as serviceName,
                pricing_id as pricingId,
                pricing_name as pricingName,
                pricing_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                coalesce(serviceProdAddPriority.additional_priority, 0) as additionalPriority
from raw_service
         left join serviceProdAddPriority on serviceProdAddPriority.type = product_type
where pricing_name is not null
  and pricing_name <> ''
union
-- Tên gói combo dịch vụ --
select distinct 1000 as priority,
                comboplan_name as keyword,
                'COMBO_PLAN_NAME' as keywordType,
                lower(vnpt_dev.unaccent(comboplan_name)) as standardized,
                'COMBO_PLAN' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where comboplan_name is not null
  and comboplan_name <> ''
union
-- Tên nhà cung cấp dịch vụ
select distinct 900 as priority,
                provider_name as keyword,
                'SERVICE_PROVIDER' as keywordType,
                lower(vnpt_dev.unaccent(provider_name)) as standardized,
                'SERVICE' as type,
                service_id as serviceId,
                service_name as serviceName,
                cast(null as int8) as pricingId,
                cast(null as text) as pricingName,
                pricing_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                coalesce(serviceProdAddPriority.additional_priority, 0) as additionalPriority
from raw_service
         left join serviceProdAddPriority on serviceProdAddPriority.type = product_type
where provider_name is not null
  and provider_name <> ''
union
-- Tên nhà cung cấp combo dịch vụ
select distinct 800 as priority,
                provider_name as keyword,
                'COMBO_PROVIDER' as keywordType,
                lower(vnpt_dev.unaccent(provider_name)) as standardized,
                'COMBO' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id as providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where provider_name is not null
  and provider_name <> ''
union
-- Danh mục dịch vụ
select distinct 700 as priority,
                category_name as keyword,
                'SERVICE_CATEGORIES' as keywordType,
                lower(vnpt_dev.unaccent(category_name)) as standardized,
                'SERVICE' as type,
                service_id as serviceId,
                service_name as serviceName,
                cast(null as int8) as pricingId,
                cast(null as text) as pricingName,
                pricing_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                coalesce(serviceProdAddPriority.additional_priority, 0) as additionalPriority
from raw_service
         left join serviceProdAddPriority on serviceProdAddPriority.type = product_type
where category_name is not null
  and category_name <> ''
union
-- Danh mục combo
select distinct 600 as priority,
                category_name as keyword,
                'COMBO_CATEGORIES' as keywordType,
                lower(vnpt_dev.unaccent(category_name)) as standardized,
                'COMBO' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where category_name is not NULL
  and category_name <> ''
union
-- Mô tả dịch vụ
select distinct 500 as priority,
                service_description as keyword,
                'SERVICE_DESCRIPTION' as keywordType,
                lower(vnpt_dev.unaccent(service_description)) as standardized,
                'SERVICE' as type,
                service_id as serviceId,
                service_name as serviceName,
                cast(null as int8) as pricingId,
                cast(null as text) as pricingName,
                pricing_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                coalesce(serviceProdAddPriority.additional_priority, 0) as additionalPriority
from raw_service
         left join serviceProdAddPriority on serviceProdAddPriority.type = product_type
where service_description is not null
  and service_description <> ''
union
-- Mô tả combo dịch vụ
select distinct 400 as priority,
                combo_description as keyword,
                'COMBO_DESCRIPTION' as keywordType,
                lower(vnpt_dev.unaccent(combo_description)) as standardized,
                'COMBO' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where combo_description is not null
  and combo_description <> ''
union
-- Mô tả ngắn combo (short_description)
select distinct 300 as priority,
                combo_short_description as keyword,
                'COMBO_SHORT_DESCRIPTION' as keywordType,
                lower(vnpt_dev.unaccent(combo_short_description)) as standardized,
                'COMBO_PLAN' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where comboplan_description is not null
  and comboplan_description <> ''
union
-- Mô tả gói dịch vụ
select distinct 200 as priority,
                pricing_description as keyword,
                'PRICING_DESCRIPTION' as keywordType,
                lower(vnpt_dev.unaccent(pricing_description)) as standardized,
                'PRICING' as type,
                service_id as serviceId,
                service_name as serviceName,
                pricing_id as pricingId,
                pricing_name as pricingName,
                pricing_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                coalesce(serviceProdAddPriority.additional_priority, 0) as additionalPriority
from raw_service
         left join serviceProdAddPriority on serviceProdAddPriority.type = product_type
where pricing_description is not null
  and pricing_description <> ''
union
-- Mô tả gói combo dịch vụ
select distinct 100 as priority,
                comboplan_description as keyword,
                'COMBO_PLAN_DESCRIPTION' as keywordType,
                lower(vnpt_dev.unaccent(comboplan_description)) as standardized,
                'COMBO_PLAN' as type,
                combo_id as serviceId,
                combo_name as serviceName,
                comboplan_id as pricingId,
                comboplan_name as pricingName,
                comboplan_draft_id as pricingDraftId,
                provider_id AS providerId,
                product_type as productType,
                customer_types as customerTypes,
                0 as additionalPriority
from raw_combo
where comboplan_description is not null
  and comboplan_description <> ''
    );