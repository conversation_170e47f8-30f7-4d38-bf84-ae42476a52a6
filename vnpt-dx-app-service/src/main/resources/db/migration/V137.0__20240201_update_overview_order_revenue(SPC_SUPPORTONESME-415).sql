-- vnpt_dev.overview_order_revenue source
create or replace view vnpt_dev.overview_order_revenue as (
with billingBillItemCte as (
    select
        b.subscriptions_id, b.created_source_migration, b.created_by,
        bill_item.amount, bill_item.object_type, bill_item.billing_id,
        bill_item.id as bill_item_id
    from
        vnpt_dev.billings b
        left join vnpt_dev.bill_item on b.id = bill_item.billing_id
), billingBillItemBillCPTotalCte as(
    select
        billBillItemCte.*,
        bct.amount_by_cash, bct.amount_by_percent
    from
        billingBillItemCte billBillItemCte
        left join vnpt_dev.bill_coupon_total bct on bct.billing_item_id = billBillItemCte.bill_item_id
), billingBillItemBillCPPrivateCte as(
    select
        billBillItemCte.*,
        bcp.amount_by_cash, bcp.amount_by_percent
    from
        billingBillItemCte billBillItemCte
        left join vnpt_dev.bill_coupon_private  bcp on bcp.billing_item_id = billBillItemCte.bill_item_id
)
select
    subdateandid.id,
    subdateandid.registration_date,
    bill.billing_code as billcode,
    case
        when bill.isrenew = 0 then subdateandid.registration_date
    else bill.created_at::date
    end as registrationdate,
    coalesce(newsubspromotionandunitamount.unitamount, renewsubspromotionandunitamount.unitamount, 0::double precision) as unitamount
from
    (
    select
        subscriptions.id,
        subscriptions.created_at::date as registration_date
    from
        vnpt_dev.subscriptions
    where
        subscriptions.deleted_flag = 1
        and (subscriptions.created_source_migration is null
            or subscriptions.created_source_migration = 0)
        and (subscriptions.pricing_id is not null
            or subscriptions.combo_plan_id is not null)
        and subscriptions.confirm_status = 1
    ) subdateandid
    left join
        (
        select
            b2.id,
            b2.status,
            b2.payment_date,
            b2.created_at,
            b2.billing_code,
            b2.subscriptions_id as sub_id,
            0 as isrenew
        from
            (
                select
                    max(b.id) as id,
                    b.subscriptions_id as sub_id
                from
                    vnpt_dev.billings b
                where
                    b.created_by::text <> 'batch'::text
                    or b.created_by is null
                    and (b.created_source_migration is null or b.created_source_migration = 0)
                group by
                    b.subscriptions_id
            ) newbill
            join vnpt_dev.billings b2 on  b2.id = newbill.id
        union
        select
            b2.id,
            b2.status,
            b2.payment_date,
            b2.created_at,
            b2.billing_code,
            b2.subscriptions_id as sub_id,
            1 as isrenew
        from
            (
                select
                    b.id,
                    b.subscriptions_id as sub_id
                from
                    vnpt_dev.billings b
                where
                    b.created_by::text = 'batch'::text
                and (b.created_source_migration is null
                    or b.created_source_migration = 0)
            ) renewbill
            join vnpt_dev.billings b2 on b2.id = renewbill.id
        ) bill
        on bill.sub_id = subdateandid.id
    left join
        (
        select
            subspromotionamount.id,
            case
                when subspromotionamount.promotionamount < 0::double precision then 0::double precision
            else subspromotionamount.promotionamount
            end as promotionamount,
            case
                when subsunitamount.unitamount < 0::double precision then 0::double precision
            else subsunitamount.unitamount
            end as unitamount
        from
            (
            select
                subsprivateamount.id,
                coalesce(subsprivateamount.privateamount, 0::double precision) + coalesce(substotalamount.totalamount, 0::double precision) as promotionamount
            from
                (
                select
                    cte.subscriptions_id as id,
                    sum(coalesce(cte.amount_by_cash, 0::double precision)) + sum(coalesce(cte.amount_by_percent, 0::double precision)) as privateamount
                from
                    billingBillItemBillCPPrivateCte cte
                where
                    cte.object_type <> 3
                    and (cte.created_source_migration is null
                    or cte.created_source_migration = 0)
                    and
                    (cte.created_by::text <> 'batch'::text or cte.created_by is null)
                group by
                    cte.subscriptions_id
                ) subsprivateamount
                join
                (
                select
                    cte.subscriptions_id as id,
                    sum(coalesce(cte.amount_by_cash, 0::double precision)) + sum(coalesce(cte.amount_by_percent, 0::double precision)) as totalamount
                from
                    billingBillItemBillCPTotalCte cte
                where
                    cte.object_type <> 3
                    and
                    (cte.created_source_migration is null or cte.created_source_migration = 0)
                    and
                    (cte.created_by::text <> 'batch'::text or cte.created_by is null)
                group by
                cte.subscriptions_id
                ) substotalamount
                    on subsprivateamount.id = substotalamount.id
            ) subspromotionamount
            join (
                select
                    billingBillItemCte.subscriptions_id as id,
                            sum(coalesce(billingBillItemCte.amount, 0::double precision)) as unitamount
                from
                    billingBillItemCte
                where
                    billingBillItemCte.object_type <> 3
                    and (billingBillItemCte.created_source_migration is null
                        or billingBillItemCte.created_source_migration = 0)
                    and (billingBillItemCte.created_by::text <> 'batch'::text
                        or billingBillItemCte.created_by is null)
                group by
                    billingBillItemCte.subscriptions_id)
                subsunitamount on subspromotionamount.id = subsunitamount.id
        ) newsubspromotionandunitamount
            on newsubspromotionandunitamount.id = subdateandid.id and bill.isrenew = 0
    left join (
        select
            subspromotionamount.id,
            case
                when subspromotionamount.promotionamount < 0::double precision then 0::double precision
            else subspromotionamount.promotionamount
            end as promotionamount,
            case
                when subsunitamount.unitamount < 0::double precision then 0::double precision
            else subsunitamount.unitamount
            end as unitamount
        from
            (
            select
                subsprivateamount.id,
                coalesce(subsprivateamount.privateamount, 0::double precision) + coalesce(substotalamount.totalamount, 0::double precision) as promotionamount
            from
                (
                select
                    cte.billing_id as id,
                    sum(coalesce(cte.amount_by_cash, 0::double precision)) + sum(coalesce(cte.amount_by_percent, 0::double precision)) as privateamount
                from
                    billingBillItemBillCPPrivateCte cte
                where
                    cte.object_type <> 3
                    and cte.created_by::text = 'batch'::text
                    and (cte.created_source_migration is null or cte.created_source_migration = 0)
                group by
                    cte.billing_id
                ) subsprivateamount
                join (
                select
                    cte.billing_id as id,
                    sum(coalesce(cte.amount_by_cash, 0::double precision)) + sum(coalesce(cte.amount_by_percent, 0::double precision)) as totalamount
                from
                    billingBillItemBillCPTotalCte cte
                where
                    cte.object_type <> 3
                    and cte.created_by::text = 'batch'::text
                    and (cte.created_source_migration is null or cte.created_source_migration = 0)
                group by
                    cte.billing_id
                ) substotalamount on subsprivateamount.id = substotalamount.id
            ) subspromotionamount
            join (
                select
                    billingBillItemCte.billing_id as id,
                    sum(coalesce(billingBillItemCte.amount, 0::double precision)) as unitamount
                from
                    billingBillItemCte
                where
                    billingBillItemCte.object_type <> 3
                    and billingBillItemCte.created_by::text = 'batch'::text
                    and (billingBillItemCte.created_source_migration is null or billingBillItemCte.created_source_migration = 0)
                group by
                    billingBillItemCte.billing_id
                ) subsunitamount on subspromotionamount.id = subsunitamount.id
        ) renewsubspromotionandunitamount on renewsubspromotionandunitamount.id = bill.id and bill.isrenew = 1
)