
drop table if exists vnpt_dev.automation_rule;
create table vnpt_dev.automation_rule (
                                          id bigserial,
                                          name varchar(50) not null,
                                          code varchar(10) not null,
                                          description varchar(500),
                                          status varchar(50),
                                          scanning_policy jsonb,
                                          created_by int8,
                                          modified_by int8,
                                          created_at timestamp,
                                          modified_at timestamp,
                                          migration_id int8,
                                          created_source int2,
                                          data_valid_from date,
                                          data_valid_to date,
                                          object_type varchar(50)
);

CREATE INDEX "index_automation_Id" ON "vnpt_dev"."automation_rule" USING btree ("id" desc nulls last);
COMMENT ON TABLE vnpt_dev.automation_rule is 'Bảng lưu thông tin chung automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.name is 'Tên automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.code is 'Mã automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.description is 'Mô tả automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.status is 'Trạng thái automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.scanning_policy is 'Cấu hình chạy automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.object_type is 'Đối tượng quét automation rule';
COMMENT ON COLUMN vnpt_dev.automation_rule.data_valid_from is 'Thời gian tạo dự liệu ';
COMMENT ON COLUMN vnpt_dev.automation_rule.data_valid_to is 'Thời gian kết thúc tạo dữ liệu';
COMMENT ON COLUMN vnpt_dev.automation_rule.migration_id is 'ID assignment rule được chuyển đổi từ bảng crm_assignment_rule';


------------------------------------

drop table if exists vnpt_dev.rule_condition;
create table vnpt_dev.rule_condition (
                                         id bigserial,
                                         rule_id int8 not null,
                                         index int8 not null,
                                         name varchar(50),
                                         condition jsonb,
                                         condition_sql text,
                                         lst_notification_type text,
                                         action_type varchar(20),
                                         email_notify boolean,
                                         sms_notify boolean,
                                         notification_notify boolean,
                                         last_scanned_at timestamp,
                                         migration_id int8
);
CREATE INDEX "index_ruleCondition_Id" ON "vnpt_dev"."rule_condition" USING btree ("id" desc nulls last);
CREATE INDEX "index_ruleCondition_ruleId" ON "vnpt_dev"."rule_condition" USING hash ("rule_id");
COMMENT ON TABLE vnpt_dev.rule_condition is 'Bảng lưu thông tin điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_condition.rule_id is 'Id bảng automation rule';
COMMENT ON COLUMN vnpt_dev.rule_condition.index is 'Mã điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_condition.name is 'Tên điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_condition.condition is 'Danh sách Điều kiện automation rule dạng json';
COMMENT ON COLUMN vnpt_dev.rule_condition.condition_sql is 'Danh sách Điều kiện automation rule dạng query';
COMMENT ON COLUMN vnpt_dev.rule_condition.lst_notification_type is 'Danh sách cấu hình hình thức gửi thông báo';
COMMENT ON COLUMN vnpt_dev.rule_condition.action_type is 'Loại hành động của điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_condition.migration_id is 'ID assignment rule được chuyển đổi từ bảng crm_assignment_rule';

-------------------------------------------
drop table if exists vnpt_dev.rule_action_update;
create table vnpt_dev.rule_action_update (
                                             id bigserial,
                                             rule_condition_id int8 not null,
                                             update_policy jsonb,
                                             update_sql text
);
CREATE INDEX "index_ruleActionUpdate_Id" ON "vnpt_dev"."rule_action_update" USING btree ("id" desc nulls last);
CREATE INDEX "index_ruleActionUpdate_ruleConditionId" ON "vnpt_dev"."rule_action_update" USING hash ("rule_condition_id");
COMMENT ON TABLE vnpt_dev.rule_action_update is 'Bảng lưu thông tin hành động automation rule cập nhật dữ liệu';
COMMENT ON COLUMN vnpt_dev.rule_action_update.rule_condition_id is 'Id bảng điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_update.update_policy is 'Quy tắc cập nhật dữ liệu automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_update.update_sql is 'Query cập nhật dữ liệu automation rule';

----------------------------------------

drop table if exists vnpt_dev.rule_action_notification;
create table vnpt_dev.rule_action_notification (
                                                   id bigserial,
                                                   rule_condition_id int8 not null,
                                                   receiver_type varchar(100),
                                                   lst_receiver_id int8[],
                                                   lst_content_config jsonb
);
CREATE INDEX "index_ruleActionNotif_Id" ON "vnpt_dev"."rule_action_notification" USING btree ("id" desc nulls last);
CREATE INDEX "index_ruleActionNotif_ruleConditionId" ON "vnpt_dev"."rule_action_notification" USING hash ("rule_condition_id");
COMMENT ON TABLE vnpt_dev.rule_action_notification is 'Bảng lưu thông tin hành động automation rule gửi thông báo';
COMMENT ON COLUMN vnpt_dev.rule_action_notification.rule_condition_id is 'Id bảng điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_notification.receiver_type is 'Hình thức gửi thông báo cho :';
COMMENT ON COLUMN vnpt_dev.rule_action_notification.lst_receiver_id is 'Danh sách id người nhận thông báo';

----------------------------------------

drop table if exists vnpt_dev.rule_action_assignment;
create table vnpt_dev.rule_action_assignment (
                                                 id bigserial,
                                                 rule_condition_id int8 not null,
                                                 reassign_policy jsonb,
                                                 notification_policy jsonb,
                                                 warning_policy jsonb,
                                                 assignment_method varchar(20),
                                                 lst_assignees_id int8[]
);
CREATE INDEX "index_ruleActionAssignment_Id" ON "vnpt_dev"."rule_action_assignment" USING btree ("id" desc nulls last);
CREATE INDEX "index_ruleActionAssignment_ruleConditionId" ON "vnpt_dev"."rule_action_assignment" USING hash ("rule_condition_id");
COMMENT ON TABLE vnpt_dev.rule_action_assignment is 'Bảng lưu thông tin hành động automation rule gán nhân sự phụ trách';
COMMENT ON COLUMN vnpt_dev.rule_action_assignment.rule_condition_id is 'Id bảng điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_assignment.reassign_policy is 'Cấu hình gán lại nhân sự phụ trách của hành động phân giao automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_assignment.notification_policy is 'Cấu hình gửi thông báo của hành động phân giao automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_assignment.warning_policy is 'Cấu hình gửi cảnh báo của hành động phân giao automation rule';
COMMENT ON COLUMN vnpt_dev.rule_action_assignment.lst_assignees_id is 'Danh sách id người phụ trách';
--------------------------------------------

drop table if exists vnpt_dev.rule_assignee;
create table vnpt_dev.rule_assignee (
                                        id bigserial,
                                        rule_condition_id int8 not null,
                                        assignee_id int8,
                                        priority int2,
                                        weight int2
);
CREATE INDEX "index_ruleAssignee_Id" ON "vnpt_dev"."rule_assignee" USING btree ("id" desc nulls last);
CREATE INDEX "index_ruleAssignee_ruleConditionId" ON "vnpt_dev"."rule_assignee" USING hash ("rule_condition_id");
COMMENT ON TABLE vnpt_dev.rule_assignee is 'Bảng lưu thông tin cấu hình nhân sự phụ trách automation rule';
COMMENT ON COLUMN vnpt_dev.rule_assignee.rule_condition_id is 'Id bảng điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.rule_assignee.assignee_id is 'Id nhân sự phụ trách';
COMMENT ON COLUMN vnpt_dev.rule_assignee.priority is 'Thông tin cấu hình độ ưu tiên nhân sự phụ trách';
COMMENT ON COLUMN vnpt_dev.rule_assignee.weight is 'Thông tin cấu hình trọng số nhân sự phụ trách';

------------------------------------------

drop table if exists vnpt_dev.history_assignment;
create table vnpt_dev.history_assignment (
                                             id bigserial,
                                             rule_condition_id int8 not null,
                                             assignee_id int8,
                                             object_type varchar(20),
                                             object_id int8,
                                             assigned_at timestamp,
                                             re_assigned_at timestamp,
                                             viewed_at timestamp,
                                             modified_at timestamp
);
CREATE INDEX "index_historyAssignment_Id" ON "vnpt_dev"."history_assignment" USING btree ("id" desc nulls last);
CREATE INDEX "index_historyAssignment_ruleConditionId" ON "vnpt_dev"."history_assignment" USING hash ("rule_condition_id");
CREATE INDEX "index_historyAssignment_assigneeId" ON "vnpt_dev"."history_assignment" USING hash ("assignee_id");
CREATE INDEX "index_historyAssignment_objectId" ON "vnpt_dev"."history_assignment" USING hash ("object_id");
COMMENT ON TABLE vnpt_dev.history_assignment is 'Bảng lưu thông tin lịch sử phân giao automation rule';
COMMENT ON COLUMN vnpt_dev.history_assignment.rule_condition_id is 'Id điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.history_assignment.assignee_id is 'Id nhân sự phụ trách';
COMMENT ON COLUMN vnpt_dev.history_assignment.object_type is 'Loại đối tượng áp dụng';
COMMENT ON COLUMN vnpt_dev.history_assignment.object_id is 'Id bản ghi đối tượng được phụ trách';
COMMENT ON COLUMN vnpt_dev.history_assignment.assigned_at is 'Thời gian phân giao';
COMMENT ON COLUMN vnpt_dev.history_assignment.viewed_at is 'Thời gian xem bản ghi gấn nhất';
COMMENT ON COLUMN vnpt_dev.history_assignment.viewed_at is 'Thời gian chỉnh sửa bản ghi gấn nhất';

--------------------------------------
drop table if exists vnpt_dev.history_inactive_warning;
create table vnpt_dev.history_inactive_warning (
                                                   id bigserial,
                                                   rule_condition_id int8 not null,
                                                   assignee_id int8,
                                                   object_type varchar(20),
                                                   object_id int8,
                                                   created_at timestamp
);

COMMENT ON TABLE vnpt_dev.history_inactive_warning is 'Bảng lưu thông tin gửi cảnh báo nhân sự không tương tác automation rule';
COMMENT ON COLUMN vnpt_dev.history_inactive_warning.rule_condition_id is 'Id điều kiện automation rule';
COMMENT ON COLUMN vnpt_dev.history_inactive_warning.assignee_id is 'Id nhân sự phụ trách';
COMMENT ON COLUMN vnpt_dev.history_inactive_warning.object_type is 'Loại đối tượng áp dụng';
COMMENT ON COLUMN vnpt_dev.history_inactive_warning.object_id is 'Id bản ghi đối tượng được phụ trách';
COMMENT ON COLUMN vnpt_dev.history_inactive_warning.created_at is 'Thời gian gửi cảnh báo nhân sự không tương tác';
------------------------------------------
--Bổ sung cấu hình doanh thu mục tiêu
delete from vnpt_dev.system_params where  param_type like 'RULE_INACTIVE_CONFIG';
insert into vnpt_dev.system_params(param_name, param_type, param_text_value)
select 'Cấu hình automation rules', 'RULE_INACTIVE_CONFIG', '{"objectInactiveConfig":{"lstAction":["OPEN","UPDATE"],"timeOpen":3,"timeUnitOpen":"DAY","timeUpate":3,"timeUnitUpdate":"HOUR"},"notificationConfig":{"lstNotification":["EMAIL","SMS","NOTIFICATION"],"frequency":1,"spaceUnit":"DAY", "timeSpace": 8}}'
    where not exists (
   select * from vnpt_dev.system_params
   where param_type like 'RULE_INACTIVE_CONFIG'
);



-- Migrate rule cũ

TRUNCATE vnpt_dev.automation_rule;
TRUNCATE vnpt_dev.rule_condition;
TRUNCATE vnpt_dev.rule_action_assignment;
TRUNCATE vnpt_dev.rule_assignee;

ALTER TABLE "vnpt_dev"."automation_rule" DROP COLUMN IF EXISTS "migration_id";
ALTER TABLE "vnpt_dev"."automation_rule" ADD COLUMN "migration_id" int8;
COMMENT ON COLUMN "vnpt_dev"."automation_rule"."migration_id" IS 'ID assignment rule được chuyển đổi từ bảng crm_assignment_rule';
INSERT INTO vnpt_dev.automation_rule (migration_id, name, code, description, status, scanning_policy, object_type, created_by, data_valid_from)
SELECT id, name, code, description,
       CASE WHEN status = 0 THEN 'ACTIVE' ELSE 'INACTIVE' END,
       '{"type": "RECURRING", "occasion": "NEW_RECORD"}',
       CASE
           WHEN object_type = 0 THEN 'USER'
           WHEN object_type = 1 THEN 'CUSTOMER_TICKET'
           WHEN object_type = 2 THEN 'ENTERPRISE'
           WHEN object_type = 3 THEN 'CUSTOMER_CONTACT'
           WHEN object_type = 4 THEN 'SUBSCRIPTION'
           WHEN object_type = 5 THEN 'BILL'
           WHEN object_type = 6 THEN 'ECONTRACT'
           WHEN object_type = 7 THEN 'AFFILIATE'
           END,
       created_by,
       '2023-01-01'
FROM vnpt_dev.crm_assignment_rule;


ALTER TABLE "vnpt_dev"."rule_condition"
DROP COLUMN IF EXISTS "email_notify",
  DROP COLUMN IF EXISTS "sms_notify",
  DROP COLUMN IF EXISTS "notification_notify",
  DROP COLUMN IF EXISTS "migration_id";
ALTER TABLE "vnpt_dev"."rule_condition"
    ADD COLUMN "email_notify" bool DEFAULT false,
  ADD COLUMN "sms_notify" bool DEFAULT false,
  ADD COLUMN "notification_notify" bool DEFAULT false,
  ADD COLUMN "migration_id" int8;
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."email_notify" IS 'Có gửi email khi hoàn thành việc quét quy tắc hay không';
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."sms_notify" IS 'Có gửi SMS khi hoàn thành việc quét quy tắc hay không';
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."notification_notify" IS 'Có gửi thông báo trên nền tảng khi hoàn thành việc quét quy tắc hay không';
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."migration_id" IS 'ID assignment rule được chuyển đổi từ bảng crm_assignment_rule';


INSERT INTO vnpt_dev.rule_condition (migration_id, rule_id, index, name, condition, condition_sql, action_type, email_notify, sms_notify, notification_notify)
SELECT
    mOld.id, mNew.id, 1, mOld.name, mOld.condition, mOld.condition_query, 'ASSIGN',
    COALESCE(mOld.email_notity, false), COALESCE(mOld.sms_notity, false), true
FROM vnpt_dev.crm_assignment_rule mOld
         LEFT JOIN vnpt_dev.automation_rule mNew ON mNew.migration_id = mOld.id;


INSERT INTO vnpt_dev.rule_action_assignment (rule_condition_id, lst_assignees_id)
SELECT
    mNew.id, mOld.lst_assignees_id
FROM vnpt_dev.crm_assignment_rule mOld
         JOIN vnpt_dev.rule_condition mNew ON mNew.migration_id = mOld.id;


COMMENT ON COLUMN "vnpt_dev"."rule_assignee"."priority" IS 'Thông tin cấu hình độ ưu tiên nhân sự phụ trách (0 là độ ưu tiên cao nhất)';
INSERT INTO vnpt_dev.rule_assignee(rule_condition_id, assignee_id, priority, weight)
SELECT
    mNew.id, mOld.assignee_id, 0, 1
FROM vnpt_dev.crm_assignment_rule mOld
         JOIN vnpt_dev.rule_condition mNew ON mNew.migration_id = mOld.id;

-- Thêm thông tin job quét rule mỗi phút
DELETE FROM "vnpt_dev"."schedules" WHERE bean_name = 'task-scanning-automation-rule' AND method_name = 'scanningAutomationRule';
INSERT INTO "vnpt_dev"."schedules" ( "bean_name", "method_name", "method_params", "cron_expression", "remark", "job_status", "created_by", "created_at", "modified_by", "modified_at" )
VALUES
    ('task-scanning-automation-rule', 'scanningAutomationRule', NULL, '0 * * ? * *', 'scanning-automation-rule', 1, 'batch', NULL, 'batch', NULL );
-- Thêm thông tin job quét không tương tác mỗi giờ
DELETE FROM "vnpt_dev"."schedules" WHERE bean_name = 'task-scanning-automation-rule' AND method_name = 'scanningAssigneeNonInteractive';
INSERT INTO "vnpt_dev"."schedules" ( "bean_name", "method_name", "method_params", "cron_expression", "remark", "job_status", "created_by", "created_at", "modified_by", "modified_at" )
VALUES
    ('task-scanning-automation-rule', 'scanningAssigneeNonInteractive', NULL, '0 0 * ? * *', 'scanning-assignee-non-interactive', 1, 'batch', NULL, 'batch', NULL );

-- Bổ sung các operator, operand mới
INSERT INTO "vnpt_dev"."mc_operator" ("name", "code", "created_at", "modified_at", "created_by", "modified_by", "status", "deleted_flag") VALUES ('Thay đổi', 25, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operator" ("name", "code", "created_at", "modified_at", "created_by", "modified_by", "status", "deleted_flag") VALUES ('Cập nhật', 26, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;

INSERT INTO "vnpt_dev"."mc_data_type" ("name", "code", "created_at", "modified_at", "created_by", "modified_by", "status", "deleted_flag") VALUES ('Alter String Value', 64, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_data_type" ("name", "code", "created_at", "modified_at", "created_by", "modified_by", "status", "deleted_flag") VALUES ('Alter String Value', 64, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;

INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ("operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (17, 25, 64, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ("operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (17, 26, 17, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand_operator_mapping" ("operand_data_type_code", "operator_code", "value_data_type_code", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES (25, 26, 25, '2022-06-09 08:07:14.178877', NULL, NULL, NULL, NULL, NULL) on conflict do nothing;

INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Độ ưu tiên phiếu hỗ trợ', 6006, 6, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Loại phiếu hỗ trợ', 6007, 6, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Trạng thái phiếu hỗ trợ', 6008, 6, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Nguồn tạo phiếu hỗ trợ', 6009, 6, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Trạng thái affiliate', 8003, 7, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;
INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Trạng thái tài khoản', 60, 0, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;

-- tắt các job Cũ
update vnpt_dev.schedules set job_status = 0 where bean_name = 'scanning-assignment-rule';

-- cập nhật các func liên quan
DROP FUNCTION IF EXISTS "vnpt_dev"."func_list_object_by_condition_query"("conditionquery" text, "objecttype" int4);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_list_object_by_condition_query"("conditionquery" text, "objecttype" int4)
  RETURNS TABLE("id" int8) AS $BODY$
DECLARE
mQuery text;
    sQuery text;
BEGIN
CASE objectType
    WHEN 0 THEN
        mQuery = 'SELECT users.id as id FROM vnpt_dev.users JOIN vnpt_dev.view_role_sme on users.id = view_role_sme.user_id WHERE users.deleted_flag = 1 and ';
WHEN 1 THEN
        mQuery = 'SELECT customer_ticket.id as id FROM vnpt_dev.customer_ticket WHERE customer_ticket.deleted_flag = 1 and ';
WHEN 2 THEN
        mQuery = 'SELECT enterprise.id as id FROM vnpt_dev.enterprise WHERE enterprise.deleted_flag = 1 and ';
WHEN 3 THEN
        mQuery = 'SELECT customer_contact.id as id FROM vnpt_dev.customer_contact WHERE customer_contact.deleted_flag = 1 and ';
WHEN 4 THEN
        mQuery = 'SELECT subscriptions.id as id FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and ';
WHEN 7 THEN
        mQuery = 'SELECT users.id as id FROM vnpt_dev.users WHERE affiliate_type is not null and users.deleted_flag = 1 and ';
END CASE;
    sQuery = conditionQuery;
    mQuery = CONCAT(mQuery, sQuery);
    RAISE NOTICE 'mQuery: %', mQuery;
RETURN QUERY EXECUTE mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100;

DROP FUNCTION IF EXISTS "vnpt_dev"."func_count_object_by_rule_condition"("objecttype" int4, "rulecondition" text);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_count_object_by_rule_condition"("objecttype" int4, "rulecondition" text)
  RETURNS "pg_catalog"."int4" AS $BODY$
    DECLARE
mQuery text;
        mResult
int4;
BEGIN
CASE objecttype
WHEN 0
    THEN mQuery = CONCAT('SELECT COUNT(users.id) FROM vnpt_dev.users JOIN vnpt_dev.view_role_sme on users.id = view_role_sme.user_id WHERE users.deleted_flag = 1 and ', rulecondition);
WHEN 1
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.customer_ticket WHERE customer_ticket.deleted_flag = 1 and ', rulecondition);
WHEN 2
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.enterprise WHERE enterprise.deleted_flag = 1 and ', rulecondition);
WHEN 3
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.customer_contact WHERE customer_contact.deleted_flag = 1 and ', rulecondition);
WHEN 4
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and ', rulecondition);
WHEN 7
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.users WHERE affiliate_type IS NOT NULL AND users.deleted_flag = 1 and ', rulecondition);
END
CASE;
        RAISE
NOTICE 'mQuery: %', mQuery;
EXECUTE mQuery INTO mResult;
RETURN mResult;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100;

TRUNCATE vnpt_dev.automation_rule;
TRUNCATE vnpt_dev.rule_condition;
TRUNCATE vnpt_dev.rule_action_assignment;
TRUNCATE vnpt_dev.rule_assignee;

ALTER TABLE "vnpt_dev"."automation_rule" DROP COLUMN IF EXISTS "migration_id";
ALTER TABLE "vnpt_dev"."automation_rule" ADD COLUMN "migration_id" int8;
COMMENT ON COLUMN "vnpt_dev"."automation_rule"."migration_id" IS 'ID assignment rule được chuyển đổi từ bảng crm_assignment_rule';
INSERT INTO vnpt_dev.automation_rule (migration_id, name, code, description, status, scanning_policy, object_type, created_by, data_valid_from)
SELECT id, name, code, description,
       CASE WHEN status = 0 THEN 'ACTIVE' ELSE 'INACTIVE' END,
       '{"type": "RECURRING", "occasion": "NEW_RECORD"}',
       CASE
           WHEN object_type = 0 THEN 'USER'
           WHEN object_type = 1 THEN 'CUSTOMER_TICKET'
           WHEN object_type = 2 THEN 'ENTERPRISE'
           WHEN object_type = 3 THEN 'CUSTOMER_CONTACT'
           WHEN object_type = 4 THEN 'SUBSCRIPTION'
           WHEN object_type = 5 THEN 'BILL'
           WHEN object_type = 6 THEN 'ECONTRACT'
           WHEN object_type = 7 THEN 'AFFILIATE'
           END,
       created_by,
       '2023-01-01'
FROM vnpt_dev.crm_assignment_rule;

-- migrate rule
ALTER TABLE "vnpt_dev"."rule_condition"
DROP COLUMN IF EXISTS "email_notify",
  DROP COLUMN IF EXISTS "sms_notify",
  DROP COLUMN IF EXISTS "notification_notify",
  DROP COLUMN IF EXISTS "migration_id";
ALTER TABLE "vnpt_dev"."rule_condition"
    ADD COLUMN "email_notify" bool DEFAULT false,
  ADD COLUMN "sms_notify" bool DEFAULT false,
  ADD COLUMN "notification_notify" bool DEFAULT false,
  ADD COLUMN "migration_id" int8;
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."email_notify" IS 'Có gửi email khi hoàn thành việc quét quy tắc hay không';
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."sms_notify" IS 'Có gửi SMS khi hoàn thành việc quét quy tắc hay không';
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."notification_notify" IS 'Có gửi thông báo trên nền tảng khi hoàn thành việc quét quy tắc hay không';
COMMENT ON COLUMN "vnpt_dev"."rule_condition"."migration_id" IS 'ID assignment rule được chuyển đổi từ bảng crm_assignment_rule';


INSERT INTO vnpt_dev.rule_condition (migration_id, rule_id, index, name, condition, condition_sql, action_type, email_notify, sms_notify, notification_notify)
SELECT
    mOld.id, mNew.id, 1, mOld.name, mOld.condition, mOld.condition_query, 'ASSIGN',
    COALESCE(mOld.email_notity, false), COALESCE(mOld.sms_notity, false), true
FROM vnpt_dev.crm_assignment_rule mOld
         LEFT JOIN vnpt_dev.automation_rule mNew ON mNew.migration_id = mOld.id;


INSERT INTO vnpt_dev.rule_action_assignment (rule_condition_id, lst_assignees_id)
SELECT
    mNew.id, mOld.lst_assignees_id
FROM vnpt_dev.crm_assignment_rule mOld
         JOIN vnpt_dev.rule_condition mNew ON mNew.migration_id = mOld.id;


COMMENT ON COLUMN "vnpt_dev"."rule_assignee"."priority" IS 'Thông tin cấu hình độ ưu tiên nhân sự phụ trách (0 là độ ưu tiên cao nhất)';
INSERT INTO vnpt_dev.rule_assignee(rule_condition_id, assignee_id, priority, weight)
SELECT
    mNew.id, mOld.assignee_id, 0, 1
FROM vnpt_dev.crm_assignment_rule mOld
         JOIN vnpt_dev.rule_condition mNew ON mNew.migration_id = mOld.id;