-- vnpt_dev.view_subscription_statistic_new
drop view if exists vnpt_dev.view_subscription_statistic_new;
CREATE OR REPLACE VIEW vnpt_dev.view_subscription_statistic_new
AS SELECT
       to_char(subs.created_at, 'YYYY'::text) AS create_year,
       to_char(subs.created_at, 'YYYY/MM'::text) AS create_month,
       to_char(subs.created_at, 'YYYY/MM/dd'::text) AS create_date,
       to_char(subs.end_current_cycle, 'YYYY'::text) AS end_current_cycle_year,
       to_char(subs.end_current_cycle, 'YYYY/MM'::text) AS end_current_cycle_month,
       to_char(subs.end_current_cycle, 'YYYY/MM/dd'::text) AS end_current_cycle_date,
       subs.id AS subscription_id,
       users.id AS user_id,
       users.province_id as province_id,
       users.customer_type as customer_type,
       province.name as province_name,
           CASE
               WHEN combo.combo_owner = 0 OR combo.combo_owner = 1 THEN 1
               WHEN services.service_owner = 0 OR services.service_owner = 1 THEN 1
               ELSE 2
           END AS service_type,
           CASE
               WHEN combo.id IS NOT NULL THEN concat(combo.id, '0001')::bigint
               WHEN services.id IS NOT NULL THEN concat(services.id, '0000')::bigint
               ELSE NULL::bigint
           END AS unique_id,
       subs.created_at,
       subs.modified_at,
       subs.pricing_id,
       subs.confirm_status,
       subs.reg_type,
       subs.deleted_flag,
       subs.end_current_cycle,
       services.user_id as service_developer_id,
       combo.user_id as combo_developer_id
   FROM subscriptions subs
       LEFT JOIN users ON subs.user_id = users.id
       LEFT JOIN province ON users.province_id = province.id
       LEFT JOIN pricing ON subs.pricing_id = pricing.id
       LEFT JOIN services ON pricing.service_id = services.id
       LEFT JOIN combo_plan ON subs.combo_plan_id = combo_plan.id
       LEFT JOIN combo ON combo_plan.combo_id = combo.id;