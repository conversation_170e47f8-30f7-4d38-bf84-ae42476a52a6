DELETE FROM vnpt_dev.apis WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id FROM vnpt_dev.api_permission JOIN vnpt_dev.permission ON permission.id = api_permission.permission_portal_id
    WHERE vnpt_dev.permission.code IN ('QUAN_LY_CHIEN_DICH_QUANG_CAO', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO',
                                       'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD')
);

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CHIEN_DICH_QUANG_CAO', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO',
                                                       'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO',
                                                        'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD'))
);

DELETE FROM vnpt_dev.roles_permissions WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CHIEN_DICH_QUANG_CAO', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO',
                                                       'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO',
                                                        'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD'))
);

DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CHIEN_DICH_QUANG_CAO', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO',
                                                       'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO',
                                                        'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD'))
);

DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CHIEN_DICH_QUANG_CAO', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO',
                                               'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO',
                                                'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD', 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD');


-- INSERT vao bang permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Quản lý chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO',
        -1,
        16000000
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem danh sách chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000001
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000002
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Tạo chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000003
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Chỉnh sửa chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000004
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Gửi thông báo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000007
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
     (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Thay đổi trạng thái chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000010
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
     (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xóa chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000011
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
     (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Nhân đôi chiến dịch quảng cáo',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000012
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
     (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem dashboard',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000013
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
     (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Tải dashboard',
        'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        16000014
    );

-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
(
    (SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 2 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 3 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 4 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 5 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 8 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_GUI_THONG_BAO' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 11 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 12 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 13 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 14 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 15 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' ORDER BY id DESC LIMIT 1),
    1
);

-- Add vao bang apis, api_permission

--add api lấy thông tin số lượng CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/list/get-num-by-status',
        'ROLE_MC_LAY_THONG_TIN_SO_LUONG_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_LAY_THONG_TIN_SO_LUONG_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Lấy danh sách CDQC theo trạng thái
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/list/get-list-campaign',
        'ROLE_MC_LAY_DS_CDQC_THEO_TRANG_THAI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_LAY_DS_CDQC_THEO_TRANG_THAI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DANH_SACH' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Xóa danh sách CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/delete',
        'ROLE_MC_XOA_DANH_SACH_CDQC',
        'DELETE'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_XOA_DANH_SACH_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XOA' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Clone CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/clone',
        'ROLE_MC_CLONE_CDQC',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_CLONE_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_NHAN_DOI' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Tạo CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/create',
        'ROLE_MC_TAO_CDQC',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_TAO_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAO' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Sửa CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/update',
        'ROLE_MC_SUA_CDQC',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_SUA_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_SUA' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Xem chi tiết CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/detail',
        'ROLE_MC_XEM_CHI_TIET_THONG_TIN_CHUNG_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_XEM_CHI_TIET_THONG_TIN_CHUNG_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_CHI_TIET' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api Chuyển trạng thái hoạt động
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/update-status',
        'ROLE_MC_CAP_NHAT_TRANG_THAI_HOAT_DONG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_CAP_NHAT_TRANG_THAI_HOAT_DONG' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_THAY_DOI_TRANG_THAI' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--add api gửi notification tức thì
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/send-notification',
        'ROLE_MC_SEND_NOTIFICATION_NOW',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_SEND_NOTIFICATION_NOW' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- DASHBOARD
-- PHAN 1
-- dashboard xem so luong cdqc
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/get-num-and-amount',
        'ROLE_MC_DASHBOARD_OVERVIEW_SO_LUONG_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_SO_LUONG_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview tong so tien su dung cho cdqc
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-total-amount',
        'ROLE_MC_DASHBOARD_PREVIEW_TONG_SO_TIEN_CHO_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_TONG_SO_TIEN_CHO_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export tong so tien su dung cho cdqc
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-total-amount',
        'ROLE_MC_DASHBOARD_EXPORT_TONG_SO_TIEN_CHO_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_TONG_SO_TIEN_CHO_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview tong so cdqc theo trang thai
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-total-num',
        'ROLE_MC_DASHBOARD_PREVIEW_TONG_SO_CDQC_THEO_TRANG_THAI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_TONG_SO_CDQC_THEO_TRANG_THAI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export tong so cdqc theo trang thai
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-total-num',
        'ROLE_MC_DASHBOARD_EXPORT_TONG_SO_CDQC_THEO_TRANG_THAI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_TONG_SO_CDQC_THEO_TRANG_THAI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- PHAN 2
-- dashboard overview tong so khach hang chuyen doi
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/overview-total-conversion',
        'ROLE_MC_DASHBOARD_OVERVIEW_TONG_SO_KHACH_HANG_CHUYEN_DOI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_TONG_SO_KHACH_HANG_CHUYEN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview khach hang chuyen doi
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-conversion',
        'ROLE_MC_DASHBOARD_PREVIEW_KHACH_HANG_CHUYEN_DOI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_KHACH_HANG_CHUYEN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export khach hang chuyen doi
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-conversion',
        'ROLE_MC_DASHBOARD_EXPORT_KHACH_HANG_CHUYEN_DOI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_KHACH_HANG_CHUYEN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview hien thi quang cao
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-ads-display',
        'ROLE_MC_DASHBOARD_PREVIEW_HIEN_THI_QUANG_CAO',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_HIEN_THI_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export hien thi quang cao
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-ads-display',
        'ROLE_MC_DASHBOARD_EXPORT_HIEN_THI_QUANG_CAO',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_HIEN_THI_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview click quang cao
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-ads-click',
        'ROLE_MC_DASHBOARD_PREVIEW_CLICK_QUANG_CAO',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_CLICK_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export click quang cao
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-ads-click',
        'ROLE_MC_DASHBOARD_EXPORT_CLICK_QUANG_CAO',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_CLICK_QUANG_CAO' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview tong so khach hang chuyen doi
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-total-conversion',
        'ROLE_MC_DASHBOARD_PREVIEW_TONG_SO_KHACH_HANG_CHUYEN_DOI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_TONG_SO_KHACH_HANG_CHUYEN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export tong so khach hang chuyen doi
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-total-conversion',
        'ROLE_MC_DASHBOARD_EXPORT_TONG_SO_KHACH_HANG_CHUYEN_DOI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_TONG_SO_KHACH_HANG_CHUYEN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- PHAN 3
-- dashboard overview ty le chuyen doi
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/overview-conversion-rate',
        'ROLE_MC_DASHBOARD_OVERVIEW_TY_LE_CHUYEN_DOI',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_TY_LE_CHUYEN_DOI' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard preview CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/preview-campaign',
        'ROLE_MC_DASHBOARD_PREVIEW_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_PREVIEW_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- dashboard export CDQC
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-campaign',
        'ROLE_MC_DASHBOARD_EXPORT_CDQC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_CDQC' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- PHAN 4
-- dashboard overview so lan hien thi, so lan click
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/overview-ads-click-rate',
        'ROLE_MC_DASHBOARD_OVERVIEW_SO_LAN_HIEN_THI_VA_CLICK',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_SO_LAN_HIEN_THI_VA_CLICK' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- PHAN 5
-- dashboard overview cdqc nhieu thue bao nhat
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/overview-most-applied',
        'ROLE_MC_DASHBOARD_OVERVIEW_CDQC_NHIEU_THUE_BAO_NHAT',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_CDQC_NHIEU_THUE_BAO_NHAT' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- PHAN 6
-- dashboard overview cdqc co ty le nhap chuot cao nhat
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/overview-most-ads-clicked',
        'ROLE_MC_DASHBOARD_OVERVIEW_CDQC_CO_TY_LE_CLICK_CAO_NHAT',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_CDQC_CO_TY_LE_CLICK_CAO_NHAT' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- PHAN 6
-- dashboard Overview Thống kê số lần hiển thị và số lần click của chiến dịch quảng cáo
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/overview-ads-click-rate',
        'ROLE_MC_DASHBOARD_OVERVIEW_CDQC_CO_TY_LE_NHAP_CHUOT_CAO_NHAT',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_OVERVIEW_CDQC_CO_TY_LE_NHAP_CHUOT_CAO_NHAT' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_XEM_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

--Export tổng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/marketing-campaign/dashboard/export-all',
        'ROLE_MC_DASHBOARD_EXPORT_ALL',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_MC_DASHBOARD_EXPORT_ALL' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_CHIEN_DICH_QUANG_CAO_TAI_DASHBOARD' AND pp.portal_id = 1 LIMIT 1),
        1, 1
    );

-- REFRESH MATERIALIZED VIEW
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;