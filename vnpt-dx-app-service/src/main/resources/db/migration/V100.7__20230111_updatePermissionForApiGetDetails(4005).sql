-- Update permission for api /api/admin-portal/crm/enterprise-mgmt/details/get-user-personal-detail
UPDATE
    vnpt_dev.api_permission
SET
    permission_portal_id = (SELECT id FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET'))
WHERE
        api_id = (SELECT id FROM vnpt_dev.apis WHERE api_code = 'XEM_CHI_TIET_TAI_KHOAN_CA_NHAN' AND method = 'GET' LIMIT 1);

-- Update permission for api /api/admin-portal/crm/enterprise-mgmt/details/get-user-business-detail
UPDATE
    vnpt_dev.api_permission
SET
    permission_portal_id = (SELECT id FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET'))
WHERE
        api_id = (SELECT id FROM vnpt_dev.apis WHERE api_code = 'XEM_CHI_TIET_TAI_KHOAN_DOANH_NGHIEP' AND method = 'GET' LIMIT 1);

-- Refresh view
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;