DROP VIEW IF EXISTS vnpt_dev.view_report_sub_services;
CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_services AS
SELECT pricing.id,
       'SERVICE'::text AS sub_type,
       pricing.pricing_name,
       pricing.pricing_type,
       CASE
           WHEN pricing.cycle_type = 0 THEN 'DAILY'::text
           WHEN pricing.cycle_type = 1 THEN 'WEEKLY'::text
           WHEN pricing.cycle_type = 2 THEN 'MONTHLY'::text
           WHEN pricing.cycle_type = 3 THEN 'YEARLY'::text
           ELSE NULL::text
           END AS plan_cycle_type,
       pricing.payment_cycle AS plan_payment_cycle,
       pricing.cycle_type,
       services.service_name,
       CASE
           WHEN services.service_owner_partner = 0 THEN 'ON'::text
           WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
           ELSE 'OS'::text
           END AS service_owner_type,
       services.id AS service_id,
       services.categories_id,
       services.user_id AS provider_id,
       services.service_owner
FROM vnpt_dev.pricing
         LEFT JOIN vnpt_dev.services ON pricing.service_id = services.id
UNION ALL
SELECT NULL::bigint AS id,
       'SERVICE'::text AS sub_type,
       NULL::character varying AS pricing_name,
       NULL::smallint AS pricing_type,
       NULL::text AS plan_cycle_type,
       NULL::smallint AS plan_payment_cycle,
       null as cycle_type,
       services.service_name,
       CASE
           WHEN services.service_owner_partner = 0 THEN 'ON'::text
           WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
           ELSE 'OS'::text
           END AS service_owner_type,
       services.id AS service_id,
       services.categories_id,
       services.user_id AS provider_id,
       services.service_owner
FROM vnpt_dev.services
WHERE services.product_type = 1 OR
    (created_source_migration = 2 AND service_code = 'SS000001'); -- Dịch vụ Sim số

DROP VIEW IF EXISTS vnpt_dev.view_report_sub_combo;
CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_combo AS
(
SELECT cbPlan.id              AS id,
       'COMBO'                AS sub_type,
       cbPlan.combo_name      AS pricing_name,
       cbPlan.combo_plan_type AS pricing_type,
       CASE
           WHEN cbPlan.cycle_type = 0 THEN 'DAILY'
           WHEN cbPlan.cycle_type = 1 THEN 'WEEKLY'
           WHEN cbPlan.cycle_type = 2 THEN 'MONTHLY'
           WHEN cbPlan.cycle_type = 3 THEN 'YEARLY'
           END                AS plan_cycle_type,
       cbPlan.payment_cycle   AS plan_payment_cycle,
       cbPlan.cycle_type,
       combo.combo_name       AS service_name,
       CASE
           WHEN combo.combo_owner IN (0, 1) THEN 'ON'
           ELSE 'OS'
           END                AS service_owner_type,
       combo.id               AS combo_id,
       combo.categories_id    AS categories_id,
       combo.user_id          AS provider_id,
       combo.combo_owner AS combo_owner
FROM vnpt_dev.combo_plan AS cbPlan
         LEFT JOIN vnpt_dev.combo ON cbPlan.combo_id = combo.id
);