ALTER TABLE vnpt_dev.mail_template
    ADD COLUMN IF NOT EXISTS object_apply integer [] NULL;
COMMENT
ON COLUMN vnpt_dev.mail_template.object_apply IS '0: <PERSON><PERSON><PERSON> <PERSON>, 1: <PERSON><PERSON><PERSON> hỗ trợ, 2: <PERSON><PERSON><PERSON><PERSON> hàng, 3: <PERSON><PERSON><PERSON>ệ, 4: <PERSON><PERSON><PERSON> bao, 7: <PERSON><PERSON><PERSON><PERSON> viên affiliate, -1: All';

    -- <PERSON><PERSON><PERSON>
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[0]
WHERE EXISTS (SELECT 1 WHERE code IN
                             ('RP-02', 'EXP-02'));
-- <PERSON><PERSON><PERSON> hỗ trợ
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[1]
WHERE EXISTS (SELECT 1 WHERE code IN
                             ('TICC-02', 'TICC-04', 'TICC-06', 'TICC-07', 'TICC-09', 'TICC-10', 'TICC-12', 'TICC-14', 'TICC-15', 'TICC-16',
                              'TICC-17', 'TICC-19', 'TKD-02', 'TKD-04', 'TKD-06', 'TKD-07', 'TKD-09', 'TKD-12', 'TKD-10', 'TKD-15', 'TKD-14',
                              'TKD-16', 'TKD-17', 'TKD-20', 'TKD-21', 'TKD-23'));

-- Khách hàng
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[2]
WHERE EXISTS (SELECT 1 WHERE code IN
                             ('KS-01'));

-- Liên hệ
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[3]
WHERE EXISTS (SELECT 1 WHERE code IN
                             ('LH01', 'HP01'));

-- Thuê bao
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[4]
WHERE EXISTS (SELECT 1 WHERE code IN
                             ('SLA-05', 'SLA-07', 'SLA-09', 'EXP-01', 'EXP-03', 'GTB-11', 'RP-01', 'RP-03', 'TBC-02', 'TBC-04', 'TBC-06',
                              'TBC-08', 'TBC-02', 'TBC-11', 'TBC-15', 'TBC-19', 'TBC-23', 'TBC-27', 'TBC-31', 'TBC-35', 'TBC-37', 'TBC-39',
                              'TBC-42', 'TB-02', 'TB-04', 'TB-08', 'TB-11', 'TB-15', 'TB-19', 'TB-23', 'TB-27', 'TB-31', 'TB-35', 'TB-37',
                              'TB-39', 'TB-42', 'TBC-01', 'TBC-07', 'TBC-02', 'TBC-03', 'TBC-04', 'TBC-06', 'TBC-05', 'SCB-06', 'SC02', 'SC04',
                              'SC08', 'SC11', 'SC15', 'SC19', 'SC23', 'SC27', 'SC31', 'SC35', 'SC39', 'SC37', 'SC42', 'PGD-11',
                              'SCB-02', 'SCB-04', 'SCB-06', 'SCB-08', 'SCB-11', 'SCB-15', 'SCB-19', 'SCB-23', 'SCB-27', 'SCB-31', 'SCB-35',
                              'SCB-37', 'SCB-39', 'SCB-42',
                              'SCD-01', 'SCD-02', 'SCD-03', 'SCD-04', 'SCD-05', 'SCD-06', 'SCD-07'));

-- Thành viên affiliate
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[7]
WHERE EXISTS (SELECT 1 WHERE code IN ('AFF-07', 'AFF-21'));

-- Tất cả
UPDATE vnpt_dev.mail_template
SET object_apply = ARRAY[-1]
WHERE EXISTS (SELECT 1 WHERE code IN
                             ('PGD-01', 'PGD-03', 'PGD-04', 'PGD-05', 'PGD-06', 'PGD-07', 'PGD-08', 'PGD-09', 'PGD-10', 'PGD-02'));
