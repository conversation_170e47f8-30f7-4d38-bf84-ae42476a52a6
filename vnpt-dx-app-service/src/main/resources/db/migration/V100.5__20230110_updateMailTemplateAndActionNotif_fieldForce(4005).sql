--========================== Update template TKD-19 name ====================
UPDATE "vnpt_dev"."mail_template"
SET name = 'Admin/ Dev nhập phản hồi phiếu hỗ trợ của KHDN/ HKD'
WHERE code = 'TKD-19';

--========================== Add mail template: TICC-18 Admin/Dev nhập phản hồi ==================================
DELETE FROM "vnpt_dev"."mail_template" WHERE code = 'TICC-18';
INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text,
                                  content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES ('TICC-18', 'Admin/ Dev nhập phản hồi phiếu hỗ trợ của KHCN', 1,
'<!DOCTYPE html>
 <html>
 <head>
   <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
   <link rel="preconnect" href="https://fonts.googleapis.com">
   <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
 </head>
 <body
     style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
 <div class="container" style="background-color: #FFFFFF;">
   <div class="logo-container"
        style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
     $HEADER
   </div>
   <div class="content-container" style="padding: 40px;">
     <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
       <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_tk.png" alt="Phiếu hỗ trợ">
       <p class="main-title"
          style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Cập nhật phiếu hỗ trợ</p>
     </div>
     <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
       <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$CUSTOMER_NAME</span>,</p>
       <p style="margin: 0;">$USER vừa tạo phản hồi cho phiếu hỗ trợ</p>
       <p style="margin: 0;">Tiêu đề: <span>$TITLE_TICKET</span></p>
       <p style="margin: 0;">Loại phiếu hỗ trợ: <span>$SUPPORT_TYPE</span></p>
       <p style="margin: 0;">Nội dung phản hồi:</p>
       <p style="margin: 0;">$CONTENT_RESPONSE</p>
       <p style="margin: 0;">Thời gian cập nhật: <span>$DATE_RESPONSE_CREATE</span></p>
       <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
       <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
     </div>
   </div>
   <div class="footer-container" style="padding: 40px;">
     $FOOTER
   </div>
 </div>
 </body>
 </html>',
'<!DOCTYPE html>
 <html>
 <head>
   <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
   <link rel="preconnect" href="https://fonts.googleapis.com">
   <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
 </head>
 <body
     style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
 <div class="container" style="background-color: #FFFFFF;">
   <div class="logo-container"
        style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
     $HEADER
   </div>
   <div class="content-container" style="padding: 40px;">
     <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
       <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_tk.png" alt="Phiếu hỗ trợ">
       <p class="main-title"
          style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Cập nhật phiếu hỗ trợ</p>
     </div>
     <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
       <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$CUSTOMER_NAME</span>,</p>
       <p style="margin: 0;">$USER vừa tạo phản hồi cho phiếu hỗ trợ</p>
       <p style="margin: 0;">Tiêu đề: <span>$TITLE_TICKET</span></p>
       <p style="margin: 0;">Loại phiếu hỗ trợ: <span>$SUPPORT_TYPE</span></p>
       <p style="margin: 0;">Nội dung phản hồi:</p>
       <p style="margin: 0;">$CONTENT_RESPONSE</p>
       <p style="margin: 0;">Thời gian cập nhật: <span>$DATE_RESPONSE_CREATE</span></p>
       <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
       <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
     </div>
   </div>
   <div class="footer-container" style="padding: 40px;">
     $FOOTER
   </div>
 </div>
 </body>
 </html>',
null, null, 'TK', 'Cập nhật phiếu hỗ trợ', 'Cập nhật phiếu hỗ trợ', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 1);


--====================================== Add mail param cho TICC-18==================================
DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code = 'TICC-18';
--============ TICC-18 =============
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$HOTLINE_TINH', '[Hotline tỉnh]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$CUSTOMER_NAME', '[Người nhận]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$TITLE_TICKET', '[Tiêu đề phiếu hỗ trợ]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$SUPPORT_TYPE', '[Loại phiếu hỗ trợ]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$CONTENT_RESPONSE', '[Nội dung phản hổi]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$DATE_RESPONSE_CREATE', '[Thời gian cập nhật]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '$USER', '[Người tạo phản hồi]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'TICC-18'), '');


--=========================== Update priority order của mail template phiếu hỗ trợ ====================
UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TICC-18';

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TICC-19';

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TKD-20';

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TKD-21';

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TKD-22';

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TICC-20';

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code = 'TKD-23';

--============================ Insert lại action_notification ==================================
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code IN ('TKD-01', 'TKD-02', 'TKD-03', 'TKD-04', 'TKD-05', 'TKD-06', 'TKD-07', 'TKD-08', 'TKD-09', 'TKD-10', 'TKD-11', 'TKD-12', 'TKD-13', 'TKD-14', 'TKD-15', 'TKD-16','TKD-17', 'TKD-19', 'TKD-20', 'TKD-21', 'TKD-22', 'TKD-23');
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code IN ('TICC-01', 'TICC-02', 'TICC-03', 'TICC-04', 'TICC-05', 'TICC-06', 'TICC-07', 'TICC-08', 'TICC-09', 'TICC-10', 'TICC-11', 'TICC-12', 'TICC-13','TICC-14', 'TICC-18', 'TICC-19', 'TICC-20');
INSERT INTO "vnpt_dev"."action_notification"("action_code", "name", "is_send_email", "is_send_sms", "is_notification", "parent_id", "created_by", "created_at", "modified_by", "modified_at", "receiver", "allow_change_email", "allow_change_sms", "allow_change_notification", "priority_order", "is_send_telegram", "allow_change_telegram") VALUES
    ('TKD-01', 'Khách hàng DN/ HKD tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phiếu', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-02', 'Khách hàng DN/ HKD tạo 1 phiếu hỗ trợ mới', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Nhân viên phụ trách SME)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-01', 'Khách hàng cá nhân tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phiếu', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-02', 'Khách hàng cá nhân tạo 1 phiếu hỗ trợ mới', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Nhân viên phụ trách SME)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-03', 'Khách hàng DN/ HKD sửa nội dung phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phiếu', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-04', 'Khách hàng DN/ HKD sửa nội dung phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-03', 'Khách hàng cá nhân sửa nội dung phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phiếu', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-04', 'Khách hàng cá nhân sửa nội dung phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-05', 'Khách hàng DN/ HKD đóng phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phiếu', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-06', 'Khách hàng DN/ HKD đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách SME)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-07', 'Khách hàng DN/ HKD đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-05', 'Khách hàng cá nhân đóng phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phiếu', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-06', 'Khách hàng cá nhân đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách SME)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-07', 'Khách hàng cá nhân đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-08', 'Admin tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-08', 'Admin tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-09', 'Admin tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách SME)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-09', 'Admin tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách SME)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-10', 'Admin tạo 1 phiếu hỗ trợ mới', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-11', 'Admin sửa nội dung phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-11', 'Admin sửa nội dung phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-12', 'Admin sửa nội dung phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-13', 'Admin/Dev đóng phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-13', 'Admin/Dev đóng phiếu hỗ trợ', 1, 0, 0, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'D', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-14', 'Admin/Dev đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách SME)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-14', 'Admin/Dev đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách SME)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-15', 'Admin/Dev đóng phiếu hỗ trợ', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-16', 'Hết hạn xử lý', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-05 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-17', 'Admin/Dev chuyển trạng thái phiếu hỗ trợ thành tạm dừng', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-19', 'Admin/ Dev nhập phản hồi phiếu hỗ trợ của KHDN/ HKD', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-18', 'Admin/ Dev nhập phản hồi phiếu hỗ trợ của KHCN', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Khách hàng SME (nếu phiếu có gán với khách hàng)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-19', 'Khách hàng cá nhân nhập phản hồi', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-20', 'Khách hàng DN/ HKD nhập phản hồi', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-21', 'Admin/Dev nhập phản hồi', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Nhân viên phụ trách (Phụ trách Ticket)', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-22', 'Admin xóa phản hồi khách hàng DN/ HKD', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'SME tạo phản hồi', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TICC-20', 'Admin xóa phản hồi khách hàng cá nhân', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Người tạo phản hồi', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D'),
    ('TKD-23', 'Admin xóa phản hồi của Admin/Dev', 1, 0, 1, (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1), 'system', '2022-12-16 00:00:00', NULL, NULL, 'Người tạo phản hồi', 'B', 'D', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D');


--================== set email_type = 0 cho template TKD-19 ==============
UPDATE vnpt_dev.mail_template
SET email_type = 0
WHERE code = 'TKD-19';
