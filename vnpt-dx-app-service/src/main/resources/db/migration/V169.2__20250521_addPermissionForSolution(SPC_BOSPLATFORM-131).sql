DELETE FROM vnpt_dev.permission WHERE code IN ('PG_QUAN_LY_GIAI_PHAP', 'P_QLGP_XEM_DANH_SACH_GIAI_PHAP',
    'P_QLGP_TAO_GIAI_PHAP', 'P_QLGP_XEM_CHI_TIET_GIAI_PHAP',
    'P_QLGP_CAP_NHAT_GIAI_PHAP', 'P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP',
    'P_QLGP_PHE_DUYET_GIAI_PHAP', 'P_QLGP_XOA_GIAI_PHAP');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Quản lý giải pháp',
        'PG_QUAN_LY_GIAI_PHAP',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );

-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Xem danh sách',
        'P_QLGP_XEM_DANH_SACH_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.permission),
        'Tạo giải pháp',
        'P_QLGP_TAO_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 2 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.permission),
        'Xem chi tiết giải pháp',
        'P_QLGP_XEM_CHI_TIET_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 3 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.permission),
        'Cập nhật giải pháp',
        'P_QLGP_CAP_NHAT_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 4 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.permission),
        'Yêu cầu phê duyệt',
        'P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 5 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.permission),
        'Phê duyệt giải pháp',
        'P_QLGP_PHE_DUYET_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 6 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.permission),
        'Xóa giải pháp',
        'P_QLGP_XOA_GIAI_PHAP',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        (SELECT max(priority) + 7 from vnpt_dev.permission)
    );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_DANH_SACH_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_TAO_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_CHI_TIET_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_CAP_NHAT_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_PHE_DUYET_GIAI_PHAP'),
        1
    ),
    (
        (SELECT max(id) + 8 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_XOA_GIAI_PHAP'),
        1
    );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        2
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_DANH_SACH_GIAI_PHAP'),
        2
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_TAO_GIAI_PHAP'),
        2
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_CHI_TIET_GIAI_PHAP'),
        2
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_CAP_NHAT_GIAI_PHAP'),
        2
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP'),
        2
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGP_XOA_GIAI_PHAP'),
        2
    );

-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_DANH_SACH_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_TAO_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_CHI_TIET_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_CAP_NHAT_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_PHE_DUYET_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 8 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_XOA_GIAI_PHAP'), 
        0
    );
    
-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES 
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_GIAI_PHAP'), 
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_DANH_SACH_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_TAO_GIAI_PHAP'), 
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_XEM_CHI_TIET_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_CAP_NHAT_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP'),
        0
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGP_XOA_GIAI_PHAP'), 
        0
    );

-- Cập nhật view phân quyền
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;