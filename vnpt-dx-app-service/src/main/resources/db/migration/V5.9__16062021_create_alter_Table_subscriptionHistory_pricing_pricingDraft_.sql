drop table if exists vnpt_dev.subscription_history cascade;

create table vnpt_dev.subscription_history
(
    id         bigserial not null primary key,
	subscription_id int8,
    created_at timestamp,
    content    varchar(100),
    created_by int8
);

comment on table vnpt_dev.subscription_history is '<PERSON><PERSON><PERSON> sử hoạt động của thuê bao';
comment on column vnpt_dev.subscription_history.id is 'Id bảng lịch sử';
comment on column vnpt_dev.subscription_history.subscription_id is 'Id subscription';
comment on column vnpt_dev.subscription_history.created_at is '<PERSON>ày tạo lịch sử';
comment on column vnpt_dev.subscription_history.content is 'Nội dung thay đổi';
comment on column vnpt_dev.subscription_history.created_by is 'Id người tạo';

COMMENT ON COLUMN vnpt_dev.pricing.has_change_price IS '0: Không cho phép 1: Cho phép tăng 2: Cho phép giảm 3: Cho phép tăng và giảm';
ALTER TABLE vnpt_dev.pricing
alter column active_date TYPE int4;
COMMENT ON COLUMN vnpt_dev.pricing.active_date IS 'Thời gian kích hoạt sau hủy  -1: không giới hạn, 1: thời gian cho phép';

COMMENT ON COLUMN vnpt_dev.pricing_draft.has_change_price IS '0: Không cho phép 1: Cho phép tăng 2: Cho phép giảm 3: Cho phép tăng và giảm';
ALTER TABLE vnpt_dev.pricing_draft
alter column active_date TYPE int4;
COMMENT ON COLUMN vnpt_dev.pricing_draft.active_date IS 'Thời gian kích hoạt sau hủy  -1: không giới hạn, 1: thời gian cho phép';



drop table if exists vnpt_dev.credit_note cascade;
create table vnpt_dev.credit_note
(
    id         bigserial not null primary key,
	subscription_id int8,
    billing_id int8,
    note_type    int2,
    money_refund float8,
	status int2,
	reason_refund text,
	created_at timestamp,
	created_by varchar(100),
	modified_at timestamp,
	modified_by varchar(100),
	deleted_flag int2
);

comment on table vnpt_dev.credit_note is 'Hoàn trả sau khi hủy subscriptions';
comment on column vnpt_dev.credit_note.subscription_id is 'Id subscription';
comment on column vnpt_dev.credit_note.note_type is 'Hoàn trả thanh toán/Điều chỉnh thanh toán';
comment on column vnpt_dev.credit_note.money_refund is ' Số tiền hoàn trả';
comment on column vnpt_dev.credit_note.status is '0: chưa hoàn trả, 1: chưa điều chỉnh, 2: đã hoàn trả, 3: đã điều chỉnh, 4: vô hiệu hóa';
comment on column vnpt_dev.credit_note.reason_refund is ' Lý do được hoàn trả';

ALTER TABLE vnpt_dev.subscriptions
add column awaiting_cancel int2;
COMMENT ON COLUMN vnpt_dev.subscriptions.awaiting_cancel IS '1: đang chờ hủy, 0: đã hủy, null chưa có ai hủy ';