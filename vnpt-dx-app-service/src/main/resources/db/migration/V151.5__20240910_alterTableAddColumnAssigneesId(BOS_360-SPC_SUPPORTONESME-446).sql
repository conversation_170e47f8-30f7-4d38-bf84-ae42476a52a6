alter table vnpt_dev.customer_contact add column if not exists "lst_assignees_id" int8[];
alter table vnpt_dev.users add column if not exists "lst_assignees_id" int8[];
alter table vnpt_dev.customer_ticket add column if not exists "lst_assignees_id" int8[];
alter table vnpt_dev.subscriptions add column if not exists "lst_assignees_id" int8[];
alter table vnpt_dev.enterprise add column if not exists "lst_assignees_id" int8[];

INSERT INTO "vnpt_dev"."mc_operand" ("name", "code", "object_type", "mc_data_type_code", "description", "display_text", "created_at", "modified_at", "deleted_flag", "created_by", "modified_by", "status") VALUES ('Trạng thái', 3012, 3, 17, 'Trạng thái liên hệ', NULL, NULL, NULL, NULL, NULL, NULL, NULL) on conflict do nothing;


DROP function if exists "vnpt_dev"."func_list_object_assignee_id_by_condition_query"("conditionquery" text, "objecttype" int4);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_list_object_assignee_id_by_condition_query"("conditionquery" text, "objecttype" int4)
  RETURNS TABLE("assignee_id" int8) AS $BODY$

DECLARE

mQuery text;

    sQuery text;

BEGIN

CASE objectType

        WHEN 0 THEN

            mQuery = 'SELECT users.assignee_id as assignee_id FROM vnpt_dev.users
						          JOIN vnpt_dev.view_role_sme on users.id = view_role_sme.user_id WHERE users.deleted_flag = 1 and';

WHEN 1 THEN

            mQuery = 'SELECT customer_ticket.assignee_id as assignee_id FROM vnpt_dev.customer_ticket WHERE customer_ticket.deleted_flag = 1 and ';

WHEN 2 THEN

            mQuery = 'SELECT enterprise.assignee_id as assignee_id FROM vnpt_dev.enterprise WHERE enterprise.deleted_flag = 1 and ';

WHEN 3 THEN

            mQuery = 'SELECT customer_contact.assignee_id as assignee_id FROM vnpt_dev.customer_contact WHERE customer_contact.deleted_flag = 1 and ';

WHEN 4 THEN

						mQuery = 'SELECT subscriptions.assignee_id as assignee_id FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and ';

WHEN 7 THEN

				    mQuery = 'SELECT users.assignee_id as assignee_id

								FROM vnpt_dev.users WHERE affiliate_type is not null and users.deleted_flag = 1 and ';

END CASE;

    sQuery = conditionQuery;

    mQuery = CONCAT(mQuery, sQuery);

    RAISE NOTICE 'mQuery: %', mQuery;

RETURN QUERY EXECUTE mQuery;

END

$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP function if exists "vnpt_dev"."func_list_object_partition_admin_id_by_condition_query"("conditionquery" text, "objecttype" int4);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_list_object_partition_admin_id_by_condition_query"("conditionquery" text, "objecttype" int4)
  RETURNS TABLE("admin_id" int8) AS $BODY$

DECLARE

mQuery text;

    sQuery text;

BEGIN
    sQuery = conditionQuery;

CASE objectType

        WHEN 0 THEN
            mQuery = concat('select distinct
												unnest(crm_data_partition.lst_admin_id) as admin_id
												from vnpt_dev.crm_mapping_user_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_user_partition.lst_partition_id)
												JOIN vnpt_dev.view_role_sme on crm_mapping_user_partition.user_id = view_role_sme.user_id
												where
													crm_mapping_user_partition.user_id in (select id from vnpt_dev.users where deleted_flag = 1 and ', sQuery, ')') ;
WHEN 1 THEN
            mQuery = concat('select distinct
												unnest(crm_data_partition.lst_admin_id) as admin_id
												from vnpt_dev.crm_mapping_ticket_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_ticket_partition.lst_partition_id)
												where
													crm_mapping_ticket_partition.ticket_id in (select id from vnpt_dev.customer_ticket where deleted_flag = 1 and ', sQuery, ')') ;

WHEN 2 THEN

            mQuery =  concat('select distinct unnest(crm_data_partition.lst_admin_id) as admin_id
										from vnpt_dev.crm_data_partition
										join (
										select distinct
												unnest(crm_mapping_enterprise_partition.lst_partition_id) as partition_id
												from
												vnpt_dev.crm_mapping_enterprise_partition
												where
												crm_mapping_enterprise_partition.enterprise_id in (select id from vnpt_dev.enterprise where enterprise.deleted_flag = 1 and ', sQuery,'))
												as mappingPartition on mappingPartition.partition_id = crm_data_partition.id');

WHEN 3 THEN
            mQuery = concat('select distinct
												unnest(crm_data_partition.lst_admin_id) as admin_id
												from vnpt_dev.crm_mapping_contact_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_contact_partition.lst_partition_id)
												where
													crm_mapping_contact_partition.contact_id in (select id from vnpt_dev.customer_contact where deleted_flag = 1 and ', sQuery, ')') ;

WHEN 4 THEN
						mQuery = concat('select distinct
												unnest(crm_data_partition.lst_admin_id) as admin_id
												from vnpt_dev.crm_mapping_user_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_user_partition.lst_partition_id)
												where
													crm_mapping_user_partition.user_id in (select user_id from vnpt_dev.subscriptions where deleted_flag = 1 and ', sQuery, ')') ;
END CASE;


    RAISE NOTICE 'mQuery: %', mQuery;

RETURN QUERY EXECUTE mQuery;

END

$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP function if exists "vnpt_dev"."func_list_object_partition_am_id_by_condition_query"("conditionquery" text, "objecttype" int4);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_list_object_partition_am_id_by_condition_query"("conditionquery" text, "objecttype" int4)
  RETURNS TABLE("am_id" int8) AS $BODY$

DECLARE

mQuery text;

    sQuery text;

BEGIN
    sQuery = conditionQuery;

CASE objectType

        WHEN 0 THEN
            mQuery = concat('select distinct
												unnest(crm_data_partition.lst_am_id) as am_id
												from vnpt_dev.crm_mapping_user_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_user_partition.lst_partition_id)
												JOIN vnpt_dev.view_role_sme on crm_mapping_user_partition.user_id = view_role_sme.user_id
												where
													crm_mapping_user_partition.user_id in (select id from vnpt_dev.users where deleted_flag = 1 and ', sQuery, ')') ;
WHEN 1 THEN
            mQuery = concat('select distinct
												unnest(crm_data_partition.lst_am_id) as am_id
												from vnpt_dev.crm_mapping_ticket_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_ticket_partition.lst_partition_id)
												where
													crm_mapping_ticket_partition.ticket_id in (select id from vnpt_dev.customer_ticket where deleted_flag = 1 and ', sQuery, ')') ;

WHEN 2 THEN

            mQuery =  concat('select distinct unnest(crm_data_partition.lst_am_id) as am_id
										from vnpt_dev.crm_data_partition
										join (
										select distinct
												unnest(crm_mapping_enterprise_partition.lst_partition_id) as partition_id
												from
												vnpt_dev.crm_mapping_enterprise_partition
												where
												crm_mapping_enterprise_partition.enterprise_id in (select id from vnpt_dev.enterprise where enterprise.deleted_flag = 1 and ', sQuery,'))
												as mappingPartition on mappingPartition.partition_id = crm_data_partition.id');

WHEN 3 THEN
            mQuery = concat('select distinct
												unnest(crm_data_partition.lst_am_id) as am_id
												from vnpt_dev.crm_mapping_contact_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_contact_partition.lst_partition_id)
												where
													crm_mapping_contact_partition.contact_id in (select id from vnpt_dev.customer_contact where deleted_flag = 1 and ', sQuery, ')') ;

WHEN 4 THEN
						mQuery = concat('select distinct
												unnest(crm_data_partition.lst_am_id) as am_id
												from vnpt_dev.crm_mapping_user_partition
												join vnpt_dev.crm_data_partition on crm_data_partition.id = ANY(crm_mapping_user_partition.lst_partition_id)
												where
													crm_mapping_user_partition.user_id in (select user_id from vnpt_dev.subscriptions where deleted_flag = 1 and ', sQuery, ')') ;
END CASE;


    RAISE NOTICE 'mQuery: %', mQuery;

RETURN QUERY EXECUTE mQuery;

END

$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

