
-- <PERSON><PERSON> sung Multiperiod
CREATE TABLE vnpt_dev.pricing_setup_fee_tax (
	id bigserial NOT NULL,
	tax_id int8 NULL,
	pricing_id int8 NULL,
	has_tax int2 NULL,
	pricing_draft_id int8 NULL,
	"percent" float8 NULL,
	CONSTRAINT pricing_setup_fee_tax_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE vnpt_dev.pricing_setup_fee_tax IS 'Lưu thông tin thuế của phí thiết lập của pricing';
COMMENT ON COLUMN vnpt_dev.pricing_setup_fee_tax.tax_id IS 'ID của bảng tax';
COMMENT ON COLUMN vnpt_dev.pricing_setup_fee_tax.pricing_id IS 'ID của bảng pricing';
COMMENT ON COLUMN vnpt_dev.pricing_setup_fee_tax.has_tax IS '0 - chưa bao gồm, 1 - đã bao gồm';
COMMENT ON COLUMN vnpt_dev.pricing_setup_fee_tax.pricing_draft_id IS 'ID của bảng pricing_draft';
COMMENT ON COLUMN vnpt_dev.pricing_setup_fee_tax.percent IS 'Giá trị thuế';

-- Edit table pricing_multi_plan_addon
ALTER TABLE vnpt_dev.pricing_multi_plan_addon 
ADD COLUMN pricing_multi_plan_addon_id int8 NULL,
ADD COLUMN is_require int8 NULL;
COMMENT ON COLUMN vnpt_dev.pricing_multi_plan_addon.pricing_multi_plan_addon_id IS 'id của bảng pricing_multi_plan với trường hơp là addon cho CKTT định kỳ';
COMMENT ON COLUMN vnpt_dev.pricing_multi_plan_addon.is_require IS '0: không bắt buộc, 1: bắt buộc';
COMMENT ON COLUMN vnpt_dev.pricing_multi_plan_addon.addon_id IS 'id của addon cho trường hơp dvbs có chu kỳ thanh toán 1 lần';




