DROP VIEW IF EXISTS report_view_subscription_pricing;
DROP VIEW IF EXISTS report_view_subscription_combo;

Create VIEW report_view_subscription_pricing as
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.end_current_cycle_new,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.number_of_cycles_default,
       s.refer_subscription,
       s.traffic_user,
       p.pricing_name,
       u.customer_type,
       se.service_name,
       se.id AS c_service_id,
       (concat(se.id, '0000'))::bigint AS service_unique_id,
        (concat(p.id, '0000'))::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 1
           WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 2
           WHEN (se.service_owner IS NULL) THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
            WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
            WHEN (se.service_owner IS NULL) THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN (ss.id IS NULL) THEN 0
            WHEN (sr.order_status IS NULL) THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN (ss.id IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sr.order_status IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sm."order" = 1) THEN 'Tiếp nhận đơn hàng'::text
            WHEN (sm."order" = 2) THEN 'Đang triển khai'::text
            WHEN (sm."order" = 3) THEN 'Hoàn thành'::text
            WHEN (sm."order" = 4) THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN (s.status)::integer
            WHEN (ss.id IS NULL) THEN 10
            WHEN (sr.order_status IS NULL) THEN 10
            WHEN (sm."order" = '-1'::integer) THEN '-1'::integer
            ELSE (concat('1', sm."order"))::integer
END AS c_status,
        CASE
            WHEN (s.employee_code IS NOT NULL) THEN 1
            WHEN (s.traffic_id IS NOT NULL) THEN 2
            WHEN (s.portal_type = 1) THEN 3
            WHEN (s.portal_type = 2) THEN 3
            WHEN (s.portal_type = 3) THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code,
    p.is_one_time,
    p.has_renew
   FROM (((((((((vnpt_dev.subscriptions s
     JOIN vnpt_dev.pricing p ON (((s.pricing_id = p.id) AND (s.combo_plan_id IS NULL))))
     LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
     LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
     LEFT JOIN vnpt_dev.services se ON ((se.id = p.service_id)))
     LEFT JOIN vnpt_dev.report_view_customer c ON ((s.user_id = c.id)))
     LEFT JOIN vnpt_dev.billings bi ON (((bi.subscriptions_id = s.id) AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id)))))
     LEFT JOIN vnpt_dev.bill_item bt ON (((bt.billing_id = bi.id) AND (bt.object_id = s.pricing_id) AND (bt.object_type = 0))))
     LEFT JOIN vnpt_dev.order_service_receive sr ON ((sr.subscription_id = s.id)))
     LEFT JOIN vnpt_dev.order_service_status ss ON ((ss.id = (sr.order_status)::bigint)))
     LEFT JOIN vnpt_dev.sme_progress sm ON ((sm.id = ss.sme_progress_id)))
     LEFT JOIN vnpt_dev.units un ON ((un.id = p.unit_id)))
  WHERE ((s.deleted_flag = 1) AND (s.confirm_status = 1));

CREATE OR REPLACE VIEW "vnpt_dev"."report_view_subscription_combo" AS
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.end_current_cycle_new,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.number_of_cycles_default,
       s.refer_subscription,
       s.traffic_user,
       p.combo_name AS pricing_name,
       u.customer_type,
       co.combo_name AS service_name,
       co.id AS c_service_id,
       concat(co.id, '0001')::bigint AS service_unique_id,
        concat(p.id, '0001')::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       3 AS subscription_type,
       CASE
           WHEN co.combo_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
           WHEN co.combo_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
           WHEN co.combo_owner IS NULL THEN 'OS'::text
           ELSE NULL::text
           END AS onos,
       c.nation_id AS c_nation_id,
       c.nation_name AS c_nation_name,
       c.province_id AS c_province_id,
       c.province_name AS c_province_name,
       c.district_id AS c_district_id,
       c.district_name AS c_district_name,
       c.ward_id AS c_ward_id,
       c.ward_name AS c_ward_name,
       c.street_name,
       c.address AS c_address,
       c.name AS sme_name,
       c.email AS c_email,
       c.phone_number AS c_phone_number,
       c.tin,
       bi.status AS payment_status,
       bi.next_total_amount,
       bt.quantity AS service_quantity,
       '-1'::integer AS order_status,
        ''::text AS order_status_name,
        s.status AS c_status,
       CASE
           WHEN s.employee_code IS NOT NULL THEN 1
           WHEN s.traffic_id IS NOT NULL THEN 2
           WHEN s.portal_type = 1 THEN 3
           WHEN s.portal_type = 2 THEN 3
           WHEN s.portal_type = 3 THEN 0
           ELSE NULL::integer
END AS source,
    co.user_id AS provider_id,
    '-1'::integer AS categories_id,
    co.categories_id AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    1 AS is_one_time,
    p.has_renew
   FROM vnpt_dev.subscriptions s
     JOIN vnpt_dev.combo_plan p ON s.combo_plan_id = p.id
     LEFT JOIN vnpt_dev.users u ON u.id = s.user_id
     LEFT JOIN vnpt_dev.combo co ON p.combo_id = co.id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id))
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.combo_plan_id AND bt.object_type = 1
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;