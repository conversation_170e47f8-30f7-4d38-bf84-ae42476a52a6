-- tạ<PERSON> bảng tạm để lưu thông tin
--drop table user_business_area_temp;
create table user_business_area_temp
(
    business_area_id_old int8,
    business_area_id_new int8
);

insert into user_business_area_temp
    (
        select
            id as id_old,
            case
                when id = 50 then 1016
                when id = 70 then 1009
                when id = 90 then 1010
                when id = 100 then 1012
                when id = 120 then 1015
                when id = 888 then 1008
                when id < 1000 then null
                when id >= 1000 then id
                end as id_new
        from vnpt_dev.business_area
    );

-- cập nhật trường business_area_id = null
update vnpt_dev.users
set business_area_id = null
where
business_area_id not in
(
    select ubat.business_area_id_old
    from vnpt_dev.user_business_area_temp ubat
);


-- cập nhật trường business_area_id = business_area_id_new
update vnpt_dev.users
set business_area_id = ubat.business_area_id_new
from vnpt_dev.user_business_area_temp ubat
where business_area_id = ubat.business_area_id_old;

-- Drop table
drop table vnpt_dev.user_business_area_temp;

-- Xoá những id < 1000 trong bảng business_area
delete from vnpt_dev.business_area
where id < 1000;