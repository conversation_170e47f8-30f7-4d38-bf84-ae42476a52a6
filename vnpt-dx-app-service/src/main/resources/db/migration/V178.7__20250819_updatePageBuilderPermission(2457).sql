-- Thêm role permission quản lý giao diện với role affiliate đại lý và affiliate thành viên
delete from vnpt_dev.roles_permissions where role_id = (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN') and permission_id = (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER_XOA');
insert into vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
	(
        (select max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER_XOA'),
        0
    );
		-- Cập nhật view phân quyền
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;
select setval('vnpt_dev.roles_permissions_id_seq', COALESCE(max(id), 0)) FROM vnpt_dev.roles_permissions;