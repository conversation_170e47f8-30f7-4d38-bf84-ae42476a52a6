drop function if exists vnpt_dev.func_get_user_create_affiliate_commission;
CREATE OR REPLACE FUNCTION vnpt_dev.func_get_user_create_affiliate_commission(i_rolename character varying, i_currentuserid bigint)
 RETURNS TABLE(userid bigint, username character varying, useremail character varying)
 LANGUAGE plpgsql
AS $function$
begin
    --Admin tổng
    if i_rolename like 'FULL_ADMIN' then
        begin
            --L<PERSON>y ra tất cả user đã tạo affiliate hoa hồng
            return query (
                select distinct
                    users.id,
                    case
                        when affiliate_commission.priority = 0 
                            then concat(users.last_name, ' ', users.first_name)::character varying
                        else users.name
                    end,
                    users.email
                from vnpt_dev.affiliate_commission 
                    join vnpt_dev.users on affiliate_commission.created_by = users.id 
                where 
                    affiliate_commission.deleted_flag = 1 and 
                    users.deleted_flag = 1
            );
        end;
    --Đại lý affiliate
    else
        begin
            --Lấy ra các user đã tạo hoa hồng và nằm trong tập affiliate_user mà người đăng nhập đư<PERSON> phép nhìn thấy
            return query(
                select distinct
                    users.id, 
                    case
                        when aff_commission.priority = 0 
                            then concat(users.last_name, ' ', users.first_name)::character varying
                        else users.name
                    end, 
                    users.email
                from vnpt_dev.affiliate_commission aff_commission
                    join vnpt_dev.func_get_affiliate_user_by_role(i_rolename, i_currentuserid) func_aff_user on aff_commission.created_by = func_aff_user.userid
                    join vnpt_dev.users on aff_commission.created_by = users.id
                where 
                    aff_commission.deleted_flag = 1 and 
                    users.deleted_flag = 1
                );
        end;
    end if;
end $function$
;