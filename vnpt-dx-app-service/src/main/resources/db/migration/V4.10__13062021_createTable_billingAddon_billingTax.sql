DROP TABLE IF EXISTS vnpt_dev.billing_addon CASCADE;

create table vnpt_dev.billing_addon
(
    id         bigserial primary key,
    billing_id int8,
    addon_id   int8,
    amount     float8
);
comment on table vnpt_dev.billing_addon IS '<PERSON><PERSON><PERSON> dịch vụ bổ sung của hóa đơn';
comment on column vnpt_dev.billing_addon.billing_id is 'Id của hóa đơn';
comment on column vnpt_dev.billing_addon.addon_id is 'Id của dịch vụ bổ sung';
comment on column vnpt_dev.billing_addon.amount is 'Số tiền tr<PERSON><PERSON>c thuế, sau khuyến mại của gói dịch vụ bổ sung trên hóa đơn';


DROP TABLE IF EXISTS billing_tax CASCADE;
create table vnpt_dev.billing_tax
(
    id         bigserial primary key,
    billing_id int8,
    tax_id     int8,
    amount     float8,
    value      float4
);

comment on table vnpt_dev.billing_tax IS '<PERSON><PERSON><PERSON> c<PERSON><PERSON> thuế của hóa đơn';
comment on column vnpt_dev.billing_tax.billing_id is 'Id của hóa đơn';
comment on column vnpt_dev.billing_tax.tax_id is 'Id của thuế';
comment on column vnpt_dev.billing_tax.amount is 'Số tiền thuế phải chịu của loại thuế trên hóa đơn';
comment on column vnpt_dev.billing_tax.value is 'Số phần trăm thuế phải chịu của loại thuế trên hóa đơn';