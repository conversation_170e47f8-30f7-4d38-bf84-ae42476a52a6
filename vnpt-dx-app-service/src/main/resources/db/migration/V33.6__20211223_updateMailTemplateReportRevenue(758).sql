DELETE FROM "vnpt_dev"."mail_template" WHERE code IN ('EXP-02');
DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code IN ('EXP-02');

INSERT INTO "vnpt_dev"."mail_template" ("code", "name", "status", "content_html", "content_html_default", "content_text", "content_text_default", "parent_code", "title", "title_default", "priority_order" )
VALUES
    ('EXP-02', 'B<PERSON>o c<PERSON>o <PERSON>h nghiệp có tài khoản mới', 1, '<p>&nbsp;</p>
<div class="main ck-content" style="padding: 40px; margin: 0px auto; width: 600px; background-color: #f8f8f8; font-family: Montserrat, Helvetica, sans-serif; box-sizing: border-box; block-size: 1068.25px; perspective-origin: 300px 534.125px; transform-origin: 300px 534.125px;">
<div class="email-container" style="background-color: #ffffff; box-sizing: border-box; block-size: 988.25px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 494.125px; transform-origin: 260px 494.125px; width: 520px;">
<div class="logo-container" style="padding: 20px 20px 5px; box-sizing: border-box; block-size: 43px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 21.5px; transform-origin: 260px 21.5px; width: 520px;">
<div class="logo" style="flex-basis: 50%; text-align: left; margin-left: 40px;"><img src="https://onesme.vn/resources/upload/file/mail/images/oneSME_Logo.png" alt="OneSME Logo" /></div>
<div class="homepage" style="flex-basis: 50%; text-align: right; margin-right: 40px;"><span style="font-size: 14px; font-weight: 500;"><a style="text-decoration: none; color: #1b74e8;" href="https://www.onesme.vn/" target="_blank">www.onesme.vn</a></span></div>
</div>
<div style="border-bottom: 1px solid #cdcdcd; box-sizing: border-box; block-size: 1px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 0.5px; transform-origin: 260px 0.5px; width: 520px;">&nbsp;</div>
<div class="content-container" style="padding: 20px 40px 10px; box-sizing: border-box; block-size: 896.25px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 448.125px; transform-origin: 260px 448.125px; width: 520px;">
<p style="text-align: center; box-sizing: border-box; block-size: 86.25px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 43.125px; transform-origin: 220px 43.125px; width: 440px;"><img class="image_resized" style="width: 86.2656px; box-sizing: border-box; block-size: 86.25px; font: 16px Montserrat, Helvetica, sans-serif; max-inline-size: 100%; max-width: 100%; perspective-origin: 43.125px 43.125px; text-align: center; transform-origin: 43.125px 43.125px;" src="https://onesme.vn/resources/upload/file/mail/images/icon_ac.png" alt="T&agrave;i khoản" /></p>
<p style="margin-left: 0px; text-align: center; box-sizing: border-box; block-size: 18px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 9px; transform-origin: 220px 9px; width: 440px;"><span style="color: #3366ff;"><strong style="box-sizing: border-box; font: 700 16px Montserrat, Helvetica, sans-serif; text-align: center;">BÁO CÁO DOANH NGHIỆP MỚI NGÀY $CURRENT_DATE</strong></span></p>
<table style="border-collapse: collapse; width: 105.682%; height: 187px;" border="1">
<tbody>
<tr style="height: 34px;">
<td style="width: 17.9545%; text-align: center; height: 34px;"><strong>Doanh nghiệp mới</strong></td>
<td style="width: 28.4091%; text-align: center; height: 34px;"><strong>Thời gian</strong></td>
<td style="width: 22.5225%; text-align: center; height: 34px;"><strong>Số lượng</strong></td>
<td style="width: 36.7186%; text-align: center; height: 34px;"><strong>Tăng/Giảm (%)</strong></td>
</tr>
<tr style="height: 17px; $IF_DAY">
<td style="width: 17.9545%; height: 17px;">Ng&agrave;y</td>
<td style="width: 28.4091%; height: 17px;">$CURRENT_DATE</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_DAY_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_DAY_SHORT</td>
</tr>
<tr style="height: 17px; $IF_WEEK">
<td style="width: 17.9545%; height: 17px;">Tuần</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_WEEK - $CURRENT_DATE</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_WEEK_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_WEEK_SHORT</td>
</tr>
<tr style="height: 17px; $IF_MONTH">
<td style="width: 17.9545%; height: 17px;">Th&aacute;ng</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_MONTH - $LAST_MONTH</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_MONTH_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_MONTH_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY1">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; I</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_QUY1 - $END_DATE_QUY1</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY1_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY1_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY2">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; II</td>
<td style="width: 28.4091%; height: 17px; ">$START_DATE_QUY2 - $END_DATE_QUY2</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY2_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY2_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY3">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; III</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_QUY3 - $END_DATE_QUY3</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY3_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY3_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY4">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; IV</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_QUY4 - $END_DATE_QUY4</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY4_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY4_SHORT</td>
</tr>
<tr style="height: 17px; $IF_YEAR">
<td style="width: 17.9545%; height: 17px;">Năm</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_YEAR - $CURRENT_DATE</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_YEAR_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_YEAR_SHORT</td>
</tr>
<tr style="height: 17px; $IF_TOTAL">
<td style="width: 17.9545%; height: 17px;">Tổng</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_GET_DATA - $CURRENT_DATE</td>
<td style="width: 59.2411%; height: 17px;text-align: center;" colspan="2">&nbsp; $CUSTOMER_TOTAL</td>
</tr>
</tbody>
</table>
<p style="margin-left: 0px; box-sizing: border-box; block-size: 36px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 18px; transform-origin: 220px 18px; width: 440px;">Truy cập đường link dưới đ&acirc;y để lấy th&ecirc;m th&ocirc;ng tin: <strong style="box-sizing: border-box; font: 700 16px Montserrat, Helvetica, sans-serif;"><u style="box-sizing: border-box; font: 700 16px Montserrat, Helvetica, sans-serif;">$LINK_REPORT</u></strong></p>
<p style="margin-left: 0px; box-sizing: border-box; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 9px; transform-origin: 220px 9px; width: 440px;">Tr&acirc;n trọng,</p>
<p style="margin-left: 0px; box-sizing: border-box; block-size: 18px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 9px; transform-origin: 220px 9px; width: 440px;">Đội ngũ kinh doanh online Vinaphone - VNPT</p>
</div>
<div class="footer-container" style="padding: 20px 40px 10px; box-sizing: border-box; block-size: 48px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 24px; transform-origin: 260px 24px; width: 520px;">
<div class="social-links" style="text-align: center;"><a class="social-item" style="text-decoration: none; margin-right: 5px;" href="https://www.google.com.vn/" target="_blank"><img src="https://onesme.vn/resources/upload/file/mail/images/Facebook_white.png" /></a> <a class="social-item" style="text-decoration: none; margin-right: 5px; margin: 0;" href="https://www.google.com.vn/" target="_blank"><img src="https://onesme.vn/resources/upload/file/mail/images/Youtube_white.png" /></a></div>
<div class="company-name" style="text-align: center; color: #2c3d94; font-weight: bold; font-size: 14px; margin: 10px 0 20px;">Tập đo&agrave;n Bưu ch&iacute;nh Viễn th&ocirc;ng Việt Nam (VNPT)</div>
<div class="company-info">
<p style="margin: 10px 0; line-height: 16px; font-size: 10px; text-align: center;">Hotline: 18001260</p>
<p style="margin: 10px 0; line-height: 16px; font-size: 10px; text-align: center;">T&ograve;a nh&agrave; VNPT, số 57 Huỳnh Th&uacute;c Kh&aacute;ng, Phường L&aacute;ng Hạ, Quận Đống Đa, Th&agrave;nh phố H&agrave; Nội, Việt Nam</p>
<p style="margin: 10px 0; line-height: 16px; font-size: 10px; text-align: center;">&copy; Bản Quyền thuộc Tập đo&agrave;n Bưu ch&iacute;nh Viễn th&ocirc;ng Việt Nam</p>
</div>
</div>
</div>
</div>', '<p>&nbsp;</p>
<div class="main ck-content" style="padding: 40px; margin: 0px auto; width: 600px; background-color: #f8f8f8; font-family: Montserrat, Helvetica, sans-serif; box-sizing: border-box; block-size: 1068.25px; perspective-origin: 300px 534.125px; transform-origin: 300px 534.125px;">
<div class="email-container" style="background-color: #ffffff; box-sizing: border-box; block-size: 988.25px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 494.125px; transform-origin: 260px 494.125px; width: 520px;">
<div class="logo-container" style="padding: 20px 20px 5px; box-sizing: border-box; block-size: 43px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 21.5px; transform-origin: 260px 21.5px; width: 520px;">
<div class="logo" style="flex-basis: 50%; text-align: left; margin-left: 40px;"><img src="https://onesme.vn/resources/upload/file/mail/images/oneSME_Logo.png" alt="OneSME Logo" /></div>
<div class="homepage" style="flex-basis: 50%; text-align: right; margin-right: 40px;"><span style="font-size: 14px; font-weight: 500;"><a style="text-decoration: none; color: #1b74e8;" href="https://www.onesme.vn/" target="_blank">www.onesme.vn</a></span></div>
</div>
<div style="border-bottom: 1px solid #cdcdcd; box-sizing: border-box; block-size: 1px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 0.5px; transform-origin: 260px 0.5px; width: 520px;">&nbsp;</div>
<div class="content-container" style="padding: 20px 40px 10px; box-sizing: border-box; block-size: 896.25px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 448.125px; transform-origin: 260px 448.125px; width: 520px;">
<p style="text-align: center; box-sizing: border-box; block-size: 86.25px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 43.125px; transform-origin: 220px 43.125px; width: 440px;"><img class="image_resized" style="width: 86.2656px; box-sizing: border-box; block-size: 86.25px; font: 16px Montserrat, Helvetica, sans-serif; max-inline-size: 100%; max-width: 100%; perspective-origin: 43.125px 43.125px; text-align: center; transform-origin: 43.125px 43.125px;" src="https://onesme.vn/resources/upload/file/mail/images/icon_ac.png" alt="T&agrave;i khoản" /></p>
<p style="margin-left: 0px; text-align: center; box-sizing: border-box; block-size: 18px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 9px; transform-origin: 220px 9px; width: 440px;"><span style="color: #3366ff;"><strong style="box-sizing: border-box; font: 700 16px Montserrat, Helvetica, sans-serif; text-align: center;">BÁO CÁO DOANH NGHIỆP MỚI NGÀY $CURRENT_DATE</strong></span></p>
<table style="border-collapse: collapse; width: 105.682%; height: 187px;" border="1">
<tbody>
<tr style="height: 34px;">
<td style="width: 17.9545%; text-align: center; height: 34px;"><strong>Doanh nghiệp mới</strong></td>
<td style="width: 28.4091%; text-align: center; height: 34px;"><strong>Thời gian</strong></td>
<td style="width: 22.5225%; text-align: center; height: 34px;"><strong>Số lượng</strong></td>
<td style="width: 36.7186%; text-align: center; height: 34px;"><strong>Tăng/Giảm (%)</strong></td>
</tr>
<tr style="height: 17px; $IF_DAY">
<td style="width: 17.9545%; height: 17px;">Ng&agrave;y</td>
<td style="width: 28.4091%; height: 17px;">$CURRENT_DATE</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_DAY_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_DAY_SHORT</td>
</tr>
<tr style="height: 17px; $IF_WEEK">
<td style="width: 17.9545%; height: 17px;">Tuần</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_WEEK - $CURRENT_DATE</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_WEEK_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_WEEK_SHORT</td>
</tr>
<tr style="height: 17px; $IF_MONTH">
<td style="width: 17.9545%; height: 17px;">Th&aacute;ng</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_MONTH - $LAST_MONTH</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_MONTH_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_MONTH_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY1">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; I</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_QUY1 - $END_DATE_QUY1</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY1_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY1_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY2">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; II</td>
<td style="width: 28.4091%; height: 17px; ">$START_DATE_QUY2 - $END_DATE_QUY2</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY2_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY2_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY3">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; III</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_QUY3 - $END_DATE_QUY3</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY3_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY3_SHORT</td>
</tr>
<tr style="height: 17px; $IF_QUY4">
<td style="width: 17.9545%; height: 17px;">Qu&yacute; IV</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_QUY4 - $END_DATE_QUY4</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_QUY4_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_QUY4_SHORT</td>
</tr>
<tr style="height: 17px; $IF_YEAR">
<td style="width: 17.9545%; height: 17px;">Năm</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_YEAR - $CURRENT_DATE</td>
<td style="width: 22.5225%; height: 17px; text-align: center;">$CUSTOMER_YEAR_SHORT</td>
<td style="width: 36.7186%; height: 17px; text-align: center;">$CHANGE_CUSTOMER_YEAR_SHORT</td>
</tr>
<tr style="height: 17px; $IF_TOTAL">
<td style="width: 17.9545%; height: 17px;">Tổng</td>
<td style="width: 28.4091%; height: 17px;">$START_DATE_GET_DATA - $CURRENT_DATE</td>
<td style="width: 59.2411%; height: 17px;text-align: center;" colspan="2">&nbsp; $CUSTOMER_TOTAL</td>
</tr>
</tbody>
</table>
<p style="margin-left: 0px; box-sizing: border-box; block-size: 36px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 18px; transform-origin: 220px 18px; width: 440px;">Truy cập đường link dưới đ&acirc;y để lấy th&ecirc;m th&ocirc;ng tin: <strong style="box-sizing: border-box; font: 700 16px Montserrat, Helvetica, sans-serif;"><u style="box-sizing: border-box; font: 700 16px Montserrat, Helvetica, sans-serif;">$LINK_REPORT</u></strong></p>
<p style="margin-left: 0px; box-sizing: border-box; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 9px; transform-origin: 220px 9px; width: 440px;">Tr&acirc;n trọng,</p>
<p style="margin-left: 0px; box-sizing: border-box; block-size: 18px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 440px; perspective-origin: 220px 9px; transform-origin: 220px 9px; width: 440px;">Đội ngũ kinh doanh online Vinaphone - VNPT</p>
</div>
<div class="footer-container" style="padding: 20px 40px 10px; box-sizing: border-box; block-size: 48px; font: 16px Montserrat, Helvetica, sans-serif; inline-size: 520px; perspective-origin: 260px 24px; transform-origin: 260px 24px; width: 520px;">
<div class="social-links" style="text-align: center;"><a class="social-item" style="text-decoration: none; margin-right: 5px;" href="https://www.google.com.vn/" target="_blank"><img src="https://onesme.vn/resources/upload/file/mail/images/Facebook_white.png" /></a> <a class="social-item" style="text-decoration: none; margin-right: 5px; margin: 0;" href="https://www.google.com.vn/" target="_blank"><img src="https://onesme.vn/resources/upload/file/mail/images/Youtube_white.png" /></a></div>
<div class="company-name" style="text-align: center; color: #2c3d94; font-weight: bold; font-size: 14px; margin: 10px 0 20px;">Tập đo&agrave;n Bưu ch&iacute;nh Viễn th&ocirc;ng Việt Nam (VNPT)</div>
<div class="company-info">
<p style="margin: 10px 0; line-height: 16px; font-size: 10px; text-align: center;">Hotline: 18001260</p>
<p style="margin: 10px 0; line-height: 16px; font-size: 10px; text-align: center;">T&ograve;a nh&agrave; VNPT, số 57 Huỳnh Th&uacute;c Kh&aacute;ng, Phường L&aacute;ng Hạ, Quận Đống Đa, Th&agrave;nh phố H&agrave; Nội, Việt Nam</p>
<p style="margin: 10px 0; line-height: 16px; font-size: 10px; text-align: center;">&copy; Bản Quyền thuộc Tập đo&agrave;n Bưu ch&iacute;nh Viễn th&ocirc;ng Việt Nam</p>
</div>
</div>
</div>
</div>', 'BÁO CÁO DOANH NGHIỆP CÓ TÀI KHOẢN MỚI NGÀY $CURRENT_DATE

$IF_DAY({Trong Ngày: $CUSTOMER_DAY, $CHANGE_CUSTOMER_DAY})

$IF_WEEK({Lũy kế Tuần $START_DATE_WEEK - $CURRENT_DATE: $CUSTOMER_WEEK, $CHANGE_CUSTOMER_WEEK})

$IF_MONTH({Lũy kế Tháng $START_DATE_MONTH - $LAST_MONTH: $CUSTOMER_MONTH, $CHANGE_CUSTOMER_MONTH})

$IF_QUY1({Lũy kế Quý I $START_DATE_QUY1 - $END_DATE_QUY1: $CUSTOMER_QUY1, $CHANGE_CUSTOMER_QUY1})

$IF_QUY2({Lũy kế Quý II $START_DATE_QUY2 - $END_DATE_QUY2: $CUSTOMER_QUY2, $CHANGE_CUSTOMER_QUY2})

$IF_QUY3({Lũy kế Quý III $START_DATE_QUY3 - $END_DATE_QUY3: $CUSTOMER_QUY3, $CHANGE_CUSTOMER_QUY3})

$IF_QUY4({Lũy kế Quý IV $START_DATE_QUY4 - $END_DATE_QUY4: $CUSTOMER_QUY4, $CHANGE_CUSTOMER_QUY4})

$IF_YEAR({Lũy kế Năm $START_DATE_YEAR - $CURRENT_DATE: $CUSTOMER_YEAR, $CHANGE_CUSTOMER_YEAR})

$IF_TOTAL({Tổng $START_DATE_GET_DATA - $CURRENT_DATE: $CUSTOMER_TOTAL})

Truy cập đường link dưới đây để lấy thêm thông tin: $LINK_REPORT

Trân trọng,

Đội ngũ kinh doanh online Vinaphone - VNPT',
     'BÁO CÁO DOANH NGHIỆP MỚI NGÀY $CURRENT_DATE

$IF_DAY({Trong Ngày: $CUSTOMER_DAY, $CHANGE_CUSTOMER_DAY})

$IF_WEEK({Lũy kế Tuần $START_DATE_WEEK - $CURRENT_DATE: $CUSTOMER_WEEK, $CHANGE_CUSTOMER_WEEK})

$IF_MONTH({Lũy kế Tháng $START_DATE_MONTH - $LAST_MONTH: $CUSTOMER_MONTH, $CHANGE_CUSTOMER_MONTH})

$IF_QUY1({Lũy kế Quý I $START_DATE_QUY1 - $END_DATE_QUY1: $CUSTOMER_QUY1, $CHANGE_CUSTOMER_QUY1})

$IF_QUY2({Lũy kế Quý II $START_DATE_QUY2 - $END_DATE_QUY2: $CUSTOMER_QUY2, $CHANGE_CUSTOMER_QUY2})

$IF_QUY3({Lũy kế Quý III $START_DATE_QUY3 - $END_DATE_QUY3: $CUSTOMER_QUY3, $CHANGE_CUSTOMER_QUY3})

$IF_QUY4({Lũy kế Quý IV $START_DATE_QUY4 - $END_DATE_QUY4: $CUSTOMER_QUY4, $CHANGE_CUSTOMER_QUY4})

$IF_YEAR({Lũy kế Năm $START_DATE_YEAR - $CURRENT_DATE: $CUSTOMER_YEAR, $CHANGE_CUSTOMER_YEAR})

$IF_TOTAL({Tổng $START_DATE_GET_DATA - $CURRENT_DATE: $CUSTOMER_TOTAL})

Truy cập đường link dưới đây để lấy thêm thông tin: $LINK_REPORT

Trân trọng,

Đội ngũ kinh doanh online Vinaphone - VNPT', 'EXP', 'Báo cáo Doanh nghiệp có tài khoản mới', 'Báo cáo Doanh nghiệp có tài khoản mới', 26002);


INSERT INTO "vnpt_dev"."param_email" (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES
                                                                                                                         (
                                                                                                                             (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CURRENT_DATE',
    '[Ngày hiện tại]',
    'EXP-02',
    null
    ),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_DAY',
    '[Yêu cầu chứa nội dung báo cáo Ngày]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_DAY',
    '[Khách hàng trong Ngày]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_DAY_SHORT',
    '[Khách hàng trong Ngày]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_DAY',
    '[Phần trăm Tăng/giảm Khách hàng theo Ngày]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_DAY_SHORT',
    '[Phần trăm Tăng/giảm Khách hàng theo Ngày]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_WEEK',
    '[Yêu cầu chứa nội dung báo cáo Tuần]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_WEEK',
    '[Ngày đầu tiên của Tuần]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_WEEK',
    '[Khách hàng lũy kế Tuần]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_WEEK_SHORT',
    '[Khách hàng lũy kế Tuần]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_WEEK',
    '[Tăng trưởng Khách hàng lũy kế Tuần]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_WEEK_SHORT',
    '[Tăng trưởng Khách hàng lũy kế Tuần]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_MONTH',
    '[Yêu cầu chứa nội dung báo cáo Tháng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_MONTH',
    '[Ngày đầu tiên của Tháng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_MONTH',
    '[Khách hàng lũy kế Tháng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_MONTH_SHORT',
    '[Khách hàng lũy kế Tháng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_MONTH',
    '[Tăng trưởng Doanh thu lũy kế Tháng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_MONTH_SHORT',
    '[Tăng trưởng Doanh thu lũy kế Tháng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_QUY1',
    '[Yêu cầu chứa nội dung báo cáo Quý I]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_QUY1',
    '[Ngày bắt đầu Quý I]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$END_DATE_QUY1',
    '[Ngày kết thúc Quý I]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY1',
    '[Khách hàng lũy kế Quý I]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY1_SHORT',
    '[Khách hàng lũy kế Quý I]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY1',
    '[Tăng trưởng Khách hàng lũy kế Quý I]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY1_SHORT',
    '[Tăng trưởng Khách hàng lũy kế Quý I]',
    'EXP-02',
    null
),

-- Quý 2
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_QUY2',
    '[Yêu cầu chứa nội dung báo cáo Quý II]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_QUY2',
    '[Ngày bắt đầu Quý II]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$END_DATE_QUY2',
    '[Ngày kết thúc Quý II]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY2',
    '[Doanh thu lũy kế Quý II]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY2_SHORT',
    '[Doanh thu lũy kế Quý II]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY2',
    '[Tăng trưởng Doanh thu lũy kế Quý II]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY2_SHORT',
    '[Tăng trưởng Doanh thu lũy kế Quý II]',
    'EXP-02',
    null
),

-- Quý 3
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_QUY3',
    '[Yêu cầu chứa nội dung báo cáo Quý III]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_QUY3',
    '[Ngày bắt đầu Quý III]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$END_DATE_QUY3',
    '[Ngày kết thúc Quý III]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY3',
    '[Khách hàng lũy kế Quý III]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY3_SHORT',
    '[Khách hàng lũy kế Quý III]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY3',
    '[Tăng trưởng Khách hàng lũy kế Quý III]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY3_SHORT',
    '[Tăng trưởng Khách hàng lũy kế Quý III]',
    'EXP-02',
    null
),

-- Quý 4
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_QUY4',
    '[Yêu cầu chứa nội dung báo cáo Quý IV]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_QUY4',
    '[Ngày bắt đầu Quý IV]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$END_DATE_QUY4',
    '[Ngày kết thúc Quý IV]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY4',
    '[Khách hàng lũy kế Quý IV]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_QUY4_SHORT',
    '[Khách hàng lũy kế Quý IV]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY4',
    '[Tăng trưởng Khách hàng lũy kế Quý IV]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_QUY4_SHORT',
    '[Tăng trưởng Khách hàng lũy kế Quý IV]',
    'EXP-02',
    null
),
-- Năm
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_YEAR',
    '[Yêu cầu chứa nội dung báo cáo Năm]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_YEAR',
    '[Ngày bắt đầu Năm]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_YEAR',
    '[Khách hàng lũy kế Năm]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_YEAR_SHORT',
    '[Khách hàng lũy kế Năm]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_YEAR',
    '[Tăng trưởng Khách hàng lũy kế Năm]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CHANGE_CUSTOMER_YEAR_SHORT',
    '[Tăng trưởng Khách hàng lũy kế Năm]',
    'EXP-02',
    null
),
-- Tổng
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$IF_TOTAL',
    '[Yêu cầu chứa nội dung báo cáo Tổng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$START_DATE_GET_DATA',
    '[Ngày bắt đầu Tổng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$CUSTOMER_TOTAL',
    '[Tổng số khách hàng]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-03' LIMIT 1),
    '$LAST_MONTH',
    '[Tháng qua]',
    'EXP-02',
    null
),
(
    (SELECT id FROM vnpt_dev.mail_template WHERE code = 'EXP-02' LIMIT 1),
    '$LINK_REPORT',
    '[Link truy cập báo cáo]',
    'EXP-02',
    null
)
;