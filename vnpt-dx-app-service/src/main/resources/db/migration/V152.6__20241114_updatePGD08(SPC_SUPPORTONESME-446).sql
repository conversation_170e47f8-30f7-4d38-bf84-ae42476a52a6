update
    vnpt_dev.mail_template
set
    content_html =
'<!DOCTYPE html>
       <html>
           <head>
               <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
               <link rel="preconnect" href="https://fonts.googleapis.com">
               <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
               <style>
                   table {
                       border-collapse: collapse;
                       width: 100%;
                       table-layout: fixed;
                   }
                   table, td, th {
                       border: 1px solid #E2E8F3;
                   }
                   th, td {
                       padding: 5px;
                       text-align: center;
                       word-wrap: break-word;
                   }
                   th {
                       vertical-align: top;
                   }
               </style>
           </head>
           <body style="padding: 40px;margin: 0 auto;max-width: 1200px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
           <div class="container" style="background-color: #FFFFFF;">
               <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
                   $HEADER
               </div>
               <div class="content-container" style="padding: 40px;">
               <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                   <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_partition_rule.png" alt="Phân giao nhiệm vụ">
                   <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Cập nhật thông tin nhân sự trong quy tắc phân giao</p>
               </div>
               <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
               <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$STAFF_NAME</span>,</p>
               <p style="margin: 0;">Quy tắc phân giao $RULE_CODE vừa được thay đổi nhân sự phụ trách</p>

               <div>$TABLE</div>

               <p style="margin: 0; margin-top: 20px;">Click vào <a href="$LINK">đây</a> để xem chi tiết</p>
               </div>
               </div>
               <div class="footer-container" style="padding: 40px;">
               $FOOTER
               </div>
           </div>
           </body>
       </html>',
    content_html_default =
'<!DOCTYPE html>
      <html>
          <head>
              <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
              <link rel="preconnect" href="https://fonts.googleapis.com">
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
              <style>
                  table {
                      border-collapse: collapse;
                      width: 100%;
                      table-layout: fixed;
                  }
                  table, td, th {
                      border: 1px solid #E2E8F3;
                  }
                  th, td {
                      padding: 5px;
                      text-align: center;
                      word-wrap: break-word;
                  }
                  th {
                      vertical-align: top;
                  }
              </style>
          </head>
          <body style="padding: 40px;margin: 0 auto;max-width: 1200px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
          <div class="container" style="background-color: #FFFFFF;">
              <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
                  $HEADER
              </div>
              <div class="content-container" style="padding: 40px;">
              <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                  <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_partition_rule.png" alt="Phân giao nhiệm vụ">
                  <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Cập nhật thông tin nhân sự trong quy tắc phân giao</p>
              </div>
              <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
              <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$STAFF_NAME</span>,</p>
              <p style="margin: 0;">Quy tắc phân giao $RULE_CODE vừa được thay đổi nhân sự phụ trách</p>

              <div>$TABLE</div>

              <p style="margin: 0; margin-top: 20px;">Click vào <a href="$LINK">đây</a> để xem chi tiết</p>
              </div>
              </div>
              <div class="footer-container" style="padding: 40px;">
              $FOOTER
              </div>
          </div>
          </body>
      </html>'
where
    code = 'PGD-08';


DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code IN ('PGD-08');

INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), '$STAFF_NAME', '[Họ và tên nhân viên phụ trách]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), '$RULE_CODE', '[Mã quy tắc]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), '$TABLE', '[Danh sách bản ghi]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), '$LINK', '[Link tới quy tắc có sự thay đổi]', (SELECT code FROM vnpt_dev.mail_template WHERE code = 'PGD-08'), '');

