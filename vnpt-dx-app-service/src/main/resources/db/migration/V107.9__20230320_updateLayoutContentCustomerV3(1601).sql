-- update customField
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên', "code" = 'personal.firstName.1', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Tên","labelEnabled":true,"hintText":"Nhập tên","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[1,2,4],"patternCombination":1,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.firstName.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ', "code" = 'personal.address.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":1,"maxLength":200,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.address.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'personal.repIssueBy.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Chọn nơi cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":150,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.repIssueBy.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'personal.generalDesc.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.generalDesc.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ', "code" = 'personal.contactAddress.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.contactAddress.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên số chứng thực', "code" = 'personal.uploadIdentityNumber.1', "is_standard" = 't', "type" = 'UPLOAD_IMAGE', "category" = 'CUSTOMER', "config" = '{"label":"Tải lên giấy chứng thực","labelEnabled":true,"hintText":"Tải lên giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":10,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.uploadIdentityNumber.1';
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{canEdit}','false') WHERE code in('personalContactName', 'personalContactMessage', 'personalContactGeneralField', 'personalContactInfoAvatarId', 'personalContactInfoLastname', 'personalContactInfoFirstname', 'personalContactInfoBirthdate', 'personalContactInfoGender', 'personalContactInfoCertType', 'personalContactInfoCertNum', 'personalContactInfoCertDate', 'personalContactInfoCertAvatarId', 'personalContactInfoGen', 'personalContactUploadId');
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Loại giấy chứng thực', "code" = 'personalContactInfoCertType', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Loại giấy chứng thực", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn loại giấy chứng thực", "isUnique": null, "lstValue": ["Chứng minh nhân dân", "Hộ chiếu", "Thẻ căn cước công dân", "Khác"], "mandatory": "ALWAYS", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertType';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số giấy chứng thực', "code" = 'personalContactInfoCertNum', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Số giấy chứng thực", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Nhập số chứng thực", "isUnique": false, "lstValue": null, "mandatory": "ALWAYS", "maxLength": 30, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertNum';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ', "code" = 'personalContactInfoAddr', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Địa chỉ", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": "^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$", "hintText": "Nhập địa chỉ", "isUnique": false, "lstValue": null, "mandatory": "NONE", "maxLength": 200, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": [3, 2, 1, 4], "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": 1, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoAddr';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên giấy chứng thực', "code" = 'personalContactInfoCertAvatarId', "is_standard" = 't', "type" = 'UPLOAD_IMAGE', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tải lên giấy chứng thực","labelEnabled":true,"hintText":"Tải lên giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":10,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertAvatarId';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Trạng thái hoạt động', "code" = 'status', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Trạng thái hoạt động","labelEnabled":true,"hintText":"Chọn trạng thái hoạt động","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Đang hoạt động","Tạm dừng", "Đã đóng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'status';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'enterpriseCompanyEmail', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":100,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyEmail';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'contact.personal.personalInfo.email', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Email", "other": null, "canEdit": true, "pattern": null, "hintText": "Nhập email", "isUnique": null, "lstValue": [], "mandatory": "ALWAYS", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": null, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'contact.personal.personalInfo.email';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'addonDescription', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'ADDON', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"Mô tả dịch vụ bổ sung","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'addonDescription';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'enterprise.phoneNumber.3', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.phoneNumber.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = '', "code" = 'FirstNameLastName_9YlWGGYeNDZMyw6Y', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" ='{"label":"","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":null}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'FirstNameLastName_9YlWGGYeNDZMyw6Y';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên ảnh đại diện', "code" = 'personalContactInfoAvatarId', "is_standard" = 't', "type" = 'UPLOAD_IMAGE', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tải lên ảnh đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoAvatarId';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên ảnh liên hệ', "code" = 'personalContactUploadId', "is_standard" = 't', "type" = 'UPLOAD_IMAGE', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tải lên ảnh liên hệ","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactUploadId';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'personalContactInfoGen', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoGen';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'personalContactGeneralField', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactGeneralField';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Lời nhắn', "code" = 'personalContactMessage', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactMessage';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ lắp đặt', "code" = 'setupAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Địa chỉ lắp đặt","labelEnabled":true,"hintText":"Nhập địa chỉ lắp đặt","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'setupAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Họ HI', "code" = 'personal.lastName.1', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Họ","labelEnabled":true,"hintText":"Nhập họ","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\W]*$","lstPatternToken":[1,2,4],"patternCombination":1,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.lastName.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số chứng thực cá nhân', "code" = 'personal.identityNumber.1', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Số chứng thực cá nhân","labelEnabled":true,"hintText":"Nhập số chứng thực","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\W\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[4,3,2,1],"patternCombination":1,"maxLength":10,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.identityNumber.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ đăng ký kinh doanh', "code" = 'businessRegistrationAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'businessRegistrationAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'phone', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":13,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'phone';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'phoneNumContact', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'phoneNumContact';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'enterpriseCompanyPhone', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\d\\W]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":12,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyPhone';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'contact.personal.contactInfo.phone', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Số điện thoại", "other": null, "canEdit": true, "pattern": "^[0-9]*$", "hintText": "", "isUnique": null, "lstValue": [], "mandatory": "ALWAYS", "maxLength": 11, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": null, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'contact.personal.contactInfo.phone';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'contact.personal.personalInfo.phone', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Số điện thoại", "other": null, "canEdit": true, "pattern": null, "hintText": "Nhập số điện thoại", "isUnique": null, "lstValue": [], "mandatory": "ALWAYS", "maxLength": 20, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": null, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'contact.personal.personalInfo.phone';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số BHXH', "code" = 'socialInsuranceNo', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Số BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'socialInsuranceNo';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'companyEmail', "is_standard" = 't', "type" = 'EMAIL', "category" = 'CUSTOMER', "config" = '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz])(?=.*[\\d])(?=.*[\\W])[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d\\W]*$","lstPatternToken":[1,3,4],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'companyEmail';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'emailContact', "is_standard" = 't', "type" = 'EMAIL', "category" = 'CUSTOMER', "config" = '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'emailContact';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Loại giấy chứng thực', "code" = 'repIdentityType', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Thẻ căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'repIdentityType';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Loại giấy chứng thực', "code" = 'personal.identityType.1', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Thẻ căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.identityType.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Chọn giới tính', "code" = 'personalContactInfoGender', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Chọn giới tính", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": true, "pattern": null, "hintText": "Chọn giới tính", "isUnique": null, "lstValue": ["Nam", "Nữ", "Khác"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoGender';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số chứng thực cá nhân', "code" = 'repIdentityNo', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Số chứng thực cá nhân","labelEnabled":true,"hintText":"Nhập số chứng thực cá nhân","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'repIdentityNo';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nhà mạng', "code" = 'operator', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Nhà mạng", "other": "{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn nhà mạng", "isUnique": null, "lstValue": ["Vinaphone","Viettel","Mobifone"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'operator';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'repSex', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Giới tính", "other": "{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn giới tính", "isUnique": null, "lstValue": ["Nam","Nữ","Khác"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'repSex';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Quy mô', "code" = 'businessSizeId', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Quy mô", "other": "{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn quy mô", "isUnique": null, "lstValue": ["Chỉ có bạn","2-9","10-99","100-299","300+"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'businessSizeId';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'personal.gender.1', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Nam","Nữ","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.gender.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'household.repSex.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Giới tính", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn giới tính", "isUnique": null, "lstValue": ["Nam","Nữ","Khác"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repSex.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'enterprise.repSex.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Giới tính", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn giới tính", "isUnique": null, "lstValue": ["Nam","Nữ","Khác"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.repSex.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'enterprise.personalSex.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Giới tính", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn giới tính", "isUnique": null, "lstValue": ["Nam","Nữ","Khác"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.personalSex.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'household.personalSex.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Giới tính", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn giới tính", "isUnique": null, "lstValue": ["Nam","Nữ","Khác"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.personalSex.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'personal.gender.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Nam","Nữ","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.gender.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Quốc tịch', "code" = 'household.repNationality.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Quốc tịch", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn quốc tịch", "isUnique": null, "lstValue": ["Việt Nam"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repNationality.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Quốc tịch', "code" = 'enterprise.repNationality.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Quốc tịch", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn quốc tịch", "isUnique": null, "lstValue": ["Việt Nam"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.repNationality.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Dân tộc', "code" = 'enterprise.repEthnic.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Dân tộc", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Chọn dân tộc", "isUnique": null, "lstValue": ["Kinh"], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.repEthnic.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Dân tộc', "code" = 'personal.folkName.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Dân tộc","labelEnabled":true,"hintText":"Chọn dân tộc","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Kinh"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.folkName.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Dân tộc', "code" = 'household.repEthnic.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Dân tộc","labelEnabled":true,"hintText":"Chọn dân tộc","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Kinh"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repEthnic.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giấy chứng thực', "code" = 'personal.identityType.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Giấy chứng thực","labelEnabled":true,"hintText":"Chọn giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.identityType.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Quốc gia', "code" = 'personal.nationName.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Quốc gia","labelEnabled":true,"hintText":"Chọn quốc gia","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Việt Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.nationName.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số chứng thực', "code" = 'personal.identityNumber.3', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label": "Số chứng thực", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": "^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$", "hintText": "Nhập số chứng thực", "isUnique": false, "lstValue": null, "mandatory": "NONE", "maxLength": 30, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": [1, 2, 3, 4], "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": 1, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.identityNumber.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày cấp', "code" = 'personal.repPersonalCertDate.3', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER', "config" = '{"label": "Ngày cấp", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Nhập ngày cấp", "isUnique": null, "lstValue": null, "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": "unset", "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.repPersonalCertDate.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'personal.repPersonalCertPlace.3', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label": "Nơi cấp", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": "^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$", "hintText": "Chọn nơi cấp", "isUnique": false, "lstValue": null, "mandatory": "NONE", "maxLength": 50, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": [1, 2, 3, 4], "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": 1, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.repPersonalCertPlace.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số chứng thực', "code" = 'household.repIdentityNumber.3', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Số chứng thực","labelEnabled":true,"hintText":"Nhập số chứng thực","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repIdentityNumber.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'household.introduction.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz])(?=.*[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ])(?=.*[\\d])(?=.*[\\W])[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":0,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.introduction.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mã số thuế', "code" = 'enterprise.taxCode.3', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label": "Mã số thuế", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d]*$","lstPatternToken":[2,3],"patternCombination":1, "hintText": "Nhập mã số thuế", "isUnique": false, "lstValue": null, "mandatory": "ALWAYS", "maxLength": 13, "urlMaxNum": null, "devEnabled": false, "smeEnabled": true, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.taxCode.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên Đăng ký kinh doanh', "code" = 'businessRegistrationFileIds', "is_standard" = 't', "type" = 'UPLOAD_FILE', "category" = 'CUSTOMER', "config" = '{"label": "Tải lên Đăng ký kinh doanh", "other": "{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "", "isUnique": null, "lstValue": null, "mandatory": "NONE", "maxLength": null, "urlMaxNum": 1, "devEnabled": false, "smeEnabled": false, "uploadType": "UPLOAD_URL", "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": 10485760, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": [".xlsx, .xls", ".docx, .doc", ".ppt, .pptx", ".pdf"], "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'businessRegistrationFileIds';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên CMND/CCCD', "code" = 'repIdentityFileIds', "is_standard" = 't', "type" = 'UPLOAD_FILE', "category" = 'CUSTOMER', "config" = '{"label": "Tải lên CMND/CCCD", "other": "{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "", "isUnique": null, "lstValue": null, "mandatory": "NONE", "maxLength": null, "urlMaxNum": 1, "devEnabled": false, "smeEnabled": false, "uploadType": "UPLOAD_URL", "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": 10485760, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": [".xlsx, .xls", ".docx, .doc", ".ppt, .pptx", ".pdf"], "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'repIdentityFileIds';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giấy chứng thực', "code" = 'household.repIdentityType.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Giấy chứng thực","labelEnabled":true,"hintText":"Chọn giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repIdentityType.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'household.repIssueBy.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":150,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repIssueBy.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày cấp', "code" = 'household.repIssueDate.3', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER', "config" = '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":"2023-03-16T03:40:15.592Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.repIssueDate.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'enterprise.repIssueBy.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":150,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.repIssueBy.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giấy chứng thực', "code" = 'enterprise.repIdentityType.3', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label":"Giấy chứng thực","labelEnabled":true,"hintText":"Chọn giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.repIdentityType.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày cấp', "code" = 'enterprise.repIssueDate.3', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER', "config" = '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":"2023-03-16T07:29:27.909Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.repIssueDate.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterprise.introduction.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz])(?=.*[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ])(?=.*[\\d])(?=.*[\\W])[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.introduction.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên tỉnh', "code" = 'personal.provinceName.1', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER', "config" = '{"label": "Tên tỉnh", "canEdit": true, "pattern": null, "hintText": "Chọn tên tỉnh", "isUnique": null, "lstValue": null, "mandatory": "ALWAYS", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.provinceName.1';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên số chứng thực', "code" = 'personal.representative.3', "is_standard" = 't', "type" = 'UPLOAD_FILE', "category" = 'CUSTOMER', "config" = '{"label": "Tải lên giấy chứng thực", "other": "{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}", "canEdit": false, "pattern": null, "hintText": "Tải lên số chứng thực", "isUnique": null, "lstValue": null, "mandatory": "ALWAYS", "maxLength": null, "urlMaxNum": 1, "devEnabled": false, "smeEnabled": true, "uploadType": "UPLOAD_URL", "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": false, "defaultValue": null, "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": 10485760, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": [".jpeg, .jpg, .png, .webp, .tiff, .jfif, .ico"], "getUploadMaxFile": null, "mandatoryCondition": {"action": "CREATE_SUBSCRIPTION", "applyFor": [], "listServices": [], "requiredServiceType": null}, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 3 WHERE "code" = 'personal.representative.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'household.introduction.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz])(?=.*[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ])(?=.*[\\d])(?=.*[\\W])[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.introduction.3';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên người liên hệ', "code" = 'personalContactName', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tên người liên hệ","labelEnabled":true,"hintText":"Nhập tên người liên hệ","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactName';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ', "code" = 'personalContactAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Họ', "code" = 'personalContactInfoLastname', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Họ","labelEnabled":true,"hintText":"Nhập họ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoLastname';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên', "code" = 'personalContactInfoFirstname', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tên","labelEnabled":true,"hintText":"Nhập tên","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoFirstname';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày sinh', "code" = 'personalContactInfoBirthdate', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Chọn ngày sinh","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoBirthdate';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Loại giấy chứng thực', "code" = 'personalContactInfoCertType', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Thẻ căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertType';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số giấy chứng thực', "code" = 'personalContactInfoCertNum', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số giấy chứng thực","labelEnabled":true,"hintText":"Nhập số chứng thực","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertNum';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày cấp', "code" = 'personalContactInfoCertDate', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertDate';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'personalContactInfoCertAddr', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\w]*$","lstPatternToken":[4,3,2,1],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertAddr';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên doanh nghiệp', "code" = 'enterpriseCompanyName', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tên doanh nghiệp","labelEnabled":true,"hintText":"Nhập tên doanh nghiệp","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyName';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Chi cục thuế', "code" = 'enterpriseCompanyTaxDepartment', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Chi cục thuế","labelEnabled":true,"hintText":"Nhập chi cục thuế","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyTaxDepartment';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mã số thuế', "code" = 'enterpriseCompanyTaxCode', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyTaxCode';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ đăng ký kinh doanh', "code" = 'enterpriseCompanyBusinessAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyBusinessAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số BHXH', "code" = 'enterpriseCompanySocialInsurance', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d]*$","lstPatternToken":[1,2,3],"patternCombination":1,"maxLength":10,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanySocialInsurance';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Trạng thái hoạt động', "code" = 'enterpriseCompanyStatus', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Trạng thái hoạt động","labelEnabled":true,"hintText":"Chọn trạng thái hoạt động","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Đang hoạt động","Tạm dừng","Đã đóng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyStatus';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nhà mạng', "code" = 'enterpriseCompanyNetwork', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Nhà mạng","labelEnabled":true,"hintText":"Chọn nhà mạng","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Vinaphone","Viettel","Mobifone"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyNetwork';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'enterpriseCompanyPhone', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[\\d\\W]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":12,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyPhone';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Fax', "code" = 'enterpriseCompanyFax', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Fax","labelEnabled":true,"hintText":"Nhập số fax","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[\\d\\W]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyFax';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'enterpriseCompanyEmail', "is_standard" = 't', "type" = 'EMAIL', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":100,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyEmail';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số giấy phép ĐKKD', "code" = 'enterpriseCompanyBusinessRegistration', "is_standard" = 't', "type" = 'NUMBER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số giấy phép ĐKKD","labelEnabled":true,"hintText":"Nhập số giấy phép ĐKKD","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyBusinessRegistration';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Quy mô', "code" = 'enterpriseCompanySize', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Quy mô","labelEnabled":true,"hintText":"Chọn quy mô","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chỉ có bạn","2-9","10-99","100-299","300+"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanySize';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterpriseCompanyDescription', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyDescription';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên người đại diện', "code" = 'enterpriseRepName', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tên người đại diện","labelEnabled":true,"hintText":"Nhập tên người đại diện","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d\\W]*$","lstPatternToken":[2,1,3,4],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepName';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới tính', "code" = 'enterpriseRepGender', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Nữ","Nam","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepGender';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Chức danh', "code" = 'enterpriseRepRole', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Chức danh","labelEnabled":true,"hintText":"Nhập chức danh","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepRole';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày sinh', "code" = 'enterpriseRepBirth', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Chọn ngày sinh","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"2023-02-28T02:50:13.855Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepBirth';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Loại giấy chứng thực', "code" = 'enterpriseRepIdentityType', "is_standard" = 't', "type" = 'DROPDOWN_LIST', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Thẻ căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepIdentityType';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số chứng thực cá nhân', "code" = 'enterpriseRepIdentityNo', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số chứng thực cá nhân","labelEnabled":true,"hintText":"Nhập số chứng thực cá nhân","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\W]*$","lstPatternToken":[3,1,2,4],"patternCombination":1,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepIdentityNo';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Ngày cấp', "code" = 'enterpriseRepIdentityDate', "is_standard" = 't', "type" = 'DATE_PICKER', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"2023-02-28T02:59:42.954Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepIdentityDate';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'enterpriseRepIdentityAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\w]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":150,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepIdentityAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi đăng ký hộ khẩu', "code" = 'enterpriseRepRegisterAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Nơi đăng ký hộ khẩu","labelEnabled":true,"hintText":"Nhập nơi đăng ký hộ khẩu","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepRegisterAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Chỗ ở hiện tại', "code" = 'enterpriseRepAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Chỗ ở hiện tại","labelEnabled":true,"hintText":"Nhập chỗ ở hiện tại","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepAddress';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên CMTND/CCCD', "code" = 'enterpriseRepIdentityFile', "is_standard" = 't', "type" = 'UPLOAD_IMAGE', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tải lên CMTND/CCCD","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepIdentityFile';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterpriseContactDescription', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\Waàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[2,3,4,1],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseContactDescription';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Chức vụ', "code" = 'enterpriseContactRole', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Chức vụ","labelEnabled":true,"hintText":"Nhập chức vụ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseContactRole';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Lời nhắn', "code" = 'enterpriseContactMessage', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseContactMessage';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tải lên ảnh đại diện', "code" = 'contactAvatarFileId', "is_standard" = 't', "type" = 'UPLOAD_IMAGE', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tải lên ảnh đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'contactAvatarFileId';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Tên tổ chức', "code" = 'enterpriseContactOrganization', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Tên tổ chức","labelEnabled":true,"hintText":"Nhập tên tổ chức","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseContactOrganization';
UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'enterpriseCompanyPhone', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[\\d]*$","lstPatternToken":[3],"patternCombination":1,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyPhone';

ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

UPDATE "vnpt_dev"."custom_layout" SET "admin_enabled" = 'f' WHERE "name" in ('Liên hệ cá nhân','Liên hệ doanh nghiệp','Liên hệ hộ kinh doanh');
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{adminEnabled}','true') WHERE custom_field.category in ('CUSTOMER_CONTACT') and custom_field.is_standard = true;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{devEnabled}','false') WHERE custom_field.category in ('CUSTOMER_CONTACT') and custom_field.is_standard = true;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{smeEnabled}','false') WHERE custom_field.category in ('CUSTOMER_CONTACT') and custom_field.is_standard = true;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{adminEnabled}','true') WHERE custom_field.category in ('CUSTOMER') and custom_field.is_standard = true and portal_type = 1;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{devEnabled}','false') WHERE custom_field.category in ('CUSTOMER') and custom_field.is_standard = true and portal_type = 1;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{smeEnabled}','false') WHERE custom_field.category in ('CUSTOMER') and custom_field.is_standard = true and portal_type = 1;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{adminEnabled}','false') WHERE custom_field.category in ('CUSTOMER') and custom_field.is_standard = true and portal_type = 3;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{devEnabled}','false') WHERE custom_field.category in ('CUSTOMER') and custom_field.is_standard = true and portal_type = 3;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{smeEnabled}','true') WHERE custom_field.category in ('CUSTOMER') and custom_field.is_standard = true and portal_type = 3;

--Insert table api_permission
INSERT INTO vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."api_permission"), (SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/users-admin' LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'LAYOUT_DETAIL' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."api_permission"), (SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/crm/enterprise-mgmt/details/get-lst-enterprise-have-account' LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'LAYOUT_DETAIL' AND pp.portal_id = 1 LIMIT 1), 1, 1);
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;

UPDATE vnpt_dev.custom_field set name = 'Null' where name ='';
ALTER TABLE "vnpt_dev"."custom_layout"
    DROP CONSTRAINT IF EXISTS "uniqueCustomLayoutName";
ALTER TABLE "vnpt_dev"."custom_layout"
    DROP CONSTRAINT IF EXISTS "uniqueLayoutNameAndCategory";
ALTER TABLE "vnpt_dev"."custom_layout"
    ADD CONSTRAINT "uniqueLayoutNameAndCategory" UNIQUE ("name", "category", "is_template");

ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";