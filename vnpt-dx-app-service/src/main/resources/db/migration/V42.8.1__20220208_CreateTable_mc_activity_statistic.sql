/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:47:22
*/


-- ----------------------------
-- Table structure for mc_activity_statistic
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_activity_statistic";
CREATE TABLE "vnpt_dev"."mc_activity_statistic" (
  "id" bigserial NOT NULL,
  "mc_id" int8,
  "activity_id" int8,
  "total_email" int4,
  "success_email" int4,
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "created_by" int2,
  "modified_by" int2,
  "status" int2,
  "deleted_flag" int2
)
;

-- ----------------------------
-- Primary Key structure for table mc_activity_statistic
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_activity_statistic" ADD CONSTRAINT "mc_activity_statistic_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table mc_activity_statistic
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_activity_statistic" ADD CONSTRAINT "fk_activity_statistic_marketing_campaign_1" FOREIGN KEY ("mc_id") REFERENCES "vnpt_dev"."marketing_campaign" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "vnpt_dev"."mc_activity_statistic" ADD CONSTRAINT "fk_activity_statistic_mc_action_email_auto_sms_1" FOREIGN KEY ("activity_id") REFERENCES "vnpt_dev"."mc_action_email_auto_sms" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
