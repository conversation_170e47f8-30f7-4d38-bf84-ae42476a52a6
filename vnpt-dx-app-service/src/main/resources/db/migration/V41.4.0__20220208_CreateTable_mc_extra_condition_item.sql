/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:49:18
*/


-- ----------------------------
-- Table structure for mc_extra_condition_item
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_extra_condition_item";
CREATE TABLE "vnpt_dev"."mc_extra_condition_item" (
  "id" bigserial NOT NULL,
  "time_operator_code" int4,
  "time_value_code" int4,
  "time" date,
  "purchase_time" int2,
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "time_range" text COLLATE "pg_catalog"."default",
  "value" int2,
  "unit" int2,
  "mc_id" int8,
  "created_by" int2,
  "modified_by" int2,
  "status" int2,
  "deleted_flag" int2
)
;
COMMENT ON COLUMN "vnpt_dev"."mc_extra_condition_item"."time_operator_code" IS 'code của bảng operator';
COMMENT ON COLUMN "vnpt_dev"."mc_extra_condition_item"."time_value_code" IS 'code của bảng mc_data_type';
COMMENT ON COLUMN "vnpt_dev"."mc_extra_condition_item"."purchase_time" IS '0: Tất cả; 1: Chỉ lần mua hiện tại; 2: Trước lần mua hiện tại';

-- ----------------------------
-- Primary Key structure for table mc_extra_condition_item
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_extra_condition_item" ADD CONSTRAINT "extra_condition_item_pkey" PRIMARY KEY ("id");
