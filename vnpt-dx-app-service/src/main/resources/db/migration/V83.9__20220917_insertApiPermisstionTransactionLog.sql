--Insert table role
INSERT INTO vnpt_dev.role (deleted_flag, status, description, name, allow_edit, display_name)
VALUES (1, 1, 'Cấu hình transaction log', 'ROLE_ADMIN_A_CT', 1, 'Cấu hình transaction log');

--Insert table role_portal
INSERT INTO vnpt_dev.role_portal (role_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), 1);

--Insert table apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/list', 'ROLE_ADMIN_XEM_DANH_SACH_TRANSACTION_LOG', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity', 'ROLE_ADMIN_XEM_DANH_SACH_CAU_HINH_TRANSACTION_LOG', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/{id}', 'ROLE_ADMIN_XEM_CHI_TIET_CAU_HINH_TRANSACTION_LOG', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/{id}', 'ROLE_ADMIN_SUA_CHI_TIET_CAU_HINH_TRANSACTION_LOG', 'PUT');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/transaction/{id}', 'ROLE_ADMIN_XEM_CHI_TIET_TRANSACTION_LOG', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/all/{id}', 'ROLE_ADMIN_XEM_DANH_SACH_HOAT_DONG_CUA_GIAO_DICH', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/delete/{id}', 'ROLE_ADMIN_XOA_TRANSACTION_LOG', 'DELETE');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/get-top-transaction', 'ROLE_ADMIN_XEM_DANH_SACH_GIAO_DICH_NHIEU_NHAT', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/total_transaction_by_time', 'ROLE_ADMIN_XEM_THONG_KE_TRANSACTION_THEO_THOI_GIAN', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity-log/{id}', 'ROLE_ADMIN_CAP_NHAT_TRANG_THAI_ACTIVITY_LOG', 'PUT');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/get-top-failed-transaction', 'ROLE_ADMIN_XEM_DANH_SACH_SPDV_NHIEU_GIAO_DICH_THAT_BAI_NHAT', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/get-preview-failed-transaction/{id}', 'ROLE_ADMIN_XEM_PREVIEW_GIAO_DICH_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/export-preview-failed-transaction/{id}', 'ROLE_ADMIN_XUAT_PREVIEW_GIAO_DICH_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/system-names', 'ROLE_ADMIN_XEM_DANH_SACH_SYSTEM_NAME', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/drop-list', 'ROLE_ADMIN_XEM_DANH_SACH_ACTIVITY_DROP_LIST', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/get-failed-activity', 'ROLE_ADMIN_XEM_DANH_SACH_HOAT_DONG_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/export-failed-activity', 'ROLE_ADMIN_XUAT_DANH_SACH_HOAT_DONG_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/get-dashboard-failed-activity', 'ROLE_ADMIN_DASHBOARD_HOAT_DONG_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/get-preview-failed-activity', 'ROLE_ADMIN_XEM_PREVIEW_HOAT_DONG_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/export-preview-failed-activity', 'ROLE_ADMIN_XUAT_PREVIEW_HOAT_DONG_THAT_BAI', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/get-total-transaction', 'ROLE_ADMIN_XEM_DASHBOARD_TONG_SO_GIAO_DICH', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/get-preview-transaction', 'ROLE_ADMIN_XEM_PREVIEW_GIAO_DICH', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/export-preview-transaction', 'ROLE_ADMIN_XUAT_PREVIEW_GIAO_DICH', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/get-config-delete', 'ROLE_ADMIN_XEM_CAU_HINH_XOA_TRANSACTION_LOG', 'GET');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/transaction-log/config-delete', 'ROLE_ADMIN_CAU_HINH_XOA_TRANSACTION_LOG', 'PUT');


INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity-log/{id}', 'ROLE_ADMIN_XOA_ACTIVITY_LOG', 'DELETE');
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity-log/resend/{id}', 'ROLE_ADMIN_GUI_LAI_ACTIVITY_THAT_BAI', 'POST');

--Insert table permission
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Quản lý transaction logs', 'QUAN_LY_TRANSACTION_LOG', -1, (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem danh sách giao dịch', 'XEM_DANH_SACH_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem danh sách cấu hình giao dịch', 'XEM_DANH_SACH_CAU_HINH_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem chi tiết cấu hình giao dịch', 'XEM_CHI_TIET_CAU_HINH_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Sửa chi tiết cấu hình giao dịch', 'SUA_CHI_TIET_CAU_HINH_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem chi tiết giao dịch', 'XEM_CHI_TIET_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Danh sách hoạt động của giao dịch', 'XEM_DANH_SACH_HOAT_DONG_CUA_GIAO_DICH', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xóa transaction log', 'XOA_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem danh sách giao dịch nhiều nhất', 'XEM_DANH_SACH_GIAO_DICH_NHIEU_NHAT', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Thống kê transaction theo thời gian', 'THONG_KE_TRANSACTION_THEO_THOI_GIAN', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Cập nhật trạng thái activity log', 'CAP_NHAT_TRANG_THAI_ACTIVITY_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem danh sách SPDV có nhiều giao dịch thất bại nhất', 'XEM_DANH_SACH_SPDV_NHIEU_GIAO_DICH_THAT_BAI_NHAT', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem preview giao dịch thất bại', 'XEM_PREVIEW_GIAO_DICH_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xuất preview giao dịch thất bại', 'XUAT_PREVIEW_GIAO_DICH_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Lấy danh sách system name', 'XEM_DANH_SACH_SYSTEM_NAME', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Lấy danh sách activity drop list', 'XEM_DANH_SACH_ACTIVITY_DROP_LIST', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Lấy danh sách hoạt động thất bại', 'XEM_DANH_SACH_HOAT_DONG_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xuất danh sách hoạt động thất bại', 'XUAT_DANH_SACH_HOAT_DONG_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Dashboard hoạt động thất bại', 'DASHBOARD_HOAT_DONG_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem preview hoạt động thất bại', 'XEM_PREVIEW_HOAT_DONG_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xuất preview hoạt động thất bại', 'XUAT_PREVIEW_HOAT_DONG_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem dashboard tổng số giao dịch', 'XEM_DASHBOARD_TONG_SO_GIAO_DICH', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem preview giao dịch', 'XEM_PREVIEW_GIAO_DICH', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xuất preview giao dịch', 'XUAT_PREVIEW_GIAO_DICH', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem cấu hình xóa transaction log', 'XEM_CAU_HINH_XOA_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Cấu hình xóa transaction log', 'CAU_HINH_XOA_TRANSACTION_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));


INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem activity log', 'XOA_ACTIVITY_LOG', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Gửi lại activity log thất bại', 'GUI_LAI_ACTIVITY_THAT_BAI', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));

--Insert table permission_portal
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'SUA_CHI_TIET_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_GIAO_DICH_NHIEU_NHAT'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'THONG_KE_TRANSACTION_THEO_THOI_GIAN'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_TRANG_THAI_ACTIVITY_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SPDV_NHIEU_GIAO_DICH_THAT_BAI_NHAT'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_GIAO_DICH_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_GIAO_DICH_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_HOAT_DONG_CUA_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SYSTEM_NAME'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_ACTIVITY_DROP_LIST'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_DANH_SACH_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'DASHBOARD_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DASHBOARD_TONG_SO_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CAU_HINH_XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'CAU_HINH_XOA_TRANSACTION_LOG'), 1);


INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_ACTIVITY_LOG'), 1);
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'GUI_LAI_ACTIVITY_THAT_BAI'), 1);

--Insert table api_permission
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/list'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_CAU_HINH_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/{id}' AND "method" = 'GET'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_CHI_TIET_CAU_HINH_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/{id}' AND "method" = 'PUT'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'SUA_CHI_TIET_CAU_HINH_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/transaction/{id}'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_CHI_TIET_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/all/{id}'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_HOAT_DONG_CUA_GIAO_DICH' AND pp.portal_id = 1 LIMIT 1) , 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/delete/{id}'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XOA_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/get-top-transaction'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_GIAO_DICH_NHIEU_NHAT' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/total_transaction_by_time'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'THONG_KE_TRANSACTION_THEO_THOI_GIAN' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity-log/{id}' AND "method" = 'PUT'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'CAP_NHAT_TRANG_THAI_ACTIVITY_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/get-top-failed-transaction'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_SPDV_NHIEU_GIAO_DICH_THAT_BAI_NHAT' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/get-preview-failed-transaction/{id}'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_PREVIEW_GIAO_DICH_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/export-preview-failed-transaction/{id}'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XUAT_PREVIEW_GIAO_DICH_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/system-names'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_SYSTEM_NAME' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/drop-list'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_ACTIVITY_DROP_LIST' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/get-failed-activity'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_HOAT_DONG_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/export-failed-activity'),  (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XUAT_DANH_SACH_HOAT_DONG_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/get-dashboard-failed-activity'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'DASHBOARD_HOAT_DONG_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/get-preview-failed-activity'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'DASHBOARD_HOAT_DONG_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/export-preview-failed-activity'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XUAT_PREVIEW_HOAT_DONG_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/get-total-transaction'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DASHBOARD_TONG_SO_GIAO_DICH' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/get-preview-transaction'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_PREVIEW_GIAO_DICH' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/export-preview-transaction'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XUAT_PREVIEW_GIAO_DICH' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/get-config-delete'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_CAU_HINH_XOA_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/transaction-log/config-delete'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'CAU_HINH_XOA_TRANSACTION_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);


INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity-log/{id}' AND "method" = 'DELETE'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XOA_ACTIVITY_LOG' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity-log/resend/{id}'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'GUI_LAI_ACTIVITY_THAT_BAI' AND pp.portal_id = 1 LIMIT 1), 1, 1);

--Insert table roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'SUA_CHI_TIET_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_GIAO_DICH_NHIEU_NHAT'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'THONG_KE_TRANSACTION_THEO_THOI_GIAN'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_TRANG_THAI_ACTIVITY_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SPDV_NHIEU_GIAO_DICH_THAT_BAI_NHAT'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_GIAO_DICH_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_GIAO_DICH_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_HOAT_DONG_CUA_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SYSTEM_NAME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_ACTIVITY_DROP_LIST'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_DANH_SACH_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'DASHBOARD_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DASHBOARD_TONG_SO_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CAU_HINH_XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAU_HINH_XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_ACTIVITY_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'GUI_LAI_ACTIVITY_THAT_BAI'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_GIAO_DICH_NHIEU_NHAT'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'THONG_KE_TRANSACTION_THEO_THOI_GIAN'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SPDV_NHIEU_GIAO_DICH_THAT_BAI_NHAT'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_GIAO_DICH_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_GIAO_DICH_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_HOAT_DONG_CUA_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SYSTEM_NAME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_ACTIVITY_DROP_LIST'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_DANH_SACH_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'DASHBOARD_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_HOAT_DONG_THAT_BAI'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DASHBOARD_TONG_SO_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_PREVIEW_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_PREVIEW_GIAO_DICH'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_ACTIVITY_LOG'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'SUA_CHI_TIET_CAU_HINH_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CAU_HINH_XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAU_HINH_XOA_TRANSACTION_LOG'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.role WHERE name = 'ROLE_ADMIN_A_CT'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_TRANG_THAI_ACTIVITY_LOG'), 1);


REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;