ALTER TABLE vnpt_dev.subscriptions
ADD COLUMN IF NOT EXISTS province_id_setup_new int8,
ADD COLUMN IF NOT EXISTS ward_id_setup_new int8,
ADD COLUMN IF NOT EXISTS street_id_setup_new int8,
ADD COLUMN IF NOT EXISTS address_new varchar(500);

COMMENT ON COLUMN vnpt_dev.subscriptions.province_id_setup_new IS 'ID tỉnh mới';
COMMENT ON COLUMN vnpt_dev.subscriptions.ward_id_setup_new IS 'ID phường/xã mới';
COMMENT ON COLUMN vnpt_dev.subscriptions.street_id_setup_new IS 'ID đường/phố mới';
COMMENT ON COLUMN vnpt_dev.subscriptions.address_new IS 'Địa chỉ mới';


ALTER TABLE "vnpt_dev"."billings"
    ADD COLUMN IF NOT EXISTS province_id_customer_new int8,
    ADD COLUMN IF NOT EXISTS province_code_customer_new varchar (20),
    ADD COLUMN IF NOT EXISTS province_name_customer_new varchar (500),
    ADD COLUMN IF NOT EXISTS ward_id_customer_new int8,
    ADD COLUMN IF NOT EXISTS ward_code_customer_new varchar (20),
    ADD COLUMN IF NOT EXISTS ward_name_customer_new varchar (500),
    ADD COLUMN IF NOT EXISTS street_id_customer_new int8,
    ADD COLUMN IF NOT EXISTS street_name_customer_new varchar (500);

COMMENT ON COLUMN "vnpt_dev"."billings"."province_id_customer_new" IS 'Id tỉnh thành mới của địa chỉ khách hàng trong hóa đơn';
COMMENT ON COLUMN "vnpt_dev"."billings"."province_code_customer_new" IS 'Mã tỉnh thành mới của địa chỉ khách hàng trong hóa đơn';
COMMENT ON COLUMN "vnpt_dev"."billings"."province_name_customer_new" IS 'Tên tỉnh thành mới của địa chỉ khách hàng trong hóa đơn';

COMMENT ON COLUMN "vnpt_dev"."billings"."ward_id_customer_new" IS 'Id xã, phường mới của địa chỉ khách hàng trong hóa đơn';
COMMENT ON COLUMN "vnpt_dev"."billings"."ward_code_customer_new" IS 'Mã xã, phường mới của địa chỉ khách hàng trong hóa đơn';
COMMENT ON COLUMN "vnpt_dev"."billings"."ward_name_customer_new" IS 'Tên xã, phường mới của địa chỉ khách hàng trong hóa đơn';

COMMENT ON COLUMN "vnpt_dev"."billings"."street_id_customer_new" IS 'Id đường mới của địa chỉ khách hàng trong hóa đơn';
COMMENT ON COLUMN "vnpt_dev"."billings"."street_name_customer_new" IS 'Tên đường mới của địa chỉ khách hàng trong hóa đơn';
