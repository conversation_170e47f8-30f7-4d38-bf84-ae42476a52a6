/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:49:59
*/


-- ----------------------------
-- Table structure for mc_restriction_effect_item
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_restriction_effect_item";
CREATE TABLE "vnpt_dev"."mc_restriction_effect_item" (
  "id" bigserial NOT NULL,
  "operand_code" int4 NOT NULL,
  "operator_code" int4 NOT NULL,
  "value_code" int4,
  "value" varchar(4096) COLLATE "pg_catalog"."default",
  "mc_id" int8,
  "effect_item_id" int8 NOT NULL,
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "created_by" int2,
  "modified_by" int2,
  "status" int2,
  "deleted_flag" int2
)
;
COMMENT ON COLUMN "vnpt_dev"."mc_restriction_effect_item"."mc_id" IS 'ID của bảng marketing_campaign';

-- ----------------------------
-- Primary Key structure for table mc_restriction_effect_item
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_restriction_effect_item" ADD CONSTRAINT "effect_item_pk_copy_1" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table mc_restriction_effect_item
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_restriction_effect_item" ADD CONSTRAINT "fk_restriction_effect_item_effect_item_1" FOREIGN KEY ("effect_item_id") REFERENCES "vnpt_dev"."mc_effect_item" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
