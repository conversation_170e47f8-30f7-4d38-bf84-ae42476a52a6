create table if not exists "vnpt_dev"."users_biometric_auth" (
     "id" bigserial not null primary key,
     "user_id" int8 not null,
     "app_public_key" text not null,
     "device_id" text not null,
     "created_at" timestamp default now(),
     "created_by" int8
);
comment on table "vnpt_dev"."users_biometric_auth" is 'Bảng lưu mapping user và public_key của <PERSON>ng dụng theo thiết bị khi đăng ký xác thực bằng sinh trắc học';
comment on column "vnpt_dev"."users_biometric_auth"."user_id" is 'Id user';
comment on column "vnpt_dev"."users_biometric_auth"."app_public_key" is 'Public_key của ứng dụng theo thiết bị để kiểm tra eSignature';
comment on column "vnpt_dev"."users_biometric_auth"."device_id" is 'Id của thiết bị đăng ký xác thực sinh trắc họ<PERSON>';
comment on column "vnpt_dev"."users_biometric_auth"."created_at" is 'Thời gian đăng ký xác thực sinh trắc họ<PERSON>';
comment on column "vnpt_dev"."users_biometric_auth"."created_by" is 'Id user đăng ký sinh trắc học';

-- tạo index --
create unique index if not exists "userBiometricUserIdDeviceIdUnqIdx" on "vnpt_dev"."users_biometric_auth" using btree("user_id", "device_id");
create index if not exists "usersBiometricAuthUserIdIdx" on "vnpt_dev"."users_biometric_auth" using btree("user_id");
create index if not exists "usersBiometricAuthDeviceIdIdx" on "vnpt_dev"."users_biometric_auth" using btree("device_id");
