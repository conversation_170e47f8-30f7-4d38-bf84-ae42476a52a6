-- report_view_customer
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_customer" AS
SELECT 
    users.id, users.created_at, users.created_by, users.deleted_flag, users.modified_at, users.modified_by, 
    users.status, users.account_expired, users.account_locked, users.activation_key, users.address, 
    users.avatar, users.birthday, users.company, users.tin, users.cover_image, users.credentials_expired, 
    users.email, users.enabled, users.first_name, users.gender, users.last_name, users.password, 
    users.password_tmp, users.phone_number, users.user_name, users.website, users.district_id, 
    users.nation_id, users.province_id, users.business_area_id, users.business_size_id, 
    users.parent_id, users.user_code, users.business_email, users.name,  users.description, 
    users.department_id, users.sme_user_id, users.tech_id, users.create_type, users.customer_code, 
    users.province_code, users.ward_id, users.street_id, users.social_insurance_number, users.rep_fullname, 
    users.rep_gender, users.rep_title, users.rep_birthday, users.rep_nation_id, users.rep_folk_id, 
    users.rep_personal_cert_type_id, users.rep_personal_cert_number, users.rep_personal_cert_date, 
    users.rep_personal_cert_place::character varying(50) AS rep_personal_cert_place, users.rep_registered_place, 
    users.rep_address, users.parent_status, users.employee_code,
    nation.name as nation_name,
    province.name AS province_name,
    district.name AS district_name,
    ward.name AS ward_name,
    street.name AS street_name
FROM vnpt_dev.users
    JOIN vnpt_dev.view_role_sme on view_role_sme.user_id = users.id
    LEFT JOIN vnpt_dev.province ON province.id = users.province_id
    LEFT JOIN vnpt_dev.district ON district.id = users.district_id AND district.province_code = province.code
    LEFT JOIN vnpt_dev.ward ON ward.id = users.ward_id AND ward.province_code = province.code
    LEFT JOIN vnpt_dev.street ON street.id = users.street_id AND street.ward_id = users.ward_id AND street.district_id = users.district_id AND street.province_code = province.code
    LEFT JOIN vnpt_dev.nation ON nation.id = users.nation_id;


-- report_view_user_full_info
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_user_full_info" AS
SELECT dt.name AS district_name, na.name AS nation_name,
    wa.name AS ward_name, sd.name AS street_name,
    p.name AS province_name, ba.name AS business_area_name,
    bs.name AS business_area_size_name, dp.department_name,
    u.id, u.created_at, u.created_by, u.deleted_flag, u.modified_at,
    u.modified_by, u.status, u.account_expired, u.account_locked,
    u.activation_key, u.address, u.avatar, u.birthday, u.company,
    u.tin, u.cover_image, u.credentials_expired, u.email, u.enabled, 
    u.first_name, u.gender, u.last_name, u.password, u.password_tmp, 
    u.phone_number, u.user_name, u.website, u.district_id, u.nation_id, 
    u.province_id, u.business_area_id, u.business_size_id, u.parent_id, 
    u.user_code, u.business_email, u.name, u.description, u.department_id, 
    u.sme_user_id, u.tech_id, u.create_type, u.customer_code, u.province_code, 
    u.ward_id, u.street_id, u.social_insurance_number, u.rep_fullname, 
    u.rep_gender, u.rep_title, u.rep_birthday, u.rep_nation_id, u.rep_folk_id, 
    u.rep_personal_cert_type_id, u.rep_personal_cert_number, u.rep_personal_cert_date, 
    u.rep_personal_cert_place::character varying(50) AS rep_personal_cert_place, 
    u.rep_registered_place, u.rep_address, u.parent_status, u.employee_code
   FROM vnpt_dev.users u
     LEFT JOIN vnpt_dev.district dt ON u.district_id = dt.id AND u.province_code::text = dt.province_code::text AND u.province_id = dt.province_id
     LEFT JOIN vnpt_dev.nation na ON u.nation_id = na.id
     LEFT JOIN vnpt_dev.ward wa ON u.ward_id = wa.id AND u.district_id = wa.district_id AND u.province_code::text = wa.province_code::text
     LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id AND u.province_code::text = sd.province_code::text
     LEFT JOIN vnpt_dev.business_area ba ON u.business_area_id = ba.id
     LEFT JOIN vnpt_dev.business_size bs ON u.business_size_id = bs.id
     LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
     LEFT JOIN vnpt_dev.departments dp ON u.department_id = dp.id;

-- report_view_user_full_info_new
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_user_full_info_new" AS
SELECT 
    dt.name AS district_name, na.name AS nation_name, wa.name AS ward_name, 
    sd.name AS street_name, p.name AS province_name, ba.name AS business_area_name, 
    bs.name AS business_area_size_name, dp.department_name, u.id, u.created_at, 
    u.created_by, u.deleted_flag,  u.modified_at, u.modified_by, u.status, u.account_expired, 
    u.account_locked, u.activation_key, u.address, u.avatar, u.birthday, u.company, u.tin, 
    u.cover_image, u.credentials_expired, u.email, u.enabled, u.first_name, u.gender, u.last_name, 
    u.password, u.password_tmp, u.phone_number, u.user_name, u.website, u.district_id, u.nation_id, 
    u.province_id, u.business_area_id, u.business_size_id, u.parent_id, u.user_code, u.business_email, 
    u.name, u.customer_type, u.description, u.department_id, u.sme_user_id, u.tech_id, u.create_type, 
    u.customer_code, u.province_code, u.ward_id, u.street_id, u.social_insurance_number, u.rep_fullname, 
    u.rep_gender, u.rep_title, u.rep_birthday, u.rep_nation_id, u.rep_folk_id, u.rep_personal_cert_type_id, 
    u.rep_personal_cert_number, u.rep_personal_cert_date, 
    u.rep_personal_cert_place::character varying(50) AS rep_personal_cert_place, u.rep_registered_place, 
    u.rep_address, u.parent_status, u.employee_code
FROM vnpt_dev.users u
    LEFT JOIN vnpt_dev.district dt ON u.district_id = dt.id AND u.province_code::text = dt.province_code::text AND u.province_id = dt.province_id
    LEFT JOIN vnpt_dev.nation na ON u.nation_id = na.id
    LEFT JOIN vnpt_dev.ward wa ON u.ward_id = wa.id AND u.district_id = wa.district_id AND u.province_code::text = wa.province_code::text
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id AND u.province_code::text = sd.province_code::text
    LEFT JOIN vnpt_dev.business_area ba ON u.business_area_id = ba.id
    LEFT JOIN vnpt_dev.business_size bs ON u.business_size_id = bs.id
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.departments dp ON u.department_id = dp.id;

-- feature_view_customer_detail
CREATE OR REPLACE VIEW "vnpt_dev"."feature_view_customer_detail" AS
 SELECT
    u.id, u.created_at, u.created_by, u.deleted_flag, u.modified_at, u.modified_by, u.status, u.account_expired,
    u.account_locked, u.activation_key, u.address, u.avatar, u.birthday, u.company, u.tin, u.cover_image,
    u.credentials_expired, u.email, u.enabled, u.first_name, u.gender, u.last_name, u.password, u.password_tmp,
    u.phone_number, u.user_name, u.website, u.district_id, u.nation_id, u.province_id, u.business_area_id,
    u.business_size_id, u.parent_id, u.user_code, u.business_email, u.name, u.description, u.department_id,
    u.sme_user_id, u.tech_id, u.create_type, u.customer_code, u.customer_type, u.province_code, u.ward_id,
    u.street_id, u.social_insurance_number, u.rep_fullname, u.rep_gender, u.rep_title, u.rep_birthday,
    u.rep_nation_id, u.rep_folk_id, u.rep_personal_cert_type_id, u.rep_personal_cert_number, u.rep_personal_cert_date,
    u.rep_personal_cert_place, u.rep_registered_place, u.rep_address, u.parent_status, u.employee_code,
    n.name AS nation_name, p.name AS province_name, d.name AS district_name, w.name AS ward_name, s.name AS street_name
   FROM vnpt_dev.users u
     JOIN vnpt_dev.view_role_sme vsme ON vsme.user_id = u.id
     LEFT JOIN vnpt_dev.province p ON p.id = u.province_id
     LEFT JOIN vnpt_dev.district d ON d.id = u.district_id AND d.province_code::text = p.code::text
     LEFT JOIN vnpt_dev.ward w ON w.id = u.ward_id AND w.province_code::text = p.code::text
     LEFT JOIN vnpt_dev.street s ON s.id = u.street_id AND s.ward_id = u.ward_id AND s.district_id = u.district_id AND s.province_code::text = p.code::text
     LEFT JOIN vnpt_dev.nation n ON n.id = u.nation_id;

-- view_report_sub_sme
CREATE OR REPLACE VIEW "vnpt_dev"."view_report_sub_sme" AS
SELECT
    users.id,
    CASE
        WHEN users.customer_type IS NULL OR users.customer_type::text = 'KHDN'::text THEN 'Doanh nghiệp'::text
        WHEN users.customer_type::text = 'HKD'::text THEN 'Hộ kinh doanh'::text
        ELSE 'Cá nhân'::text
    END AS customer_type,
    users.customer_type AS customer_type_raw,
    users.province_id,
    CASE
        WHEN users.customer_type IS NULL OR (users.customer_type::text = ANY (ARRAY['KHDN'::character varying, 'HKD'::character varying]::text[])) THEN users.name::text
        ELSE concat_ws(' '::text, users.last_name, users.first_name)
    END AS name,
    CASE
        WHEN users.customer_type::text = 'CN'::text THEN users.rep_personal_cert_number
        ELSE ''::character varying
    END AS identity_no,
    users.email,
    users.phone_number,
    users.tin,
    users.address,
    street.name AS street,
    ward.name AS ward,
    district.name AS district,
    province.name AS province,
    nation.name AS nation
FROM vnpt_dev.users
    LEFT JOIN vnpt_dev.street ON users.street_id = street.id AND street.ward_id = users.ward_id AND street.district_id = users.district_id AND users.province_code::text = street.province_code::text
    LEFT JOIN vnpt_dev.ward ON users.ward_id = ward.id AND ward.district_id = users.district_id AND ward.province_code::text = users.province_code::text
    LEFT JOIN vnpt_dev.district ON users.district_id = district.id AND district.province_id = users.province_id AND district.province_code::text = users.province_code::text
    LEFT JOIN vnpt_dev.province ON users.province_id = province.id
    LEFT JOIN vnpt_dev.nation ON users.nation_id = nation.id
WHERE users.deleted_flag = 1;

-- get_report_subscriptions
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int4, "setupCode" varchar) AS $BODY$
DECLARE last_query text;
tmp_last_query text = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode
FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        a.pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate''
                            WHEN a.created_source = 4 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
                WHEN bill.status = 0 and a.serviceOwnerType = ''ON'' THEN ''Khởi tạo''
                WHEN bill.status = 1 and a.serviceOwnerType = ''ON'' THEN ''Chờ thanh toán''
                WHEN bill.status = 2 and a.serviceOwnerType = ''ON'' THEN ''Đã thanh toán''
                WHEN bill.status = 3 and a.serviceOwnerType = ''ON'' THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN osr.payment_status = ''1'' and a.serviceOwnerType = ''OS'' THEN ''Đã thanh toán''
                WHEN osr.payment_status = ''0'' and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                WHEN osr.payment_status is null and osr.id is not null and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                ELSE ''Chờ thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            CASE
                WHEN bill.isRenew = 0 then 1
                ELSE 2
            END as subscriptionState,
            CASE
                WHEN bill.isRenew = 0 then ''Đăng ký mới''
                ELSE ''Gia hạn''
            END as state,
            CASE
                WHEN bill.isRenew = 0 then a.registration_date
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.isRenew = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy.unitAmount, xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b.preAmountTax, b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt.amountTax, bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b.afterAmountTax,
                b2.afterAmountTax,
                0
            ) AS afterAmountTax
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch b ON bill.id = b.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON bill.id = bt.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = bill.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            and bill.isRenew = 1
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
    AND(
            (%6$s = -1 AND t.created_source_migration < 5) OR
            (t.created_source_migration < 5 AND (
            -- Do RK lưu created_source khác với code BE
            (%6$s = 0 AND t.created_source_migration = 1) OR
            (%6$s = 1 AND t.created_source_migration = 2) OR
            (%6$s = 2 AND t.created_source_migration = 3) OR
            (%6$s = 3 AND t.created_source_migration = 4)
            ))
        );';
tmp_all_sub_query VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )';
all_sub_query VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
table_bill_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                0 AS isRenew
        FROM (
                        SELECT max(b.id) as id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by <> ''batch''
                                or b.created_by is null
                        GROUP BY b.subscriptions_id
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
        UNION
        -- lấy thông tin hóa đơn gia hạn
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                1 AS isRenew
        FROM (
                        SELECT b.id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by = ''batch''
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_batch_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
        SELECT b.id,
                    CASE
                            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_pre_tax)
                    END AS preAmountTax,
                    CASE
                            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_after_tax)
                    END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and (
                        b.created_by <> ''batch''
                        or b.created_by is null
                )
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_after_tax)
                END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch_t///////////////
table_bill_vnptpay_batch_t_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
        SELECT b.id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
    and (
        b.created_by <> ''batch''
        or b.created_by is null
    )
GROUP BY b.id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
bill_vnptpay_batch_t2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
table_bill_promotion_xy_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
        SELECT x.id AS id,
                CASE
                        WHEN x.promotionAmount < 0 THEN 0
                        ELSE x.promotionAmount
                END AS promotionAmount,
                CASE
                        WHEN y.unitAmount < 0 THEN 0
                        ELSE y.unitAmount
                END AS unitAmount
        FROM (
                        SELECT q.id AS id,
                                COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
                        FROM (
                                        SELECT bi.billing_id AS id,
                                                sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY bi.billing_id
                                ) q
                                JOIN (
                                        SELECT bi.billing_id AS id,
                                                sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY bi.billing_id
                                ) w ON q.id = w.id
                ) x
                JOIN (
                        SELECT bi.billing_id AS id,
                                sum(COALESCE (bi.amount, 0)) AS unitAmount
                        FROM vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                        WHERE bi.object_type <> 3
                                and (
                                        b.created_by <> ''batch''
                                        or b.created_by is null
                                )
                        GROUP BY bi.billing_id
                ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
table_bill_promotion_xy2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN all_sub_query = FORMAT(
    tmp_all_sub_query,
    i_employee_code,
    i_province_id,
    i_status,
    i_category_service,
    i_service_id,
    i_pricing_id,
    i_category_combo,
    i_combo_ids,
    i_combo_plan_ids,
    i_customer_type,
    i_createdsource,
    i_pricing_ids
);
--raise 'all_sub_query: %',all_sub_query;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
EXECUTE table_bill_promotion_xy_query;
EXECUTE table_bill_promotion_xy2_query;
EXECUTE table_bill_promotion_xy2_query;
last_query = format(
    tmp_last_query,
    i_subscription_type,
    i_subscription_state,
    i_start_date,
    i_end_date,
    i_creator,
    i_createdsource,
    i_migrate_start_date,
    i_migrate_end_date,
    i_migrate_codes
);
raise notice 'Last query: %',
last_query;
return query execute last_query;
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

-- get_report_subscriptions_o2o
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_o2o"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int4, "setupCode" varchar) AS $BODY$
DECLARE last_query text;
tmp_last_query text = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode
FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        a.pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate''
                            WHEN a.created_source = 4 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
                WHEN bill.status = 0 THEN ''Khởi tạo''
                WHEN bill.status = 1 THEN ''Chờ thanh toán''
                WHEN bill.status = 2 THEN ''Đã thanh toán''
                WHEN bill.status = 3 THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 THEN ''Quá hạn thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            CASE
                WHEN bill.isRenew = 0 then 1
                ELSE 2
            END as subscriptionState,
            CASE
                WHEN bill.isRenew = 0 then ''Đăng ký mới''
                ELSE ''Gia hạn''
            END as state,
            CASE
                WHEN bill.isRenew = 0 then a.registration_date
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.isRenew = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy.unitAmount, xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b.preAmountTax, b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt.amountTax, bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b.afterAmountTax,
                b2.afterAmountTax,
                0
            ) AS afterAmountTax
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch b ON bill.id = b.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON bill.id = bt.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = bill.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            and bill.isRenew = 1
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
        AND (
                t.migrateTime >= CAST(''%7$s'' AS timestamp)
        )
        AND (
                t.migrateTime <= CAST(''%8$s'' AS timestamp)
        )
        AND (
                ''%9$s'' = ''ALL''
        OR t.migrateCode = ''%9$s''
        );';
tmp_all_sub_query VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
        AND s.created_source_migration = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
        AND s.created_source_migration = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )';
all_sub_query VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
table_bill_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                0 AS isRenew
        FROM (
                        SELECT max(b.id) as id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by <> ''batch''
                                or b.created_by is null
                        GROUP BY b.subscriptions_id
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
        UNION
        -- lấy thông tin hóa đơn gia hạn
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                1 AS isRenew
        FROM (
                        SELECT b.id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by = ''batch''
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_batch_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
        SELECT b.id,
                    CASE
                            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_pre_tax)
                    END AS preAmountTax,
                    CASE
                            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_after_tax)
                    END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and (
                        b.created_by <> ''batch''
                        or b.created_by is null
                )
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_after_tax)
                END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch_t///////////////
table_bill_vnptpay_batch_t_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
        SELECT b.id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
    and (
        b.created_by <> ''batch''
        or b.created_by is null
    )
GROUP BY b.id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
bill_vnptpay_batch_t2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
table_bill_promotion_xy_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
        SELECT x.id AS id,
                CASE
                        WHEN x.promotionAmount < 0 THEN 0
                        ELSE x.promotionAmount
                END AS promotionAmount,
                CASE
                        WHEN y.unitAmount < 0 THEN 0
                        ELSE y.unitAmount
                END AS unitAmount
        FROM (
                        SELECT q.id AS id,
                                COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
                        FROM (
                                        SELECT b.id AS id,
                                                sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.id
                                ) q
                                JOIN (
                                        SELECT b.id AS id,
                                                sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.id
                                ) w ON q.id = w.id
                ) x
                JOIN (
                        SELECT b.id AS id,
                                sum(COALESCE (bi.amount, 0)) AS unitAmount
                        FROM vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                        WHERE bi.object_type <> 3
                                and (
                                        b.created_by <> ''batch''
                                        or b.created_by is null
                                )
                        GROUP BY b.id
                ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
table_bill_promotion_xy2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN all_sub_query = FORMAT(
    tmp_all_sub_query,
    i_employee_code,
    i_province_id,
    i_status,
    i_category_service,
    i_service_id,
    i_pricing_id,
    i_category_combo,
    i_combo_ids,
    i_combo_plan_ids,
    i_customer_type,
    i_createdsource,
    i_pricing_ids
);
--raise 'all_sub_query: %',all_sub_query;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
EXECUTE table_bill_promotion_xy_query;
EXECUTE table_bill_promotion_xy2_query;
EXECUTE table_bill_promotion_xy2_query;
last_query = format(
    tmp_last_query,
    i_subscription_type,
    i_subscription_state,
    i_start_date,
    i_end_date,
    i_creator,
    i_createdsource,
    i_migrate_start_date,
    i_migrate_end_date,
    i_migrate_codes
);
raise notice 'Last query: %',
last_query;
return query execute last_query;
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

-- get_report_subscriptions_redash
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_redash"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_user_ids" text, "i_unique_ids" text, "i_on_os" int4)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int2, "unique_id" int8, "user_id" int8) AS $BODY$
DECLARE last_query text;
tmp_last_query text = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.unique_id,
                t.user_id
FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        a.pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        a.createdSource,
                        a.created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
            CASE
                WHEN bill.status = 0 THEN ''Khởi tạo''
                WHEN bill.status = 1 THEN ''Chờ thanh toán''
                WHEN bill.status = 2 THEN ''Đã thanh toán''
                WHEN bill.status = 3 THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 THEN ''Quá hạn thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            CASE
                WHEN bill.isRenew = 0 then 1
                ELSE 2
            END as subscriptionState,
            CASE
                WHEN bill.isRenew = 0 then ''Đăng ký mới''
                ELSE ''Gia hạn''
            END as state,
            CASE
                WHEN bill.isRenew = 0 then a.registration_date
                ELSE CAST (bill.created_at AS TIMESTAMP)
            END as registrationDate,
            CASE
                WHEN bill.isRenew = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy.unitAmount, xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b.preAmountTax, b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt.amountTax, bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b.afterAmountTax,
                b2.afterAmountTax,
                0
            ) AS afterAmountTax,
                        a.unique_id,
                        a.user_id
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch b ON a.id = b.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON a.id = bt.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = a.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            and bill.isRenew = 1
                        LEFT JOIN vnpt_dev.report_view_service_combo_uniqueid rps on rps.id = a.id
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
    AND(
            (%6$s = -1) OR
            (%6$s = 0) OR
            (
                (%6$s > 0)
                AND(
                    t.created_source_migration = 0 OR
                    t.created_source_migration IS NULL OR
                    ((t.created_source_migration > 0) AND (
                            t.migrateTime >= CAST(''%7$s'' AS timestamp)
                    )
                    AND (
                            t.migrateTime <= CAST(''%8$s'' AS timestamp)
                    ))
                )
                AND (%9$s)
            )
        )AND(
        ''%10$s'' = ''0''
        OR t.unique_id = ANY(''{%10$s}''::int8[])
        );';
tmp_all_sub_query text = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT
        s.id AS id,
        s.user_id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''Chưa thiết lập''
        WHEN s.status = 0 THEN ''Đang chờ''
        WHEN s.status = 1 THEN ''Dùng thử''
        WHEN s.status = 2 THEN ''Hoạt động''
        WHEN s.status = 3 THEN ''Hủy''
        WHEN s.status = 4 THEN ''Kết thúc''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 THEN ''AM G.Thiệu''
            WHEN s.created_source_migration = 2 THEN ''Affiliate''
            WHEN s.created_source_migration = 3 THEN ''Dev/Admin''
            WHEN s.created_source_migration = 4 THEN ''ĐHSXKD''
            WHEN s.created_source_migration = 0 OR s.created_source_migration IS NULL THEN ''oneSME''
            ELSE ''''
        END as createdSource,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        concat(s2.id, ''0000'')::bigint AS unique_id
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
        LEFT JOIN vnpt_dev.report_view_service_combo_uniqueid rs on rs.id = s.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
    AND (
        %11$s = -1
        OR (s.created_source_migration = 1 and  %11$s = 1)
        OR (s.created_source_migration = 2 and  %11$s = 2)
        OR (s.created_source_migration = 3 and  %11$s = 3)
        OR (s.created_source_migration = 4 and  %11$s = 4)
                OR (%11$s = 0 and (s.created_source_migration = 0 OR s.created_source_migration IS NULL))
    )
        AND (
        ''%13$s'' = ''-1''
        OR s.user_id = ANY(''{%13$s}''::int8[])
        )
        AND (
        %14$s = 0
        OR (s2.service_owner IN (0, 1) AND %14$s = 1)
        OR (s2.service_owner IN (2, 3) AND %14$s = 2)
        OR (s2.service_owner IS NULL AND %14$s = 2)
        )

UNION ALL
SELECT
        s.id AS id,
        s.user_id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 THEN ''AM G.Thiệu''
            WHEN s.created_source_migration = 2 THEN ''Affiliate''
            WHEN s.created_source_migration = 3 THEN ''Dev/Admin''
            WHEN s.created_source_migration = 4 THEN ''ĐHSXKD''
            WHEN s.created_source_migration = 0 OR s.created_source_migration IS NULL THEN ''oneSME''
            ELSE ''''
        END as createdSource,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        concat(s2.id, ''0001'')::bigint AS unique_id
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
        LEFT JOIN vnpt_dev.report_view_service_combo_uniqueid rs on rs.id = s.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )
    AND (
        %11$s = -1
        OR (s.created_source_migration = 1 and  %11$s = 1)
        OR (s.created_source_migration = 2 and  %11$s = 2)
        OR (s.created_source_migration = 3 and  %11$s = 3)
        OR (s.created_source_migration = 4 and  %11$s = 4)
                OR (%11$s = 0 and (s.created_source_migration = 0 OR s.created_source_migration IS NULL))
    )
        AND (
        ''%13$s'' = ''-1''
        OR s.user_id = ANY(''{%13$s}''::int8[])
        )
        AND (
        %14$s = 0
        OR (s2.combo_owner IN (0, 1) AND %14$s = 1)
        OR (s2.combo_owner IN (2, 3) AND %14$s = 2)
        OR (s2.combo_owner IS NULL AND %14$s = 2)
        )
        ';
all_sub_query text;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
table_bill_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                0 AS isRenew
        FROM (
                        SELECT max(b.id) as id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by <> ''batch''
                                or b.created_by is null
                        GROUP BY b.subscriptions_id
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
        UNION
        -- lấy thông tin hóa đơn gia hạn
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                1 AS isRenew
        FROM (
                        SELECT b.id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by = ''batch''
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_batch_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
        SELECT b.subscriptions_id,
                    CASE
                            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_pre_tax)
                    END AS preAmountTax,
                    CASE
                            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_after_tax)
                    END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and (
                        b.created_by <> ''batch''
                        or b.created_by is null
                )
        GROUP BY b.subscriptions_id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_after_tax)
                END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch_t///////////////
table_bill_vnptpay_batch_t_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
        SELECT b.subscriptions_id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
    and (
        b.created_by <> ''batch''
        or b.created_by is null
    )
GROUP BY b.subscriptions_id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
bill_vnptpay_batch_t2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
table_bill_promotion_xy_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
        SELECT x.id AS id,
                CASE
                        WHEN x.promotionAmount < 0 THEN 0
                        ELSE x.promotionAmount
                END AS promotionAmount,
                CASE
                        WHEN y.unitAmount < 0 THEN 0
                        ELSE y.unitAmount
                END AS unitAmount
        FROM (
                        SELECT q.id AS id,
                                COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
                        FROM (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.subscriptions_id
                                ) q
                                JOIN (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.subscriptions_id
                                ) w ON q.id = w.id
                ) x
                JOIN (
                        SELECT b.subscriptions_id AS id,
                                sum(COALESCE (bi.amount, 0)) AS unitAmount
                        FROM vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                        WHERE bi.object_type <> 3
                                and (
                                        b.created_by <> ''batch''
                                        or b.created_by is null
                                )
                        GROUP BY b.subscriptions_id
                ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
table_bill_promotion_xy2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN all_sub_query = FORMAT(
    tmp_all_sub_query,
    i_employee_code,
    i_province_id,
    i_status,
    i_category_service,
    i_service_id,
    i_pricing_id,
    i_category_combo,
    i_combo_ids,
    i_combo_plan_ids,
    i_customer_type,
    i_createdsource,
    i_pricing_ids,
        i_user_ids,
        i_on_os);
--raise 'all_sub_query: %',all_sub_query;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
EXECUTE table_bill_promotion_xy_query;
EXECUTE table_bill_promotion_xy2_query;
EXECUTE table_bill_promotion_xy2_query;

last_query = format(
    tmp_last_query,
    i_subscription_type,
    i_subscription_state,
    i_start_date,
    i_end_date,
    i_creator,
    i_createdsource,
    i_migrate_start_date,
    i_migrate_end_date,
    i_migrate_codes,
        i_unique_ids
);

--raise notice 'Last query: %', last_query;
return query execute last_query;
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


-- get_report_subscriptions_o2o_update
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_o2o_update"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int4, "setupCode" varchar, "paymentDate" timestamp, "createdExportInvoice" timestamp, "codeInvoice" text, "cancelledTime" timestamp, "isOneTime" int2) AS $BODY$
DECLARE last_query text;
tmp_last_query text = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode,
                t.paymentDate,
                t.createdExportInvoice,
                t.codeInvoice,
                CASE
                        WHEN t.status = ''CANCELED'' OR t.status = ''NON_RENEWING'' THEN t.cancelledTime
                END as  cancelledTime,
                t.isOneTime
FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        a.pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate''
                            WHEN a.created_source = 4 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
               WHEN bill.status = 0 and a.serviceOwnerType = ''ON'' THEN ''Khởi tạo''
                WHEN bill.status = 1 and a.serviceOwnerType = ''ON'' THEN ''Chờ thanh toán''
                WHEN bill.status = 2 and a.serviceOwnerType = ''ON'' THEN ''Đã thanh toán''
                WHEN bill.status = 3 and a.serviceOwnerType = ''ON'' THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN osr.payment_status = ''1'' and a.serviceOwnerType = ''OS'' THEN ''Đã thanh toán''
                WHEN osr.payment_status = ''0'' and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                WHEN osr.payment_status is null and osr.id is not null and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                ELSE ''Chờ thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            bill.payment_date as paymentDate,
            bill.createdExportInvoice,
            bill.code as codeInvoice,
            a.cancelledTime,
                        a.isOneTime,
            CASE
                WHEN bill.isRenew = 0 then 1
                ELSE 2
            END as subscriptionState,
            CASE
                WHEN bill.isRenew = 0 then ''Đăng ký mới''
                ELSE ''Gia hạn''
            END as state,
            CASE
                WHEN bill.isRenew = 0 then a.registration_date
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.isRenew = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy.unitAmount, xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b.preAmountTax, b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt.amountTax, bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b.afterAmountTax,
                b2.afterAmountTax,
                0
            ) AS afterAmountTax
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch b ON a.id = b.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON a.id = bt.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = a.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            and bill.isRenew = 1
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
        AND (
        (''%10$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') >= ''%10$s'')

    )
        AND (
        (''%11$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') <= ''%11$s'')

    )
        AND (
                (''%7$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%7$s'')
        )
        AND (
                (''%8$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%8$s'')
        )

        AND (
                ''%9$s'' = ''ALL''
        OR t.migrateCode = ''%9$s''
        );';
tmp_all_sub_query VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
        p2.pricing_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
        s.number_of_cycles as numberOfCycle,
        p2.payment_cycle as planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
        AND s.created_source_migration = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
        p2.combo_plan_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
        AND s.created_source_migration = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )';
all_sub_query VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
table_bill_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                0 AS isRenew
        FROM (
                        SELECT max(b.id) as id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by <> ''batch''
                                or b.created_by is null
                        GROUP BY b.subscriptions_id
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
        UNION
        -- lấy thông tin hóa đơn gia hạn
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                1 AS isRenew
        FROM (
                        SELECT b.id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by = ''batch''
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_batch_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
        SELECT b.subscriptions_id,
                    CASE
                            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_pre_tax)
                    END AS preAmountTax,
                    CASE
                            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_after_tax)
                    END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and (
                        b.created_by <> ''batch''
                        or b.created_by is null
                )
        GROUP BY b.subscriptions_id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_after_tax)
                END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch_t///////////////
table_bill_vnptpay_batch_t_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
        SELECT b.subscriptions_id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
    and (
        b.created_by <> ''batch''
        or b.created_by is null
    )
GROUP BY b.subscriptions_id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
bill_vnptpay_batch_t2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
table_bill_promotion_xy_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
        SELECT x.id AS id,
                CASE
                        WHEN x.promotionAmount < 0 THEN 0
                        ELSE x.promotionAmount
                END AS promotionAmount,
                CASE
                        WHEN y.unitAmount < 0 THEN 0
                        ELSE y.unitAmount
                END AS unitAmount
        FROM (
                        SELECT q.id AS id,
                                COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
                        FROM (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.subscriptions_id
                                ) q
                                JOIN (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.subscriptions_id
                                ) w ON q.id = w.id
                ) x
                JOIN (
                        SELECT b.subscriptions_id AS id,
                                sum(COALESCE (bi.amount, 0)) AS unitAmount
                        FROM vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                        WHERE bi.object_type <> 3
                                and (
                                        b.created_by <> ''batch''
                                        or b.created_by is null
                                )
                        GROUP BY b.subscriptions_id
                ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
table_bill_promotion_xy2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN all_sub_query = FORMAT(
    tmp_all_sub_query,
    i_employee_code,
    i_province_id,
    i_status,
    i_category_service,
    i_service_id,
    i_pricing_id,
    i_category_combo,
    i_combo_ids,
    i_combo_plan_ids,
    i_customer_type,
    i_createdsource,
    i_pricing_ids
);
--raise 'all_sub_query: %',all_sub_query;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
EXECUTE table_bill_promotion_xy_query;
EXECUTE table_bill_promotion_xy2_query;
EXECUTE table_bill_promotion_xy2_query;
last_query = format(
    tmp_last_query,
    i_subscription_type,
    i_subscription_state,
    i_start_date,
    i_end_date,
    i_creator,
    i_createdsource,
    i_migrate_start_date,
    i_migrate_end_date,
    i_migrate_codes,
        i_cancelled_time_start,
        i_cancelled_time_end
);
raise notice 'Last query: %',
last_query;
return query execute last_query;
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


-- get_report_subscriptions_o2o_update
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_o2o_update"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_provider_ids" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "provider" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int4, "setupCode" varchar, "paymentDate" timestamp, "createdExportInvoice" timestamp, "codeInvoice" text, "cancelledTime" timestamp, "isOneTime" int2) AS $BODY$
DECLARE
    last_query                       text;
    tmp_last_query                   text    = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.provider,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode,
                t.paymentDate,
                t.createdExportInvoice,
                t.codeInvoice,
                CASE
                        WHEN t.status = ''CANCELED'' OR t.status = ''NON_RENEWING'' THEN t.cancelledTime
                END as  cancelledTime,
                t.isOneTime
FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        a.provider,
                        a.pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate''
                            WHEN a.created_source = 4 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
               WHEN bill.status = 0 and a.serviceOwnerType = ''ON'' THEN ''Khởi tạo''
                WHEN bill.status = 1 and a.serviceOwnerType = ''ON'' THEN ''Chờ thanh toán''
                WHEN bill.status = 2 and a.serviceOwnerType = ''ON'' THEN ''Đã thanh toán''
                WHEN bill.status = 3 and a.serviceOwnerType = ''ON'' THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN osr.payment_status = ''1'' and a.serviceOwnerType = ''OS'' THEN ''Đã thanh toán''
                WHEN osr.payment_status = ''0'' and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                WHEN osr.payment_status is null and osr.id is not null and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                ELSE ''Chờ thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            bill.payment_date as paymentDate,
            bill.createdExportInvoice,
            bill.code as codeInvoice,
            a.cancelledTime,
                        a.isOneTime,
            CASE
                WHEN bill.isRenew = 0 then 1
                ELSE 2
            END as subscriptionState,
            CASE
                WHEN bill.isRenew = 0 then ''Đăng ký mới''
                ELSE ''Gia hạn''
            END as state,
            CASE
                WHEN bill.isRenew = 0 then a.registration_date
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.isRenew = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy.unitAmount, xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b.preAmountTax, b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt.amountTax, bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b.afterAmountTax,
                b2.afterAmountTax,
                0
            ) AS afterAmountTax
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch b ON a.id = b.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON a.id = bt.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = a.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            and bill.isRenew = 1
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
        AND (
        (''%10$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') >= ''%10$s'')

    )
        AND (
        (''%11$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') <= ''%11$s'')

    )
        AND (
                (''%7$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%7$s'')
        )
        AND (
                (''%8$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%8$s'')
        )

        AND (
                ''%9$s'' = ''ALL''
        OR t.migrateCode = ''%9$s''
        );';
    tmp_all_sub_query                VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    coalesce(provider.name, concat_ws('' '', provider.last_name, provider.first_name)) AS provider,
    p2.pricing_name AS pricingName,
        p2.pricing_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
        s.number_of_cycles as numberOfCycle,
        p2.payment_cycle as planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.users AS provider ON provider.id = s2.user_id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
        AND s.created_source_migration = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
    AND (''-1'' = ''%13$s'' OR provider.id = ANY(''{%13$s}''::int8[]))
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    coalesce(provider.name, concat_ws('' '', provider.last_name, provider.first_name)) AS provider,
    p2.combo_name AS pricingName,
        p2.combo_plan_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.users AS provider ON provider.id = s2.user_id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
        AND s.created_source_migration = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )
    AND (''-1'' = ''%13$s'' OR provider.id = ANY(''{%13$s}''::int8[]));';
    all_sub_query                    VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
    table_bill_query                 VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                0 AS isRenew
        FROM (
                        SELECT max(b.id) as id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by <> ''batch''
                                or b.created_by is null
                        GROUP BY b.subscriptions_id
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
        UNION
        -- lấy thông tin hóa đơn gia hạn
        SELECT b2.id,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                1 AS isRenew
        FROM (
                        SELECT b.id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        WHERE b.created_by = ''batch''
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
    table_bill_vnptpay_query         VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
    table_bill_vnptpay_batch_query   VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
        SELECT b.subscriptions_id,
                    CASE
                            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_pre_tax)
                    END AS preAmountTax,
                    CASE
                            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_after_tax)
                    END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and (
                        b.created_by <> ''batch''
                        or b.created_by is null
                )
        GROUP BY b.subscriptions_id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
    table_bill_vnptpay_batch2_query  VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_after_tax)
                END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch_t///////////////
    table_bill_vnptpay_batch_t_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
        SELECT b.subscriptions_id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
    and (
        b.created_by <> ''batch''
        or b.created_by is null
    )
GROUP BY b.subscriptions_id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
    bill_vnptpay_batch_t2_query      VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
        WHERE b.status = 2
                and b.created_by = ''batch''
        GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
    table_bill_promotion_xy_query    VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
        SELECT x.id AS id,
                CASE
                        WHEN x.promotionAmount < 0 THEN 0
                        ELSE x.promotionAmount
                END AS promotionAmount,
                CASE
                        WHEN y.unitAmount < 0 THEN 0
                        ELSE y.unitAmount
                END AS unitAmount
        FROM (
                        SELECT q.id AS id,
                                COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
                        FROM (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.subscriptions_id
                                ) q
                                JOIN (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
                                                and (
                                                        b.created_by <> ''batch''
                                                        or b.created_by is null
                                                )
                                        GROUP BY b.subscriptions_id
                                ) w ON q.id = w.id
                ) x
                JOIN (
                        SELECT b.subscriptions_id AS id,
                                sum(COALESCE (bi.amount, 0)) AS unitAmount
                        FROM vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                        WHERE bi.object_type <> 3
                                and (
                                        b.created_by <> ''batch''
                                        or b.created_by is null
                                )
                        GROUP BY b.subscriptions_id
                ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
    table_bill_promotion_xy2_query   VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN
    all_sub_query = FORMAT(
            tmp_all_sub_query,
            i_employee_code,
            i_province_id,
            i_status,
            i_category_service,
            i_service_id,
            i_pricing_id,
            i_category_combo,
            i_combo_ids,
            i_combo_plan_ids,
            i_customer_type,
            i_createdsource,
            i_pricing_ids,
            i_provider_ids
        );
--raise 'all_sub_query: %',all_sub_query;
    execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
    execute table_bill_query;
    EXECUTE table_bill_vnptpay_query;
    EXECUTE table_bill_vnptpay_batch_query;
    EXECUTE table_bill_vnptpay_batch2_query;
    EXECUTE table_bill_vnptpay_batch_t_query;
    EXECUTE bill_vnptpay_batch_t2_query;
    EXECUTE table_bill_promotion_xy_query;
    EXECUTE table_bill_promotion_xy2_query;
    EXECUTE table_bill_promotion_xy2_query;
    last_query = format(
            tmp_last_query,
            i_subscription_type,
            i_subscription_state,
            i_start_date,
            i_end_date,
            i_creator,
            i_createdsource,
            i_migrate_start_date,
            i_migrate_end_date,
            i_migrate_codes,
            i_cancelled_time_start,
            i_cancelled_time_end
        );
    raise notice 'Last query: %',
        last_query;
    return query execute last_query;
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


-- get_report_subscriptions_update
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_update"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int4, "setupCode" varchar, "paymentDate" timestamp, "createdExportInvoice" timestamp, "codeInvoice" text, "cancelledTime" timestamp, "isOneTime" int2) AS $BODY$
DECLARE last_query text;
tmp_last_query text = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode,
                t.paymentDate,
                t.createdExportInvoice,
                t.codeInvoice,
                CASE
                        WHEN t.status = ''CANCELED'' OR t.status = ''NON_RENEWING'' THEN t.cancelledTime
                END as  cancelledTime,
                t.isOneTime
FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        bill.pricing_name as pricingName,
                        a.registration_date,
                        --a.start_at,
                        CASE
                            WHEN bill.bill_action_type = 2 THEN a.numberOfCycleChange
                            ELSE a.numberOfCycle
                        END as numberOfCycle,
                        CASE
                            WHEN bill.bill_action_type = 2 THEN a.planPaymentCycleChange
                            ELSE a.planPaymentCycle
                        END as planPaymentCycle,
                        CASE
                            WHEN bill.bill_action_type = 2 THEN a.planCycleTypeChange
                            ELSE a.planCycleType
                        END as planCycleType,
                        CASE
                            WHEN bill.bill_action_type = 2 THEN a.multiPaymentCycleChange
                            ELSE a.multiPaymentCycle
                        END as multiPaymentCycle,
                        CASE
                            WHEN bill.bill_action_type = 2 THEN a.multiCycleTypeChange
                            ELSE a.multiCycleType
                        END as multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 and bill.portal_type is null THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate''
                            WHEN a.created_source = 4 and bill.portal_type is null THEN ''Dev/Admin''
                            WHEN bill.portal_type is not null and bill.portal_type = 1 THEN ''Dev/Admin''
                            WHEN bill.portal_type is not null and bill.portal_type = 2 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
                WHEN bill.status = 0 and a.serviceOwnerType = ''ON'' THEN ''Khởi tạo''
                WHEN bill.status = 1 and a.serviceOwnerType = ''ON'' THEN ''Chờ thanh toán''
                WHEN bill.status = 2 and a.serviceOwnerType = ''ON'' THEN ''Đã thanh toán''
                WHEN bill.status = 3 and a.serviceOwnerType = ''ON'' THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN osr.payment_status = ''1'' and a.serviceOwnerType = ''OS'' THEN ''Đã thanh toán''
                WHEN osr.payment_status = ''0'' and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                WHEN osr.payment_status is null and osr.id is not null and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                ELSE ''Chờ thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            CASE
                WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                            AND bill.created_at::date = bill.billing_date::date
                            AND a.pricingType = 0
                THEN bill.created_at
                ELSE bill.payment_date
            END paymentDate,
            bill.createdExportInvoice,
            bill.code as codeInvoice,
            a.cancelledTime,
            a.isOneTime,
-- subscriptionState cũ 1: TẠO MỚI, 2: GIA HẠN, -1: ALL =>subscriptionState mới:   0: Tạo mới, 2: đổi gói, 5: Gia hạn, -1: All
            bill.bill_action_type as subscriptionState,
            CASE
                WHEN bill.bill_action_type = 2 then ''Đổi gói''
                WHEN bill.bill_action_type = 5 then ''Gia hạn''
                ELSE ''Đăng ký mới''
            END as state,
            CASE
                WHEN bill.bill_action_type = 0 then a.registration_date
                ELSE COALESCE(
                    a.created_at_change,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.bill_action_type = 0 then a.start_at
                ELSE COALESCE(
                    bill.billing_date + ''00:00:01''::time,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN a.subscriptionType = ''SERVICE'' AND a.planPaymentCycle IS NULL AND bill.bill_action_type = 2 THEN a.multiPaymentCycleChange
                WHEN a.subscriptionType = ''SERVICE'' AND a.planPaymentCycle IS NULL AND bill.bill_action_type <> 2 THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN a.subscriptionType = ''SERVICE'' AND a.planCycleType IS NULL AND bill.bill_action_type <> 2 THEN a.multiCycleType
                WHEN a.subscriptionType = ''SERVICE'' AND a.planCycleType IS NULL AND bill.bill_action_type = 2 THEN a.multiCycleTypeChange
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL AND bill.portal_type is null THEN a.registedBy
                WHEN a.traffic_id IS NULL AND bill.portal_type is not null and bill.portal_type = 1 THEN concat(''Admin - '', bill_new.email)
                WHEN a.traffic_id IS NULL AND bill.portal_type is not null and bill.portal_type = 2 THEN concat(''Dev - '', bill_new.email)
                WHEN a.traffic_id IS NULL AND bill.portal_type is not null and bill.portal_type = 3 THEN ''OneSME''
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
--                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b2.afterAmountTax,
--              b.afterAmountTax,
                0
            ) AS afterAmountTax
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
--            LEFT JOIN tmp_table_bill_vnptpay_batch b ON a.id = b.subscriptions_id
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
--            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON a.id = bt.subscriptions_id
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
--            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = a.id
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
                        LEFT JOIN (select tbl.id, u.email from vnpt_dev.users u
                                    join (select b.id, b.created_by from vnpt_dev.billings b where (b.action_type = 2 or b.action_type = 5)
                                    and b.created_by <> ''batch''
                                    ) tbl ON CAST(tbl.created_by AS int8) = u.id) bill_new ON bill_new.id = bill.id
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
        AND (
        (''%10$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') >= ''%10$s'')

    )
        AND (
        (''%11$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') <= ''%11$s'')

    )
        AND (
                (''%7$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%7$s'')
        )
        AND (
                (''%8$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%8$s'')
        )
    AND(
            (%6$s = -1 AND t.created_source_migration < 5) OR
            (t.created_source_migration < 5 AND (
            -- Do RK lưu created_source khác với code BE
            (%6$s = 0 AND t.created_source_migration = 1) OR
            (%6$s = 1 AND t.created_source_migration = 2) OR
            (%6$s = 2 AND t.created_source_migration = 3) OR
            (%6$s = 3 AND t.created_source_migration = 4)
            ))
        );';
tmp_all_sub_query VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    cs.created_at AS created_at_change,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
        p2.pricing_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
        s.number_of_cycles as numberOfCycle,
        cs.number_of_cycles::int2 AS numberOfCycleChange,
        p2.payment_cycle as planPaymentCycle,
        p3.payment_cycle AS planPaymentCycleChange,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
        CASE
        WHEN p3.cycle_type = 0 THEN ''DAILY''
        WHEN p3.cycle_type = 1 THEN ''WEEKLY''
        WHEN p3.cycle_type = 2 THEN ''MONTHLY''
        WHEN p3.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleTypeChange,
    pmp.payment_cycle AS multiPaymentCycle,
        pmp1.payment_cycle AS multiPaymentCycleChange,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
        CASE
        WHEN pmp1.circle_type = 0 THEN ''DAILY''
        WHEN pmp1.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp1.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp1.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleTypeChange,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime
FROM vnpt_dev.subscriptions s
        LEFT JOIN vnpt_dev.change_subscription cs ON cs.subscription_id = s.id
            AND cs.id = (select MAX(id) from vnpt_dev.change_subscription where subscription_id = s.id)
        LEFT JOIN vnpt_dev.pricing p3 ON cs.pricing_id = p3.id
        LEFT JOIN vnpt_dev.pricing_multi_plan pmp1 ON cs.pricing_multi_plan_id = pmp1.id
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
        cs.created_at AS created_at_change,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
        p2.combo_plan_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles as numberOfCycle,
        cs.number_of_cycles::int2 AS numberOfCycleChange,
        p2.payment_cycle as planPaymentCycle,
        pmp1.payment_cycle AS planPaymentCycleChange,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
        CASE
        WHEN pmp1.cycle_type = 0 THEN ''DAILY''
        WHEN pmp1.cycle_type = 1 THEN ''WEEKLY''
        WHEN pmp1.cycle_type = 2 THEN ''MONTHLY''
        WHEN pmp1.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleTypeChange,
    NULL AS multiPaymentCycle,
        NULL AS multiPaymentCycleChange,
    NULL AS multiCycleType,
        NULL AS multiCycleTypeChange,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime
FROM vnpt_dev.subscriptions s
        LEFT JOIN vnpt_dev.change_subscription cs ON cs.subscription_id = s.id
            AND cs.id = (select MAX(id) from vnpt_dev.change_subscription where subscription_id = s.id)
        LEFT JOIN vnpt_dev.combo_plan pmp1 ON cs.combo_plan_id = pmp1.id
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )';
all_sub_query VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
table_bill_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
                p.pricing_name,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                b2.portal_type as portal_type,
                b2.created_by as created_by,
                CASE
-- bill_action_type: 0: tạo mới, 1: sửa, 2: đổi, 3, 4: kích hoạt lại,  5: gia hạn,
                    WHEN b2.action_type = -1 or (b2.action_type is null and b2.created_by <> ''batch'') THEN 0 -- thuê bao tạo mới
                    WHEN b2.action_type = 1 THEN 0  -- thuê bao sửa
                    WHEN b2.action_type = 2 THEN 2  -- thuê bao đổi gói
                    WHEN b2.action_type = 3 or b2.action_type = 4 THEN 0    -- thuê bao kích hoạt lại
                    WHEN b2.action_type = 5 or (b2.action_type is null and b2.created_by = ''batch'') THEN 5    -- thuê bao gia hạn
                    ELSE b2.action_type
                END AS bill_action_type
        FROM (
                        SELECT b.id as id,
                                b.subscriptions_id AS sub_id
                        FROM vnpt_dev.billings b
                        where b.status in (0,1,2,3,4)
                ) mb
                JOIN vnpt_dev.billings b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
                left join vnpt_dev.pricing p on b2.pricing_id = p.id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_batch_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
        SELECT b.subscriptions_id,
                    CASE
                            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_pre_tax)
                    END AS preAmountTax,
                    CASE
                            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
                            ELSE sum(bi2.amount_after_tax)
                    END AS afterAmountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
--              and (
--                      b.created_by <> ''batch''
--                      or b.created_by is null
--              )
        GROUP BY b.subscriptions_id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        select tbl.id,
    CASE
        WHEN tbl.preAmountTax < 0 THEN 0
        WHEN tbl.discount > 0 THEN (tbl.preAmountTax - tbl.discount)
        ELSE tbl.preAmountTax
    END AS preAmountTax,
    CASE
        WHEN tbl.afterAmountTax < 0 THEN 0
        WHEN tbl.discount > 0 THEN (tbl.afterAmountTax - tbl.discount)
        ELSE tbl.afterAmountTax
    END AS afterAmountTax
    from (
        SELECT b.id,
        CASE
            WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
            ELSE sum(bi2.amount_pre_tax)
        END AS preAmountTax,
        CASE
            WHEN sum(bi2.amount_after_tax) < 0 THEN 0
            ELSE sum(bi2.amount_after_tax)
        END AS afterAmountTax,
        (b.total_amount-b.total_amount_after_adjustment) as discount
        FROM vnpt_dev.billings b
        LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE b.status = 2
        GROUP BY b.id ) tbl';
-- tmp_table_bill_vnptpay_batch_t///////////////
table_bill_vnptpay_batch_t_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
        SELECT b.subscriptions_id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
--    and (
--        b.created_by <> ''batch''
--        or b.created_by is null
--    )
GROUP BY b.subscriptions_id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
bill_vnptpay_batch_t2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM vnpt_dev.billings b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
        WHERE b.status = 2
--              and b.created_by = ''batch''
        GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
table_bill_promotion_xy_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
        SELECT x.id AS id,
                CASE
                        WHEN x.promotionAmount < 0 THEN 0
                        ELSE x.promotionAmount
                END AS promotionAmount,
                CASE
                        WHEN y.unitAmount < 0 THEN 0
                        ELSE y.unitAmount
                END AS unitAmount
        FROM (
                        SELECT q.id AS id,
                                COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
                        FROM (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
--                                              and (
--                                                      b.created_by <> ''batch''
--                                                      or b.created_by is null
--                                              )
                                        GROUP BY b.subscriptions_id
                                ) q
                                JOIN (
                                        SELECT b.subscriptions_id AS id,
                                                sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                                        FROM vnpt_dev.billings b
                                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                                        WHERE bi.object_type <> 3
--                                              and (
--                                                      b.created_by <> ''batch''
--                                                      or b.created_by is null
--                                              )
                                        GROUP BY b.subscriptions_id
                                ) w ON q.id = w.id
                ) x
                JOIN (
                        SELECT b.subscriptions_id AS id,
                                sum(COALESCE (bi.amount, 0)) AS unitAmount
                        FROM vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                        WHERE bi.object_type <> 3
--                              and (
--                                      b.created_by <> ''batch''
--                                      or b.created_by is null
--                              )
                        GROUP BY b.subscriptions_id
                ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
table_bill_promotion_xy2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
--                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
--                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
--            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN all_sub_query = FORMAT(
    tmp_all_sub_query,
    i_employee_code,
    i_province_id,
    i_status,
    i_category_service,
    i_service_id,
    i_pricing_id,
    i_category_combo,
    i_combo_ids,
    i_combo_plan_ids,
    i_customer_type,
    i_createdsource,
    i_pricing_ids
);
--raise 'all_sub_query: %',all_sub_query;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
--EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
--EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
--EXECUTE table_bill_promotion_xy_query;
EXECUTE table_bill_promotion_xy2_query;
last_query = format(
    tmp_last_query,
    i_subscription_type,
    i_subscription_state,
    i_start_date,
    i_end_date,
    i_creator,
    i_createdsource,
    i_migrate_start_date,
    i_migrate_end_date,
    i_migrate_codes,
        i_cancelled_time_start,
        i_cancelled_time_end
);
raise notice 'Last query: %',
last_query;
return query execute last_query;
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


-- func_get_address_text
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_address_text"("i_provinceid" int8, "i_districtid" int8, "i_wardid" int8, "i_streetid" int8)
  RETURNS "pg_catalog"."text" AS $BODY$
BEGIN
    RETURN (
        SELECT CONCAT_WS(', ', street.name, ward.name, district.name, province.name)
        FROM
            vnpt_dev.province
            LEFT JOIN vnpt_dev.district ON district.id = i_districtid AND district.province_id = province.id
            LEFT JOIN vnpt_dev.ward ON ward.id = i_wardid AND ward.district_id = district.id AND ward.province_code = province.code
            LEFT JOIN vnpt_dev.street ON street.id = i_streetid AND street.ward_id = ward.id AND street.district_id = district.id AND street.province_code = province.code
        WHERE province.id = i_provinceid
        LIMIT 1
    );
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;




-- get_report_subscriptions_actual_revenue_by_sub_and_billings
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_actual_revenue_by_sub_and_billings"("i_sub_ids" varchar, "i_bill_ids" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "createdat" timestamp, "provincename" varchar, "subsstatus" text, "smename" text, "customertype" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifieat" timestamp, "serviceownertype" text, "registedby" text, "trafficid" varchar, "trafficuser" varchar, "employeecode" varchar, "dhsxkdsubcode" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "createdsourcemigration" int2, "setupcode" varchar, "paymentdate" timestamp, "createdexportinvoice" timestamp, "codeinvoice" text, "cancelledtime" timestamp, "isonetime" int2, "affiliatecode" varchar, "endcurrentcycle" date, "identityno" varchar) AS $BODY$

DECLARE
    run_query text;
    raw_query varchar = 'WITH tmp_sme AS (
    SELECT
            users.id AS id,
            CASE
                WHEN users.customer_type IS NULL OR users.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
                WHEN users.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
                ELSE ''Cá nhân''
            END AS customer_type,
            CASE
                WHEN users.customer_type IS NULL OR users.customer_type IN (''KHDN'', ''HKD'') THEN users.name
                ELSE CONCAT_WS('' '', users.last_name, users.first_name)
            END AS name,
            CASE
                WHEN users.customer_type = ''CN'' THEN users.rep_personal_cert_number
                ELSE ''''
            END AS identity_no,
            users.email AS email,
            users.phone_number AS phone_number,
            users.tin AS tin,
            users.address AS address,
            street.name AS street,
            ward.name AS ward,
            district.name AS district,
            province.name AS province,
            nation.name AS nation
        FROM vnpt_dev.users
            LEFT JOIN vnpt_dev.street ON users.street_id = street.id AND street.ward_id = users.ward_id AND street.district_id = users.district_id AND users.province_code = street.province_code
            LEFT JOIN vnpt_dev.ward ON users.ward_id = ward.id AND ward.district_id = users.district_id AND ward.province_code = users.province_code
            LEFT JOIN vnpt_dev.district ON users.district_id = district.id AND district.province_id = users.province_id AND district.province_code = users.province_code
            LEFT JOIN vnpt_dev.province ON users.province_id = province.id
            LEFT JOIN vnpt_dev.nation ON users.nation_id = nation.id
        WHERE
            users.deleted_flag = 1
),
tmp_services AS (
    SELECT
            pricing.id AS id,
            ''SERVICE'' AS sub_type,
            pricing.pricing_name AS pricing_name,
            pricing.pricing_type AS pricing_type,
            CASE
                    WHEN pricing.cycle_type = 0 THEN ''DAILY''
                    WHEN pricing.cycle_type = 1 THEN ''WEEKLY''
                    WHEN pricing.cycle_type = 2 THEN ''MONTHLY''
                    WHEN pricing.cycle_type = 3 THEN ''YEARLY''
            END AS plan_cycle_type,
            pricing.payment_cycle AS plan_payment_cycle,
            services.service_name AS service_name,
            CASE
                WHEN services.service_owner IN (0, 1) THEN ''ON''
                WHEN services.service_owner IN (2, 3) THEN ''OS''
                WHEN services.service_owner IS NULL THEN ''OS''
            END AS service_owner_type
        FROM vnpt_dev.pricing
                LEFT JOIN vnpt_dev.services ON pricing.service_id = services.id
),
tmp_combos AS (
  SELECT
            cbPlan.id AS id,
            ''COMBO'' AS sub_type,
            cbPlan.combo_name AS pricing_name,
            cbPlan.combo_plan_type AS pricing_type,
            CASE
                    WHEN cbPlan.cycle_type = 0 THEN ''DAILY''
                    WHEN cbPlan.cycle_type = 1 THEN ''WEEKLY''
                    WHEN cbPlan.cycle_type = 2 THEN ''MONTHLY''
                    WHEN cbPlan.cycle_type = 3 THEN ''YEARLY''
            END AS plan_cycle_type,
            cbPlan.payment_cycle AS plan_payment_cycle,
            combo.combo_name AS service_name,
            CASE
                WHEN combo.combo_owner IN (0, 1) THEN ''ON''
                WHEN combo.combo_owner IN (2, 3) THEN ''OS''
                WHEN combo.combo_owner IS NULL THEN ''OS''
            END AS service_owner_type
        FROM vnpt_dev.combo_plan AS cbPlan
            LEFT JOIN vnpt_dev.combo ON cbPlan.combo_id = combo.id
),
tmp_bills AS (
    SELECT
                bill.id,
                bill.action_type,
                pricing.pricing_name,
                bill.status as status,
                bill.payment_date,
                bill.created_at,
                bill.billing_code,
                bill.subscriptions_id,
                bill.total_amount,
                bill.billing_date,
                bill.total_amount_after_adjustment,
                invoice.created_at      AS created_export_invoice,
                invoice.code,
                bill.portal_type      AS portal_type,
                bill.created_by       AS created_by,
                CASE
                -- bill_action_type: 0: tạo mới, 1: sửa, 2: đổi, 3, 4: kích hoạt lại,  5: gia hạn,
                    WHEN bill.action_type = -1 OR (bill.action_type is null and bill.created_by <> ''batch'') THEN 0 -- thuê bao tạo mới
                    WHEN bill.action_type = 1 THEN 2 -- thuê bao sửa
                    WHEN bill.action_type = 2 THEN 2 -- thuê bao đổi gói
                    WHEN bill.action_type = 3 OR bill.action_type = 4 THEN 0 -- thuê bao kích hoạt lại
                    WHEN bill.action_type = 5 OR (bill.action_type is null and bill.created_by = ''batch'') THEN 5 -- thuê bao gia hạn
                    ELSE bill.action_type
                END AS bill_action_type
         FROM vnpt_dev.billings bill
             LEFT JOIN (
                SELECT
                        billing_id,
                        string_agg(code, ''; ''::text) AS code,
                        max(created_at) AS created_at
                    FROM vnpt_dev.e_invoice
                    GROUP BY billing_id
         ) invoice ON bill.id = invoice.billing_id
        LEFT JOIN  vnpt_dev.pricing ON bill.pricing_id = pricing.id
        WHERE bill.status in (0, 1, 2, 3, 4)
),
tmp_bills_payment AS (
    SELECT
                bill.id,
                SUM(COALESCE(billItem.amount_pre_tax, 0)) AS pre_amount_tax,
                SUM(COALESCE(bill.total_amount_after_adjustment, 0)) AS after_amount_tax,
                SUM(COALESCE(biTax.amount, 0)) AS amount_tax,
                SUM(COALESCE(billItem.amount, 0)) AS unit_amount,
                (
                SUM(COALESCE(billCouponPrivate.amount_by_cash, 0)) +
                SUM(COALESCE(billCouponPrivate.amount_by_percent, 0)) +
                SUM(COALESCE(billCouponTotal.amount_by_cash, 0)) +
                SUM(COALESCE(billCouponTotal.amount_by_percent, 0))
                ) AS promotion_amount
         FROM vnpt_dev.billings AS bill
              JOIN vnpt_dev.bill_item AS billItem ON billItem.billing_id = bill.id
              LEFT JOIN vnpt_dev.bill_tax biTax ON billItem.id = biTax.billing_item_id
              LEFT JOIN vnpt_dev.bill_coupon_private AS billCouponPrivate ON billCouponPrivate.billing_item_id = billItem.id
              LEFT JOIN vnpt_dev.bill_coupon_total AS billCouponTotal ON billItem.id = billCouponTotal.billing_item_id
         WHERE billItem.object_type <> 3
         GROUP BY bill.id
),
tmp_bill_vnptpay AS (
    SELECT vpr_m.bill_id                AS bill_id,
           vpr2.vnptpay_transaction_id AS transaction_code
    FROM (
             SELECT vnptPayRes.billing_id AS bill_id,
                    max(vnptPayRes.id)    AS id
             FROM vnpt_dev.vnpt_pay_response vnptPayRes
             GROUP BY vnptPayRes.billing_id
         ) vpr_m
             JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id
),
tmp_bill_change AS (
     WITH bill_change as (
        SELECT DISTINCT
                b.id                                          as bill_id,
                b.subscriptions_id                            as sub_id,
                round(bill_item.amount_pre_tax)               as amount_pre_tax,
                round(bill_item.amount_after_tax)             as amount_after_tax,
                CASE
                    WHEN bill_item.amount_incurred < 0 then 0
                    ELSE round(bill_item.amount_incurred)
                END AS amount_incurred
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item on bill_item.billing_id = b.id
        WHERE b.action_type = 1
          AND bill_item.object_type <> 3
    )
    SELECT
           bill.id,
           sum(bill.amount) as amount
    FROM (
        SELECT
                 bill_change.bill_id as id,
                 CASE
                     WHEN bill_change.amount_after_tax = 0 THEN 0
                     ELSE round(COALESCE(bill_change.amount_incurred * (bill_change.amount_pre_tax / bill_change.amount_after_tax), 0))
                 END AS amount
          FROM bill_change
          ORDER BY bill_change.bill_id DESC) AS bill
    GROUP BY bill.id
),
-- sub select --
tmp_sub AS (
    SELECT
        sub.id AS id,
        sub.sub_code AS sub_code,
        sub.created_at AS created_at,
        CAST(sub.created_at AS DATE) AS registation_date,
        sub.modified_at AS modified_at,
        CASE
                    WHEN sub.status = -1 THEN ''NOT_SET''
                    WHEN sub.status = 0 THEN ''FUTURE''
                    WHEN sub.status = 1 THEN ''IN_TRIAL''
                    WHEN sub.status = 2 THEN ''ACTIVE''
                    WHEN sub.status = 3 THEN ''CANCELED''
                    WHEN sub.status = 4 THEN ''NON_RENEWING''
        END AS subs_status,
        CASE
            WHEN sub.installed IS NULL AND sub.installed = 0 THEN ''Đang cài đặt''
            WHEN sub.installed = 1 THEN ''Đã cài đặt''
            WHEN sub.installed = 2 THEN ''Gặp sự cố''
        END AS subs_installed,
        sub.started_at AS start_at,
        sub.number_of_cycles AS number_of_cycles,
        sub.traffic_id AS traffic_id,
        sub.traffic_user AS traffic_user,
        sub.employee_code AS employee_code,
        sub.dhsxkd_sub_code AS dksxkh_sub_code,
        sub.portal_type AS portal_type,
        CASE
            WHEN sub.created_source_migration = 1 then 5
            WHEN sub.traffic_source = ''accesstrade'' then 6
            WHEN sub.traffic_source = ''apinfo'' then 8
            WHEN sub.affiliate_one is not null then 7
            WHEN sub.traffic_id is not null then 3
            WHEN sub.employee_code is not null then 2
            WHEN sub.portal_type in (1,2) then 4
            ELSE 1
        END AS created_source,
        COALESCE(sub.end_current_cycle_new, sub.end_current_cycle) AS end_current_cycle,
        sub.created_source_migration AS created_source_migration,
        sub.migrate_time AS migrate_time,
        sub.migrate_code AS migrate_code,
        osServiceReceive.setup_code AS setup_code,
        sme_progress.name AS sme_progress_name,
        sub.is_one_time AS is_one_time,
        sub.affiliate_one AS affiliate_one,
        sub.cancelled_time AS cancelled_time,

        -- thông tin sme được đăng ký sub --
        sme.customer_type AS customer_type,
        sme.name AS sme_name,
        sme.identity_no AS identity_no,
        sme.email AS email,
        sme.phone_number AS phone_number,
        sme.tin AS tax_code,
        sme.address AS address,
        sme.street AS street,
        sme.ward AS ward,
        sme.district AS district,
        sme.province AS province,
        sme.nation AS nation,

        -- thông tin user đăng ký sub  --
         CASE
             WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
             WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
             WHEN sub.portal_type = 3 THEN ''OneSME''
         END AS registed_by,

       -- thông tin sản phẩm dịch vụ --
       COALESCE(tmp_services.service_name, tmp_combos.service_name) AS service_name,
       COALESCE(tmp_services.pricing_name, tmp_combos.service_name) AS pricing_name,
       COALESCE(tmp_services.pricing_type, tmp_combos.pricing_type) AS pricing_type,
       COALESCE(tmp_services.plan_payment_cycle, tmp_combos.plan_payment_cycle) AS plan_payment_cycle,
       COALESCE(tmp_services.plan_cycle_type, tmp_combos.plan_cycle_type) AS plan_cycle_type,
       COALESCE(tmp_services.sub_type, tmp_combos.sub_type) AS sub_type,
       COALESCE(tmp_services.service_owner_type, tmp_combos.service_owner_type) AS service_owner_type,

        -- thông tin multi plan
       prcMultiPlan.payment_cycle AS multi_payment_cycle,
        CASE
            WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
            WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
            WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
            WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
            ELSE ''''
        END AS multi_cycle_type,
    -- thông tin os receive
        osServiceReceive.id AS os_receive_id,
        osServiceReceive.payment_status AS os_receive_payment_status,
        osServiceReceive.transaction_code AS os_receive_tran_code
    FROM vnpt_dev.subscriptions sub
        LEFT JOIN tmp_services ON sub.pricing_id = tmp_services.id
        LEFT JOIN tmp_combos ON sub.combo_plan_id = tmp_combos.id
        LEFT JOIN tmp_sme AS sme ON sub.user_id = sme.id
        LEFT JOIN vnpt_dev.users AS register ON sub.registed_by = register.id
        LEFT JOIN vnpt_dev.pricing_multi_plan prcMultiPlan ON prcMultiPlan.id = sub.pricing_multi_plan_id
        LEFT JOIN vnpt_dev.order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id
        LEFT JOIN vnpt_dev.order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)
        LEFT JOIN vnpt_dev.sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id
    WHERE
          sub.deleted_flag = 1 AND
          sub.confirm_status = 1
)
    SELECT
           tmp_sub.id AS id,
           tmp_sub.sub_code AS subCode,
           tmp_sub.created_at AS createdAt,
           tmp_sub.province            AS provinceName,
           tmp_sub.subs_status         AS subsStatus,
           tmp_sub.sme_name            AS smeName,
           tmp_sub.customer_type       AS customerType,
           tmp_sub.tax_code            AS taxtNo,
           tmp_sub.address             AS address,
           tmp_sub.street              AS street,
           tmp_sub.ward                AS ward,
           tmp_sub.district            AS district,
           tmp_sub.province            AS province,
           tmp_sub.nation              AS nation,
           tmp_sub.phone_number        AS phoneNo,
           tmp_sub.email               AS email,
           tmp_sub.service_name        AS serviceName,
           tmp_sub.pricing_name        AS pricingName,
           tmp_sub.number_of_cycles    AS numberOfCycle,
           tmp_sub.plan_payment_cycle  AS planPaymentCycle,
           tmp_sub.plan_cycle_type     AS planCycleType,
           tmp_sub.multi_payment_cycle AS multiPaymentCycle,
           tmp_sub.multi_cycle_type    AS multiCycleType,
           tmp_sub.subs_installed      AS subsInstalled,
           tmp_sub.sme_progress_name   AS smeProgressName,
           tmp_sub.sub_type            AS subscriptionType,
           tmp_sub.modified_at         AS modifiedAt,
           tmp_sub.service_owner_type  AS serviceOwnerType,
           tmp_sub.registed_by         AS registedBy,
           -- --
           tmp_sub.traffic_id          AS trafficId,
           tmp_sub.traffic_user        AS trafficUser,
           tmp_sub.employee_code       AS employeeCode,
           -- --
           tmp_sub.dksxkh_sub_code     AS dhsxkdSubCode,
           CASE
               WHEN tmp_sub.created_source = 1 and bill.portal_type is null THEN ''oneSME''
               WHEN tmp_sub.created_source = 2 THEN ''AM G.Thiệu''
               WHEN tmp_sub.created_source = 3 THEN ''Affiliate Masoffer''
               WHEN tmp_sub.created_source = 4 and bill.portal_type is null THEN ''Dev/Admin''
               WHEN bill.portal_type is not null and bill.portal_type in (1, 2) THEN ''Dev/Admin''
               WHEN tmp_sub.created_source = 5 THEN ''ĐHSXKD''
               WHEN tmp_sub.created_source = 6 THEN ''Affiliate AccessTrade''
               WHEN tmp_sub.created_source = 7 THEN ''Affiliate Onesme''
               WHEN tmp_sub.created_source = 8 THEN ''Affiliate Apinfo''
               ELSE ''oneSME''
            END AS createdSource,
           tmp_sub.migrate_time AS migrateTime,
           tmp_sub.migrate_code AS migrateCode,
           CASE
               WHEN bill.status = 0 and tmp_sub.service_owner_type = ''ON'' THEN ''Khởi tạo''
               WHEN bill.status = 1 and tmp_sub.service_owner_type = ''ON'' THEN ''Chờ thanh toán''
               WHEN bill.status = 2 and tmp_sub.service_owner_type = ''ON'' THEN ''Đã thanh toán''
               WHEN bill.status = 3 and tmp_sub.service_owner_type = ''ON'' THEN ''Thanh toán thất bại''
               WHEN bill.status = 4 and tmp_sub.service_owner_type = ''ON'' THEN ''Quá hạn thanh toán''
               WHEN tmp_sub.os_receive_payment_status = ''1'' and tmp_sub.service_owner_type = ''OS'' THEN ''Đã thanh toán''
               WHEN tmp_sub.os_receive_payment_status = ''0'' and tmp_sub.service_owner_type = ''OS'' THEN ''Chờ thanh toán''
               WHEN tmp_sub.os_receive_payment_status IS NULL AND tmp_sub.os_receive_id IS NOT NULL AND
                    tmp_sub.service_owner_type = ''OS'' THEN ''Chờ thanh toán''
               ELSE ''Chờ thanh toán''
               END AS billStatus,
           bill.billing_code  AS billCode,
           bill.bill_action_type AS subscriptionState,
           CASE
               WHEN bill.action_type is null or bill.action_type = -1 then ''Đăng ký mới''
               WHEN bill.action_type = 1 then ''Chỉnh sửa''
               WHEN bill.action_type in (3, 4) then ''Kích hoạt''
               WHEN bill.bill_action_type = 5 then ''Gia hạn''
               ELSE ''Đổi gói''
               END                                     as state,
           CASE
               WHEN bill.bill_action_type = 0 THEN tmp_sub.registation_date
               ELSE COALESCE(bill.payment_date, CAST(bill.created_at AS DATE))
               END AS registrationDate,
           CASE
               WHEN bill.bill_action_type = 0 THEN tmp_sub.start_at
               ELSE COALESCE(bill.payment_date, CAST(bill.created_at AS DATE))
               END  AS startAt,
           CASE
               WHEN tmp_sub.service_owner_type = ''ON'' THEN tmp_sub.dksxkh_sub_code
               WHEN tmp_sub.service_owner_type = ''OS'' THEN tmp_sub.os_receive_tran_code
               ELSE ''''
               END  AS dhsxkdCode,
           CASE
               WHEN tmp_sub.sub_type = ''SERVICE'' AND tmp_sub.plan_payment_cycle IS NULL THEN tmp_sub.multi_payment_cycle
               ELSE tmp_sub.plan_payment_cycle
               END AS paymentCycle,
           CASE
               WHEN tmp_sub.sub_type = ''SERVICE'' AND tmp_sub.plan_cycle_type IS NULL THEN tmp_sub.multi_cycle_type
               ELSE tmp_sub.plan_cycle_type
               END AS cycleType,
           CASE
               WHEN tmp_sub.service_owner_type = ''ON'' THEN COALESCE(tmp_sub.subs_installed, '''')
               WHEN tmp_sub.service_owner_type = ''OS'' THEN COALESCE(tmp_sub.sme_progress_name, '''')
               ELSE ''''
               END AS installedStatus,
           CASE
               WHEN tmp_sub.sme_progress_name = ''Hủy'' THEN ''CANCEL''
               ELSE tmp_sub.subs_status
               END AS status,
           CASE
               WHEN tmp_sub.traffic_id IS NOT NULL THEN tmp_sub.registed_by
               WHEN tmp_sub.traffic_user IS NOT NULL THEN tmp_sub.traffic_id
               ELSE tmp_sub.traffic_user
               END AS creator,
           billVnptPay.transaction_code AS payTransactionCode,
           CASE
                WHEN billPayments.promotion_amount < 0 THEN 0
                ELSE billPayments.promotion_amount
           END AS promotionAmount,
           CASE
                WHEN billPayments.unit_amount < 0 THEN 0
                ELSE billPayments.unit_amount
           END AS unitAmount,
           CASE
               WHEN bill.action_type = 1 THEN billChange.amount
               WHEN bill.action_type <> 1 AND billPayments.pre_amount_tax > 0 THEN billPayments.pre_amount_tax
               ELSE 0
               END AS preAmountTax,
           CASE
                WHEN billPayments.amount_tax > 0 then billPayments.amount_tax
                ELSE 0
           END AS amountTax,
           CASE
                WHEN billPayments.after_amount_tax > 0 then billPayments.after_amount_tax
                ELSE 0
           END AS afterAmountTax,
           tmp_sub.created_source_migration AS createdSourceMigration,
           tmp_sub.setup_code AS setupCode,
           CASE
               WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                   AND CAST(bill.created_at AS DATE) = bill.billing_date AND tmp_sub.pricing_type = 0
                   THEN bill.created_at
               ELSE bill.payment_date
               END paymentDate,
           bill.created_export_invoice AS createdExportInvoice,
           bill.code AS codeInvoice,
           CASE
               WHEN tmp_sub.sme_progress_name = ''Hủy'' OR tmp_sub.subs_status IN (''CANCELED'', ''NON_RENEWING'')
                   THEN tmp_sub.cancelled_time
               END AS cancelledTime,
           tmp_sub.is_one_time AS isOneTime,
           CASE
               WHEN tmp_sub.created_source_migration = 3 THEN tmp_sub.traffic_user
               WHEN tmp_sub.created_source_migration = 6 THEN tmp_sub.traffic_user
               WHEN tmp_sub.created_source_migration = 7 THEN tmp_sub.affiliate_one
               END AS affiliateCode,
           tmp_sub.end_current_cycle AS endCurrentCycle,
           tmp_sub.identity_no AS identityNo
    FROM tmp_sub
             LEFT JOIN tmp_bills AS bill ON bill.subscriptions_id = tmp_sub.id
             LEFT JOIN tmp_bill_vnptpay AS billVnptPay ON bill.id = billVnptPay.bill_id
             LEFT JOIN tmp_bills_payment AS billPayments ON bill.id = billPayments.id
             LEFT JOIN tmp_bill_change AS billChange ON bill.id = billChange.id
    WHERE tmp_sub.id  = ANY(''{%1$s}''::int8[]) AND bill.id = ANY(''{%2$s}''::int8[])
    ORDER BY registrationDate DESC';

BEGIN
    run_query = format(raw_query, i_sub_ids, i_bill_ids);
    raise notice 'Run_query: %',
        run_query;
return query execute run_query;
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


-- get_report_subscriptions_actual_revenue_new
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_actual_revenue_new"("is_target" int8, "i_customer_email" varchar, "i_customer_name" varchar, "i_object_id" varchar, "i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_affiliate_code" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "createdat" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customertype" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkdsubcode" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "createdsourcemigration" int4, "setupcode" varchar, "paymentdate" timestamp, "createdexportinvoice" timestamp, "codeinvoice" text, "cancelledtime" timestamp, "isonetime" int2, "affiliatecode" varchar, "endcurrentcycle" date, "identityno" varchar) AS $BODY$

DECLARE
last_query                      text; targetQuery varchar; all_bill_selected text;
    tmp_last_query                  text    = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode,
                t.paymentDate,
                t.createdExportInvoice,
                t.codeInvoice,
                CASE
                        WHEN t.status = ''CANCELED'' OR t.status = ''NON_RENEWING'' THEN t.cancelledTime
                END as  cancelledTime,
                t.isOneTime,
                case
                    when t.created_source_migration = 3 then t.traffic_user
                    when t.created_source_migration = 6 then t.traffic_user
                    when t.created_source_migration = 7 then t.affiliateOne
                end as affiliateCode,
                t.endCurrentCycle,
                t.identityNo

FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        bill.pricing_name as pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 and bill.portal_type is null THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate Masoffer''
                            WHEN a.created_source = 4 and bill.portal_type is null THEN ''Dev/Admin''
                            WHEN bill.portal_type is not null and bill.portal_type = 1 THEN ''Dev/Admin''
                            WHEN bill.portal_type is not null and bill.portal_type = 2 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            WHEN a.created_source = 6 THEN ''Affiliate AccessTrade''
                            WHEN a.created_source = 7 THEN ''Affiliate Onesme''
                            WHEN a.created_source = 8 THEN ''Affiliate Apinfo''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
                WHEN bill.status = 0 and a.serviceOwnerType = ''ON'' THEN ''Khởi tạo''
                WHEN bill.status = 1 and a.serviceOwnerType = ''ON'' THEN ''Chờ thanh toán''
                WHEN bill.status = 2 and a.serviceOwnerType = ''ON'' THEN ''Đã thanh toán''
                WHEN bill.status = 3 and a.serviceOwnerType = ''ON'' THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN osr.payment_status = ''1'' and a.serviceOwnerType = ''OS'' THEN ''Đã thanh toán''
                WHEN osr.payment_status = ''0'' and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                WHEN osr.payment_status is null and osr.id is not null and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                ELSE ''Chờ thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
                        (bill.total_amount - bill.total_amount_after_adjustment) as refund,
            CASE
                WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                            AND bill.created_at::date = bill.billing_date::date
                            AND a.pricingType = 0
                THEN bill.created_at
                ELSE bill.payment_date
            END paymentDate,
            bill.createdExportInvoice,
            bill.code as codeInvoice,
            a.cancelledTime,
            a.isOneTime,
            a.affiliateOne,
-- subscriptionState cũ null, -1 là tạo mới, 5 là gia hạn, 1,2,3,4 đổi gói
            bill.bill_action_type as subscriptionState,
            CASE
                WHEN bill.action_type = -1 or bill.action_type = null then ''Đăng ký mới''
                WHEN bill.action_type = 1 then ''Chỉnh sửa''
                WHEN bill.action_type = 3 or bill.action_type = 4 then ''Kích hoạt''
                WHEN bill.bill_action_type = 5 then ''Gia hạn''
                ELSE ''Đổi gói''
            END as state,
            CASE
                WHEN bill.bill_action_type = 0 then a.registration_date
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.bill_action_type = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
--                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy2.unitAmount, 0) AS unitAmount,
             CASE
                            WHEN bill.action_type = 1 then bill_change.amount
                            ELSE COALESCE(b2.preAmountTax, 0) END AS preAmountTax,
            COALESCE (bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b2.afterAmountTax,
--              b.afterAmountTax,
                0
            ) AS afterAmountTax,
            a.endCurrentCycle,
            a.identityNo
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            LEFT JOIN tmp_table_bill_change bill_change ON bill_change.id = bill.id
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            ) AND ( ''-1'' in (%12$s) OR bill.id in (%12$s))
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
        AND (
        (''%10$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') >= ''%10$s'')

    )
        AND (
        (''%11$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') <= ''%11$s'')

    )
        AND (
                (''%7$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%7$s'')
        )
        AND (
                (''%8$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%8$s'')
        )
    AND(
            (%6$s = -1 AND t.created_source_migration <> 5) OR
            (t.created_source_migration <> 5 AND (
            -- Do RK lưu created_source khác với code BE
            (%6$s = 1 AND t.created_source_migration = 1) OR
            (%6$s = 2 AND t.created_source_migration = 2) OR
            (%6$s = 3 AND t.created_source_migration = 3) OR
            (%6$s = 4 AND t.created_source_migration = 4) OR
            (%6$s = 6 AND t.created_source_migration = 6) OR
            (%6$s = 7 AND t.created_source_migration = 7) OR
            (%6$s = 8 AND t.created_source_migration = 8)
            ))
        )
        AND (
        ''%15$s'' = '''' OR t.affiliateOne = ''%15$s''
        )
        AND (
        ''%13$s'' = '''' OR t.smeName ilike (''%%'' || ''%13$s'' || ''%%'')
        )
        AND (
        ''%14$s'' = '''' OR t.email = ''%14$s''
        );';
    tmp_all_sub_query               VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
        p2.pricing_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
        s.number_of_cycles as numberOfCycle,
        p2.payment_cycle as planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_source = ''accesstrade'' then 6
            WHEN s.traffic_source = ''apinfo'' then 8
            WHEN s.affiliate_one is not null then 7
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime,
        s.affiliate_one as affiliateOne,
        case
            when s.end_current_cycle_new is not null then s.end_current_cycle_new
            else s.end_current_cycle
        end as endCurrentCycle,
        CASE
            WHEN u.customer_type = ''CN'' then u.rep_personal_cert_number
                ELSE ''''
        END AS identityNo
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
        p2.combo_plan_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_source = ''accesstrade'' then 6
            WHEN s.traffic_source = ''apinfo'' then 8
            WHEN s.affiliate_one is not null then 7
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime,
        s.affiliate_one as affiliateOne,
        case
            when s.end_current_cycle_new is not null then s.end_current_cycle_new
            else s.end_current_cycle
        end as endCurrentCycle,
        CASE
            WHEN u.customer_type = ''CN'' then u.rep_personal_cert_number
                ELSE ''''
        END AS identityNo
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )';
    all_sub_query                   VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
    table_bill_query                VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
        b2.action_type,
                p.pricing_name,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                b2.portal_type as portal_type,
                b2.created_by as created_by,
                CASE
-- bill_action_type: 0: tạo mới, 1: sửa, 2: đổi, 3, 4: kích hoạt lại,  5: gia hạn,
                    WHEN b2.action_type = -1 or (b2.action_type is null and b2.created_by <> ''batch'' ) THEN 0 -- thuê bao tạo mới
                    WHEN b2.action_type = 1 THEN 2  -- thuê bao sửa
                    WHEN b2.action_type = 2 THEN 2  -- thuê bao đổi gói
                    WHEN b2.action_type = 3 or b2.action_type = 4 THEN 0    -- thuê bao kích hoạt lại
                    WHEN b2.action_type = 5 or (b2.action_type is null and b2.created_by = ''batch'') THEN 5    -- thuê bao gia hạn
                    ELSE b2.action_type
                END AS bill_action_type
        FROM (
                        SELECT b.id as id,
                                b.subscriptions_id AS sub_id
                        FROM tmp_table_bill_selected b
                        where b.status in (0,1,2,3,4)
                ) mb
                JOIN tmp_table_bill_selected b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
                left join vnpt_dev.pricing p on b2.pricing_id = p.id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
    table_bill_vnptpay_query        VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
    table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN b.total_amount_after_adjustment < 0 THEN 0
                        ELSE b.total_amount_after_adjustment
                END AS afterAmountTax
        FROM tmp_table_bill_selected b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE bi2.object_type <> 3
--              and b.created_by = ''batch''
        GROUP BY b.id, b.total_amount_after_adjustment';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
    bill_vnptpay_batch_t2_query     VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM tmp_table_bill_selected b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
--      WHERE b.status = 2
--              and b.created_by = ''batch''
        GROUP BY b.id';

-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
    table_bill_promotion_xy2_query  VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM tmp_table_bill_selected b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
--                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM tmp_table_bill_selected b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
--                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM tmp_table_bill_selected b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
--            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
    bill_selected                   VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_selected;
        CREATE TEMP TABLE tmp_table_bill_selected AS
        SELECT b.*
        FROM vnpt_dev.billings b
        WHERE ( ''-1'' in (%1$s) OR id in (%1$s))
--          AND (
--              (COALESCE(payment_date,created_at) >= CAST(''%2$s'' AS timestamp))
--     )
--     AND (
--         (COALESCE(payment_date,created_at) <= CAST(''%3$s'' AS timestamp))
                ';
    bill_change                     VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_change;
        CREATE TEMP TABLE tmp_table_bill_change AS
        with bill_change as (
select distinct b.id as bill_id, b.subscriptions_id as sub_id,
round(bill_item.amount_pre_tax) as amount_pre_tax,
round(bill_item.amount_after_tax) as amount_after_tax,
case
    when bill_item.amount_incurred < 0 then 0
    else round(bill_item.amount_incurred) end as amount_incurred
from vnpt_dev.billings b
left join vnpt_dev.bill_item on bill_item.billing_id = b.id
where  b.action_type = 1 and bill_item.object_type <> 3
order by b.id desc
)
select bill.id, sum(bill.amount) as amount from
(select bill_change.bill_id as id,
    case when bill_change.amount_after_tax = 0 then 0
    else round(COALESCE(bill_change.amount_incurred*(bill_change.amount_pre_tax/bill_change.amount_after_tax),0)) end as amount
 from bill_change
ORDER BY bill_change.bill_id desc) as bill
GROUP BY bill.id
ORDER BY bill.id DESC
                ';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN
    all_sub_query = FORMAT(
            tmp_all_sub_query,
            i_employee_code,
            i_province_id,
            i_status,
            i_category_service,
            i_service_id,
            i_pricing_id,
            i_category_combo,
            i_combo_ids,
            i_combo_plan_ids,
            i_customer_type,
            i_createdsource,
            i_pricing_ids
        );
-- Thêm filter quản lý doanh thu
    targetQuery = 'SELECT ''-1'' ';
execute targetQuery into i_object_id;
all_bill_selected = FORMAT(
            bill_selected,
            i_object_id,
            i_start_date,
            i_end_date
        );

execute all_bill_selected;
execute bill_change;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
--EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
--EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
EXECUTE table_bill_promotion_xy2_query;

last_query = format(
            tmp_last_query,
            i_subscription_type,
            i_subscription_state,
            i_start_date,
            i_end_date,
            i_creator,
            i_createdsource,
            i_migrate_start_date,
            i_migrate_end_date,
            i_migrate_codes,
            i_cancelled_time_start,
            i_cancelled_time_end,
            i_object_id,
            i_customer_name,
            i_customer_email,
            i_affiliate_code
        );
    raise notice 'Last query: %',
        last_query;
return query execute last_query;
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;



-- get_report_subscriptions_actual_revenue
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_actual_revenue"("is_target" int8, "i_customer_email" varchar, "i_customer_name" varchar, "i_object_id" varchar, "i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_affiliate_code" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int4, "setupCode" varchar, "paymentdate" timestamp, "createdExportInvoice" timestamp, "codeInvoice" text, "cancelledTime" timestamp, "isOneTime" int2, "affiliateCode" varchar, "endCurrentCycle" date, "identityNo" varchar, "endCurrentCycleContract" date, "renewStatus" text) AS $BODY$

DECLARE
    last_query                      text; targetQuery varchar; all_bill_selected text;
    tmp_last_query                  text    = 'SELECT DISTINCT
                t.id,
                t.subCode,
                t.created_at,
                t.provinceName,
                t.subsStatus,
                t.smeName,
                t.customerType,
                t.taxtNo,
                t.address,
                t.street,
                t.ward,
                t.district,
                t.province,
                t.nation,
                t.phoneNo,
                t.email,
                t.serviceName,
                t.pricingName,
                t.numberOfCycle,
                t.planPaymentCycle,
                t.planCycleType,
                t.multiPaymentCycle,
                t.multiCycleType,
                t.subsInstalled,
                t.smeProgressName,
                t.subscriptionType,
                t.modifiedAt,
                t.serviceOwnerType,
                t.registedBy,
                t.traffic_id,
                t.traffic_user,
                t.employeeCode,
                t.dhsxkd_sub_code,
                t.createdSource,
                t.migrateTime,
                t.migrateCode,
                t.billStatus,
                t.billCode,
                t.subscriptionState,
                t.state,
                t.registrationDate,
                t.startAt,
                t.dhsxkdCode,
                t.paymentCycle,
                t.cycleType,
                t.installedStatus,
                t.status,
                t.creator,
                t.payTransactionCode,
                t.promotionAmount,
                t.unitAmount,
                t.preAmountTax,
                t.amountTax,
                t.afterAmountTax,
                t.created_source_migration,
                t.setupCode,
                t.paymentDate,
                t.createdExportInvoice,
                t.codeInvoice,
                CASE
                        WHEN t.status = ''CANCELED'' OR t.status = ''NON_RENEWING'' THEN t.cancelledTime
                END as  cancelledTime,
                t.isOneTime,
                case
                    when t.created_source_migration = 3 then t.traffic_user
                    when t.created_source_migration = 6 then t.traffic_user
                    when t.created_source_migration = 7 then t.affiliateOne
                end as affiliateCode,
                t.endCurrentCycle,
                t.identityNo,
                t.end_current_cycle_contract,
                t.renewStatus

FROM (
        SELECT
                        a.id,
                        a.subCode,
                        a.created_at,
                        a.provinceName,
                        a.subsStatus,
                        a.smeName,
                        a.customerType,
                        a.taxtNo,
                        a.address,
                        a.street,
                        a.ward,
                        a.district,
                        a.province,
                        a.nation,
                        a.phoneNo,
                        a.email,
                        a.serviceName,
                        bill.pricing_name as pricingName,
                        a.registration_date,
                        --a.start_at,
                        a.numberOfCycle,
                        a.planPaymentCycle,
                        a.planCycleType,
                        a.multiPaymentCycle,
                        a.multiCycleType,
                        a.subsInstalled,
                        a.smeProgressName,
                        a.subscriptionType,
                        a.modifiedAt,
                        a.serviceOwnerType,
                        a.registedBy,
                        a.traffic_id,
                        a.traffic_user,
                        a.employeeCode,
                        a.dhsxkd_sub_code,
                        CASE
                            WHEN a.created_source = 1 and bill.portal_type is null THEN ''oneSME''
                            WHEN a.created_source = 2 THEN ''AM G.Thiệu''
                            WHEN a.created_source = 3 THEN ''Affiliate Masoffer''
                            WHEN a.created_source = 4 and bill.portal_type is null THEN ''Dev/Admin''
                            WHEN bill.portal_type is not null and bill.portal_type = 1 THEN ''Dev/Admin''
                            WHEN bill.portal_type is not null and bill.portal_type = 2 THEN ''Dev/Admin''
                            WHEN a.created_source = 5 THEN ''ĐHSXKD''
                            WHEN a.created_source = 6 THEN ''Affiliate AccessTrade''
                            WHEN a.created_source = 7 THEN ''Affiliate Onesme''
                            WHEN a.created_source = 8 THEN ''Affiliate Apinfo''
                            ELSE ''oneSME''
                        END as createdSource,
                        a.created_source as created_source_migration,
                        a.migrateTime,
                        a.migrateCode,
                        a.setupCode,
            CASE
                WHEN bill.status = 0 and a.serviceOwnerType = ''ON'' THEN ''Khởi tạo''
                WHEN bill.status = 1 and a.serviceOwnerType = ''ON'' THEN ''Chờ thanh toán''
                WHEN bill.status = 2 and a.serviceOwnerType = ''ON'' THEN ''Đã thanh toán''
                WHEN bill.status = 3 and a.serviceOwnerType = ''ON'' THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 and a.serviceOwnerType = ''ON'' THEN ''Quá hạn thanh toán''
                WHEN osr.payment_status = ''1'' and a.serviceOwnerType = ''OS'' THEN ''Đã thanh toán''
                WHEN osr.payment_status = ''0'' and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                WHEN osr.payment_status is null and osr.id is not null and a.serviceOwnerType = ''OS'' THEN ''Chờ thanh toán''
                ELSE ''Chờ thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
                        (bill.total_amount - bill.total_amount_after_adjustment) as refund,
            CASE
                WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                            AND bill.created_at::date = bill.billing_date::date
                            AND a.pricingType = 0
                THEN bill.created_at
                ELSE bill.payment_date
            END paymentDate,
            bill.createdExportInvoice,
            bill.code as codeInvoice,
            a.cancelledTime,
            a.isOneTime,
            a.affiliateOne,
-- subscriptionState cũ null, -1 là tạo mới, 5 là gia hạn, 1,2,3,4 đổi gói
            bill.bill_action_type as subscriptionState,
            CASE
                WHEN bill.action_type = -1 or bill.action_type = null then ''Đăng ký mới''
                WHEN bill.action_type = 1 then ''Chỉnh sửa''
                WHEN bill.action_type = 3 or bill.action_type = 4 then ''Kích hoạt''
                WHEN bill.bill_action_type = 5 then ''Gia hạn''
                ELSE ''Đổi gói''
            END as state,
            CASE
                WHEN bill.bill_action_type = 0 then a.registration_date
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as registrationDate,
            CASE
                WHEN bill.bill_action_type = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
--                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy2.unitAmount, 0) AS unitAmount,
             CASE
                            WHEN bill.action_type = 1 then bill_change.amount
                            ELSE COALESCE(b2.preAmountTax, 0) END AS preAmountTax,
            COALESCE (bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b2.afterAmountTax,
--              b.afterAmountTax,
                0
            ) AS afterAmountTax,
                        a.endCurrentCycle,
                        a.identityNo,
                        a.end_current_cycle_contract,
                        case
                            when a.endCurrentCycle > a.end_current_cycle_contract then ''Chưa gia hạn''
                                when a.endCurrentCycle < a.end_current_cycle_contract then ''Đã gia hạn''
                                else null
                        end as renewStatus
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
                        LEFT JOIN tmp_table_bill_change bill_change ON bill_change.id = bill.id
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            ) AND ( ''-1'' in (%12$s) OR bill.id in (%12$s))
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
        AND (
        (''%10$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') >= ''%10$s'')

    )
        AND (
        (''%11$s'' = '''' or to_char(t.cancelledTime,''YYYY-MM-DD'') <= ''%11$s'')

    )
        AND (
                (''%7$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%7$s'')
        )
        AND (
                (''%8$s'' = '''' or to_char(t.migrateTime,''YYYY-MM-DD'') >= ''%8$s'')
        )
    AND(
            (%6$s = -1 AND t.created_source_migration <> 5) OR
            (t.created_source_migration <> 5 AND (
            -- Do RK lưu created_source khác với code BE
            (%6$s = 1 AND t.created_source_migration = 1) OR
            (%6$s = 2 AND t.created_source_migration = 2) OR
            (%6$s = 3 AND t.created_source_migration = 3) OR
            (%6$s = 4 AND t.created_source_migration = 4) OR
            (%6$s = 6 AND t.created_source_migration = 6) OR
            (%6$s = 7 AND t.created_source_migration = 7) OR
            (%6$s = 8 AND t.created_source_migration = 8)
            ))
        )
        AND (
        ''%15$s'' = '''' OR t.affiliateOne = ''%15$s''
        )
        AND (
        ''%13$s'' = '''' OR t.smeName ilike (''%%'' || ''%13$s'' || ''%%'')
        )
        AND (
        ''%14$s'' = '''' OR t.email = ''%14$s''
        );';
    tmp_all_sub_query               VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
        p2.pricing_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
        s.number_of_cycles as numberOfCycle,
        p2.payment_cycle as planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_source = ''accesstrade'' then 6
            WHEN s.traffic_source = ''apinfo'' then 8
            WHEN s.affiliate_one is not null then 7
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime,
        s.affiliate_one as affiliateOne,
        case
            when s.end_current_cycle_new is not null then s.end_current_cycle_new
            else s.end_current_cycle
        end as endCurrentCycle,
        CASE
            WHEN u.customer_type = ''CN'' then u.rep_personal_cert_number
                ELSE ''''
        END AS identityNo,
        s.end_current_cycle_contract
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
                -- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
                -- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
                -- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
                -- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
                -- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
                --i_pricingId
        OR ''%6$s'' = ''-1''
                OR s.pricing_id = %6$s
                OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN u.name
            WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
            WHEN u.customer_type = ''HKD'' THEN u.name
            ELSE u.name
        END as smeName,
        CASE
            WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
            WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
            WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
            ELSE ''''
        END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
        p2.combo_plan_type as pricingType,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
        CASE
            WHEN s.created_source_migration = 1 then 5
            WHEN s.traffic_source = ''accesstrade'' then 6
            WHEN s.traffic_source = ''apinfo'' then 8
            WHEN s.affiliate_one is not null then 7
            WHEN s.traffic_id is not null then 3
            WHEN s.employee_code is not null then 2
            WHEN s.portal_type = 1 then 4
            WHEN s.portal_type = 2 then 4
            ELSE 1
        END as created_source,
        s.created_source_migration,
        s.migrate_time as migrateTime,
        s.migrate_code as migrateCode,
        osr2.setup_code as setupCode,
        s.cancelled_time as cancelledTime,
        s.is_one_time as isOneTime,
        s.affiliate_one as affiliateOne,
        case
            when s.end_current_cycle_new is not null then s.end_current_cycle_new
            else s.end_current_cycle
        end as endCurrentCycle,
        CASE
            WHEN u.customer_type = ''CN'' then u.rep_personal_cert_number
                ELSE ''''
        END AS identityNo,
        s.end_current_cycle_contract
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street sd ON u.street_id = sd.id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
                -- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
                OR p2.combo_id = ANY(''{%8$s}''::int8[])
                --i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
                -- i_comboPlanIds
    )';
    all_sub_query                   VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
    table_bill_query                VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill;
        CREATE TEMP TABLE tmp_table_bill AS
        SELECT b2.id,
        b2.action_type,
                p.pricing_name,
                b2.status,
                b2.payment_date,
                b2.created_at,
                b2.billing_code,
                b2.subscriptions_id AS sub_id,
                b2.total_amount,
                b2.billing_date,
                b2.total_amount_after_adjustment,
                inv.created_at as createdExportInvoice,
              inv.code,
                b2.portal_type as portal_type,
                b2.created_by as created_by,
                CASE
-- bill_action_type: 0: tạo mới, 1: sửa, 2: đổi, 3, 4: kích hoạt lại,  5: gia hạn,
                    WHEN b2.action_type = -1 or (b2.action_type is null and b2.created_by <> ''batch'' ) THEN 0 -- thuê bao tạo mới
                    WHEN b2.action_type = 1 THEN 2  -- thuê bao sửa
                    WHEN b2.action_type = 2 THEN 2  -- thuê bao đổi gói
                    WHEN b2.action_type = 3 or b2.action_type = 4 THEN 0    -- thuê bao kích hoạt lại
                    WHEN b2.action_type = 5 or (b2.action_type is null and b2.created_by = ''batch'') THEN 5    -- thuê bao gia hạn
                    ELSE b2.action_type
                END AS bill_action_type
        FROM (
                        SELECT b.id as id,
                                b.subscriptions_id AS sub_id
                        FROM tmp_table_bill_selected b
                        where b.status in (0,1,2,3,4)
                ) mb
                JOIN tmp_table_bill_selected b2 ON b2.id = mb.id
                left join (
                        select string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
                        from vnpt_dev.e_invoice
                        GROUP BY billing_id
                ) inv ON b2.id = inv.billing_id
                left join vnpt_dev.pricing p on b2.pricing_id = p.id
        ';
-- tmp_table_bill_vnptpay ///////////////////////////////
    table_bill_vnptpay_query        VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
        CREATE TEMP TABLE tmp_table_bill_vnptpay AS
        SELECT vpr_m.billId AS billId,
            vpr2.vnptpay_transaction_id AS transactionCode
        FROM (
                    SELECT vpr.billing_id AS billId,
                            max(vpr.id) AS id
                    FROM vnpt_dev.vnpt_pay_response vpr
                    GROUP BY vpr.billing_id
            ) vpr_m
            JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
    table_bill_vnptpay_batch2_query VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
                        ELSE sum(bi2.amount_pre_tax)
                END AS preAmountTax,
                CASE
                        WHEN b.total_amount_after_adjustment < 0 THEN 0
                        ELSE b.total_amount_after_adjustment
                END AS afterAmountTax
        FROM tmp_table_bill_selected b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
        WHERE  bi2.object_type <> 3
--              and b.created_by = ''batch''
        GROUP BY b.id, b.total_amount_after_adjustment';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
    bill_vnptpay_batch_t2_query     VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
        CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
        SELECT b.id,
                CASE
                        WHEN sum(bt.amount) < 0 THEN 0
                        ELSE sum(bt.amount)
                END AS amountTax
        FROM tmp_table_bill_selected b
                LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
                LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
--              and b.created_by = ''batch''
        GROUP BY b.id';

-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
    table_bill_promotion_xy2_query  VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
        CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
        SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM tmp_table_bill_selected b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
--                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM tmp_table_bill_selected b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
--                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM tmp_table_bill_selected b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
--            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
    bill_selected                   VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_selected;
        CREATE TEMP TABLE tmp_table_bill_selected AS
        SELECT b.*
        FROM vnpt_dev.billings b
        WHERE ( ''-1'' in (%1$s) OR id in (%1$s))
--          AND (
--              (COALESCE(payment_date,created_at) >= CAST(''%2$s'' AS timestamp))
--     )
--     AND (
--         (COALESCE(payment_date,created_at) <= CAST(''%3$s'' AS timestamp))
                ';
    bill_change                     VARCHAR = '
        DROP TABLE IF EXISTS tmp_table_bill_change;
        CREATE TEMP TABLE tmp_table_bill_change AS
        with bill_change as (
select distinct b.id as bill_id, b.subscriptions_id as sub_id,
round(bill_item.amount_pre_tax) as amount_pre_tax,
round(bill_item.amount_after_tax) as amount_after_tax,
case
    when bill_item.amount_incurred < 0 then 0
    else round(bill_item.amount_incurred) end as amount_incurred
from vnpt_dev.billings b
left join vnpt_dev.bill_item on bill_item.billing_id = b.id
where  b.action_type = 1 and bill_item.object_type <> 3
order by b.id desc
)
select bill.id, sum(bill.amount) as amount from
(select bill_change.bill_id as id,
    case when bill_change.amount_after_tax = 0 then 0
    else round(COALESCE(bill_change.amount_incurred*(bill_change.amount_pre_tax/bill_change.amount_after_tax),0)) end as amount
 from bill_change
ORDER BY bill_change.bill_id desc) as bill
GROUP BY bill.id
ORDER BY bill.id DESC
                ';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN
    all_sub_query = FORMAT(
            tmp_all_sub_query,
            i_employee_code,
            i_province_id,
            i_status,
            i_category_service,
            i_service_id,
            i_pricing_id,
            i_category_combo,
            i_combo_ids,
            i_combo_plan_ids,
            i_customer_type,
            i_createdsource,
            i_pricing_ids
        );
-- Thêm filter quản lý doanh thu
    case is_target
        when 0 then case i_object_id
            when 'ALL' then targetQuery = 'SELECT ''-1'' ';
            else targetQuery = concat('SELECT COALESCE((SELECT string_agg(actual_sub_code, '','') AS subCode
            from vnpt_dev.feature_view_get_actual_revenue_sub_code
            where target_id = ', i_object_id, '::int8
            GROUP BY target_id), ''-2'')');
            end case;
        else case i_object_id
            when 'ALL' then targetQuery = 'SELECT ''-1'' ';
            else targetQuery = concat('SELECT COALESCE((SELECT string_agg(actual_sub_code, '','') AS subCode
            from vnpt_dev.feature_view_get_actual_revenue_sub_code
            where target_value_id in ( ', i_object_id, ')), ''-2'')');
            end case;
        end case;
    execute targetQuery into i_object_id;
    all_bill_selected = FORMAT(
            bill_selected,
            i_object_id,
            i_start_date,
            i_end_date
        );

    execute all_bill_selected;
    execute bill_change;
    execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
    execute table_bill_query;
    EXECUTE table_bill_vnptpay_query;
--EXECUTE table_bill_vnptpay_batch_query;
    EXECUTE table_bill_vnptpay_batch2_query;
--EXECUTE table_bill_vnptpay_batch_t_query;
    EXECUTE bill_vnptpay_batch_t2_query;
    EXECUTE table_bill_promotion_xy2_query;

    last_query = format(
            tmp_last_query,
            i_subscription_type,
            i_subscription_state,
            i_start_date,
            i_end_date,
            i_creator,
            i_createdsource,
            i_migrate_start_date,
            i_migrate_end_date,
            i_migrate_codes,
            i_cancelled_time_start,
            i_cancelled_time_end,
            i_object_id,
            i_customer_name,
            i_customer_email,
            i_affiliate_code
        );
    raise notice 'Last query: %',
        last_query;
    return query execute last_query;
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;