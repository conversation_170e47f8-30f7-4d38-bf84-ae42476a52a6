SELECT setval('api_permission_id_seq', (select max(id) from vnpt_dev.api_permission ap) + 1);
SELECT setval('roles_permissions_id_seq', (select max(id) from vnpt_dev.roles_permissions rp) + 1);
-- Dev xem seo cua dich vu
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id) VALUES
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_THONG_TIN_SEO_DICH_VU_GOI_DICH_VU' ORDER BY id DESC LIMIT 1), 2);

INSERT INTO vnpt_dev.api_permission (api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
((SELECT id FROM vnpt_dev.apis a WHERE a.api_code = 'ROLE_ADMIN_GET_SEO_SERVICE_DETAIL' LIMIT 1),
(SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
WHERE p.code = 'XEM_THONG_TIN_SEO_DICH_VU_GOI_DICH_VU' AND pp.portal_id = 2 LIMIT 1), 1, 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit) VALUES
((SELECT r.id FROM vnpt_dev."role" r WHERE r.description = 'FULL_DEV'), (SELECT p.id FROM vnpt_dev."permission" p WHERE p.code = 'XEM_THONG_TIN_SEO_DICH_VU_GOI_DICH_VU'), 1);

-- Admin, Dev xem seo cua goi dich vu
INSERT INTO vnpt_dev.apis
(id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/seo/pricing/{pricingDraftId}',
        'ROLE_ADMIN_DEV_GET_SEO_PRICING', 'GET'),
       ((SELECT max(id) + 2 FROM vnpt_dev.apis), '/api/dev-portal/seo/{id}/service',
        'ROLE_DEV_UPDATE_SEO_SERVICE', 'PUT');

INSERT INTO vnpt_dev.api_permission (api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
((SELECT id FROM vnpt_dev.apis a WHERE a.api_code = 'ROLE_ADMIN_DEV_GET_SEO_PRICING' LIMIT 1),
(SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
WHERE p.code = 'XEM_THONG_TIN_SEO_GOI_DICH_VU' AND pp.portal_id = 1 LIMIT 1), 1, 1),
((SELECT id FROM vnpt_dev.apis a WHERE a.api_code = 'ROLE_ADMIN_DEV_GET_SEO_PRICING' LIMIT 1),
(SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
WHERE p.code = 'XEM_THONG_TIN_SEO_GOI_DICH_VU' AND pp.portal_id = 2 LIMIT 1), 1, 1),
((SELECT id FROM vnpt_dev.apis a WHERE a.api_code = 'ROLE_DEV_UPDATE_SEO_SERVICE' LIMIT 1),
(SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
WHERE p.code = 'CAU_HINH_SEO_DICH_VU' AND pp.portal_id = 2 LIMIT 1), 1, 1);

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;