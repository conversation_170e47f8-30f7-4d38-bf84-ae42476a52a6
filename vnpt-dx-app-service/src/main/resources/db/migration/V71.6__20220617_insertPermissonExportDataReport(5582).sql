DELETE
FROM vnpt_dev.permission_portal
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('BAO_CAO_XUAT_DU_LIEU'))
);

DELETE
FROM vnpt_dev.permission
WHERE code IN  ('BAO_CAO_XUAT_DU_LIEU');

DELETE
FROM vnpt_dev.roles_permissions
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('BAO_CAO_XUAT_DU_LIEU'))
);

-- thêm vào bảng permision
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xuất dữ liệu',
        'BAO_CAO_XUAT_DU_LIEU',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE' ORDER BY id DESC LIMIT 1),
    10000209 );

-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_XUAT_DU_LIEU' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );
-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_XUAT_DU_LIEU' ORDER BY id DESC LIMIT 1),
    1 );

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;