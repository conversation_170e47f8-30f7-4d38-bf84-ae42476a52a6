DROP TABLE IF EXISTS affiliate_commission_bill CASCADE;
CREATE TABLE "vnpt_dev"."affiliate_commission_bill" (
                                                        "id" bigserial NOT NULL,
                                                        "code" varchar(255),
                                                        "content" varchar(255),
                                                        "status" int2,
                                                        "deleted_flag" int2,
                                                        "time_type" int2,
                                                        "start_time" date,
                                                        "end_time" date,
                                                        "payment_time" timestamp(6),
                                                        "aff_id" int8,
                                                        "count_sub" int8,
                                                        "commission_value" float8,
                                                        "created_at" timestamp(6),
                                                        "created_by" int8,
                                                        "modified_at" timestamp(6),
                                                        "modified_by" int8,
                                                        PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."code" IS 'Mã thanh toán';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."status" IS 'Trạng thái thanh toán (0:hủy, 1: chờ, 2 : đã thanh to<PERSON>)';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."time_type" IS '0:Hôm nay
1:<PERSON>ô<PERSON> qua
2:7 ngày gần đây
3:30 ngày gần đây
4:Tháng này
5:Tháng trước
6:Tùy chỉnh';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."start_time" IS 'Thời gian bắt đầu';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."end_time" IS 'Thời gian kết thúc';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."payment_time" IS 'Thời gian thanh toán';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."aff_id" IS 'id thành viên';
COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."count_sub" IS 'Số đơn hàng';
COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."commission_value" IS 'số tiền cần thanh toán';

ALTER TABLE "vnpt_dev"."affiliate_commission_bill"
    ADD COLUMN IF NOT EXISTS "min_commission_payment" float8;

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."min_commission_payment" IS 'Mức thanh toán tối thiểu';

ALTER TABLE "vnpt_dev"."system_params"
    ADD COLUMN IF NOT EXISTS "commission_payment_min" float8;

COMMENT ON COLUMN "vnpt_dev"."system_params"."commission_payment_min" IS 'Mức thanh toán tối thiểu';

ALTER TABLE "vnpt_dev"."affiliate_commission_bill"
    ADD COLUMN IF NOT EXISTS "lst_bill_id" int8[],
    ADD COLUMN IF NOT EXISTS "lst_aff_com_event_id" int8[];
;

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."lst_bill_id" IS 'Danh sách id bill được áp dụng';
COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_bill"."lst_aff_com_event_id" IS 'Danh sách id affiliate commission event được áp dụng';

insert into vnpt_dev.system_params(param_name, param_type, commission_payment_min)
select 'Cấu hình mức thanh toán tối thiểu affiliate', 'AFFILIATE_COMMISSION_VALUE_MIN',10000
    where not exists (
   select * from vnpt_dev.system_params
   where param_type like 'AFFILIATE_COMMISSION_VALUE_MIN'
);

ALTER TABLE "vnpt_dev"."affiliate_commission_event"
    ADD COLUMN IF NOT EXISTS "level_intro" int2,
    ADD COLUMN IF NOT EXISTS "user_id_intro" int8,
    ADD COLUMN IF NOT EXISTS "commission_value_config" float8;

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_event"."level_intro" IS 'Level thành viên giới thiệu';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_event"."user_id_intro" IS 'Thành viên giới thiệu';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission_event"."commission_value_config" IS 'Số tiền cấu hình';

ALTER TABLE "vnpt_dev"."affiliate_users"
    ADD COLUMN IF NOT EXISTS "commission_payment_min" float8 DEFAULT 0;

COMMENT ON COLUMN "vnpt_dev"."affiliate_users"."commission_payment_min" IS 'Mức thanh toán tổi thiểu';