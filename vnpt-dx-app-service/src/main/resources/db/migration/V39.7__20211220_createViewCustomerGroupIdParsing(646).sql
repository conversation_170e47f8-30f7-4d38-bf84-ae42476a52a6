CREATE OR REPLACE VIEW "vnpt_dev"."view_customer_group_id_parsing" AS

 SELECT customer_group.id AS group_id,
    unnest(array_cat(string_to_array(btrim(customer_group.lst_enterprise_id, '[]'::text), ','::text), string_to_array(btrim(customer_group.lst_condition_enterprise_id, '[]'::text), ','::text))::bigint[]) AS enterprise_id,
    unnest(array_cat(string_to_array(btrim(customer_group.lst_contact_id, '[]'::text), ','::text), string_to_array(btrim(customer_group.lst_condition_contact_id, '[]'::text), ','::text))::bigint[]) AS customer_contact_id,
    unnest(string_to_array(btrim(customer_group.lst_uploaded_customer_id, '[]'::text), ','::text)::bigint[]) AS uploaded_customer_id
   FROM vnpt_dev.customer_group