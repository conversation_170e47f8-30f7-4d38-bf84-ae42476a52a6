-- Drop table
DROP TABLE IF EXISTS vnpt_dev.history_confirm_data_policy;
CREATE TABLE IF NOT EXISTS vnpt_dev.history_confirm_data_policy (
	id int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	device_type int2 NULL, -- <PERSON><PERSON><PERSON> thiết bị
	device_operator_system int2 NULL, -- <PERSON>ệ điều hành
	device_ip_address varchar(50) NULL, -- Đ<PERSON><PERSON> chỉ ip
	uuid uuid NOT NULL, -- B<PERSON>n ghi tác động lên người dùng này
	parent_uuid uuid NOT NULL, -- ID chủ doanh nghiệp
    confirmer_id int8 NOT NULL, -- Ng<PERSON><PERSON><PERSON> x<PERSON><PERSON> nh<PERSON>n
	created_at timestamp NOT NULL, -- thời gian tạo
	confirm_accept_at timestamp NULL, -- B<PERSON>n ghi Hủy đồng ý Lưu thêm thời gian đồng ý chính sách
	status bool NOT NULL
);
ALTER TABLE vnpt_dev.history_confirm_data_policy DROP CONSTRAINT IF EXISTS history_confirm_data_policy_pkey;
ALTER TABLE vnpt_dev.history_confirm_data_policy ADD PRIMARY KEY(id);
COMMENT ON TABLE vnpt_dev.history_confirm_data_policy IS 'bảng lịch sử xác nhận ( đồng ý, hủy) chính sách dữ liệu cá nhân';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.device_type IS 'Loại thiết bị 1: MAC, 2: Desktop, 3: Mobile';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.device_operator_system IS 'Hệ điều hành 1: MACOS, 2: Window';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.device_ip_address IS 'Địa chỉ ip';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.uuid IS 'Bản ghi tác động lên người dùng này - hủy/ đồng ý chính sách cho user có uuid này';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.parent_uuid IS 'ID chủ doanh nghiệp';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.confirmer_id IS 'Người xác nhận';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.created_at IS 'thời gian tạo';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.confirm_accept_at IS 'Bản ghi Hủy đồng ý Lưu thêm thời gian đồng ý chính sách';
COMMENT ON COLUMN vnpt_dev.history_confirm_data_policy.status IS 'true: đồng ý, false: hủy đồng ý';

ALTER TABLE vnpt_dev.users ADD COLUMN IF NOT EXISTS status_confirm_data_policy bool NULL;
COMMENT ON COLUMN vnpt_dev.users.status_confirm_data_policy IS 'true : Đồng ý Chính sách bảo vệ dữ liệu cá nhân; false: không đồng ý';
UPDATE vnpt_dev.users SET status_confirm_data_policy = TRUE WHERE 1 = 1