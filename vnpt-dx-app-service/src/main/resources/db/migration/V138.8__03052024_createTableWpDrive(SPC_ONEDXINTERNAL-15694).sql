-- Table lưu trữ file
drop TABLE if exists vnpt_dev.wp_drive;
CREATE TABLE "vnpt_dev"."wp_drive"
(
    "id"                     bigserial NOT NULL,
    "etag"                   varchar(255),
    "bucket_name"            varchar(255),
    "key"                    varchar(255),
    "file_name"              varchar(255),
    "size"                   int8,
    "file_type"              int2,
    "source"                 int2,
    "wp_drive_connection_id" int8,
    "share_type"             int2,
    "share_user_ids"         int8[],
    "old_file_name"          varchar(255),

    "deleted_flag"           int2,
    "created_at"             timestamp(6),
    "created_by"             int8,
    "modified_at"            timestamp(6),
    "modified_by"            int8
);

COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."etag" IS '(Entity Tag) là một chuỗi định danh duy nhất cho một phiên bản của tài nguyên';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."bucket_name" IS 'Tên đ<PERSON> l<PERSON> tr<PERSON>, sử dụng để định danh cho mỗi người dùng với IDG';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."key" IS 'Tương ứng là filePath';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."file_name" IS 'Tên tài liệu hoặc folder';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."size" IS 'Kích cỡ của tệp';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."file_type" IS 'Loại file:  FOLDER(0), OTHER(1), DOC(2), XLS(3), PPT(4), PIC(5), PDF(6), SOUND(7), ZIP(8), VIDEO(9)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."source" IS 'Nguồn file hoặc folder: IDG(0), GOOGLE_DRIVE(1), ONE_DRIVE(2), DROPBOX(3);';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."wp_drive_connection_id" IS 'id tài khoản connection(Nếu nguồn File không phải IDG cung cấp thì lưu) ';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."share_type" IS 'Loại chia sẻ: ONLY_ME(0), EVERYBODY(1), SOME_PEOPLE(2);';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."share_user_ids" IS 'Id người dùng được chia sẻ TH share_type = SOME_PEOPLE(2)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive"."old_file_name" IS 'Tên cũ file folder';

-- Table lưu trữ hành động liên quan đến file/folder
drop TABLE if exists vnpt_dev.wp_drive_action;
CREATE TABLE "vnpt_dev"."wp_drive_action"
(
    "id"            bigserial NOT NULL,
    "wp_drive_id"   int8,
    "liked"         int2,
    "pin_number"    int2,
    "viewed_number" int2,

    "deleted_flag"  int2,
    "created_at"    timestamp(6),
    "created_by"    int8,
    "modified_at"   timestamp(6),
    "modified_by"   int8
);

COMMENT
ON COLUMN "vnpt_dev"."wp_drive_action"."wp_drive_id" IS 'Id của file hoặc folder trong bảng wp_drive';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_action"."liked" IS '1: Thích, 2: Không thích';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_action"."pin_number" IS 'Hành động ghim file hoặc folder của người đang đăng nhập(1: chủ sở hữu, 2: người được share): theo thứ tự tăng dần 1 => ...';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_action"."viewed_number" IS 'Hành động đã xem file hoặc folder của người đang đăng nhập(1: chủ sở hữu, 2: người được share): theo thứ tự tăng dần 1 => ...';

-- Table lưu trữ hành động liên quan đến file/folder
drop TABLE if exists vnpt_dev.wp_drive_user_role;
CREATE TABLE "vnpt_dev"."wp_drive_user_role"
(
    "id"           bigserial NOT NULL,
    "wp_drive_id"  int8,
    "user_id"      int8,
    "role"         int2,

    "deleted_flag" int2,
    "created_at"   timestamp(6),
    "created_by"   int8,
    "modified_at"  timestamp(6),
    "modified_by"  int8
);

COMMENT
ON COLUMN "vnpt_dev"."wp_drive_user_role"."wp_drive_id" IS 'Id của file hoặc folder trong bảng wp_drive';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_user_role"."user_id" IS 'Id người dùng';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_user_role"."role" IS 'Phân quyền: READONLY(0), EDIT_ONLY(1);';

-- Table lưu trữ thông tin kết nối cloud storage
drop TABLE if exists vnpt_dev.wp_drive_connection;
CREATE TABLE "vnpt_dev"."wp_drive_connection"
(
    "id"            bigserial NOT NULL,
    "user_id"       int8,
    "email"         varchar(255),
    "code"          varchar(255),
    "access_token"  varchar(255),
    "refresh_token" varchar(255),
    "source"        int2,

    "deleted_flag" int2,
    "created_at"   timestamp(6),
    "created_by"   int8,
    "modified_at"  timestamp(6),
    "modified_by"  int8
);

COMMENT
ON COLUMN "vnpt_dev"."wp_drive_connection"."email" IS 'Email đăng nhập';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_connection"."code" IS 'Mã đăng nhập';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_connection"."access_token" IS 'Token đăng nhập';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_connection"."refresh_token" IS 'Token làm mới';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_connection"."source" IS 'Nguồn file hoặc folder: IDG(0), GOOGLE_DRIVE(1), ONE_DRIVE(2), DROPBOX(3);';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_connection"."user_id" IS 'Id người dùng';

-- Table lưu trữ thông tin kết nối cloud storage
drop TABLE if exists vnpt_dev.wp_drive_history;
CREATE TABLE "vnpt_dev"."wp_drive_history"
(
    "id"                            bigserial NOT NULL,
    "action"                        int2,
    "wp_drive_id"                   int8,
    "wp_drive_id_affected"          int8[],

    "deleted_flag"                  int2,
    "created_at"                    timestamp(6),
    "created_by"                    int8,
    "modified_at"                   timestamp(6),
    "modified_by"                   int8
);

COMMENT
ON COLUMN "vnpt_dev"."wp_drive_history"."action" IS 'Trạng thái hoạt động: CREATE(0), UPDATE(1), UPLOAD(2), DOWNLOAD(3), DELETE(4), RESTORE(5), MOVE(6)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_history"."wp_drive_id" IS 'Id của file và folder';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_history"."wp_drive_id_affected" IS 'Id folder ảnh hưởng';



