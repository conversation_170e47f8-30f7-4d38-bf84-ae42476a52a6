--- FROM 28.2
DELETE
FROM
    vnpt_dev.pricing_multi_plan_addon
WHERE
        id IN (
        SELECT
            ps.id
        FROM
            (
                SELECT
                    pmpa.id,
                    CASE
                        WHEN s.service_owner IN (0, 1) THEN 'ON'
                        ELSE 'OS'
                        END AS service_type
                FROM
                    vnpt_dev.pricing_multi_plan_addon pmpa
                        INNER JOIN vnpt_dev.pricing_multi_plan pmp ON
                            pmpa.pricing_multi_plan_id = pmp.id
                        INNER JOIN vnpt_dev.pricing p ON
                            pmp.pricing_id = p.id
                        INNER JOIN vnpt_dev.services s ON
                            p.service_id = s.id) ps
                INNER JOIN (
                SELECT
                    pmpa.id,
                    CASE
                        WHEN s.service_owner IN (0, 1) THEN 'ON'
                        ELSE 'OS'
                        END AS service_type
                FROM
                    vnpt_dev.pricing_multi_plan_addon pmpa
                        INNER JOIN vnpt_dev.addons a ON
                            pmpa.addon_id = a.id
                        INNER JOIN vnpt_dev.services s ON
                            a.service_id = s.id) ads ON
                        ps.id = ads.id
                    AND ps.service_type <> ads.service_type );

DELETE
FROM
    vnpt_dev.pricing_addons
WHERE
        id IN (
        SELECT
            ps.id
        FROM
            (
                SELECT
                    pa.id,
                    CASE
                        WHEN s.service_owner IN (0, 1) THEN 'ON'
                        ELSE 'OS'
                        END AS service_type
                FROM
                    vnpt_dev.pricing_addons pa
                        INNER JOIN vnpt_dev.pricing p ON
                            pa.pricing_id = p.id
                        INNER JOIN vnpt_dev.services s ON
                            p.service_id = s.id) ps
                INNER JOIN (
                SELECT
                    pa.id,
                    CASE
                        WHEN s.service_owner IN (0, 1) THEN 'ON'
                        ELSE 'OS'
                        END AS service_type
                FROM
                    vnpt_dev.pricing_addons pa
                        INNER JOIN vnpt_dev.addons a ON
                            pa.addons_id = a.id
                        INNER JOIN vnpt_dev.services s ON
                            a.service_id = s.id) ads ON
                        ps.id = ads.id
                    AND ps.service_type <> ads.service_type);

--- FROM 28.3

UPDATE
    vnpt_dev.pricing_multi_plan_addon pm
SET
    pricing_multi_plan_addon_id = (
        SELECT
            pmpas.id
        FROM
            vnpt_dev.pricing_multi_plan_addon pmpa
                LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON
                    pmpa.pricing_multi_plan_id = pmp.id
                LEFT JOIN (
                SELECT
                    pmp.addon_id AS addonId,
                    pmp.id AS id,
                    pmp.payment_cycle AS paymentCycle,
                    pmp.circle_type AS circleType
                FROM
                    vnpt_dev.pricing_multi_plan pmp
                WHERE
                        addon_id IN (
                        SELECT
                            DISTINCT pmpa.addon_id
                        FROM
                            vnpt_dev.pricing_multi_plan_addon pmpa )) pmpas ON
                        pmpa.addon_id = pmpas.addonId
                    AND pmpas.paymentCycle = pmp.payment_cycle
                    AND pmpas.circleType = pmp.circle_type
        WHERE
                pmpa.id = pm.id)
WHERE
    pricing_multi_plan_addon_id IS NULL;

-- --- FROM 28.7

UPDATE
    vnpt_dev.pricing
SET
    payment_cycle = NULL,
    cycle_type = NULL
WHERE
        id IN (
        SELECT
            DISTINCT p.id
        FROM
            vnpt_dev.pricing p
                INNER JOIN vnpt_dev.pricing_multi_plan pmp ON
                    p.id = pmp.pricing_id
        WHERE
            p.payment_cycle IS NOT NULL);