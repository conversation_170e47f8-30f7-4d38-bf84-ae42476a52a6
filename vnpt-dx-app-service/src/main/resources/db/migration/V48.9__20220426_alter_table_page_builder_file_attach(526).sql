DROP TABLE IF EXISTS "vnpt_dev"."page_builder";

CREATE TABLE "vnpt_dev"."page_builder" (
   id bigserial NOT NULL,
   "page_content" text,
   "user_id" int8,
   "status" int2,
   "deleted_flag" int2,
   "created_at" timestamp(6),
   "created_by" int8,
   "modified_at" timestamp(6),
   "modified_by" int8,
   "slug_name" varchar(500),
   "page_name" varchar(255),
   "builder_template_id" int4,
   "is_default" int2 DEFAULT 0
);

DELETE
FROM vnpt_dev.apis
WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id
    FROM vnpt_dev.api_permission
             JOIN vnpt_dev.permission ON permission.id = api_permission.permission_id
    WHERE vnpt_dev.permission.code IN ('XEM_DANH_SACH_PAGE_BUILDER', 'XEM_CHI_TIET_PAGE_BUILDER')
);

DELETE
FROM vnpt_dev.api_permission
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('XEM_DANH_SACH_PAGE_BUILDER', 'XEM_CHI_TIET_PAGE_BUILDER'))
);

DELETE
FROM vnpt_dev.roles_permissions
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('XEM_DANH_SACH_PAGE_BUILDER', 'XEM_CHI_TIET_PAGE_BUILDER'))
);

DELETE
FROM vnpt_dev.permission_portal
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('XEM_DANH_SACH_PAGE_BUILDER', 'XEM_CHI_TIET_PAGE_BUILDER'))
);

DELETE
FROM vnpt_dev.permission
WHERE code IN ('XEM_DANH_SACH_PAGE_BUILDER', 'XEM_CHI_TIET_PAGE_BUILDER');



INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem danh sách page builder',
        'XEM_DANH_SACH_PAGE_BUILDER',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    19000002 );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết page builder',
        'XEM_CHI_TIET_PAGE_BUILDER',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    19000003 );


-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'SME' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'SME' ORDER BY id DESC LIMIT 1)
    );

-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_SME' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    1 );

INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_SME' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    1 );

-- Add vao bang apis, api_permission

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/sme-portal/page-builder',
        'ROLE_SME_XEM_DANH_SACH_PAGE_BUILDER',
        'GET');
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_XEM_DANH_SACH_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_PAGE_BUILDER' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/sme-portal/page-builder/{id}',
        'ROLE_SME_XEM_CHI_TIET_PAGE_BUILDER',
        'GET');
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_XEM_CHI_TIET_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_PAGE_BUILDER' ORDER BY id DESC LIMIT 1)
    );

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;

DELETE
FROM vnpt_dev.apis
WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id
    FROM vnpt_dev.api_permission
             JOIN vnpt_dev.permission ON permission.id = api_permission.permission_id
    WHERE vnpt_dev.permission.code IN ('TAO_BUILDER_TEMPLATE', 'XEM_DANH_SACH_BUILDER_TEMPLATE','XEM_CHI_TIET_BUILDER_TEMPLATE','CAP_NHAT_BUILDER_TEMPLATE')
);

DELETE
FROM vnpt_dev.api_permission
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('TAO_BUILDER_TEMPLATE', 'XEM_DANH_SACH_BUILDER_TEMPLATE','XEM_CHI_TIET_BUILDER_TEMPLATE','CAP_NHAT_BUILDER_TEMPLATE'))
);

DELETE
FROM vnpt_dev.roles_permissions
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('TAO_BUILDER_TEMPLATE', 'XEM_DANH_SACH_BUILDER_TEMPLATE','XEM_CHI_TIET_BUILDER_TEMPLATE','CAP_NHAT_BUILDER_TEMPLATE'))
);

DELETE
FROM vnpt_dev.permission_portal
WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('TAO_BUILDER_TEMPLATE', 'XEM_DANH_SACH_BUILDER_TEMPLATE','XEM_CHI_TIET_BUILDER_TEMPLATE','CAP_NHAT_BUILDER_TEMPLATE'))
);

DELETE
FROM vnpt_dev.permission
WHERE code IN ('TAO_BUILDER_TEMPLATE', 'XEM_DANH_SACH_BUILDER_TEMPLATE','XEM_CHI_TIET_BUILDER_TEMPLATE','CAP_NHAT_BUILDER_TEMPLATE');


-- thêm vào bảng permision

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Thêm mới template',
        'TAO_BUILDER_TEMPLATE',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    19000004 );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem danh sách template',
        'XEM_DANH_SACH_BUILDER_TEMPLATE',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    19000005 );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết template',
        'XEM_CHI_TIET_BUILDER_TEMPLATE',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    19000006 );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Cập nhật template',
        'CAP_NHAT_BUILDER_TEMPLATE',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_PAGE_BUILDER' ORDER BY id DESC LIMIT 1),
    19000007 );





-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'TAO_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );


-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'TAO_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
    1 );

INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
    1 );

INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
    1 );
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
    1 );


-- Add vao bang apis, api_permission
-- xem danh sách giao diện
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/builder-template',
        'ROLE_ADMIN_TAO_BUILDER_TEMPLATE',
        'POST');
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_TAO_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'TAO_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/builder-template',
        'ROLE_ADMIN_CAP_NHAT_BUILDER_TEMPLATE',
        'PUT');
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CAP_NHAT_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/builder-template',
        'ROLE_ADMIN_XEM_DANH_SACH_BUILDER_TEMPLATE',
        'GET');
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_XEM_DANH_SACH_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/builder-template/{id}',
        'ROLE_ADMIN_XEM_CHI_TIET_BUILDER_TEMPLATE',
        'GET');
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_XEM_DANH_SACH_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1),
       (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_BUILDER_TEMPLATE' ORDER BY id DESC LIMIT 1)
    );

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;