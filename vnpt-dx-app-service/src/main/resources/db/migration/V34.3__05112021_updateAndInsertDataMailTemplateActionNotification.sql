--update name mail_template
UPDATE vnpt_dev.mail_template
SET name = '<PERSON><PERSON><PERSON><PERSON> lý thuê bao'
WHERE code = 'SB';

--update receiver in action_notification
UPDATE vnpt_dev.action_notification
SET receiver = '<PERSON><PERSON> đ<PERSON> phân quyền phê duyệt CTKM'
WHERE action_code = 'CP-12';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-01';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-03';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-02';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-05';
UPDATE vnpt_dev.action_notification
SET receiver = '<PERSON><PERSON>h nghiệp SME'
WHERE action_code = 'CP-07';
UPDATE vnpt_dev.action_notification
SET receiver = '<PERSON><PERSON>h nghiệp SME'
WHERE action_code = 'CP-04';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-06';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-09';
UPDATE vnpt_dev.action_notification
SET receiver = 'Doanh nghiệp SME'
WHERE action_code = 'CP-08';
UPDATE vnpt_dev.action_notification
SET receiver = 'Dev tạo chương trình khuyến mại WHERE Dev Admin WHERE Admin tạo CTKM'
WHERE action_code = 'CP-14';
UPDATE vnpt_dev.action_notification
SET receiver = 'Dev tạo chương trình khuyến mại WHERE Dev Admin WHERE Admin tạo CTKM'
WHERE action_code = 'CP-13';
UPDATE vnpt_dev.action_notification
SET receiver = 'Dev tạo chương trình khuyến mại WHERE Dev Admin WHERE Admin tạo CTKM'
WHERE action_code = 'CP-11';
UPDATE vnpt_dev.action_notification
SET receiver = 'Nhà cung cấp dịch vụ'
WHERE action_code = 'CP-10';
UPDATE vnpt_dev.action_notification
SET receiver = 'Dev tạo chương trình khuyến mại WHERE Dev Admin WHERE Admin tạo CTKM'
WHERE action_code = 'CP-15';

--insert action_notification
INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                          modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order)
VALUES ('SME nhận thông báo khi thuê bao sắp hết hạn', 1, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-09-15 00:00:00.000000', '<EMAIL>', '2021-09-15 00:00:00.000000', 'Sme admin', 'SB-01', 'B', 'B', 'B', 9005),
       ('Dev/Admin nhận thông báo khi thuê bao do Dev/Admin tạo cho SME sắp hết hạn', 1, 0, 1,
        (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system', '2021-09-15 00:00:00.000000', '<EMAIL>',
        '2021-09-15 00:00:00.000000', 'Dev admin, admin tỉnh', 'SB-02', 'B', 'B', 'B', 9006),
       ('Đăng ký thuê bao thành công', 1, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-09-15 00:00:00.000000', '<EMAIL>', '2021-09-15 00:00:00.000000', 'Dev admin, dev tạo thuê bao', 'SB-06', 'B', 'B', 'B',
        9007),
       ('Sắp đến kỳ gia hạn', 0, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-10-11 00:00:00.000000', '<EMAIL>', '2021-10-11 00:00:00.000000', 'Sme admin, sme tạo thuê bao', 'SC-43', 'B', 'B', 'B',
        10006),
       ('Sắp đến kỳ gia hạn', 0, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-10-11 00:00:00.000000', '<EMAIL>', '2021-10-11 00:00:00.000000', 'Dev admin, dev tạo thuê bao', 'SC-44', 'B', 'B', 'B',
        10007),
       ('Sắp đến kỳ gia hạn', 0, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-10-11 00:00:00.000000', '<EMAIL>', '2021-10-11 00:00:00.000000', 'Dev admin, admin tỉnh, ban KHDN', 'SC-45', 'B', 'B', 'B',
        10008),
       ('Sắp đến kỳ thanh toán', 0, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-10-11 00:00:00.000000', '<EMAIL>', '2021-10-11 00:00:00.000000', 'Sme admin, sme tạo', 'SC-46', 'B', 'B', 'B', 10009),
       ('Sắp đến kỳ thanh toán', 0, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-10-11 00:00:00.000000', '<EMAIL>', '2021-10-11 00:00:00.000000', 'Dev admin, dev tạo', 'SC-47', 'B', 'B', 'B', 10010),
       ('Sắp đến kỳ thanh toán', 0, 0, 1, (SELECT id FROM action_notification WHERE name = 'Quản lý thuê bao'), 'system',
        '2021-10-11 00:00:00.000000', '<EMAIL>', '2021-10-11 00:00:00.000000', 'Dev admin, admin tỉnh, ban KHDN', 'SC-48', 'B', 'B', 'B',
        10011),
       ('Đăng ký subscription combo', 0, 1, 1, -1, 'system', '2021-03-18 00:00:00.000000', '<EMAIL>', '2021-10-31 00:00:00.000000', null,
        'SCB', null, null, null, 14000),
       ('SME đổi gói thành công ', 1, 1, 1, (SELECT id FROM action_notification WHERE name = 'Đăng ký subscription combo'), 'system',
        '2021-03-18 00:00:00.000000', '<EMAIL>', '2021-10-31 00:00:00.000000', 'Sme admin, sme đổi gói', 'SCB-20', 'B', 'B', 'B', 14002),
       ('SME hủy thuê bao thành công ', 1, 1, 1, (SELECT id FROM action_notification WHERE name = 'Đăng ký subscription combo'), 'system',
        '2021-03-18 00:00:00.000000', '<EMAIL>', '2021-10-31 00:00:00.000000', 'Sme admin, sme hủy thuê bao', 'SCB-28', 'B', 'B', 'B',
        14003),
       ('SME sửa thuê bao thành công', 1, 1, 1, (SELECT id FROM action_notification WHERE name = 'Đăng ký subscription combo'), 'system',
        '2021-03-18 00:00:00.000000', '<EMAIL>', '2021-10-31 00:00:00.000000', 'Sme admin, sme sửa dịch vụ', 'SCB-16', 'B', 'B', 'B',
        14001);