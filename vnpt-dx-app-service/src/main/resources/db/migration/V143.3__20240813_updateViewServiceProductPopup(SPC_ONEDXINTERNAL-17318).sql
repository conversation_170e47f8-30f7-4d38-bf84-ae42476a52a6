DROP VIEW IF EXISTS vnpt_dev.feature_view_service_product_popup;
CREATE VIEW vnpt_dev.feature_view_service_product_popup as
( SELECT 0 AS calculate_type,
         services.pre_order_url,
         concat(services.id, '0000')::bigint AS service_unique_id,
         services.id AS services_id,
         pricing.id AS pricing_id,
         concat(pricing.id, '0000')::bigint AS pricing_unique_id,
         pricing_multi_plan.id AS pricing_multi_plan_id,
         services.service_name,
         services.product_type,
         NULL :: TEXT AS object_type,
         services.services_draft_id AS draft_id,
         services.pricing_default,
         pricing.pricing_draft_id,
         ARRAY[services.categories_id] AS lst_category,
         COALESCE(services.service_owner::integer, 3) = ANY (ARRAY[0, 1]) AS is_on,
         services.user_id AS provider_id,
         pricing.pricing_name,
         COALESCE(pricing.number_of_trial::integer, 0) AS number_of_trial,
         COALESCE(pricing.trial_type::integer, 0) AS trial_type,
         CASE
             WHEN COALESCE(pricing.number_of_trial::integer, 0) > 0 THEN 1
             ELSE 0
             END AS is_trial,
         services.allow_multi_sub,
         string_to_array(translate(services.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
         CASE
             WHEN pricing.id IS NOT NULL THEN string_to_array(translate(pricing.customer_type_code::text, '[]"'::text, ''::text), ','::text)
             ELSE '{HKD,KHDN,CN}'::text[]
             END AS pricing_lst_customer_type,
         CASE
             WHEN pricing_multi_plan.id IS NOT NULL THEN string_to_array(translate(pricing_multi_plan.customer_type_code::text, '[]"'::text, ''::text), ','::text)
             ELSE '{HKD,KHDN,CN}'::text[]
             END AS pricing_multi_plan_lst_customer_type,
         CASE
             WHEN pricing_multi_plan.circle_type = 0 THEN concat(pricing_multi_plan.payment_cycle, ' ngày')
             WHEN pricing_multi_plan.circle_type = 1 THEN concat(pricing_multi_plan.payment_cycle, ' tuần')
             WHEN pricing_multi_plan.circle_type = 2 THEN concat(pricing_multi_plan.payment_cycle, ' tháng')
             WHEN pricing_multi_plan.circle_type = 3 THEN concat(pricing_multi_plan.payment_cycle, ' năm')
             WHEN pricing.cycle_type = 0 THEN concat(pricing.payment_cycle, ' ngày')
             WHEN pricing.cycle_type = 1 THEN concat(pricing.payment_cycle, ' tuần')
             WHEN pricing.cycle_type = 2 THEN concat(pricing.payment_cycle, ' tháng')
             WHEN pricing.cycle_type = 3 THEN concat(pricing.payment_cycle, ' năm')
             ELSE NULL::text
             END AS payment_cycle,
         COALESCE(pricing.is_one_time::integer, 1) AS is_one_time,
         services.service_owner,
         services.modified_at as modified_at
FROM vnpt_dev.services
         LEFT JOIN vnpt_dev.users ON users.id = services.created_by AND users.deleted_flag = 1 AND users.status = 1
         LEFT JOIN ( SELECT count(cpricing.id) AS count,
                            cpricing.service_id
                     FROM ( SELECT max(pricing_1.id) AS id,
                                   pricing_1.service_id
                            FROM vnpt_dev.pricing pricing_1
                            WHERE pricing_1.status = 1 AND pricing_1.approve = 1 AND pricing_1.deleted_flag = 1
                            GROUP BY pricing_1.pricing_draft_id, pricing_1.service_id) cpricing
                     GROUP BY cpricing.service_id) pcount ON pcount.service_id = services.id
         LEFT JOIN ( SELECT p.id,
                            p.pricing_code,
                            p.pricing_name,
                            p.description,
                            p.payment_cycle,
                            p.number_of_cycles,
                            p.cycle_type,
                            p.pricing_plan,
                            p.service_id,
                            p.unit_id,
                            p.currency_id,
                            p.amount,
                            p.setup_fee,
                            p.free_quantity,
                            p.estimate_quantity,
                            p.created_at,
                            p.created_by,
                            p.modified_at,
                            p.modified_by,
                            p.status,
                            p.deleted_flag,
                            p.price,
                            p.list_feature_id,
                            p.pricing_order,
                            p.recommended_status,
                            p.update_reason,
                            p.approve,
                            p.pricing_draft_id,
                            p.approve_time,
                            p.sme_pricing_id,
                            p.trial_type,
                            p.number_of_trial,
                            p.pricing_type,
                            p.has_change_price,
                            p.has_change_quantity,
                            p.has_refund,
                            p.cancel_date,
                            p.active_date,
                            p.duration_type,
                            p.department_id,
                            p.province_id,
                            p.update_subscription_date,
                            p.change_pricing_date,
                            p.is_change_now,
                            p.is_update_now,
                            p.seo_id,
                            p.has_renew,
                            p.customer_type_code,
                            p.priority,
                            p.type_active_in_payment_type,
                            p.payment_request,
                            p.is_one_time,
                            p.creation_layout_id,
                            p.change_pricing_payment_time
                     FROM vnpt_dev.pricing p
                     WHERE p.status = 1 AND p.deleted_flag = 1 AND p.approve = 1 AND (p.id IN ( SELECT max(p_1.id) AS max
FROM vnpt_dev.pricing_draft pd
         JOIN vnpt_dev.pricing p_1 ON pd.id = p_1.pricing_draft_id AND pd.deleted_flag = 1
GROUP BY pd.id))) pricing ON pcount.service_id = pricing.service_id AND (pricing.id IN (
         SELECT max(pricing_1.id) AS id
         FROM vnpt_dev.pricing pricing_1
         WHERE pricing_1.status = 1 AND pricing_1.approve = 1 AND pricing_1.deleted_flag = 1
         GROUP BY pricing_1.pricing_draft_id)) AND (pricing.pricing_draft_id IN ( SELECT pricing_draft.id
                                                                                  FROM vnpt_dev.pricing_draft
                                                                                  WHERE pricing_draft.deleted_flag = 1))
         LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.pricing_id = pricing.id AND pricing_multi_plan.deleted_flag = 1 AND pricing_multi_plan.display_status = 1
WHERE services.deleted_flag = 1 AND services.status = 1 AND services.approve = 1 AND (pricing.id IS NOT NULL OR services.product_type = 1) AND (services.product_type = 1 OR (services.product_type <> 1 OR services.product_type IS NULL) AND pcount.count <> 0) AND (services.categories_app IS NOT NULL OR services.categories_id IS NOT NULL)
ORDER BY services.id, pricing.id, pricing_multi_plan.id);