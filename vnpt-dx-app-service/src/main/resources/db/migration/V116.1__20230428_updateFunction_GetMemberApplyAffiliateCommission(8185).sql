drop function if exists vnpt_dev.func_get_member_apply_affiliate_commission;
create or replace function vnpt_dev.func_get_member_apply_affiliate_commission(i_role_name character varying, i_current_user_id bigint) 
returns table(
    userid bigint,
    userlevel smallint,
    username character varying,
    useremail character varying) 
language plpgsql as $function$ 
begin
    --<PERSON><PERSON> tổng 
    if i_role_name like 'FULL_ADMIN' then 
        begin
        --L<PERSON>y ra tất cả affiliate_user 
            return query ( 
                select users.id, affiliate_users.affiliate_level, users.name, users.email 
                from vnpt_dev.affiliate_users 
                    join vnpt_dev.users on affiliate_users.user_id = users.id 
                where users.deleted_flag = 1 and affiliate_users.affiliate_status = 2 
            ); 
        end; 
    --Đại lý affiliate 
    else 
        begin 
             --<PERSON><PERSON> quy để lấy ra đại lý và tất cả thành viên con 
            return query (
                with recursive affiliate_users_all_level_cte as ( 
                    select user_id, affiliate_code, affiliate_level, affiliate_status 
                    from vnpt_dev.affiliate_users 
                    where user_id = i_current_user_id 
                    union all 
                    select 
                        aff_users_child.user_id, 
                        aff_users_child.affiliate_code, 
                        aff_users_child.affiliate_level, 
                        aff_users_child.affiliate_status 
                    from vnpt_dev.affiliate_users aff_users_child 
                        join affiliate_users_all_level_cte aff_users_parent on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
                ) 
                --Chỉ lấy ra các thành viên cấp dưới và đã được duyệt 
                select users.id, aff_multi_level.affiliate_level, users."name", users.email 
                from affiliate_users_all_level_cte aff_multi_level 
                    join vnpt_dev.users on aff_multi_level.user_id = users.id 
                where users.deleted_flag = 1 and users.id != i_current_user_id and 
                    aff_multi_level.affiliate_status = 2 
            ); 
        end; 
    end if; 
end $function$;