--DROP TRIGGER IF EXISTS check_duplicate_tin ON vnpt_dev.users CASCADE;
--DROP FUNCTION IF EXISTS prevent_duplicate_tin() CASCADE;
CREATE OR REPLACE FUNCTION vnpt_dev.prevent_duplicate_tin()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM vnpt_dev.users
        WHERE tin = NEW.tin AND id != NEW.id
    ) THEN
        RAISE EXCEPTION 'TIN already exists: %', NEW.tin;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_duplicate_tin
BEFORE INSERT OR UPDATE ON vnpt_dev.users
FOR EACH ROW
EXECUTE FUNCTION vnpt_dev.prevent_duplicate_tin();


