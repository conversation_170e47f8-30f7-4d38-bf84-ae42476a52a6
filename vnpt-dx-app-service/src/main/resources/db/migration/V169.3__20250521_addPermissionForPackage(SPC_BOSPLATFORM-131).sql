DELETE FROM vnpt_dev.permission WHERE code IN ('PG_QUAN_LY_PACKAGE', 'P_QLGB_XEM_DANH_SACH_PACKAGE',
    'P_QLGB_TAO_PACKAGE', 'P_QLGB_XEM_CHI_TIET_PACKAGE',
    'P_QLGB_CAP_NHAT_PACKAGE', 'P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE',
    'P_QLGB_PHE_DUYET_PACKAGE', 'P_QLGB_XOA_PACKAGE');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
    
-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES 
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Quản lý gói bundling',
        'PG_QUAN_LY_PACKAGE',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES 
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Xem danh sách gói bundling',
        'P_QLGB_XEM_DANH_SACH_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.permission),
        'Tạo gói bundling',
        'P_QLGB_TAO_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 2 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.permission),
        'Xem chi tiết gói bundling',
        'P_QLGB_XEM_CHI_TIET_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 3 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.permission),
        'Cập nhật gói bundling',
        'P_QLGB_CAP_NHAT_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 4 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.permission),
        'Yêu cầu phê duyệt',
        'P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 5 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.permission),
        'Phê duyệt gói bundling',
        'P_QLGB_PHE_DUYET_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 6 from vnpt_dev.permission)
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.permission),
        'Xóa gói bundling',
        'P_QLGB_XOA_PACKAGE',
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        (SELECT max(priority) + 7 from vnpt_dev.permission)
    );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES 
    (   
        (SELECT max(id) + 1 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 2 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_DANH_SACH_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 3 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_TAO_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 4 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_CHI_TIET_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 5 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_CAP_NHAT_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 6 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 7 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_PHE_DUYET_PACKAGE'),
        1
    ),
    (   
        (SELECT max(id) + 8 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_XOA_PACKAGE'),
        1
    );

-- Thêm permission vào dev portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES 
    (   
        (SELECT max(id) + 1 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        2
    ),
    (   
        (SELECT max(id) + 2 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_DANH_SACH_PACKAGE'),
        2
    ),
    (   
        (SELECT max(id) + 3 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_TAO_PACKAGE'),
        2
    ),
    (   
        (SELECT max(id) + 4 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_CHI_TIET_PACKAGE'),
        2
    ),
    (   
        (SELECT max(id) + 5 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_CAP_NHAT_PACKAGE'),
        2
    ),
    (   
        (SELECT max(id) + 6 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE'),
        2
    ),
    (   
        (SELECT max(id) + 7 from vnpt_dev.permission_portal),
        (SELECT id from vnpt_dev.permission WHERE code = 'P_QLGB_XOA_PACKAGE'),
        2
    );
    
-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES 
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_DANH_SACH_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_TAO_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_CHI_TIET_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_CAP_NHAT_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_PHE_DUYET_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 8 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_XOA_PACKAGE'),
        0
    );
    
-- Thêm permission vào role FULL_DEV
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES 
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'PG_QUAN_LY_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_DANH_SACH_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_TAO_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_XEM_CHI_TIET_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_CAP_NHAT_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE'),
        0
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.roles_permissions), 
        (select id from vnpt_dev.role WHERE name = 'FULL_DEV'), 
        (select id from vnpt_dev.permission WHERE code = 'P_QLGB_XOA_PACKAGE'),
        0
    );

-- Cập nhật view phân quyền
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;