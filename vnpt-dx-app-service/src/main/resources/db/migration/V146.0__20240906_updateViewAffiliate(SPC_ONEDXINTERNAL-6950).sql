drop function if E<PERSON>ISTS "vnpt_dev"."func_get_affiliate_child_by_role_user_id_and_user_change_parent";
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_affiliate_child_by_role_user_id_and_user_change_parent"("i_role_name" varchar, "i_current_user_id" int8)
  RETURNS TABLE("user_id" int8) AS $BODY$
begin
	--Role full_admin
	if
i_role_name like 'FULL_ADMIN' then
begin
			--Lấy ra tất cả affiliate_link
return query(select affiliate_users.user_id from vnpt_dev.affiliate_users);
end;
	--Đại lý affiliate
	elseif
i_role_name like 'ROLE_AFFILIATE_DAILY' then
begin
return query(
    --Đ<PERSON> quy để lấy ra đại lý và tất cả thành viên con
    with recursive affiliate_users_all_level_cte as(
					select affiliate_users.user_id, affiliate_users.affiliate_code from vnpt_dev.affiliate_users affiliate_users where affiliate_users.user_id = i_current_user_id
					union
					select aff_users_child.user_id, aff_users_child.affiliate_code
					from vnpt_dev.affiliate_users aff_users_child
					join affiliate_users_all_level_cte aff_users_parent on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
		)
		select
			distinct data.user_id
		from
		(
			(
				select
					aff_all_level.user_id
				from affiliate_users_all_level_cte aff_all_level
			)
			union
			--lấy cả những user đã chuyển cấp nhưng trc đó là con của current user và có giới thiệu thành công đơn hàng
			(
				select
				  user_id_intro
				from vnpt_dev.affiliate_commission_event ace
				where ace.user_id = i_current_user_id
			)
		) as data
    );
end;
	--Cá nhân affiliate
else
begin
			--Lấy ra các affiliate link được gán cho cá nhân
return query(select affiliate_users.user_id from vnpt_dev.affiliate_users where affiliate_users.user_id = i_current_user_id);
end;
end if;
end $BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;