-- tạo bảng "product_orders"
DROP TABLE IF EXISTS "vnpt_dev"."product_orders" CASCADE;
CREATE TABLE "vnpt_dev"."product_orders" (
     id bigserial primary key ,
     subscription_id int8 unique ,
     cart_code varchar(50) unique ,
     order_addresses jsonb,
     has_installation boolean default null,
     preferred_installation_date date,
     order_progress int2,
     payment_policy jsonb,
     payment_status varchar(255),
     assignee_id int8,
     employee_code varchar(50) default null,
     delivery_method int2,
     notes text
);

COMMENT ON TABLE "vnpt_dev"."product_orders" IS 'Quản lý các thông tin bổ sung của đơn hàng order';

COMMENT ON COLUMN "vnpt_dev"."product_orders"."id" IS 'Id bảng';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."subscription_id" IS 'ID thuê bao (trong trường hợp mua đơn hàng chỉ có 1 hàng hóa)';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."cart_code" IS 'Mã giỏ hàng (trong trường hợp mua đơn hàng của gói bundling hoặc từ giỏ hàng có nhiều hàng hóa)';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."order_addresses" IS 'Danh sách các địa chỉ ứng với đơn hàng';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."has_installation" IS 'Đơn hàng có yêu cầu lắp đặt hay không (true: có yêu cầu lắp đặt, false: không yêu cầu lắp đặt)';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."preferred_installation_date" IS 'Ngày lắp đặt mong muốn';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."order_progress" IS 'Trạng thái đơn hàng tổng của thuê bao (trạng thái tổng quát của tất cả các thuê bao thành phần) 1-Tiếp nhận đơn hàng, 2-Đang xử lý, 3-Đã hoàn tất, 4-Đã hủy';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."payment_policy" IS 'Cấu hình thanh toán của đơn hàng';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."payment_status" IS 'Trạng thái thanh toán của đơn hàng';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."assignee_id" IS 'ID nhân viên kinh doanh phụ trách đơn hàng';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."employee_code" IS 'Mã nhân viên giơi thiệu';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."delivery_method" IS 'Phương thức giao hàng (1-Giao hàng tiêu chuân , 2-Giao hàng nhanh, 3-Giao hàng hỏa tốc)';
COMMENT ON COLUMN "vnpt_dev"."product_orders"."notes" IS 'Thông tin lời nhắn ứng với đơn hàng';

-- tạo index tương ứng với 2 field là subscription_id và cart_code --
CREATE UNIQUE INDEX product_orders_subscription_id_idx
    ON "vnpt_dev"."product_orders" using btree ("subscription_id");
CREATE UNIQUE INDEX product_orders_cart_code_idx
    ON "vnpt_dev"."product_orders" using btree ("cart_code");

ALTER TABLE "vnpt_dev"."subscription_metadata"
    ADD COLUMN IF NOT EXISTS "product_order_id" int8,
    ADD COLUMN IF NOT EXISTS "order_progress_code" int8,
    ADD COLUMN IF NOT EXISTS "order_item_status" int4;

COMMENT ON COLUMN "vnpt_dev"."subscription_metadata"."product_order_id" IS 'ID bảng product_orders';
COMMENT ON COLUMN "vnpt_dev"."subscription_metadata"."order_progress_code" IS 'Các trạng thái tiến trình đơn hàng thiết bị hàng hóa:
Không lắp đặt
requested_warehouse, preparing_stock, ready_for_shipping, pending_shipping, shipped, delivered, online_verified, cancelled
Có lắp đặt:
1: requested_warehouse, 2: preparing_stock, 3: ready_for_shipping, 4: pending_shipping, 5: shipped, 6: delivered, 7: waiting_installation, 8: in_installation, 9: installed, 10: online_verified, 11: cancelled';

COMMENT ON COLUMN "vnpt_dev"."subscription_metadata"."order_item_status" IS
    'Trạng thái của thuê bao thành phần (trong đơn hàng order) theo bộ trạng thái mới (Khai báo chi tiết trong enum OrderItemStatusEnum):
    * null/-1 : Không xác định,
    * 1xx - Hàng hóa vật lý/ thiết bị không lắp đặt,
    * 2xx - Hàng hóa vật lý/ thiết bị có lắp đặt,
    * 3xx - SAAS ON: tương đường với trạng thái cài đặt installed của thuê bao
    * 4xx - SAAS OS: VNPT/3rd Party được chuyển đổi từ bộ trạng thái trong order_receive_status -> bộ trạng thái rút gọn tương tự với sme_progress';

-- TODO: Convert/Migrate trạng thái của thành phần trong đơn hàng order (hiện tại) theo bộ trạng thái mới vào các cột mới định nghĩa