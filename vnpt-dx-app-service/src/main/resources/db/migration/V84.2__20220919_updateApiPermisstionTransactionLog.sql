DELETE FROM vnpt_dev.roles_permissions WHERE role_id = (SELECT id FROM vnpt_dev.ROLE WHERE name = 'ROLE_ADMIN_A_CT') AND permission_id = (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CAU_HINH_XOA_TRANSACTION_LOG');

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CAU_HINH_XOA_TRANSACTION_LOG'), 1);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/activity/getAll', 'ROLE_ADMIN_XEM_DANH_SACH_TRANSACTION_DROP_DOWN_LIST', 'GET');
INSERT INTO vnpt_dev."permission" (id, "name", code, parent_id, priority)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem danh transaction drop down list', 'XEM_DANH_SACH_TRANSACTION_DROP_DOWN_LIST', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_TRANSACTION_LOG'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));
INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id)
VALUES ((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_TRANSACTION_DROP_DOWN_LIST'), 1);
INSERT INTO vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/activity/getAll'), (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XEM_DANH_SACH_TRANSACTION_DROP_DOWN_LIST' AND pp.portal_id = 1 LIMIT 1), 1, 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_TRANSACTION_DROP_DOWN_LIST'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'ROLE_ADMIN_A'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_TRANSACTION_DROP_DOWN_LIST'), 1);

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;