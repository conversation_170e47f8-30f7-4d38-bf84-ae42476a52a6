alter table vnpt_dev.subscriptions
add column pre_order json;
comment on column vnpt_dev.subscriptions.pre_order is 'Thông tin pre_order';

alter table vnpt_dev.services
add column pre_order_url varchar(200);
comment on column vnpt_dev.services.pre_order_url is 'url để nhúng iframe với các bên liên quan';

alter table vnpt_dev.credit_note
drop column if exists reason_refund;

drop table if exists vnpt_dev.reason_credit_note cascade;
create table vnpt_dev.reason_credit_note
(
    id      bigserial not null primary key,
	credit_note_id int8,
	reason varchar(200),
	content varchar(200),
    created_at timestamp,
    created_by  varchar(100),
	modified_at timestamp,
	modified_by varchar(100),
	deleted_flag int2
);
comment on table vnpt_dev.reason_credit_note is 'Lý do được hoàn trả';
comment on column vnpt_dev.reason_credit_note.credit_note_id is 'Id credit_note';
comment on column vnpt_dev.reason_credit_note.reason is '<PERSON><PERSON> do được hoàn trả';
comment on column vnpt_dev.reason_credit_note.content is 'Hiển thị nội dung tương ứng với tưng trường hợp refund';
comment on column vnpt_dev.reason_credit_note.created_at is 'Ngày tạo';
comment on column vnpt_dev.reason_credit_note.created_by is 'Id người tạo';
comment on column vnpt_dev.reason_credit_note.modified_at is 'Ngày chỉnh sửa';
comment on column vnpt_dev.reason_credit_note.modified_by is 'Id người sửa';

comment on column vnpt_dev.pricing.has_change_price is 'Cho phép thay đổi giá 0: không, 1: có';
comment on column vnpt_dev.pricing.has_change_quantity is '0: Không cho phép 1: Cho phép tăng 2: Cho phép giảm 3: Cho phép tăng và giảm';