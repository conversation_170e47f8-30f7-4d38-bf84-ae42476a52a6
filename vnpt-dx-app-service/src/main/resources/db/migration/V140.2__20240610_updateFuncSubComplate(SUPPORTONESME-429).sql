ALTER TABLE "vnpt_dev"."billings"
    ADD COLUMN IF NOT EXISTS "setup_contract_complete_date" date;

COMMENT ON COLUMN "vnpt_dev"."billings"."setup_contract_complete_date" IS 'Ngày hoàn thành đơn hàng ( OS mua mới lấy theo tr<PERSON> nà<PERSON>, ON lấy theo trường payment_date)';

DROP VIEW IF EXISTS vnpt_dev.view_report_sub_bills;
DROP VIEW IF EXISTS vnpt_dev.get_report_subscriptions_setup_complete;
drop view if exists vnpt_dev.view_spc_supportonesme_429;
CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_bills AS
(
SELECT bill.id,
       bill.action_type,
       bill.status,
       bill.payment_date,
       bill.created_at,
       bill.billing_code,
       bill.subscriptions_id,
       bill.total_amount,
       bill.billing_date,
       bill.total_amount_after_adjustment,
       max(invoice.created_at)        AS created_export_invoice,
       string_agg(invoice.code, '; ') AS code,
       bill.portal_type               AS portal_type,
       bill.created_by                AS created_by,
       bill.end_date,
       bill.end_date_new_renewal,
       CASE
           -- bill_action_type: 0: tạo mới, 1: sửa, 2: đổi, 3, 4: kích hoạt lại,  5: gia hạn,
           WHEN bill.action_type = -1 OR (bill.action_type IS NULL AND bill.created_by <> 'batch') THEN 0 -- thuê bao tạo mới
           WHEN bill.action_type = 1 THEN 1 -- thuê bao sửa
           WHEN bill.action_type = 2 THEN 2 -- thuê bao đổi gói
           WHEN bill.action_type IN (3, 4) THEN 3 -- thuê bao kích hoạt lại
           WHEN bill.action_type = 5 OR (bill.action_type IS NULL AND bill.created_by = 'batch') THEN 5 -- thuê bao gia hạn
           ELSE bill.action_type
           END                                  AS bill_action_type,
       affAgency.aff_user_id         AS aff_agency_user_id,
       affAgency.affiliate_code      AS aff_agency_code,
       bill.setup_contract_complete_date
FROM vnpt_dev.billings AS bill
         LEFT JOIN vnpt_dev.e_invoice AS invoice ON bill.id = invoice.billing_id
         LEFT JOIN vnpt_dev.affiliate_bill_commission_note AS affAgency ON bill.id = affAgency.bill_id AND affAgency.affiliate_level = 1
WHERE bill.status in (0, 1, 2, 3, 4)
GROUP BY bill.id, affAgency.aff_user_id, affAgency.affiliate_code
    );


-- func get_report_subscriptions_setup_complete --
CREATE OR REPLACE FUNCTION vnpt_dev.get_report_subscriptions_setup_complete("i_start_date" varchar, "i_end_date" varchar)
    RETURNS TABLE
            (
                id                     int8,
                subcode                varchar,
                createdat              timestamp,
                provincename           varchar,
                subsstatus             text,
                smename                text,
                customertype           text,
                taxtno                 varchar,
                address                varchar,
                street                 varchar,
                ward                   varchar,
                district               varchar,
                province               varchar,
                nation                 varchar,
                phoneno                varchar,
                email                  varchar,
                servicename            varchar,
                provider               varchar,
                pricingname            varchar,
                numberofcycle          int2,
                planpaymentcycle       int2,
                plancycletype          text,
                multipaymentcycle      int8,
                multicycletype         text,
                subsinstalled          text,
                smeprogressname        varchar,
                subscriptiontype       text,
                modifieat              timestamp,
                serviceownertype       text,
								setupContractCompleteDate timestamp,
                registedby             text,
                trafficid              varchar,
                trafficuser            varchar,
                employeecode           varchar,
                dhsxkdsubcode          varchar,
                createdsource          text,
                migratetime            timestamp,
                migratecode            varchar,
                billstatus             text,
                billcode               varchar,
                subscriptionstate      int4,
                state                  text,
                registrationdate       timestamp,
                startat                timestamp,
                dhsxkdcode             varchar,
                paymentcycle           int8,
                cycletype              text,
                installedstatus        text,
                status                 text,
                creator                varchar,
                paytransactioncode     varchar,
                promotionamount        float8,
                unitamount             float8,
                preamounttax           float8,
                amounttax              float8,
                afteramounttax         float8,
                createdsourcemigration int4,
                setupcode              varchar,
                paymentdate            timestamp,
                createdexportinvoice   timestamp,
                codeinvoice            text,
                cancelledtime          timestamp,
                isonetime              int2,
                affiliatecode          varchar,
                affagencyuserid         int8,
                affagencyname           varchar,
                affagencycode           varchar,
                endcurrentcycle        date,
                identityno             varchar,
                billId                 int8
            )
AS
$BODY$

DECLARE
run_query text;
    raw_query
varchar = '
    WITH subReportCTE AS (
     SELECT
           sub.id AS id,
           sub.sub_code AS subCode,
           sub.created_at AS createdAt,
           sme.province AS provinceName,
           CASE
               WHEN sub.status = -1 THEN ''NOT_SET''
               WHEN sub.status = 0 THEN ''FUTURE''
               WHEN sub.status = 1 THEN ''IN_TRIAL''
               WHEN sub.status = 2 THEN ''ACTIVE''
               WHEN sub.status = 3 THEN ''CANCELED''
               WHEN sub.status = 4 THEN ''NON_RENEWING''
           END AS subsStatus,
           sme.name AS smeName,
           sme.customer_type AS customerType,
           sme.tin AS taxtNo,
           sme.address AS address,
           sme.street AS street,
           sme.ward AS ward,
           sme.district AS district,
           sme.province AS province,
           sme.nation AS nation,
           sme.phone_number AS phoneNo,
           sme.email AS email,
           COALESCE(subServices.service_name, subCombo.service_name) AS serviceName,
           COALESCE(provider.name, '''') AS provider,
           COALESCE(subServices.pricing_name, subCombo.pricing_name) AS pricingName,
           sub.number_of_cycles AS numberOfCycle,
           COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle) AS planPaymentCycle,
           COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type) AS planCycleType,
           prcMultiPlan.payment_cycle AS multiPaymentCycle,
           CASE
                    WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
                    WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
                    WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
                    WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
                    ELSE ''''
           END AS multiCycleType,
           CASE
                    WHEN sub.installed IS NULL AND sub.installed = 0 THEN ''Đang cài đặt''
                    WHEN sub.installed = 1 THEN ''Đã cài đặt''
                    WHEN sub.installed = 2 THEN ''Gặp sự cố''
                    ELSE ''''
           END AS subsInstalled,
           sme_progress.name AS smeProgressName,
           COALESCE(subServices.sub_type, subCombo.sub_type) AS subscriptionType,
           sub.modified_at AS modifiedAt,
           COALESCE(subServices.service_owner_type, subCombo.service_owner_type) AS serviceOwnerType,
					 CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' and bill.action_type <> 5 THEN bill.setup_contract_complete_date
               ELSE bill.payment_date
           END setupContractCompleteDate,
           CASE
                     WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                     WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                     ELSE ''OneSME''
           END AS registedBy,
           sub.traffic_id  AS trafficId,
           sub.traffic_user AS trafficUser,
           sub.employee_code AS employeeCode,
           sub.dhsxkd_sub_code AS dhsxkdSubCode,
           CASE
               WHEN sub.created_source_migration = 1 THEN ''ĐHSXKD''
               WHEN sub.traffic_source = ''accesstrade'' THEN ''Affiliate AccessTrade''
               WHEN sub.traffic_source = ''apinfo'' THEN ''Affiliate Apinfo''
               WHEN sub.affiliate_one IS NOT NULL THEN ''Affiliate Onesme''
               WHEN sub.traffic_id IS NOT NULL THEN ''Affiliate Masoffer''
               WHEN sub.employee_code IS NOT NULL THEN ''AM G.Thiệu''
               WHEN sub.portal_type IN (1,2) OR (bill.portal_type IS NOT NULL AND bill.portal_type IN (1,2)) THEN ''Dev/Admin''
               ELSE ''oneSME''
           END AS createdSource,
           sub.migrate_time AS migrateTime,
           sub.migrate_code AS migrateCode,
           CASE
               WHEN bill.status = 0 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Khởi tạo''
               WHEN bill.status = 1 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Chờ thanh toán''
               WHEN bill.status = 2 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Đã thanh toán''
               WHEN bill.status = 3 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Thanh toán thất bại''
               WHEN bill.status = 4 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Quá hạn thanh toán''
               WHEN osServiceReceive.payment_status = ''1'' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Đã thanh toán''
               WHEN osServiceReceive.payment_status = ''0'' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Chờ thanh toán''
               WHEN osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL AND
                    COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Chờ thanh toán''
               ELSE ''Chờ thanh toán''
           END AS billStatus,
           bill.billing_code AS billCode,
           CASE
               WHEN bill.bill_action_type = 3 THEN 0
               ELSE bill.bill_action_type
           END AS subscriptionState,
           CASE
               WHEN bill.bill_action_type = 0 then ''Đăng ký mới''
               WHEN bill.bill_action_type = 1 then ''Chỉnh sửa''
               WHEN bill.bill_action_type = 2 THEN ''Đổi gói''
               WHEN bill.bill_action_type = 3 then ''Kích hoạt''
               WHEN bill.bill_action_type = 5 then ''Gia hạn''
               ELSE ''''
           END AS state,
           CASE
               WHEN bill.bill_action_type = 0 THEN sub.created_at
               ELSE COALESCE(bill.payment_date, bill.created_at)
           END AS registrationDate,
           CASE
               WHEN bill.bill_action_type = 0 THEN sub.started_at
               ELSE COALESCE(bill.payment_date, CAST(bill.created_at AS DATE))
           END AS startAt,
           CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN sub.dhsxkd_sub_code
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN osServiceReceive.transaction_code
               ELSE ''''
           END  AS dhsxkdCode,
           CASE
               WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND subServices.plan_payment_cycle IS NULL THEN prcMultiPlan.payment_cycle
               ELSE COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle)
           END AS paymentCycle,
           CASE
               WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND subServices.plan_cycle_type IS NULL THEN
                   (
                     CASE
                            WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
                            WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
                            WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
                            WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
                            ELSE ''''
                     END
                    )
               ELSE COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type)
           END AS cycleType,
           CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN (
                    CASE
                        WHEN sub.installed IS NULL OR sub.installed = 0 THEN ''Đang cài đặt''
                        WHEN sub.installed = 1 THEN ''Đã cài đặt''
                        WHEN sub.installed = 2 THEN ''Gặp sự cố''
                        ELSE ''''
                    END)
                         WHEN COALESCE(subServices.service_owner, subCombo.combo_owner) = 2 THEN (
                                    CASE
                                        WHEN sub.os_3rd_status = 1 THEN ''Tiếp nhận đơn hàng''
                                        WHEN sub.os_3rd_status = 2 THEN ''Đang triển khai''
                                        WHEN sub.os_3rd_status = 3 THEN ''Hoàn thành''
                                        WHEN sub.os_3rd_status = 4 THEN ''Huỷ đơn hàng''
                                        WHEN sub.os_3rd_status = 5 THEN ''Đặt hàng thành công''
                                        ELSE ''''
                                    END
                              )
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN COALESCE(sme_progress.name , '''')
               ELSE ''''
           END AS installedStatus,
           CASE
               WHEN sme_progress.name = ''Hủy'' THEN ''CANCELED''
               ELSE (
                        CASE
                            WHEN sub.status = -1 THEN ''NOT_SET''
                            WHEN sub.status = 0 THEN ''FUTURE''
                            WHEN sub.status = 1 THEN ''IN_TRIAL''
                            WHEN sub.status = 2 THEN ''ACTIVE''
                            WHEN sub.status = 3 THEN ''CANCELED''
                            WHEN sub.status = 4 THEN ''NON_RENEWING''
                        END
                   )
           END AS status,
           CASE
               WHEN sub.traffic_id IS NULL THEN (
                    CASE
                         WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                         WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                         WHEN sub.portal_type = 3 THEN ''OneSME''
                         ELSE ''''
                    END
               )
               WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id
               ELSE sub.traffic_user
           END AS creator,
           billVnptPay.transaction_code AS payTransactionCode,
           CASE
                WHEN billPayments.promotion_amount < 0 THEN 0
                ELSE billPayments.promotion_amount
           END AS promotionAmount,
           CASE
                WHEN billPayments.unit_amount < 0 THEN 0
                ELSE billPayments.unit_amount
           END AS unitAmount,
           CASE
               WHEN bill.action_type = 1 THEN billPayments.amount_change
               WHEN bill.action_type <> 1 AND billPayments.pre_amount_tax > 0 THEN billPayments.pre_amount_tax
               ELSE 0
               END AS preAmountTax,
           CASE
                WHEN billPayments.amount_tax > 0 then billPayments.amount_tax
                ELSE 0
           END AS amountTax,
           CASE
                WHEN billPayments.after_amount_tax > 0 then billPayments.after_amount_tax
                ELSE 0
           END AS afterAmountTax,
           CASE
			    WHEN sub.created_source_migration = 1 THEN 5
			    WHEN sub.traffic_source = ''accesstrade'' THEN 6
			    WHEN sub.traffic_source = ''apinfo'' THEN 8
			    WHEN sub.affiliate_one IS NOT NULL THEN 7
			    WHEN sub.traffic_id IS NOT NULL THEN 3
			    WHEN sub.employee_code IS NOT NULL THEN 2
			    WHEN sub.portal_type IN (1,2) THEN 4
			    ELSE 1
		   END AS createdSourceMigration,
           osServiceReceive.setup_code AS setupCode,
           CASE
               WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                   AND CAST(bill.created_at AS DATE) = bill.billing_date AND COALESCE(subServices.pricing_type, subCombo.pricing_type) = 0
                   THEN bill.created_at
               ELSE bill.payment_date
           END paymentDate,
           bill.created_export_invoice AS createdExportInvoice,
           bill.code AS codeInvoice,
           CASE
               WHEN sme_progress.name = ''Hủy'' OR sub.status IN (3, 4) THEN sub.cancelled_time
               END AS cancelledTime,
           sub.is_one_time AS isOneTime,
           CASE
               WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user
               WHEN sub.traffic_source = ''accesstrade'' THEN sub.traffic_user
               WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one
           END AS affiliateCode,
           bill.aff_agency_user_id AS affAgencyUserId,
           COALESCE(affiliateAgency.name, CONCAT('' '', affiliateAgency.last_name, affiliateAgency.first_name)) AS affAgencyName,
           COALESCE(bill.aff_agency_code, '''') AS affAgencyCode,
           CASE
               WHEN bill.bill_action_type = 5 THEN bill.end_date_new_renewal
               ELSE sub.end_current_cycle
           END AS endCurrentCycle,
           sme.identity_no AS identityNo,
           bill.id AS billId
        FROM vnpt_dev.subscriptions sub
            LEFT JOIN vnpt_dev.view_report_sub_sme AS sme ON sub.user_id = sme.id
            LEFT JOIN vnpt_dev.users AS register ON sub.registed_by = register.id
            LEFT JOIN vnpt_dev.pricing_multi_plan prcMultiPlan ON prcMultiPlan.id = sub.pricing_multi_plan_id
            LEFT JOIN vnpt_dev.order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id
            LEFT JOIN vnpt_dev.order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)
            LEFT JOIN vnpt_dev.sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id
            LEFT JOIN vnpt_dev.view_report_sub_services AS subServices ON sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id
            LEFT JOIN vnpt_dev.view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id
            LEFT JOIN vnpt_dev.view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id
            LEFT JOIN vnpt_dev.view_report_sub_bill_payment AS billPayments ON bill.id = billPayments.id
            LEFT JOIN vnpt_dev.view_report_sub_vnpt_pay_response AS billVnptPay ON bill.id = billVnptPay.bill_id
            LEFT JOIN vnpt_dev.users AS provider ON COALESCE(subServices.provider_id, subCombo.provider_id) = provider.id
            LEFT JOIN vnpt_dev.users AS affiliateAgency ON affiliateAgency.id = bill.aff_agency_user_id
        WHERE
              sub.deleted_flag = 1 AND
              sub.confirm_status = 1 AND
              (COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) -- ON lay het, OS chi lay neu co trang thai
				OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 3)
				OR (sub.os_3rd_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 2)) AND
              ((bill.bill_action_type <> 5 OR COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' -- ẩn Gia hạn, OS, chưa thanh toán --
                OR NOT ((osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL) or osServiceReceive.payment_status = ''0'')))

)
SELECT * FROM subReportCTE
				WHERE (subReportCTE.setupContractCompleteDate >= CAST(''%1$s'' AS TIMESTAMP)) AND
            (subReportCTE.setupContractCompleteDate <= CAST(''%2$s'' AS TIMESTAMP))
        ORDER BY subReportCTE.setupContractCompleteDate DESC';

BEGIN
    run_query
= format(raw_query,i_start_date, i_end_date);
    RAISE
NOTICE 'Run query: %', run_query;
RETURN QUERY EXECUTE run_query;
END
$BODY$
LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;

create view vnpt_dev.view_spc_supportonesme_429 as
select
    'Tỉnh thành' as provinceName,
    'Loại thuê bao' as subType,
    'Tên khách hàng' as customerName,
    'Loại khách hàng' as customerType,
    'Mã số thuế' as taxNo,
    'Số chứng từ' as identityNo,
    'Địa chỉ' as address,
    'SĐT' as phoneNo,
    'Email' as email,
    'Mã nhân viên giới thiệu' as employeeCode,
    'Dịch vụ' as serviceName,
    'Nhà cung cấp' as providerName,
    'Trạng thái dịch vụ' as subscriptionStatus,
    'Thời gian hoàn thành đơn hàng' as subscriptionCompletionTime,
    'Gói dịch vụ' as pricingName,
    'ON/OS' as onOs,
    'Ngày đăng ký/ Ngày gia hạn' as registrationDate,
    'Ngày bắt đầu sử dụng' as startUsingDate,
    'Ngày kết thúc (oneSME)' as endDate,
    'Ngày đến hạn gia hạn (DHSXKD)' as renewalDate,
    'Trạng thái gia hạn' renewalStatus,
    'Ngày thanh toán' as paymentDate,
    'Ngày hủy' as cancelledDate,
    'Số chu kỳ' as numCycle,
    'Chu kỳ thanh toán' as paymentCycle,
    'Giá cước dịch vụ/gói cước' as price,
    'Số tiền khuyến mại' as promotionAmount,
    'Số tiền đã thanh toán (chưa thuế)' as preTaxAmount,
    'Số tiền nộp thuế' as taxAmount,
    'Số tiền thanh toán (đã có thuế)' as afterTaxAmount,
    'Mã giao dịch với PAY' as PAYTransactionCode,
    'Mã giao dịch với DHSX' as DHSXTransactionCode,
    'Ngày xuất hóa đơn điện tử' as eInvoiceCreationDate,
    'Số hóa đơn điện tử' as eInvoiceNo,
    'Đơn hàng tạo bởi' as createdBy,
    'Mã đơn hàng' as subscriptionCode,
    'Mã lắp đặt' as setupCode,
    'Mã hóa đơn' as billingCode,
    'Trạng thái thanh toán' as paymentStatus,
    'Nguồn tạo' as creationSource,
    'Tên đại lý affiliate' as affiliateAgentName,
    'Mã đại lý cấp 1' as affiliate1stAgentName,
    'Mã thành viên affiliate giới thiệu' as affiliateMemberCode,
    'Mã đồng bộ' as migrationCode,
    'Thời gian đồng bộ' as migrationTime
union all
select
    provinceName as provinceName, -- Tỉnh thành
    state as subType, -- Loại thuê bao
    smeName as customerName, -- Tên khách hàng
    customerType as customerType, -- Loại khách hàng
    taxtNo as taxNo, -- Mã số thuế
    identityNo as identityNo, -- Số chứng từ
    address as address, -- Địa chỉ
    phoneNo as phoneNo, -- SĐT
    email ,  -- Email
    employeeCode as email, -- Mã nhân viên giới thiệu
    serviceName as employeeCode, -- Dịch vụ
    provider as serviceName, -- Nhà cung cấp
    status as providerName, -- Trạng thái dịch vụ
    null as subscriptionStatus, -- Thời gian hoàn thành đơn hàng
    pricingName as subscriptionCompletionTime, -- Gói dịch vụ
    serviceOwnerType as pricingName, -- ON/OS
    to_char(registrationDate , 'dd/MM/yyyy HH24:MI:SS') as onOs, -- Ngày đăng ký/ Ngày gia hạn
    to_char(startAt , 'dd/MM/yyyy HH24:MI:SS') as registrationDate, -- Ngày bắt đầu sử dụng
    to_char(endCurrentCycle , 'dd/MM/yyyy HH24:MI:SS') as startUsingDate, -- Ngày kết thúc (oneSME)
    null as endDate, -- Ngày đến hạn gia hạn (DHSXKD)
    null as renewalDate, -- Trạng thái gia hạn
    to_char(paymentDate , 'dd/MM/yyyy HH24:MI:SS') as paymentDate, -- Ngày thanh toán
    to_char(cancelledTime , 'dd/MM/yyyy HH24:MI:SS') as cancelledDate, -- Ngày hủy
    case
        when numberOfCycle = -1 then 'Không giới hạn'
        else cast(numberOfCycle as text)
        end as numCycle, -- Số chu kỳ
    case
        when planCycleType = 'DAILY' then concat(planPaymentCycle , ' ngày')
        when planCycleType = 'WEEKLY' then concat(planPaymentCycle , ' tuần')
        when planCycleType = 'MONTHLY' then concat(planPaymentCycle , ' tháng')
        when planCycleType = 'YEARLY' then concat(planPaymentCycle , ' năm')
        when multiCycleType = 'DAILY' then concat(multiPaymentCycle , ' ngày')
        when multiCycleType = 'WEEKLY' then concat(multiPaymentCycle , ' tuần')
        when multiCycleType = 'MONTHLY' then concat(multiPaymentCycle , ' tháng')
        when multiCycleType = 'YEARLY' then concat(multiPaymentCycle , ' năm')
        end as paymentCycle, -- Chu kỳ thanh toán
    cast(unitAmount as text) as price, -- Giá cước dịch vụ/gói cước
    cast(promotionAmount as text) as promotionAmount, -- Số tiền khuyến mại
    cast(preAmountTax as text) as preTaxAmount, -- Số tiền đã thanh toán (chưa thuế)
    cast(amountTax as text) as taxAmount, -- Số tiền nộp thuế
    cast(afterAmountTax as text) as afterTaxAmount, -- Số tiền thanh toán (đã có thuế)
    payTransactionCode as PAYTransactionCode, -- Mã giao dịch với PAY
    dhsxkdCode as DHSXTransactionCode, -- Mã giao dịch với DHSX
    to_char(createdExportInvoice , 'dd/MM/yyyy HH24:MI:SS') as eInvoiceCreationDate, -- Ngày xuất hóa đơn điện tử
    codeInvoice as eInvoiceNo, -- Số hóa đơn điện tử
    creator as createdBy, -- Đơn hàng tạo bởi
    subCode as subscriptionCode, -- Mã đơn hàng
    setupCode as setupCode, -- Mã lắp đặt
    billCode as billingCode, -- Mã hóa đơn ,
    billStatus as paymentStatus, -- Trạng thái thanh toán
    createdSource as creationSource, -- Nguồn tạo
    affagencyname as affiliateAgentName, -- Tên đại lý affiliate
    affagencycode as affiliate1stAgentName, -- Mã đại lý cấp 1
    cast(affagencyuserid as text) as affiliateMemberCode, -- Mã thành viên affiliate giới thiệu
    migrateCode as migrationCode, -- Mã đồng bộ
    to_char(migrateTime , 'dd/MM/yyyy HH24:MI:SS') as migrationTime -- Thời gian đồng bộ
from vnpt_dev.get_report_subscriptions_setup_complete(to_char(date_trunc('day', CURRENT_TIMESTAMP) - interval '1' day, 'yyyy-MM-dd HH24:MI:ss'),
                                                      to_char(date_trunc('day', CURRENT_TIMESTAMP) - interval '1' second, 'yyyy-MM-dd HH24:MI:ss'));