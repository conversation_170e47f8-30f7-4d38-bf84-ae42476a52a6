DROP TABLE IF EXISTS vnpt_dev.api_log;
CREATE TABLE vnpt_dev.api_log (
    id serial4 NOT NULL,
    request text NULL,
    response text NULL,
    created_at timestamp NOT NULL,
    request_at timestamp NOT NULL,
    response_at timestamp NULL,
    response_time int8 NULL,
    user_id int8 NULL,
    client_ip varchar(100) NULL,
    method_name varchar(255) NULL,
    "exception" varchar(255) NULL,
    request_id UUID NOT NULL UNIQUE,
    CONSTRAINT api_log_pkey PRIMARY KEY (id)
);
-- Thêm chỉ mục sau khi tạo bảng
CREATE INDEX idx_request_id ON vnpt_dev.api_log USING hash(request_id);
CREATE INDEX idx_created_at ON vnpt_dev.api_log USING btree(created_at);