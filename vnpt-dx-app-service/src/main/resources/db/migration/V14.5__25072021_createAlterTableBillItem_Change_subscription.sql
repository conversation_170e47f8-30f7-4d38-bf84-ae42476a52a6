alter table vnpt_dev.bill_item
add column quantity   int8;
comment on column vnpt_dev.bill_item.quantity is ' số lượng item';

alter table vnpt_dev.subscriptions
add column change_date   date,
add column change_status   int2;
comment on column vnpt_dev.subscriptions.change_date is ' <PERSON><PERSON>y đổi gói';
comment on column vnpt_dev.subscriptions.change_status is ' Null: không đổi, 0: chờ đổi, 1: đã đổi';

drop table if exists vnpt_dev.change_subscription;
create table vnpt_dev.change_subscription 
(
    id        bigserial not null,
	subscription_id    int8,
    quantity    int4,
    total_amount    float8,
	status int2,
	pricing_id int8,
	trial_day int4,
	reg_type int2,
	start_current_cycle date,
	end_current_cycle date,
	current_payment_date date,
	next_payment_time date,
	number_of_cycles int4,
	cycle_type int2,
	combo_plan_id int8,
	deleted_flag int2,
	created_at timestamp,
	created_by int8,
	modified_at timestamp,
	modified_by int8,
	PRIMARY KEY (id)
);
comment on table vnpt_dev.change_subscription  is ' Đổi subscription khÁC';
comment on column vnpt_dev.change_subscription.quantity is 'Số lượng đăng ký';
comment on column vnpt_dev.change_subscription.total_amount   is 'Tổng tiền';
comment on column vnpt_dev.change_subscription.status    is 'Trạng thái thay đổi (0: chưa đổi, 1: đã đổi)';
comment on column vnpt_dev.change_subscription.pricing_id is 'Số lượng đăng ký';
comment on column vnpt_dev.change_subscription.trial_day   is 'Số ngày dùng thử. nếu reg_type = 0 thì trial_day <> null';
comment on column vnpt_dev.change_subscription.reg_type    is 'Loại đăng ký: 0 - dùng thử, 1 - chính thức';
comment on column vnpt_dev.change_subscription.start_current_cycle is 'Ngày bắt đầu chu kỳ hiện tại';
comment on column vnpt_dev.change_subscription.end_current_cycle   is 'Ngày thanh toán chu kỳ hiện tại';
comment on column vnpt_dev.change_subscription.current_payment_date    is 'Trạng thái thay đổi (0: chưa đổi, 1: đã đổi)';
comment on column vnpt_dev.change_subscription.next_payment_time is 'Ngày thanh toán chu kỳ tiếp theo';
comment on column vnpt_dev.change_subscription.number_of_cycles   is 'Số chu kỳ ';
comment on column vnpt_dev.change_subscription.cycle_type    is 'Loại chu kỳ (0 - ngày, 1 - tuần, 2 - tháng, 3 - năm)';
comment on column vnpt_dev.change_subscription.combo_plan_id is 'id gói combo';



drop table if exists vnpt_dev.change_subscription_addons;
create table vnpt_dev.change_subscription_addons 
(
    id        bigserial not null,
	subscription_id    int8,
	addons_id int8,
    quantity    int4,
	status int2,
	pricing_id int8,
	action int2,
	PRIMARY KEY (id)
);
comment on column vnpt_dev.change_subscription_addons.quantity is 'Số lượng đăng ký';
comment on column vnpt_dev.change_subscription_addons.status    is 'Trạng thái thay đổi (0: chưa đổi, 1: đã đổi)';
comment on column vnpt_dev.change_subscription_addons.pricing_id is 'id của gói dịch vụ';
comment on column vnpt_dev.change_subscription_addons.action is 'Thêm/xóa addon cho subscription (0: Thêm mới, 1: Xóa)';

drop table if exists vnpt_dev.change_subscription_addon_coupon;
create table vnpt_dev.change_subscription_addon_coupon 
(
    id        bigserial not null,
	change_subscription_addons_id     int8,
	coupon_id  int8,
    pricing_id    int8,
	status int2,
	PRIMARY KEY (id)
);
comment on column vnpt_dev.change_subscription_addon_coupon.change_subscription_addons_id is 'id của change_subscription_addons';
comment on column vnpt_dev.change_subscription_addon_coupon.status    is 'Trạng thái thay đổi (0: chưa đổi, 1: đã đổi)';
comment on column vnpt_dev.change_subscription_addon_coupon.pricing_id is 'id của gói dịch vụ';

drop table if exists vnpt_dev.change_subscription_coupons;
create table vnpt_dev.change_subscription_coupons 
(
    id        bigserial not null,
	subscription_id    int8,
	coupon_id  int8,
    quantity    int4,
	status int2,
	pricing_id int8,
	action int2,
	PRIMARY KEY (id)
);
comment on column vnpt_dev.change_subscription_coupons.quantity is 'Số lượng đăng ký';
comment on column vnpt_dev.change_subscription_coupons.status    is 'Trạng thái thay đổi (0: chưa đổi, 1: đã đổi)';
comment on column vnpt_dev.change_subscription_coupons.pricing_id is 'id của gói dịch vụ';
comment on column vnpt_dev.change_subscription_coupons.action is 'Thêm/xóa addon cho subscription (0: Thêm mới, 1: Xóa)';


drop table if exists vnpt_dev.change_subscription_pricing_coupon;
create table vnpt_dev.change_subscription_pricing_coupon 
(
    id        bigserial not null,
	subscription_id    int8,
	coupon_id  int8,
    quantity    int4,
	status int2,
	pricing_id int8,
	action int2,
	PRIMARY KEY (id)
);
comment on column vnpt_dev.change_subscription_pricing_coupon.quantity is 'Số lượng đăng ký';
comment on column vnpt_dev.change_subscription_pricing_coupon.status    is 'Trạng thái thay đổi (0: chưa đổi, 1: đã đổi)';
comment on column vnpt_dev.change_subscription_pricing_coupon.pricing_id is 'id của gói dịch vụ';
comment on column vnpt_dev.change_subscription_pricing_coupon.action is 'Thêm/xóa addon cho subscription (0: Thêm mới, 1: Xóa)';