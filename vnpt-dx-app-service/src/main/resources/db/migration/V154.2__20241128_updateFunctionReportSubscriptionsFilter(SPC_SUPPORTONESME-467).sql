DROP FUNCTION IF EXISTS "vnpt_dev"."get_report_subscriptions_details_new";

CREATE
    OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_details_new"("is_target" int4, "i_customer_email" varchar, "i_customer_name" varchar, "i_object_id" varchar, "i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_affiliate_code" varchar, "i_bill_ids" varchar, "i_provider_ids" varchar, "i_aff_agency_user_ids" varchar, "i_service_owner" varchar, "i_start_payment_date" varchar, "i_end_payment_date" VARCHAR,"i_start_installed_date" VARCHAR, "i_end_installed_date" varchar, "i_payment_method" varchar)
    RETURNS TABLE("id" int8, "subcode" varchar, "createdat" timestamp, "provincename" varchar, "subsstatus" text, "smename" text, "customertype" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "provider" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifieat" timestamp, "serviceownertype" text, "registedby" text, "trafficid" varchar, "trafficuser" varchar, "employeecode" varchar, "dhsxkdsubcode" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "createdsourcemigration" int4, "setupcode" varchar, "paymentdate" timestamp, "createdexportinvoice" timestamp, "codeinvoice" text, "cancelledtime" timestamp, "isonetime" int2, "affiliatecode" varchar, "affagencyuserid" int8, "affagencyname" varchar, "affagencycode" varchar, "endcurrentcycle" date, "identityno" varchar, "billid" int8, "serviceOwner" text, "installedtime" date, "paymenttime" timestamp) AS $BODY$

DECLARE
    run_query text;
    raw_query
              varchar = '
    WITH subReportCTE AS (
     SELECT
					 distinct
           sub.id AS id,
           sub.sub_code AS subCode,
           sub.created_at AS createdAt,
           sme.province AS provinceName,
           CASE
               WHEN sub.status = -1 THEN ''NOT_SET''
               WHEN sub.status = 0 THEN ''FUTURE''
               WHEN sub.status = 1 THEN ''IN_TRIAL''
               WHEN sub.status = 2 THEN ''ACTIVE''
               WHEN sub.status = 3 THEN ''CANCELED''
               WHEN sub.status = 4 THEN ''NON_RENEWING''
           END AS subsStatus,
           sme.name AS smeName,
           sme.customer_type AS customerType,
           sme.tin AS taxtNo,
           sme.address AS address,
           sme.street AS street,
           sme.ward AS ward,
           sme.district AS district,
           sme.province AS province,
           sme.nation AS nation,
           sme.phone_number AS phoneNo,
           sme.email AS email,
           CASE
               WHEN sg.id IS NOT NULL THEN COALESCE(subServices.service_name, subCombo.service_name) || '' ('' || sg.name || '')''
               ELSE COALESCE(subServices.service_name, subCombo.service_name)
           END AS serviceName,
           COALESCE(provider.name, '''') AS provider,
           COALESCE(subServices.pricing_name, subCombo.pricing_name) AS pricingName,
           sub.number_of_cycles AS numberOfCycle,
           COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle) AS planPaymentCycle,
           COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type) AS planCycleType,
           prcMultiPlan.payment_cycle AS multiPaymentCycle,
           CASE
                    WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
                    WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
                    WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
                    WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
                    ELSE ''''
           END AS multiCycleType,
           CASE
                    WHEN sub.installed IS NULL AND sub.installed = 0 THEN ''Đang cài đặt''
                    WHEN sub.installed = 1 THEN ''Đã cài đặt''
                    WHEN sub.installed = 2 THEN ''Gặp sự cố''
                    ELSE ''''
           END AS subsInstalled,
           sme_progress.name AS smeProgressName,
           COALESCE(subServiceGroup.sub_type, subServices.sub_type, subCombo.sub_type) AS subscriptionType,
           sub.modified_at AS modifiedAt,
           COALESCE(subServices.service_owner_type, subCombo.service_owner_type) AS serviceOwnerType,
           CASE
                     WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                     WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                     ELSE ''OneSME''
           END AS registedBy,
           sub.traffic_id  AS trafficId,
           sub.traffic_user AS trafficUser,
           sub.employee_code AS employeeCode,
           sub.dhsxkd_sub_code AS dhsxkdSubCode,
           CASE
               WHEN sub.created_source_migration = 1 THEN ''ĐHSXKD''
               WHEN sub.traffic_source = ''accesstrade'' THEN ''Affiliate AccessTrade''
               WHEN sub.traffic_source = ''apinfo'' THEN ''Affiliate Apinfo''
               WHEN sub.affiliate_one IS NOT NULL THEN ''Affiliate Onesme''
               WHEN sub.traffic_id IS NOT NULL THEN ''Affiliate Masoffer''
               WHEN sub.employee_code IS NOT NULL THEN ''AM G.Thiệu''
               WHEN sub.portal_type IN (1,2) OR (bill.portal_type IS NOT NULL AND bill.portal_type IN (1,2)) THEN ''Dev/Admin''
               ELSE ''oneSME''
           END AS createdSource,
           sub.migrate_time AS migrateTime,
           sub.migrate_code AS migrateCode,
           CASE
               WHEN bill.status = 0 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Khởi tạo''
               WHEN bill.status = 1 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Chờ thanh toán''
               WHEN bill.status = 2 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Đã thanh toán''
               WHEN bill.status = 3 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Thanh toán thất bại''
               WHEN bill.status = 4 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Quá hạn thanh toán''
               WHEN osServiceReceive.payment_status = ''1'' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Đã thanh toán''
               WHEN osServiceReceive.payment_status = ''0'' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Chờ thanh toán''
               WHEN osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL AND
                    COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Chờ thanh toán''
               ELSE ''Chờ thanh toán''
           END AS billStatus,
           bill.billing_code AS billCode,
           CASE
               WHEN bill.bill_action_type = 3 THEN 0
               ELSE bill.bill_action_type
           END AS subscriptionState,
           CASE
               WHEN bill.bill_action_type = 0 then ''Đăng ký mới''
               WHEN bill.bill_action_type = 1 then ''Chỉnh sửa''
               WHEN bill.bill_action_type = 2 THEN ''Đổi gói''
               WHEN bill.bill_action_type = 3 then ''Kích hoạt''
               WHEN bill.bill_action_type = 5 then ''Gia hạn''
               ELSE ''''
           END AS state,
           CASE
               WHEN bill.bill_action_type = 0 THEN sub.created_at
               ELSE COALESCE(bill.payment_date, bill.created_at)
           END AS registrationDate,
           CASE
               WHEN bill.bill_action_type = 0 THEN sub.started_at
               ELSE COALESCE(bill.payment_date, CAST(bill.created_at AS DATE))
           END AS startAt,
           CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN sub.dhsxkd_sub_code
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN osServiceReceive.transaction_code
               ELSE ''''
           END  AS dhsxkdCode,
           CASE
               WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND subServices.plan_payment_cycle IS NULL THEN prcMultiPlan.payment_cycle
               ELSE COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle)
           END AS paymentCycle,
           CASE
               WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND subServices.plan_cycle_type IS NULL THEN
                   (
                     CASE
                            WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
                            WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
                            WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
                            WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
                            ELSE ''''
                     END
                    )
               ELSE COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type)
           END AS cycleType,
           CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN (
                    CASE
                        WHEN sub.installed IS NULL OR sub.installed = 0 THEN ''Đang cài đặt''
                        WHEN sub.installed = 1 THEN ''Đã cài đặt''
                        WHEN sub.installed = 2 THEN ''Gặp sự cố''
                        ELSE ''''
                    END)
                         WHEN COALESCE(subServices.service_owner, subCombo.combo_owner) = 2 THEN (
                                    CASE
                                        WHEN sub.os_3rd_status = 1 THEN ''Tiếp nhận đơn hàng''
                                        WHEN sub.os_3rd_status = 2 THEN ''Đang triển khai''
                                        WHEN sub.os_3rd_status = 4 THEN ''Hoàn thành''
                                        WHEN sub.os_3rd_status = 3 THEN ''Huỷ đơn hàng''
                                        WHEN sub.os_3rd_status = 5 THEN ''Đặt hàng thành công''
                                        ELSE ''''
                                    END
                              )
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN COALESCE(sme_progress.name , '''')
               ELSE ''''
           END AS installedStatus,
           CASE
               WHEN sme_progress.name = ''Hủy'' THEN ''CANCELED''
               ELSE (
                        CASE
                            WHEN sub.status = -1 THEN ''NOT_SET''
                            WHEN sub.status = 0 THEN ''FUTURE''
                            WHEN sub.status = 1 THEN ''IN_TRIAL''
                            WHEN sub.status = 2 THEN ''ACTIVE''
                            WHEN sub.status = 3 THEN ''CANCELED''
                            WHEN sub.status = 4 THEN ''NON_RENEWING''
                        END
                   )
           END AS status,
           CASE
               WHEN sub.traffic_id IS NULL THEN (
                    CASE
                         WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                         WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                         WHEN sub.portal_type = 3 THEN ''OneSME''
                         ELSE ''''
                    END
               )
               WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id
               ELSE sub.traffic_user
           END AS creator,
           billVnptPay.transaction_code AS payTransactionCode,
           CASE
                WHEN billPayments.promotion_amount < 0 THEN 0
                ELSE billPayments.promotion_amount
           END AS promotionAmount,
           CASE
                WHEN billPayments.unit_amount < 0 THEN 0
                ELSE billPayments.unit_amount
           END AS unitAmount,
           CASE
               WHEN bill.action_type = 1 THEN billPayments.amount_change
               WHEN bill.bill_action_type <> 1 AND billPayments.pre_amount_tax > 0 THEN billPayments.pre_amount_tax
               ELSE 0
               END AS preAmountTax,
           CASE
                WHEN billPayments.amount_tax > 0 then billPayments.amount_tax
                ELSE 0
           END AS amountTax,
           CASE
                WHEN billPayments.after_amount_tax > 0 then billPayments.after_amount_tax
                ELSE 0
           END AS afterAmountTax,
           CASE
			    WHEN sub.created_source_migration = 1 THEN 5
			    WHEN sub.traffic_source = ''accesstrade'' THEN 6
			    WHEN sub.traffic_source = ''apinfo'' THEN 8
			    WHEN sub.affiliate_one IS NOT NULL THEN 7
			    WHEN sub.traffic_id IS NOT NULL THEN 3
			    WHEN sub.employee_code IS NOT NULL THEN 2
			    WHEN sub.portal_type IN (1,2) THEN 4
			    ELSE 1
		   END AS createdSourceMigration,
           osServiceReceive.setup_code AS setupCode,
           CASE
               WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                   AND CAST(bill.created_at AS DATE) = bill.billing_date AND COALESCE(subServices.pricing_type, subCombo.pricing_type) = 0
                   THEN bill.created_at
               ELSE bill.payment_date
           END paymentDate,
           bill.created_export_invoice AS createdExportInvoice,
           bill.code AS codeInvoice,
           CASE
               WHEN sme_progress.name = ''Hủy'' OR sub.status IN (3, 4) THEN sub.cancelled_time
               END AS cancelledTime,
           sub.is_one_time AS isOneTime,
           CASE
               WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user
               WHEN sub.traffic_source = ''accesstrade'' THEN sub.traffic_user
               WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one
           END AS affiliateCode,
           bill.aff_agency_user_id AS affAgencyUserId,
           COALESCE(affiliateAgency.name, CONCAT('' '', affiliateAgency.last_name, affiliateAgency.first_name)) AS affAgencyName,
           COALESCE(bill.aff_agency_code, '''') AS affAgencyCode,
           CASE
               WHEN bill.bill_action_type = 5 THEN bill.end_date_new_renewal
               ELSE sub.end_current_cycle
           END AS endCurrentCycle,
           sme.identity_no AS identityNo,
           bill.id AS billId,
					 CASE
               WHEN  COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) then ''ON''
               ELSE ''OS''
           END AS serviceOwner,
					 sub.installed_time as installedTime,
					 bill.payment_date as paymentTime
        FROM vnpt_dev.subscriptions sub
            JOIN vnpt_dev.view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id AND (''%28$s'' = ''-1'' OR ''%28$s'' = '''' OR bill.id = ANY((''{'' || ''%28$s'' || ''}'')::int8[]))
            LEFT JOIN vnpt_dev.view_report_sub_sme AS sme ON sub.user_id = sme.id
            LEFT JOIN vnpt_dev.users AS register ON sub.registed_by = register.id
            LEFT JOIN vnpt_dev.pricing_multi_plan prcMultiPlan ON prcMultiPlan.id = sub.pricing_multi_plan_id
            LEFT JOIN vnpt_dev.order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id
            LEFT JOIN vnpt_dev.order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)
            LEFT JOIN vnpt_dev.sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id
            LEFT JOIN vnpt_dev.view_report_sub_services AS subServices ON (sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id) or (sub.pricing_id IS NULL AND sub.service_id = subServices.service_id and subServices.id is null)
            LEFT JOIN vnpt_dev.view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id
            LEFT JOIN vnpt_dev.view_report_sub_service_group AS subServiceGroup ON sub.service_group_id IS NOT NULL AND sub.service_group_id = subServiceGroup.id
            LEFT JOIN vnpt_dev.service_group as sg on sg.id = sub.service_group_id
            LEFT JOIN vnpt_dev.view_report_sub_bill_payment AS billPayments ON bill.id = billPayments.id
            LEFT JOIN vnpt_dev.view_report_sub_vnpt_pay_response AS billVnptPay ON bill.id = billVnptPay.bill_id
            LEFT JOIN vnpt_dev.users AS provider ON COALESCE(subServices.provider_id, subCombo.provider_id) = provider.id
            LEFT JOIN vnpt_dev.users AS affiliateAgency ON affiliateAgency.id = bill.aff_agency_user_id
        WHERE
              sub.deleted_flag = 1 AND
              sub.confirm_status = 1 AND
              bill.status != 0 AND
              ((subServices.service_owner in (0,1) OR (subCombo.combo_owner in (0,1))) -- ON lay het, OS chi lay neu co trang thai
				OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and (subServices.service_owner = 3 OR (subCombo.combo_owner = 3)))
				OR (sub.os_3rd_status is not null and (subServices.service_owner = 2 OR (subCombo.combo_owner = 2)))) AND
              (%5$s = -1 OR sme.province_id = %5$s) AND
              (''%6$s'' = ''ALL'' OR sme.customer_type_raw = ''%6$s'') AND
              (''%19$s'' = ''ALL'' OR (sub.service_group_id is not null AND ''SERVICE_GROUP''= ''%19$s'') OR (subServices.sub_type is not null AND ''SERVICE''= ''%19$s'') OR (subCombo.sub_type is not null AND ''COMBO''= ''%19$s'')) AND
              (''%22$s'' = ''ALL'' OR sub.employee_code = ''%22$s'') AND
              (%13$s = -2 OR sub.status = %13$s) AND
              (%14$s = -1 OR sub.service_id = %14$s) AND
              (
                (
                     subServices.sub_type is not null AND
                     (%20$s = -1 OR subServices.categories_id = %20$s) AND
                     (%16$s = -1 OR sub.pricing_id = %16$s) AND
                     (''%17$s'' = ''-1'' OR  sub.pricing_id = ANY ((''{'' || ''%17$s'' || ''}'')::int8[]))
                 ) OR
                 (
                      subCombo.sub_type is not null AND
                      (''%21$s'' = ''-1'' OR string_to_array(subCombo.categories_id, '','') && string_to_array(''%21$s'', '','')) AND
                      (%16$s = -1 OR ''%18$s'' = ''-1'' OR sub.combo_plan_id = ANY ((''{'' || ''%18$s'' || ''}'')::int8[])) AND
                      (%14$s = -1 OR ''%15$s'' = ''-1'' OR subCombo.combo_id = ANY ((''{'' || ''%15$s'' || ''}'')::int8[]))
                 )
              ) AND
              (''%29$s'' = ''-1'' OR COALESCE(subServices.provider_id, subCombo.provider_id) = ANY((''{'' || ''%29$s'' || ''}'')::int8[])) AND
              (''%30$s'' = ''-1'' OR bill.aff_agency_user_id = ANY((''{'' || ''%30$s'' || ''}'')::int8[])) AND
              (''%36$s'' = ''-1'' OR sub.payment_method = ANY((''{'' || ''%36$s'' || ''}'')::int8[]))
)
SELECT distinct * FROM subReportCTE
        WHERE
            (%23$s = -1 OR subReportCTE.subscriptionState = %23$s) AND
						(''%31$s'' = ''-1'' OR subReportCTE.serviceOwner = ''%31$s'') AND
            (CAST(''%32$s'' AS date) = cast(''1970-01-01'' as date) OR cast(subReportCTE.paymentTime as date) >= CAST(''%32$s'' AS date)) AND
            (CAST(''%33$s'' AS date) = cast(''3000-01-01'' as date) OR cast(subReportCTE.paymentTime as date) <= CAST(''%33$s'' AS date)) AND
            (CAST(''%34$s'' AS date) = cast(''1970-01-01'' as date) OR cast(subReportCTE.installedTime as date) >= CAST(''%34$s'' AS date)) AND
            (CAST(''%35$s'' AS date) = cast(''3000-01-01'' as date) OR cast(subReportCTE.installedTime as date) <= CAST(''%35$s'' AS date)) AND
            (cast(subReportCTE.registrationDate as date) >= CAST(''%11$s'' AS date)) AND
            (cast(subReportCTE.registrationDate as date) <= CAST(''%12$s'' AS date)) AND
            (''%24$s'' = ''-1'' OR subReportCTE.creator = ''%24$s'') AND
            (''%25$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') >= ''%25$s'') AND
            (''%26$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') <= ''%26$s'') AND
            (''%8$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%8$s'') AND
            (''%9$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%9$s'') AND
            (
                    (subReportCTE.createdSourceMigration <> 5) AND
                    ( -- Do RK lưu created_source khác với code BE --
                        %7$s = -1 OR (%7$s IN (1, 2, 3, 4, 6, 7, 8) AND %7$s = subReportCTE.createdSourceMigration)
                    )
            ) AND
            (''%27$s'' = '''' OR subReportCTE.affiliateCode = ''%27$s'') AND
            (''%3$s'' = '''' OR subReportCTE.smeName ilike (''%%'' || ''%3$s'' || ''%%'')) AND
            (''%2$s'' = '''' OR subReportCTE.email = ''%2$s'')
        ORDER BY subReportCTE.registrationDate DESC';

BEGIN
    run_query
        = format(raw_query, is_target, i_customer_email, i_customer_name, i_object_id,
                 i_province_id, i_customer_type, i_createdsource, i_migrate_start_date,
                 i_migrate_end_date, i_migrate_codes, i_start_date, i_end_date,
                 i_status, i_service_id, i_combo_ids, i_pricing_id, i_pricing_ids,
                 i_combo_plan_ids, i_subscription_type, i_category_service, i_category_combo,
                 i_employee_code, i_subscription_state, i_creator, i_cancelled_time_start,
                 i_cancelled_time_end, i_affiliate_code, i_bill_ids, i_provider_ids, i_aff_agency_user_ids,
                 i_service_owner, i_start_payment_date, i_end_payment_date, i_start_installed_date,
                 i_end_installed_date, i_payment_method);
    RAISE
        NOTICE 'Run query: %', run_query;
    RETURN QUERY EXECUTE run_query;
END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."get_report_subscriptions_filter_bill_ids";

CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_filter_bill_ids"("is_target" int4, "i_customer_email" varchar, "i_customer_name" varchar, "i_object_id" varchar, "i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_affiliate_code" varchar, "i_provider_ids" varchar, "i_aff_agency_user_ids" varchar, "i_service_owner" varchar, "i_start_payment_date" varchar, "i_end_payment_date" varchar, "i_start_installed_date" varchar, "i_end_installed_date" varchar, "i_payment_method" varchar)
    RETURNS TABLE("subid" int8, "billid" int8) AS $BODY$

DECLARE
    run_query text;
    raw_query
              varchar = 'WITH subReportCTE AS (
    SELECT
                     distinct
           sub.id AS id,
           bill.id AS billId,
           sme.name AS smeName,
           sme.email AS email,
           sub.migrate_time AS migrateTime,
           CASE
               WHEN bill.bill_action_type = 3 THEN 0
               ELSE bill.bill_action_type
           END AS subscriptionState,
           CASE
               WHEN  COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) then ''ON''
               ELSE ''OS''
           END AS serviceOwner,
                     sub.installed_time as installedTime,
                     bill.payment_date as paymentTime,
                     CASE
               WHEN bill.bill_action_type = 0 THEN sub.created_at
               ELSE COALESCE(bill.payment_date, bill.created_at)
           END AS registrationDate,
           CASE
               WHEN sub.traffic_id IS NULL THEN (
                    CASE
                         WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                         WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                         WHEN sub.portal_type = 3 THEN ''OneSME''
                         ELSE ''''
                    END
                )
               WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id
               ELSE sub.traffic_user
           END AS creator,
           CASE
                WHEN sub.created_source_migration = 1 THEN 5
                WHEN sub.traffic_source = ''accesstrade'' THEN 6
                WHEN sub.traffic_source = ''apinfo'' THEN 8
                WHEN sub.affiliate_one IS NOT NULL THEN 7
                WHEN sub.traffic_id IS NOT NULL THEN 3
                WHEN sub.employee_code IS NOT NULL THEN 2
                WHEN sub.portal_type IN (1,2) THEN 4
                ELSE 1
           END AS createdSourceMigration,
           CASE
               WHEN sme_progress.name = ''Hủy'' OR sub.status IN (3, 4) THEN sub.cancelled_time
           END AS cancelledTime,
           CASE
               WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user
               WHEN sub.traffic_source = ''accesstrade'' THEN sub.traffic_user
               WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one
           END AS affiliateCode
        FROM vnpt_dev.subscriptions sub
            JOIN vnpt_dev.view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id
            LEFT JOIN vnpt_dev.users AS sme ON sub.user_id = sme.id
            LEFT JOIN vnpt_dev.users AS register ON sub.registed_by = register.id
            LEFT JOIN vnpt_dev.order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id
            LEFT JOIN vnpt_dev.order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)
            LEFT JOIN vnpt_dev.sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id
            LEFT JOIN vnpt_dev.view_report_sub_services AS subServices ON (sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id) or (sub.pricing_id IS NULL AND sub.service_id = subServices.service_id)
            LEFT JOIN vnpt_dev.view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id
        WHERE
              sub.deleted_flag = 1 AND
              sub.confirm_status = 1 AND
              bill.status != 0 AND
              (COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) -- ON lay het, OS chi lay neu co trang thai
                OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 3)
                OR (sub.os_3rd_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 2)) AND
              (%5$s = -1 OR sme.province_id = %5$s) AND
              (''%6$s'' = ''ALL'' OR sme.customer_type = ''%6$s'') AND
              (''%19$s'' = ''ALL'' OR (sub.service_group_id is not null AND ''SERVICE_GROUP''= ''%19$s'') OR (subServices.sub_type is not null AND ''SERVICE''= ''%19$s'') OR (subCombo.sub_type is not null AND ''COMBO''= ''%19$s'')) AND
              (''%22$s'' = ''ALL'' OR sub.employee_code = ''%22$s'') AND
              (%13$s = -2 OR sub.status = %13$s) AND
              (%14$s = -1 OR sub.service_id = %14$s) AND
              (
                (
                     subServices.sub_type is not null AND
                     (%20$s = -1 OR subServices.categories_id = %20$s) AND
                     (%16$s = -1 OR sub.pricing_id = %16$s) AND
                     (''%17$s'' = ''-1'' OR  sub.pricing_id = ANY ((''{'' || ''%17$s'' || ''}'')::int8[]))
                 ) OR
                 (
                      subCombo.sub_type is not null AND
                      (''%21$s'' = ''-1'' OR string_to_array(subCombo.categories_id, '','') && string_to_array(''%21$s'', '','')) AND
                      (%16$s = -1 OR ''%18$s'' = ''-1'' OR sub.combo_plan_id = ANY ((''{'' || ''%18$s'' || ''}'')::int8[])) AND
                      (%14$s = -1 OR ''%15$s'' = ''-1'' OR subCombo.combo_id = ANY ((''{'' || ''%15$s'' || ''}'')::int8[]))
                 )
              ) AND
              (''%28$s'' = ''-1'' OR COALESCE(subServices.provider_id, subCombo.provider_id) = ANY((''{'' || ''%28$s'' || ''}'')::int8[])) AND
              (''%29$s'' = ''-1'' OR bill.aff_agency_user_id = ANY((''{'' || ''%29$s'' || ''}'')::int8[])) AND
              (''%35$s'' = ''-1'' OR sub.payment_method = ANY((''{'' || ''%35$s'' || ''}'')::int8[]))
)
SELECT
            subReportCTE.id AS subId,
            subReportCTE.billId AS billId
       FROM subReportCTE
       WHERE
            (%23$s = -1 OR subReportCTE.subscriptionState = %23$s) AND
            (''%30$s'' = ''-1'' OR subReportCTE.serviceOwner = ''%30$s'') AND
            (CAST(''%31$s'' AS date) = cast(''1970-01-01'' as date) OR cast(subReportCTE.paymentTime as date) >= CAST(''%31$s'' AS date)) AND
            (CAST(''%32$s'' AS date) = cast(''3000-01-01'' as date) OR cast(subReportCTE.paymentTime as date) <= CAST(''%32$s'' AS date)) AND
            (CAST(''%33$s'' AS date) = cast(''1970-01-01'' as date) OR cast(subReportCTE.installedTime as date) >= CAST(''%33$s'' AS date)) AND
            (CAST(''%34$s'' AS date) = cast(''3000-01-01'' as date) OR cast(subReportCTE.installedTime as date) <= CAST(''%34$s'' AS date)) AND
            (cast(subReportCTE.registrationDate as date) >= CAST(''%11$s'' AS date)) AND
            (cast(subReportCTE.registrationDate as date) <= CAST(''%12$s'' AS date)) AND
            (''%24$s'' = ''-1'' OR subReportCTE.creator = ''%24$s'') AND
            (''%25$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') >= ''%25$s'') AND
            (''%26$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') <= ''%26$s'') AND
            (''%8$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%8$s'') AND
            (''%9$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%9$s'') AND
            (
                    (subReportCTE.createdSourceMigration <> 5) AND
                    ( -- Do RK lưu created_source khác với code BE --
                        %7$s = -1 OR (%7$s IN (1, 2, 3, 4, 6, 7, 8) AND %7$s = subReportCTE.createdSourceMigration)
                    )
            ) AND
            (''%27$s'' = '''' OR subReportCTE.affiliateCode = ''%27$s'') AND
            (''%3$s'' = '''' OR subReportCTE.smeName ILIKE (''%%'' || ''%3$s'' || ''%%'')) AND
            (''%2$s'' = '''' OR subReportCTE.email = ''%2$s'')
            ORDER BY subReportCTE.registrationDate DESC
';

BEGIN run_query = format(raw_query, is_target, i_customer_email, i_customer_name, i_object_id,
                         i_province_id, i_customer_type, i_createdsource, i_migrate_start_date,
                         i_migrate_end_date, i_migrate_codes, i_start_date, i_end_date,
                         i_status, i_service_id, i_combo_ids, i_pricing_id, i_pricing_ids,
                         i_combo_plan_ids, i_subscription_type, i_category_service, i_category_combo,
                         i_employee_code, i_subscription_state, i_creator, i_cancelled_time_start,
                         i_cancelled_time_end, i_affiliate_code, i_provider_ids, i_aff_agency_user_ids,
                         i_service_owner, i_start_payment_date, i_end_payment_date, i_start_installed_date, i_end_installed_date, i_payment_method);
RAISE
    NOTICE 'Run query: %', run_query;
RETURN QUERY EXECUTE run_query;
END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;