drop view if exists report_view_pricing_and_combo_plan;
CREATE
OR REPLACE VIEW report_view_pricing_and_combo_plan AS
select p.id,
       cast(concat(p.id, '0000') as bigint)      as unique_id,
       concat(s.service_name, '/', pricing_name) as name,
       service_id,
       p.price,
       cycle_type,
       payment_cycle
from pricing p
         left join services s on p.service_id = s.id
where p.deleted_flag = 1
  and p.approve = 1
  and p.status = 1
union
select id,
       cast(concat(id, '0001') as bigint) as unique_id,
       combo_name                         as name,
       null                               as service_id,
       price,
       cycle_type,
       payment_cycle
from combo_plan
where deleted_flag = 1
  and approve = 1
  and status = 1;



drop view if exists vnpt_dev.report_view_service_combo_uniqueid;
create
or replace view vnpt_dev.report_view_service_combo_uniqueid as
SELECT services.id,
       (concat(services.id, '0000'))::bigint AS unique_id, CASE
                                                               WHEN ((services.service_owner = 0) OR (services.service_owner = 1))
                                                                   THEN concat('(ON) ', services.service_name)
                                                               ELSE concat('(OS) ', services.service_name)
    END AS name,
       services.user_id,
       services.service_owner,
       NULL::smallint AS combo_owner, services.categories_id,
       NULL::character varying AS categories_ids,
    true AS is_service
FROM vnpt_dev.services
WHERE services.deleted_flag = 1 AND services.approve = 1 AND services.status = 1
UNION
SELECT combo.id,
       concat(combo.id, '0001')::bigint AS unique_id, concat('(Combo) ', combo.combo_name) AS name,
       combo.user_id,
       NULL::smallint AS service_owner, combo.combo_owner,
       NULL::bigint AS categories_id, combo.categories_id AS categories_ids,
       false AS is_service
FROM vnpt_dev.combo
WHERE combo.deleted_flag = 1
  AND combo.approve = 1
  AND combo.status = 1

UNION
SELECT service_group.id,
       (concat(service_group.id, '0004'))::bigint AS unique_id, concat('(Service group) ', service_group.name) AS name,
       service_group.user_id,
       service_group.group_service_owner AS service_owner,
       service_group.group_service_owner AS combo_owner,
       NULL::bigint AS categories_id, REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids,
       false                             AS is_service
FROM vnpt_dev.service_group
WHERE service_group.deleted_flag = 1
  AND service_group.approve = 1
  AND service_group.status = 1
  AND service_group.id in (SELECT MAX(id) FROM vnpt_dev.service_group GROUP BY group_service_draft_id);











drop view if exists vnpt_dev.export_view_service_and_combo;
create
or replace view vnpt_dev.export_view_service_and_combo as
SELECT services.id,
       concat(services.id, '0000')::bigint AS unique_id,
        CASE
            WHEN ((services.service_owner = 0) OR (services.service_owner = 1))
                THEN concat('(ON) ', services.service_name)
            ELSE concat('(OS) ', services.service_name)
            END AS name,
       services.user_id,
       CASE
           WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 1
           WHEN services.service_owner = ANY (ARRAY[2, 3]) THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 1
            WHEN services.service_owner = ANY (ARRAY[2, 3]) THEN 2
            ELSE NULL::integer
END AS sub_type,
    services.categories_id,
    services.approve,
    services.status,
    NULL::character varying AS categories_ids,
    services.created_at,
    services.modified_at
   FROM vnpt_dev.services
  WHERE services.deleted_flag = 1
UNION
SELECT combo.id,
       concat(combo.id, '0001')::bigint AS unique_id,
        concat('(Combo) ', combo.combo_name) AS name,
       combo.user_id,
       3 AS subscription_type,
       CASE
           WHEN combo.combo_owner = ANY (ARRAY[0, 1]) THEN 1
           WHEN combo.combo_owner = ANY (ARRAY[2, 3]) THEN 2
           ELSE NULL::integer
END AS sub_type,
    NULL::bigint AS categories_id,
    combo.approve,
    combo.status,
    combo.categories_id AS categories_ids,
    combo.created_at,
    combo.modified_at
   FROM vnpt_dev.combo
  WHERE combo.deleted_flag = 1
UNION
SELECT service_group.id,
       concat(service_group.id, '0004')::bigint AS unique_id,
        concat('(Nhóm dịch vụ) ', service_group.name) AS name,
       service_group.user_id,
       4 AS subscription_type,
       CASE
           WHEN service_group.group_service_owner = ANY (ARRAY[0, 1]) THEN 1
           WHEN service_group.group_service_owner = ANY (ARRAY[2, 3]) THEN 2
           ELSE NULL::integer
END AS sub_type,
    NULL::bigint AS categories_id,
    service_group.approve,
    service_group.status,
    REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids,
    service_group.created_at,
    service_group.modified_at
FROM vnpt_dev.service_group
WHERE service_group.deleted_flag = 1
  AND service_group.id in (SELECT MAX(id) FROM vnpt_dev.service_group GROUP BY group_service_draft_id);












DROP VIEW IF EXISTS report_view_subscription_service_group;
Create VIEW report_view_subscription_service_group as
SELECT
    subscriptions.id,
    subscriptions.service_id,
    subscriptions.quantity,
    subscriptions.total_amount,
    subscriptions.status,
    subscriptions.deleted_flag,
    subscriptions.created_by,
    subscriptions.modified_by,
    subscriptions.created_at,
    subscriptions.modified_at,
    subscriptions.user_id,
    subscriptions.from_date,
    subscriptions.cancelled_time,
    subscriptions.pricing_id,
    subscriptions.sme_subscription_id,
    subscriptions.installed,
    subscriptions.expired_time,
    subscriptions.used_quantity,
    subscriptions.registed_by,
    subscriptions.sub_registration_id,
    subscriptions.started_at,
    subscriptions.start_charge_at,
    subscriptions.trial_day,
    subscriptions.reg_type,
    subscriptions.payment_method,
    subscriptions.confirm_status,
    subscriptions.current_cycle,
    subscriptions.phone_no,
    subscriptions.contact,
    subscriptions.address,
    subscriptions.sub_code,
    subscriptions.start_current_cycle,
    subscriptions.end_current_cycle,
    subscriptions.end_current_cycle_new,
    subscriptions.current_payment_date,
    subscriptions.dhsxkd_sub_code,
    subscriptions.next_payment_time,
    subscriptions.awaiting_cancel,
    subscriptions.pre_order,
    subscriptions.number_of_cycles,
    subscriptions.cycle_type,
    subscriptions.traffic_id,
    subscriptions.combo_plan_id,
    subscriptions.canceled_by,
    subscriptions.subscription_contract_id,
    subscriptions.called_trans,
    subscriptions.change_date,
    subscriptions.change_status,
    subscriptions.update_date,
    subscriptions.update_status,
    subscriptions.portal_type,
    subscriptions.message_setup,
    subscriptions.employee_code,
    subscriptions.pricing_multi_plan_id,
    subscriptions.number_of_cycles_default,
    subscriptions.refer_subscription,
    subscriptions.traffic_user,
    null::text AS pricing_name,
        users.customer_type,
    (concat(service_group.id, ' (', service_group.name, ')'))::text AS service_name,
    services.id AS c_service_id,
    (concat(service_group.id, '0004'))::bigint AS service_unique_id,
        null::bigint AS pricing_unique_id,
        '-1'::integer AS p_payment_cycle,
        '-1'::integer AS p_cycle_type,
        null::bigint AS price,
        4 AS subscription_type,
    CASE
        WHEN (service_group.group_service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
        WHEN (service_group.group_service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
        WHEN (service_group.group_service_owner IS NULL) THEN 'OS'::text
        ELSE NULL::text
        END AS onos,
    report_view_customer.nation_id AS c_nation_id,
    report_view_customer.nation_name AS c_nation_name,
    report_view_customer.province_id AS c_province_id,
    report_view_customer.province_name AS c_province_name,
    report_view_customer.district_id AS c_district_id,
    report_view_customer.district_name AS c_district_name,
    report_view_customer.ward_id AS c_ward_id,
    report_view_customer.ward_name AS c_ward_name,
    report_view_customer.street_name,
    report_view_customer.address AS c_address,
    report_view_customer.name AS sme_name,
    report_view_customer.email AS c_email,
    report_view_customer.phone_number AS c_phone_number,
    report_view_customer.tin,
    billings.status AS payment_status,
    billings.next_total_amount,
    bill_item.quantity AS service_quantity,
    '-1'::integer AS order_status,
        ''::text AS order_status_name,
        subscriptions.status AS c_status,
    CASE
        WHEN (subscriptions.employee_code IS NOT NULL) THEN 1
        WHEN (subscriptions.traffic_id IS NOT NULL) THEN 2
        WHEN (subscriptions.portal_type = 1) THEN 3
        WHEN (subscriptions.portal_type = 2) THEN 3
        WHEN (subscriptions.portal_type = 3) THEN 0
        ELSE NULL::integer
END AS source,
    service_group.user_id AS provider_id,
    '-1'::integer AS categories_id,
    REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    1 AS is_one_time,
    0 as has_renew
FROM ((((((vnpt_dev.subscriptions
    JOIN vnpt_dev.service_group ON ((subscriptions.service_group_id = service_group.id)))
    LEFT JOIN vnpt_dev.users ON ((users.id = subscriptions.user_id)))
    LEFT JOIN vnpt_dev.services ON ((services.id = subscriptions.service_id)))
    LEFT JOIN vnpt_dev.report_view_customer ON ((subscriptions.user_id = report_view_customer.id)))
    LEFT JOIN vnpt_dev.billings ON (((billings.subscriptions_id = subscriptions.id)
        AND (billings.id IN (
            SELECT max(billings.id) AS max
            FROM vnpt_dev.billings
            GROUP BY billings.subscriptions_id)))))
    LEFT JOIN vnpt_dev.bill_item ON (((bill_item.billing_id = billings.id) AND (bill_item.object_id = subscriptions.combo_plan_id) AND (bill_item.object_type = 1))))
WHERE ((subscriptions.deleted_flag = 1) AND (subscriptions.confirm_status = 1));