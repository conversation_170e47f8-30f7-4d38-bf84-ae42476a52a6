
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION vnpt_dev.prevent_duplicate_tin()
RETURNS TRIGGER AS $$
BEGIN
    -- <PERSON><PERSON><PERSON> tra khi thêm mới (INSERT)
    IF TG_OP = 'INSERT' THEN
        IF EXISTS (
            SELECT 1
            FROM vnpt_dev.users
            WHERE tin = NEW.tin
        ) THEN
            RAISE EXCEPTION 'TIN already exists: %', NEW.tin;
        END IF;

    -- Kiểm tra khi cập nhật (UPDATE)
    ELSIF TG_OP = 'UPDATE' THEN
        IF NEW.tin IS DISTINCT FROM OLD.tin THEN
            IF EXISTS (
                SELECT 1
                FROM vnpt_dev.users
                WHERE tin = NEW.tin
                AND id != NEW.id
            ) THEN
                RAISE EXCEPTION 'TIN already exists: %', NEW.tin;
            END IF;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS check_duplicate_tin ON vnpt_dev.users CASCADE;
CREATE TRIGGER check_duplicate_tin
BEFORE INSERT OR UPDATE ON vnpt_dev.users
FOR EACH ROW
EXECUTE FUNCTION vnpt_dev.prevent_duplicate_tin();