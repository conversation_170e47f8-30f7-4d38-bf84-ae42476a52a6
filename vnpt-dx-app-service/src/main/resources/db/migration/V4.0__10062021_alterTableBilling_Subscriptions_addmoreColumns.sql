alter table vnpt_dev.billings
add column current_payment_time date,
add column final_payment_term date;
comment on column vnpt_dev.billings.status is '0 - khởi tạo, 1 - chờ thanh toán, 2 - đ<PERSON> thanh toán, 3 - thanh to<PERSON> thất bại, 4- qu<PERSON> hạn thanh toán';
comment on column vnpt_dev.billings.billing_date is '<PERSON><PERSON>y bắt đầu sử dụng';
comment on column vnpt_dev.billings.current_payment_time is '<PERSON><PERSON><PERSON> bắt đầu sử dụng';
comment on column vnpt_dev.billings.final_payment_term is 'Hạn thanh toán cuối cùng';

create table vnpt_dev.subscription_pricing_coupon
(
    id                 bigserial NOT NULL,
    subscription_id	    int8,
    coupon_id            int8,
    pricing_id        int8,
    CONSTRAINT subscription_pricing_coupon_PK PRIMARY KEY (id)
);

comment
on table vnpt_dev.subscription_pricing_coupon is '<PERSON>ả<PERSON> thông tin CTKM cho gói';
comment
on column vnpt_dev.subscription_pricing_coupon.subscription_id is 'Mã đăng ký';
comment
on column vnpt_dev.subscription_pricing_coupon.coupon_id is 'Mã CTKM';
comment
on column vnpt_dev.subscription_pricing_coupon.pricing_id is 'Mã gói được nhận của CTKM';

create table vnpt_dev.subscription_addon_coupon
(
    id                 bigserial NOT NULL,
    subscription_addon_id	    int8,
    coupon_id            int8,
    pricing_id        int8,
    CONSTRAINT subscription_addon_coupon_PK PRIMARY KEY (id)
);

comment
on table vnpt_dev.subscription_addon_coupon is 'Bảng thông tin CTKM cho addon';
comment
on column vnpt_dev.subscription_addon_coupon.subscription_addon_id is 'Mã đăng ký';
comment
on column vnpt_dev.subscription_addon_coupon.coupon_id is 'Mã CTKM';
comment
on column vnpt_dev.subscription_addon_coupon.pricing_id is 'Mã gói được nhận của CTKM';
