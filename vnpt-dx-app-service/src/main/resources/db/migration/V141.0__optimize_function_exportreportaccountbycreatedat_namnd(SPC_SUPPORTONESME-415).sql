    CREATE OR REPLACE
    FUNCTION vnpt_dev.exportreportaccountbycreatedat_namnd(i_accounttype CHARACTER VARYING[],
    i_accountstatustype CHARACTER VARYING[],
    i_starttime CHARACTER VARYING,
    i_endtime CHARACTER VARYING)
     RETURNS TABLE(id bigint,
    "roleName" CHARACTER VARYING,
    total_trial NUMERIC,
    total_active NUMERIC,
    total_future NUMERIC,
    total_cancel NUMERIC,
    total_end NUMERIC,
    "Loại tài khoản" TEXT,
    "Họ và Tên" TEXT,
    "Tên công ty" CHARACTER VARYING,
    "Email" CHARACTER VARYING,
    "Tên" CHARACTER VARYING,
    "Mã số thuế" CHARACTER VARYING,
    "Mã BHXH" CHARACTER VARYING,
    "Tên nguòi đại diện" CHARACTER VARYING,
    "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i" CHARACTER VARYING,
    "Đ<PERSON><PERSON> chỉ" CHARACTER VARYING,
    "Website" CHARACTER VARYING,
    status integer,
    "Trạng thái tài khoản" TEXT,
    "<PERSON><PERSON> quyền" CHARACTER VARYING,
    created_at timestamp WITHOUT time ZONE,
    "Ngày cập nhật" timestamp WITHOUT time ZONE,
    "TechId" CHARACTER VARYING,
    "Tỉnh" CHARACTER VARYING,
    "Bộ phận" CHARACTER VARYING,
    "Lĩnh vực kinh doanh" CHARACTER VARYING,
    "Quy mô doanh nghiệp" CHARACTER VARYING,
    "Quận/Huyện" CHARACTER VARYING,
    "Phố/Đường" CHARACTER VARYING,
    "Quốc gia" CHARACTER VARYING,
    "Phường/xã" CHARACTER VARYING,
    parent_id bigint,
    user_code CHARACTER VARYING)
     LANGUAGE plpgsql
    AS $function$

    DECLARE
    baseQuery varchar = 'with _u as (with _u1 as (select u.id
                                                    from vnpt_dev.users u
                                                         where u.created_at >= timestamp ''%s''
                                                        and u.created_at < timestamp ''%s'')
                                        select u.*, r.name as "roleName"
                                        from _u1 u
                                            inner join vnpt_dev.users_roles ur on u.id = ur.user_id
                                            inner join vnpt_dev.role r on ur.role_id = r.id
                                        where  r.name = any(''%s'')),
                total_subs AS (
                    SELECT
                        tmp.ID,
                        tmp."roleName",
                        SUM ( tmp.trial ) AS "total_trial",
                        SUM ( tmp.active ) AS "total_active",
                        SUM ( tmp.future ) AS "total_future",
                        SUM ( tmp.cancel ) AS "total_cancel",
                        SUM ( tmp.END ) AS "total_end"
                    FROM
                    (
                        SELECT
                            u22.ID,
                            u22."roleName",
                            SUM ( CASE WHEN s.status = 1 THEN 1 ELSE 0 END ) AS "trial",
                            SUM ( CASE WHEN s.status = 2 THEN 1 ELSE 0 END ) AS "active",
                            SUM ( CASE WHEN s.status = 0 THEN 1 ELSE 0 END ) AS "future",
                            SUM ( CASE WHEN s.status = 3 THEN 1 ELSE 0 END ) AS "cancel",
                            SUM ( CASE WHEN s.status = 4 THEN 1 ELSE 0 END ) AS "end"
                        FROM    _u u22
                            LEFT JOIN vnpt_dev.subscriptions s ON u22.ID = s.user_id
                        GROUP BY
                            u22.ID,
                            u22."roleName",
                            s.status,
                            s.ID
                    ) tmp
                    GROUP BY
                        tmp.ID,
                        tmp."roleName"
                ),
        s_full_info as (    SELECT T.*,
            CASE
                WHEN T."roleName" = ''ROLE_SME'' THEN  ''SME''
                WHEN T."roleName" = ''ROLE_DEVELOPER'' THEN ''DEV''
                WHEN T."roleName" = ''ROLE_ADMIN'' THEN  ''ADMIN''
                WHEN T."roleName" = ''ROLE_SME_EMPLOYEE'' THEN ''EMPLOYEE''
            END AS "Loại tài khoản",
            (v_user.last_name ||  '' '' || v_user.first_name) AS "Họ và Tên",
            CASE
                WHEN T."roleName" = ''ROLE_SME'' THEN v_user.NAME
                WHEN T."roleName" = ''ROLE_DEVELOPER'' THEN  v_user.NAME
                WHEN T."roleName" = ''ROLE_ADMIN'' THEN  v_user.NAME
                WHEN T."roleName" = ''ROLE_SME_EMPLOYEE'' THEN  v_user.NAME
            END AS "Tên công ty",
            v_user.email,
            v_user.NAME,
            v_user."tin",
            v_user.social_insurance_number AS "Mã BHXH",
            v_user.rep_fullname AS "Tên nguòi đại diện",
            v_user.phone_number AS "Điện thoại",
            v_user.address AS "Địa chỉ lắp đặt",
            v_user.website AS "Website",
            v_user.status,
            CASE
                WHEN v_user.status = 1 THEN        ''Hoạt động''
                WHEN v_user.status = 0 THEN        ''Không hoạt động''
            END AS "Trạng thái tài khoản",
            T."roleName" AS "Phân quyền",
            v_user.created_at AS "created_at",
            v_user.modified_at AS "Ngày cập nhật",
            v_user.tech_id AS "TechId",
            v_user.province_name           as "Tỉnh",
            v_user.department_name         as "Bộ phận",
            v_user.business_area_name      as "Lĩnh vực kinh doanh",
            v_user.business_area_size_name as "Quy mô doanh nghiệp",
            v_user.district_name           as "Quận/Huyện",
            v_user.street_name             as "Phố/Đường",
            v_user.nation_name             as "Quốc gia",
            v_user.ward_name               as "Phường/xã",
            v_user.parent_id as "parent_id",' ||    'v_user.user_code
        FROM
            total_subs  T
            INNER JOIN vnpt_dev.report_view_user_full_info v_user ON v_user.ID = T.ID ) ';
    --    select * from s_full_info where 0 = 1';

    DECLARE fullQuery varchar;
    BEGIN
        fullQuery := FORMAT(baseQuery,
        i_startTime,
        i_endTime,
        i_accountType);

    FOR i IN 1 .. array_length(i_accountStatusType, 1)
            LOOP
                CASE    i_accountStatusType[i]
                    WHEN 'SME_ACTIVE' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.total_active > 0 and t.status = 1 ';
                    WHEN 'SME_TRIAL' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.total_active = 0 and t.total_trial > 0 ';
                    WHEN 'SME_NON_SUB' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.total_active = 0 and t.total_trial = 0 and t.total_future = 0 and t.total_cancel = 0 and t.total_end = 0 ';
                    WHEN 'SME_NON_EXTEND' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.total_active = 0 and t.total_end > 0 ';
                    WHEN 'SME_EXPIRED' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.total_active = 0 and t.total_cancel > 0 ';
                    WHEN 'SME_HAS_LEFT' THEN fullQuery := fullQuery || '  select * from s_full_info t where (t.total_cancel > 0 or t.total_end > 0 ) and t.total_active = 0 and t.total_trial = 0 and t.total_future = 0 ';
                    WHEN 'SEM_CURRENT' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.total_cancel > 0 or t.total_end > 0 or t.total_active > 0 or t.total_trial > 0 or t.total_future > 0 ';
                    WHEN 'INACTIVE' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.status = 0';
                    WHEN 'NONE' THEN fullQuery := fullQuery || 'select * from s_full_info t ';
                    WHEN 'NON_SME_ACTIVE' THEN fullQuery := fullQuery || ' select * from s_full_info t where t.status = 1';
                    WHEN 'NON_SME_INACTIVE' THEN fullQuery := fullQuery || '  select * from s_full_info t where t.status = 0';
                END CASE;
            END LOOP;

    RAISE NOTICE 'Value: %',
    fullQuery;

    RETURN query EXECUTE fullQuery;
    END;

    $function$
    ;
