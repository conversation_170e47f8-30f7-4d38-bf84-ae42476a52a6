-- Tạo function cho phép tạo partition của tháng sau cho một bảng bất kì
CREATE OR REPLACE FUNCTION dx20_common.func_create_partition_by_month(schema_name varchar, tbl_name varchar, timeout int4) RETURNS int2 AS $$
DECLARE
    partition_name TEXT;
    deleting_partition TEXT;
    sql_query TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- <PERSON><PERSON><PERSON> thời gian là tháng sau
    EXECUTE 'SELECT cast(date_trunc(''month'', now() + INTERVAL ''1 month'') as date)' INTO start_date;
    end_date := cast(start_date + INTERVAL '1 month' as date);
    
    -- Tạo tên partition dựa trên tháng
    partition_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM');

    -- <PERSON><PERSON><PERSON> tra nếu partition đã tồn tại
    IF NOT EXISTS (
        SELECT 1
        FROM pg_class
            JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace
        WHERE pg_class.relname = partition_name AND pg_namespace.nspname = schema_name
    ) THEN
        -- Tạo partition mới nếu chưa tồn tại
        sql_query := format(
            'CREATE TABLE %I.%I PARTITION OF %I.%I FOR VALUES FROM (%L) TO (%L)',
            schema_name,
            partition_name,
            schema_name,
            tbl_name,
            start_date,
            end_date
        );
        RAISE NOTICE 'SQL query % ', sql_query;
        EXECUTE sql_query;
        RAISE NOTICE 'Partition % created.', partition_name;

        -- Xóa các partition cũ (cùng lúc tạo partition mới)
        IF timeout != -1 THEN
            EXECUTE format('SELECT CONCAT(''%I_'', TO_CHAR(now() - INTERVAL ''%I'' MONTH, ''YYYY_MM''))', tbl_name, timeout) INTO deleting_partition;
            EXECUTE format('DROP TABLE IF EXISTS %I.%I', schema_name, deleting_partition);
        ELSE
            RAISE NOTICE 'Skip removing old partition of %.%', schema_name, tbl_name;
        END IF;
        RETURN 1;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;

UPDATE "vnpt_dev"."schedules" SET method_name = 'cleanUpFCMToken' WHERE "bean_name" = 'cleanup' AND "method_name" = 'cleanUpExceptionHistory';