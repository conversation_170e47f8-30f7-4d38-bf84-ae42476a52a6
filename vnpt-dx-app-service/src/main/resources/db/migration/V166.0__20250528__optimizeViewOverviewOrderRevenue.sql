create or replace view vnpt_dev.view_overview_order_revenue_v202505 as (
with billingBillItemCte as (
  select
      b.subscriptions_id, b.created_source_migration, b.created_by,
      bill_item.amount, bill_item.object_type, bill_item.billing_id,
      bill_item.id as bill_item_id,
      bct.amount_by_cash as cp_total_amount_by_cash,
      bct.amount_by_percent as cp_total_amount_by_percent,
      bcp.amount_by_cash as cp_private_amount_by_cash,
      bcp.amount_by_percent as cp_private_amount_by_percent
  from vnpt_dev.billings b
           left join vnpt_dev.bill_item on b.id = bill_item.billing_id
           left join vnpt_dev.bill_coupon_total bct on bct.billing_item_id = bill_item.id
           left join vnpt_dev.bill_coupon_private bcp on bcp.billing_item_id = bill_item.id
  where
      (b.created_source_migration is null or b.created_source_migration = 0) and
          bill_item.object_type <> 3
)
select
  subdateandid.id,
  subdateandid.registration_date,
  bill.billing_code as billcode,
  case
      when bill.isrenew = 0 then subdateandid.registration_date
      else bill.created_at::date
      end as registrationdate,
  coalesce(newsubspromotionandunitamount.unitamount, renewsubspromotionandunitamount.unitamount, 0::double precision) as unitamount
from
  (
      select
          subscriptions.id,
          subscriptions.created_at::date as registration_date
      from
          vnpt_dev.subscriptions
      where
        subscriptions.deleted_flag = 1
        and (subscriptions.created_source_migration is null or subscriptions.created_source_migration = 0)
        and (subscriptions.pricing_id is not null or subscriptions.combo_plan_id is not null)
        and subscriptions.confirm_status = 1
  ) subdateandid
      left join
  (
      select
          b2.id,
          b2.status,
          b2.payment_date,
          b2.created_at,
          b2.billing_code,
          b2.subscriptions_id as sub_id,
          selectedBills.isrenew as isrenew
      from
          (
              select max(b.id) as id, b.subscriptions_id as sub_id, 0 as isrenew
              from vnpt_dev.billings b
              where
                  b.created_by::text <> 'batch'::text or b.created_by is null and
                  (b.created_source_migration is null or b.created_source_migration = 0)
              group by
                  b.subscriptions_id
              union
              select b.id, b.subscriptions_id as sub_id, 1 as isrenew
              from vnpt_dev.billings b
              where
                  b.created_by::text = 'batch'::text and
                  (b.created_source_migration is null or b.created_source_migration = 0)
          ) selectedBills
              join vnpt_dev.billings as b2 on b2.id = selectedBills.id
  ) bill on bill.sub_id = subdateandid.id
      left join
  (
      select
          subspromotionamount.id,
          case
              when (subspromotionamount.privateamount + subspromotionamount.totalamount) < 0::double precision then 0::double precision
              else subspromotionamount.privateamount + subspromotionamount.totalamount
              end as promotionamount,
          case
              when subspromotionamount.unitamount < 0::double precision then 0::double precision
              else subspromotionamount.unitamount
              end as unitamount
      from
          (
              select
                  cte.subscriptions_id as id,
                  sum(coalesce(cte.cp_private_amount_by_cash, 0::double precision)) + sum(coalesce(cte.cp_private_amount_by_percent, 0::double precision)) as privateamount,
                  sum(coalesce(cte.cp_total_amount_by_cash, 0::double precision)) + sum(coalesce(cte.cp_total_amount_by_percent, 0::double precision)) as totalamount,
                  sum(coalesce(cte.amount, 0::double precision)) as unitamount
              from billingBillItemCte cte
              where cte.created_by::text <> 'batch'::text or cte.created_by is null
              group by cte.subscriptions_id
          ) subspromotionamount
  ) newsubspromotionandunitamount on newsubspromotionandunitamount.id = subdateandid.id and bill.isrenew = 0
      left join (
      select
          subspromotionamount.id,
          case
              when (subspromotionamount.privateamount + subspromotionamount.totalamount) < 0::double precision then 0::double precision
              else subspromotionamount.privateamount + subspromotionamount.totalamount
              end as promotionamount,
          case
              when subspromotionamount.unitamount < 0::double precision then 0::double precision
              else subspromotionamount.unitamount
              end as unitamount
      from
          (
              select
                  cte.billing_id as id,
                  sum(coalesce(cte.cp_private_amount_by_cash, 0::double precision)) + sum(coalesce(cte.cp_private_amount_by_percent, 0::double precision)) as privateamount,
                  sum(coalesce(cte.cp_total_amount_by_cash, 0::double precision)) + sum(coalesce(cte.cp_total_amount_by_percent, 0::double precision)) as totalamount,
                  sum(coalesce(cte.amount, 0::double precision)) as unitamount
              from billingBillItemCte cte
              where cte.created_by::text = 'batch'::text
              group by cte.billing_id
          ) subspromotionamount
  ) renewsubspromotionandunitamount on renewsubspromotionandunitamount.id = bill.id and bill.isrenew = 1
);

