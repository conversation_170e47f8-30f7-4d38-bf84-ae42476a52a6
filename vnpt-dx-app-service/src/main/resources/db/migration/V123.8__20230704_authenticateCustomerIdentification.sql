--<PERSON><PERSON> sung cấu hình xác thực tài khoản vào bảng system_param
insert into vnpt_dev.system_params(param_name, param_type, param_text_value) 
select 'C<PERSON>u hình xác thực tài khoản', 'AUTHENTICATE_ACCOUNT', '{"enableOTPRegistry":false,"enableOTPChangePassword":false,"enableOTPLogin":true,"enableOTPSwitchAccount":true,"lengthOTP":6,"prefixOTP":"","suffixOTP":"","formatTypeOTP":"LETTER_NUMBER"}'
where not exists (
   select * from vnpt_dev.system_params
   where param_type like 'AUTHENTICATE_ACCOUNT'
);

--Tạo bảng otp để lưu các thông tin về otp
CREATE EXTENSION IF NOT EXISTS "uuid-ossp"; -- tạo module để sử dụng được uuid (trường hash trong bảng)
drop table if exists vnpt_dev.otp;
CREATE TABLE vnpt_dev.otp (
	id bigserial NOT NULL ,
	code varchar NULL,
	action_type int2 NULL, -- Lo<PERSON>i hành động (0: login, 1: change password, 2: activate)
	send_type int2 NULL, -- Loại hình gửi otp (0: email, 1: sms)
	object_type int2 NULL, -- Loại đối tượng gắn với otp (1: user)
	object_id int8 NULL, -- Id đối tượng gắn với otp
	metadata text NULL, -- Lưu các thông tin phụ của otp nếu cần
	created_at timestamp NULL,
	expired_at timestamp NULL,
	deleted_flag int2 NULL,
	hash uuid NOT NULL DEFAULT uuid_generate_v4(),
	CONSTRAINT otp_pkey PRIMARY KEY (id)
);
DROP INDEX IF EXISTS vnpt_dev."otp_hash_idx";
CREATE INDEX otp_hash_idx ON vnpt_dev.otp USING btree (hash);
-- Column comments

COMMENT ON COLUMN vnpt_dev.otp.action_type IS 'Loại hành động (0: login, 1: change-reset password, 2: activate, 3: switch account)';
COMMENT ON COLUMN vnpt_dev.otp.send_type IS 'Loại hình gửi otp (0: email, 1: sms)';
COMMENT ON COLUMN vnpt_dev.otp.object_type IS 'Loại đối tượng gắn với otp (1: user)';
COMMENT ON COLUMN vnpt_dev.otp.object_id IS 'Id đối tượng gắn với otp';
COMMENT ON COLUMN vnpt_dev.otp.metadata IS 'Lưu các thông tin phụ của otp nếu cần';
COMMENT ON COLUMN vnpt_dev.otp.hash IS 'id dạng chuỗi của otp, sẽ được trả ra cho FE khi cần';

UPDATE vnpt_dev.users SET email = lower(email)
WHERE email ~ '^[A-Z]{1,}';
-- Xóa bỏ index sử dụng điều kiện do cập nhật cho phép trùng email
DROP INDEX IF EXISTS vnpt_dev.unique_email_condition;

--Tạo view gồm các admin của hệ thống trừ các affiliate thường. Dùng để check trùng email, sđt ở module định danh khách hàng
drop view if exists view_role_admin_exclude_normal_affiliate;
create view view_role_admin_exclude_normal_affiliate 
as 
    with mResultAdmin as ( 
        select 
            mUserRole.user_id, 
            string_agg(mRole.name, ',') lstRoleName 
        from vnpt_dev.users_roles mUserRole 
            join vnpt_dev.role mRole on mUserRole.role_id = mRole.id 
            --Join vơi view_role_admin để đảm bảo tập dữ liệu là các admin
            join vnpt_dev.view_role_admin mUserAdmin on mUserAdmin.user_id = mUserRole.user_id 
        group by mUserRole.user_id 
    ) 
    select 
        user_id 
    from mResultAdmin 
    where 
        --'ROLE_ADMIN,ROLE_AFFILIATE_DAILY', 'ROLE_ADMIN,ROLE_AFFILIATE_CANHAN' là role của các affiliate thường(không cần check trùn với những affiliate này)
        lstRoleName not in ('ROLE_ADMIN,ROLE_AFFILIATE_DAILY', 'ROLE_ADMIN,ROLE_AFFILIATE_CANHAN', 'ROLE_AFFILIATE_DAILY,ROLE_ADMIN', 'ROLE_AFFILIATE_CANHAN,ROLE_ADMIN');

--Cập nhật index unique_tin_with_khdn_hkd. Thêm điều kiện deleted_flag = 1
drop index if exists unique_tin_with_khdn_hkd;
create unique index unique_tin_with_khdn_hkd on
vnpt_dev.users
    using btree (tin)
where
((id > 325078)
    and (deleted_flag = 1)
    and ((customer_type)::text = any ((array['KHDN'::character varying,
    'HKD'::character varying])::text[])));

-- Thêm batch xóa OTP hàng ngày
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'clean-otp' AND method_name = 'cleanOTPExpired';
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('clean-otp', 'cleanOTPExpired', NULL, '0 0 2 * * ?', 'cleanOTPExpired', 1, 'batch', NULL, 'batch', NULL);