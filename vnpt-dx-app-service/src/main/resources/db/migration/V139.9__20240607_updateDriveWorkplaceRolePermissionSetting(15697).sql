-- 1. Permission
-- <PERSON><PERSON><PERSON><PERSON> c<PERSON>c bảng ghi permission mặc định
-- Drive
DELETE FROM vnpt_dev.wp_permission WHERE code in (1110);
insert into vnpt_dev.wp_permission(code, name, display_name, parent_code, api_key)
values (1110, 'DRIVE_CONFIG', '<PERSON><PERSON>u hình ứng dụng Drive', 1100, 'WORKPLACE');


-- <PERSON><PERSON><PERSON> nh<PERSON>t bảng wp_role_permission
DELETE FROM vnpt_dev.wp_role_permission WHERE permission_code in (1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108, 1109, 1110);
INSERT INTO "vnpt_dev"."wp_role_permission" (role_code, permission_code)
VALUES ('DX_WORKPLACE_ADMIN', 1100),
       ('DX_WORKPLACE_ADMIN', 1101),
       ('DX_WORKPLACE_ADMIN', 1102),
       ('DX_WORKPLACE_ADMIN', 1103),
       ('DX_WORKPLACE_ADMIN', 1104),
       ('DX_WORKPLACE_ADMIN', 1105),
       ('DX_WORKPLACE_ADMIN', 1106),
       ('DX_WORKPLACE_ADMIN', 1107),
       ('DX_WORKPLACE_ADMIN', 1108),
       ('DX_WORKPLACE_ADMIN', 1109),
       ('DX_WORKPLACE_ADMIN', 1110);

-- Tạo Table lưu trữ Cấu hình ứng dụng Drive
drop TABLE if exists vnpt_dev.wp_drive_config;
CREATE TABLE vnpt_dev.wp_drive_config
(
    "id"            bigserial NOT NULL,
    "user_id"       int8,
    "draggable"     int2,
    "sort_type"     int2,

    "show_recently" int2,
    "auto_delete"   int2
);

COMMENT
ON COLUMN "vnpt_dev"."wp_drive_config"."user_id" IS 'Id của người dùng (Chủ doanh nghiêp)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_config"."draggable" IS 'Cho phép kéo thả các tệp và thư mục (0-không, 1-có)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_config"."sort_type" IS 'Thứ tự sắp xếp (1-AZ, 2-ZA, 3-Sửa đổi lần cuối, 4-Dung lượng lớn nhất, 5-Dung lượng nhỏ nhất)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_config"."show_recently" IS 'Hiển thị gần đây (0-không, 1-có)';
COMMENT
ON COLUMN "vnpt_dev"."wp_drive_config"."auto_delete" IS 'Thời gian tự động xóa trong thùng rác(Ngày)';

INSERT INTO vnpt_dev.schedules(bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('drive', 'autoDeleteDrive', NULL, '0 0 0 * * ?', 'autoDeleteDrive', 1, 'batch', NULL, 'batch', NULL);