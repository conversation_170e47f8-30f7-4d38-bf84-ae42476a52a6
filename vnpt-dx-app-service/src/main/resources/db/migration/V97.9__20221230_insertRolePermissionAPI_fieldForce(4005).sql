
-- Thêm permission quản lý phân vùng dữ liệu
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'TAO_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'SUA_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH');

DELETE FROM "vnpt_dev"."roles_permissions" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."roles_permissions" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."roles_permissions" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'TAO_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."roles_permissions" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'SUA_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."roles_permissions" WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHAN_VIEN_KINH_DOANH');
DELETE FROM "vnpt_dev"."permission" WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH';
DELETE FROM "vnpt_dev"."permission" WHERE code = 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH';
DELETE FROM "vnpt_dev"."permission" WHERE code = 'TAO_NHAN_VIEN_KINH_DOANH';
DELETE FROM "vnpt_dev"."permission" WHERE code = 'SUA_NHAN_VIEN_KINH_DOANH';
DELETE FROM "vnpt_dev"."permission" WHERE code = 'XOA_NHAN_VIEN_KINH_DOANH';
DELETE FROM "vnpt_dev"."permission" WHERE code = 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH';


INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Quản lý nhân viên kinh doanh', 'QUAN_LY_NHAN_VIEN_KINH_DOANH', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Xem danh sách nhân viên kinh doanh', 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH', (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH'), (SELECT max(priority) + 1 FROM vnpt_dev.permission));
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Tạo nhân viên kinh doanh', 'TAO_NHAN_VIEN_KINH_DOANH', (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH'), (SELECT max(priority) + 1 FROM vnpt_dev.permission));
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Sửa nhân viên kinh doanh', 'SUA_NHAN_VIEN_KINH_DOANH', (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH'), (SELECT max(priority) + 1 FROM vnpt_dev.permission));
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Xóa nhân viên kinh doanh', 'XOA_NHAN_VIEN_KINH_DOANH', (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH'), (SELECT max(priority) + 1 FROM vnpt_dev.permission));
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission), 'Xem chi tiết nhân viên kinh doanh', 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH', (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHAN_VIEN_KINH_DOANH'), (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='QUAN_LY_NHAN_VIEN_KINH_DOANH' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));
INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));
INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='TAO_NHAN_VIEN_KINH_DOANH' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));
INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='SUA_NHAN_VIEN_KINH_DOANH' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));
 INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='XOA_NHAN_VIEN_KINH_DOANH' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));
 INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.permission_portal), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' LIMIT 1), (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));


INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='QUAN_LY_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='QUAN_LY_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);


INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_MANAGER' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_MANAGER' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='QUAN_LY_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_MANAGER' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='TAO_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_MANAGER' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='SUA_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_MANAGER' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XOA_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_MANAGER' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);


INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_STAFF' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_STAFF' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN_STAFF' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='QUAN_LY_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);


INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='TAO_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='TAO_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='SUA_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='SUA_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='FULL_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XOA_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);
INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.roles_permissions), (SELECT id FROM vnpt_dev.role WHERE name='ROLE_ADMIN' LIMIT 1), (SELECT id FROM vnpt_dev.permission WHERE code='XOA_NHAN_VIEN_KINH_DOANH' LIMIT 1), 1);

-- Thêm cấu hình api/permission
DELETE  FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' AND method = 'GET');
DELETE  FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' AND method = 'GET');
DELETE  FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'TAO_NHAN_VIEN_KINH_DOANH' AND method = 'POST');
DELETE  FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'SUA_NHAN_VIEN_KINH_DOANH' AND method = 'PUT');
DELETE  FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'XOA_NHAN_VIEN_KINH_DOANH' AND method = 'DELETE');


DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' AND method = 'GET';
DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' AND method = 'GET';
DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'TAO_NHAN_VIEN_KINH_DOANH' AND method = 'POST';
DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'SUA_NHAN_VIEN_KINH_DOANH' AND method = 'PUT';
DELETE  FROM "vnpt_dev"."apis" WHERE api_code = 'XOA_NHAN_VIEN_KINH_DOANH' AND method = 'DELETE';



INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/users-admin', 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH', 'GET');
INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' AND method = 'GET' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'XEM_DANH_SACH_NHAN_VIEN_KINH_DOANH' LIMIT 1) LIMIT 1), 1, 1);
INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/users-admin', 'TAO_NHAN_VIEN_KINH_DOANH', 'POST');
INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='TAO_NHAN_VIEN_KINH_DOANH' AND method = 'POST' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'TAO_NHAN_VIEN_KINH_DOANH' LIMIT 1) LIMIT 1), 1, 1);
INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/users-admin/{id}', 'SUA_NHAN_VIEN_KINH_DOANH', 'PUT');
INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='SUA_NHAN_VIEN_KINH_DOANH' AND method = 'PUT' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'SUA_NHAN_VIEN_KINH_DOANH' LIMIT 1) LIMIT 1), 1, 1);
INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/users-admin/{id}', 'XOA_NHAN_VIEN_KINH_DOANH', 'DELETE');
INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='XOA_NHAN_VIEN_KINH_DOANH' AND method = 'DELETE' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'XOA_NHAN_VIEN_KINH_DOANH' LIMIT 1) LIMIT 1), 1, 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.apis), '/api/users/{id}', 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH', 'GET');
INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' AND method = 'GET' LIMIT 1), (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'XEM_CHI_TIET_NHAN_VIEN_KINH_DOANH' LIMIT 1) LIMIT 1), 1, 1);

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;


