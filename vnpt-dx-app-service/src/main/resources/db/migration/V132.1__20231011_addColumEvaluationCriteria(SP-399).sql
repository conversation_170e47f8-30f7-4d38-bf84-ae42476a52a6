ALTER TABLE "vnpt_dev"."evaluation_criteria"
  ADD COLUMN IF NOT EXISTS "type" int2,
  ADD COLUMN IF NOT EXISTS "priority" int4;

COMMENT ON COLUMN "vnpt_dev"."evaluation_criteria"."type" IS '0 - <PERSON><PERSON><PERSON>, 1 - Nhận xét';

DELETE FROM "vnpt_dev"."evaluation_criteria" where type = 1;
INSERT INTO "vnpt_dev"."evaluation_criteria" ("id", "name", "deleted_flag", "type") VALUES (5, 'Nhận xét', 1, 1);

UPDATE "vnpt_dev"."evaluation_criteria" SET "type" = 0, "priority" = id;

UPDATE "vnpt_dev"."evaluation_criteria" SET "type" = 1 WHERE id = (SELECT max(id) from "vnpt_dev"."evaluation_criteria");