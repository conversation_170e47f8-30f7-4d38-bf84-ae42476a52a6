WITH role_id_cte AS (
    SELECT id AS role_id
    FROM vnpt_dev.role
    WHERE name = 'ROLE_SME'
    LIMIT 1
),
user_id_cte AS (
    SELECT id AS user_id
    FROM vnpt_dev.users
    WHERE email = 'kha<PERSON><EMAIL>'
    LIMIT 1
)
INSERT INTO vnpt_dev.users_roles(user_id, role_id)
SELECT u.user_id, r.role_id
FROM user_id_cte u
     JOIN role_id_cte r ON true
     LEFT JOIN vnpt_dev.users_roles ur
           ON ur.user_id = u.user_id AND ur.role_id = r.role_id
WHERE ur.user_id IS NULL;