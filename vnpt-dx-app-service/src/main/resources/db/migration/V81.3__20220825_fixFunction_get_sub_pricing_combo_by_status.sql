DROP FUNCTION IF EXISTS "vnpt_dev"."count_sub_per_pricing_by_reg_type_namnd";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_pricing_by_reg_type_namnd"("register_type" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("pricing_id" int8, "service_id" int8, "num_sub" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT sub.pricing_id AS pricing_id, se.id as service_id, COUNT (*) AS num_sub
FROM vnpt_dev.subscriptions AS sub
         LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
         LEFT JOIN vnpt_dev.services se on p.service_id=se.id
WHERE sub.reg_type = register_type AND sub.confirm_status = 1 AND
    (p.created_at::date >= i_starttime::date AND p.created_at::date <= i_endtime::date)
GROUP BY sub.pricing_id, se.id;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."count_sub_per_pricing_by_status_namnd";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_pricing_by_status_namnd"("i_status" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("pricing_id" int8, "service_id" int8, "num_sub" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT sub.pricing_id AS pricing_id , se.id as service_id, COUNT (*) AS num_sub
FROM vnpt_dev.subscriptions AS sub
         LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
         LEFT JOIN vnpt_dev.services se on p.service_id=se.id
WHERE   sub.confirm_status = 1 AND
        sub.status = i_status AND
    (p.created_at::date >= i_starttime::date AND p.created_at::date <= i_endtime::date)
GROUP BY sub.pricing_id, se.id;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

