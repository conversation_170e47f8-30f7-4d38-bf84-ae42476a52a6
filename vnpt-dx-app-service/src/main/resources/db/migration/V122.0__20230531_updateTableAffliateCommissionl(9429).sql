ALTER TABLE "vnpt_dev"."affiliate_commission"
    ADD COLUMN IF NOT EXISTS "apply_service_ids" int8[];

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission"."apply_service_ids" IS 'Id của các SPDV được chọn áp dụng khi is_apply_object_all = false. Khi is_apply_object_all = true cột này sẽ không có giá trị';

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission"."apply_object_ids" IS 'Id pricing_comboplan duoc chon ap dung ';

UPDATE "vnpt_dev"."affiliate_commission"  SET apply_service_ids = apply_object_ids where apply_service_ids is null;

ALTER TABLE "vnpt_dev"."affiliate_commission"
    ADD COLUMN IF NOT EXISTS "is_update" bool;

COMMENT ON COLUMN "vnpt_dev"."affiliate_commission"."is_update" IS 'đã update dịch vụ thành gói dịch vụ chưa ?';