DELETE FROM "vnpt_dev"."mail_template" WHERE code IN ('AFF-08');
DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code IN ('AFF-08');
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code IN ('AFF-08');

INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by,
                                          created_at, modified_by, modified_at, receiver, action_code,
                                          allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order, is_send_telegram, allow_change_telegram)
VALUES ('<PERSON>min gửi lại thông tin tài khoản affiliate', 1, 0, 1,
        (select id from vnpt_dev.action_notification where action_code = 'AFF'), 'system', NULL, NULL,
        '2021-10-31 00:00:00', 'Tài khoản affiliate', 'AFF-08', 'B', 'B', 'B',
        (SELECT max(priority_order) + 1 FROM vnpt_dev.action_notification), NULL, NULL);

INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text,
                                    content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES ('AFF-08', 'Thông tin tài khoản', 1,
        '<!DOCTYPE html>
        <html>
          <head>
              <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
              <link rel="preconnect" href="https://fonts.googleapis.com">
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          </head>
          <body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
            <div class="container" style="background-color: #ffffff;">
              <div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>

              <div class="content-container" style="padding: 40px;">
                <div class="title-container" style="text-align: center; padding: 40px 0 60px;">
                    <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Logo" />
                    <p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Thông tin tài khoản</p>
                </div>
                <div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
                  <p style="margin: 0;">Xin chào $MEMBER_NAME,</p>
                  <p style="margin: 0;">$USER_TYPE có thể tiếp tục đăng nhập và sử dụng tài khoản affiliate với thông tin sau đây:</p>
                  <p style="margin: 0; ">- Tên đăng nhập: <span>$USER</span></p>
                  <p style="margin: 0; margin-bottom: 20px;">- Password: <span>$PASSWORD</span></p>
                  <p style="margin: 0; margin-top: 10px;text-align: center;">
                        <a href="$LINK_LOGIN_AFF">
                            <button style="background-color: #2c3d94; color: #ffffff; padding: 10px;border-radius: 0.5rem;">
                                Đặt lại mật khẩu
                            </button>
                        </a>
                  </p>
                  <p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
                  <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
                </div>
              </div>

              <div class="footer-container" style="padding: 40px;">$FOOTER</div>
            </div>
          </body>
        </html>',
        '<!DOCTYPE html>
        <html>
          <head>
              <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
              <link rel="preconnect" href="https://fonts.googleapis.com">
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          </head>
          <body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
            <div class="container" style="background-color: #ffffff;">
              <div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>

              <div class="content-container" style="padding: 40px;">
                <div class="title-container" style="text-align: center; padding: 40px 0 60px;">
                    <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Logo" />
                    <p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Thông tin tài khoản</p>
                </div>
                <div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
                  <p style="margin: 0;">Xin chào $MEMBER_NAME,</p>
                  <p style="margin: 0;">$USER_TYPE có thể tiếp tục đăng nhập và sử dụng tài khoản affiliate với thông tin sau đây:</p>
                  <p style="margin: 0; ">- Tên đăng nhập: <span>$USER</span></p>
                  <p style="margin: 0; margin-bottom: 20px;">- Password: <span>$PASSWORD</span></p>
                  <p style="margin: 0; margin-top: 10px;text-align: center;">
                        <a href="$LINK_LOGIN_AFF">
                            <button style="background-color: #2c3d94; color: #ffffff; padding: 10px;border-radius: 0.5rem;">
                                Đặt lại mật khẩu
                            </button>
                        </a>
                  </p>
                  <p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
                  <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
                </div>
              </div>

              <div class="footer-container" style="padding: 40px;">$FOOTER</div>
            </div>
          </body>
        </html>', null, null, 'AFF', 'Thông tin tài khoản','Thông tin tài khoản', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0);

INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES ((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-08' LIMIT 1), '$MEMBER_NAME', '[Tên thành viên]', 'AFF-08', ''),
 ((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-08' LIMIT 1),'$USER_TYPE','[Loại thành viên]','AFF-08',''),
 ((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-08' LIMIT 1),'$USER','[Tên đăng nhập]','AFF-08',''),
 ((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-08' LIMIT 1),'$PASSWORD','[Mật khẩu đăng nhập]','AFF-08',''),
 ((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-08' LIMIT 1),'$LINK_LOGIN_AFF','[Trang đăng nhập affiliate]','AFF-08','');

/* AFF-10 quen mat khau */
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code IN ('AFF-10');
INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by,
                                          created_at, modified_by, modified_at, receiver, action_code,
                                          allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Admin gửi mail đổi mật khẩu tài khoản affiliate', 1, 0, 1,
        (select id from vnpt_dev.action_notification where action_code = 'AFF'), 'system', NULL, NULL,
        '2021-10-31 00:00:00', 'Tài khoản affiliate', 'AFF-10', 'B', 'B', 'B',
        (SELECT max(priority_order) + 1 FROM vnpt_dev.action_notification), NULL, NULL);

DELETE FROM "vnpt_dev"."mail_template" WHERE code IN ('AFF-10');
INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text,
                                    content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES ('AFF-10', 'Đặt lại mật khẩu mới', 1,
        '<!DOCTYPE html>
        <html>
          <head>
              <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
              <link rel="preconnect" href="https://fonts.googleapis.com">
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          </head>
          <body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
            <div class="container" style="background-color: #ffffff;">
              <div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>

              <div class="content-container" style="padding: 40px;">
                <div class="title-container" style="text-align: center; padding: 40px 0 60px;">
                    <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Logo" />
                    <p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">ĐẶT LẠI MẬT KHẨU MỚI</p>
                </div>
                <div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
                  <p style="margin: 0;">Xin chào $USER,</p>
                  <p style="margin: 0;">Tổ chức đã yêu cầu đặt lại mật khẩu cho tài khoản affiliate của mình.</p>
                  <p style="margin: 0;">Vui lòng ấn vào đường link dưới đây để đặt lại mật khẩu:</p>
                  <p style="margin: 0; margin-top: 10px;text-align: center;">
                        <a href="$LINK_RESET_PWD">
                            <button style="background-color: #2c3d94; color: #ffffff; padding: 10px;border-radius: 0.5rem;">
                                Đặt lại mật khẩu
                            </button>
                        </a>
                  </p>
                  <p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
                  <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
                </div>
              </div>

              <div class="footer-container" style="padding: 40px;">$FOOTER</div>
            </div>
          </body>
        </html>',
        '<!DOCTYPE html>
        <html>
          <head>
              <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
              <link rel="preconnect" href="https://fonts.googleapis.com">
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          </head>
          <body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
            <div class="container" style="background-color: #ffffff;">
              <div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>

              <div class="content-container" style="padding: 40px;">
                <div class="title-container" style="text-align: center; padding: 40px 0 60px;">
                    <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Logo" />
                    <p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">ĐẶT LẠI MẬT KHẨU MỚI</p>
                </div>
                <div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
                  <p style="margin: 0;">Xin chào $USER,</p>
                  <p style="margin: 0;">Tổ chức đã yêu cầu đặt lại mật khẩu cho tài khoản affiliate của mình.</p>
                  <p style="margin: 0;">Vui lòng ấn vào đường link dưới đây để đặt lại mật khẩu:</p>
                  <p style="margin: 0; margin-top: 10px;text-align: center;">
                        <a href="$LINK_RESET_PWD">
                            <button style="background-color: #2c3d94; color: #ffffff; padding: 10px;border-radius: 0.5rem;">
                                Đặt lại mật khẩu
                            </button>
                        </a>
                  </p>
                  <p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
                  <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
                </div>
              </div>

              <div class="footer-container" style="padding: 40px;">$FOOTER</div>
            </div>
          </body>
        </html>', null, null, 'AFF', 'Đặt lại mật khẩu mới','Đặt lại mật khẩu mới', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0);

DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code IN ('AFF-10');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES ((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-10' LIMIT 1), '$USER', '[Tên thành viên]', 'AFF-10', ''),
((SELECT id FROM vnpt_dev.mail_template WHERE code = 'AFF-10' LIMIT 1),'$LINK_RESET_PWD','[Trang đặt lại mk affiliate]','AFF-10','');

--ACTION_NOTIFICATION
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code IN ('AFF-19');
INSERT INTO vnpt_dev.action_notification (name,is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification,priority_order,is_send_telegram,allow_change_telegram) VALUES
('Thay đổi thông thành viên cấp trên Affiliate',1,1,1, (select id from vnpt_dev.action_notification where action_code = 'AFF'),'system',NULL,NULL,'2021-10-31 00:00:00','Bạn có thêm thành viên mới', 'AFF-19', 'B','B','B',(SELECT max(priority_order) + 1 FROM vnpt_dev.action_notification),NULL,NULL);
