-- add column password_updated_status / password_type/ password_modified_at --
ALTER TABLE vnpt_dev.users ADD COLUMN if not exists password_modified_at TIMESTAMP;
COMMENT ON COLUMN vnpt_dev.users.password_modified_at is 'thời gian cập nhật mật khẩu';

alter table "vnpt_dev"."users"
    add column if not exists "password_updated_status" int4;
comment on column "vnpt_dev"."users"."password_updated_status" is 'Trạng thái cập nhật mật khẩu của tài khoản (null: <PERSON><PERSON><PERSON> tà<PERSON> khoản cũ chưa có thông tin, -1: <PERSON>h<PERSON>ng cho phép đổi mật khẩu (các nguồn tạo tài khoản khác OneSME) 0: Ch<PERSON><PERSON> đổ<PERSON> bất kỳ lần nào, 1: <PERSON><PERSON> thay đổi ít nhất một lần))';

alter table "vnpt_dev"."users"
    add column if not exists "password_type" int4;
comment on column "vnpt_dev"."users"."password_type" is '<PERSON><PERSON><PERSON> mật khẩu (-1/null: <PERSON><PERSON><PERSON> tà<PERSON>ho<PERSON> c<PERSON> ch<PERSON>a c<PERSON> thông tin, 0: <PERSON><PERSON>t kh<PERSON>u tạo ngẫu nhiên, 1: M<PERSON>t khẩu do ng<PERSON>ời dùng nhập))';

-- set thời gian đổi mk gần nhất với luc trien khai tinh nang --
update "vnpt_dev"."users"
set password_modified_at = now(),
    password_updated_status = 1
where password_modified_at is null
  and provider_type = 0;
-- với các tài khoản tạo ra trước khi tính năng được đưa lên;
-- cập nhật trường thông tin password_updated_status / password_type --
update "vnpt_dev"."users"
set password_updated_status = 1
where provider_type = 0
  and password_modified_at is not null; -- đã thay đổi mật khẩu ít nhất 1 lần;
update "vnpt_dev"."users"
set password_updated_status = -1
where provider_type in (1, 2, 4); -- các tài khoản có nguồn tạo khác OneSME -> không thông báo yêu cầu đổi mật khẩu;

update "vnpt_dev"."users"
set password_type = 1
where provider_type = 0
  and password_modified_at is not null; -- mật khẩu đã được thay đổi ít nhất 1 lần bởi người dùng -> loại mật khẩu là input_password


-- Thêm uuid cho các loại tài khoản khác ngoài sme là admin/dev --
alter table "vnpt_dev"."users"
    add constraint "users_uuid_unique_key" unique ("uuid");
comment on column "vnpt_dev"."users"."uuid" is 'UUID của user trong hệ thống';
update "vnpt_dev"."users" set "uuid" = uuid_generate_v4() where "uuid" is null;