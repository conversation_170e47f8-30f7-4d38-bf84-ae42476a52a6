drop function if exists vnpt_dev.func_get_service_combo_apply_affiliate_commission;
CREATE OR REPLACE FUNCTION vnpt_dev.func_get_service_combo_apply_affiliate_commission(
	i_currentRoleName character varying, 
	i_userId bigint,
	i_key character varying,
	i_limit int
)
 RETURNS TABLE(
 	objectUniqueId bigint,
    objectName varchar(200)
 )
 LANGUAGE plpgsql
AS $function$
begin
	--Trường hợp có ít nhất 1 bản ghi chọn all sản phẩm dịch vụ thì lấy hết sản phẩm dịch vụ 
	if exists (select 1 from vnpt_dev.affiliate_commission
				where id in (
					--<PERSON><PERSON> quyền
					select affiliate_commission_id
					from vnpt_dev.func_get_affiliate_commission_by_role(i_currentRoleName, i_userId)
				) and is_apply_object_all = true 
	) then
		begin
			--L<PERSON>y ra tất cả service_combo
			return query (
				select *
				from (
					select (services.id * 10000) as objectUniqueId, service_draft_cte.*
					from vnpt_dev.services
					left join lateral ( 
     					select services_draft.service_name as objectName
     					from vnpt_dev.services_draft 
     					where services_draft.service_id = services.id
     						and services_draft.deleted_flag = 1
    				 	order by services_draft.id desc
					limit 1) service_draft_cte on true
					where service_draft_cte.objectName ilike ('%' || i_key || '%')
					union all
					select (combo.id * 10000 + 1) as objectUniqueId, combo.combo_name as objectName
					from vnpt_dev.combo
					where combo.deleted_flag = 1 and combo.combo_name ilike ('%' || i_key || '%')
				) service_combo
				limit i_limit
			);
		end;
	--Trường hợp không có bản ghi chọn sản phẩm dịch vụ
	else
		begin
			return query (
				with affiliate_commission_product_cte as (
					select affiliate_commission.id, unnest(affiliate_commission.apply_object_ids) uniqueid
					from vnpt_dev.affiliate_commission
					where id in (
						--Phân quyền
						select affiliate_commission_id
						from vnpt_dev.func_get_affiliate_commission_by_role(i_currentRoleName, i_userId)
					)
				)
				select *
				from (
					select distinct affiliate_commission_product_cte.uniqueid as objectUniqueId, service_draft_cte.*
					from affiliate_commission_product_cte
					left join lateral ( 
     					select services_draft.service_name as objectName
     					from vnpt_dev.services_draft 
     					where (services_draft.service_id * 10000) = affiliate_commission_product_cte.uniqueid
     					and services_draft.deleted_flag = 1
     					order by services_draft.id desc
					limit 1) service_draft_cte on true
					where (affiliate_commission_product_cte.uniqueid % 10000) = 0
						and service_draft_cte.objectName ilike ('%' || i_key || '%')
					union all
					select distinct affiliate_commission_product_cte.uniqueid as objectUniqueId, combo.combo_name as objectName
					from affiliate_commission_product_cte 
						join vnpt_dev.combo on (combo.id * 10000 + 1) = affiliate_commission_product_cte.uniqueid
					where (affiliate_commission_product_cte.uniqueid % 10000) = 1
						and combo.deleted_flag = 1
						and combo.combo_name ilike ('%' || i_key || '%')
				) service_combo
				limit i_limit
			);
		end;
	end if;
end $function$
;
