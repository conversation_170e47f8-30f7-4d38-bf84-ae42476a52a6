drop view if exists vnpt_dev.view_dashboard_revenue_by_month;
create or replace view vnpt_dev.view_dashboard_revenue_by_month as 
with lastest_combo as (
    select combo_1.combo_draft_id,
        max(combo_1.id) * 10000 + 1 as lastest_combo_unique_id
    from vnpt_dev.combo combo_1
    group by combo_1.combo_draft_id
)
select 
    subscriptions.id as subscription_id,
    subscriptions.created_at::date as created_at,
    pricing.id as pricing_id,
    combo_plan.id as combo_plan_id,
    case
        when coalesce(services.service_owner, combo.combo_owner) = any (array[0, 1]) then 0
        else 1
    end as os_on,
    case
        when lastest_combo.lastest_combo_unique_id is null then concat(services.id, '0000')::bigint
        else lastest_combo.lastest_combo_unique_id
    end as service_lastest_unique_id,
    coalesce(services.user_id, combo.user_id) as developer_id,
    subscriptions.user_id,
    case
        when subscriptions.portal_type = 1 then 'Admin'::text
        when subscriptions.portal_type = 2 then 'Dev'::text
        when subscriptions.portal_type = 3 then 'OneSME'::text
        else ''::text
    end as created_source
from vnpt_dev.subscriptions
    left join vnpt_dev.pricing on pricing.id = subscriptions.pricing_id
    left join vnpt_dev.services on services.id = pricing.service_id
    left join vnpt_dev.combo_plan on combo_plan.id = subscriptions.combo_plan_id
    left join vnpt_dev.combo on combo.id = combo_plan.combo_id
    left join vnpt_dev.users on users.id = subscriptions.user_id
    left join vnpt_dev.province on province.id = users.province_id
    left join lastest_combo on lastest_combo.combo_draft_id = combo.combo_draft_id
where subscriptions.deleted_flag = 1 and subscriptions.confirm_status = 1;