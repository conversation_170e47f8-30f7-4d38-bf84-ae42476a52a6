UPDATE
    mail_template
SET content_html = '<!DOCTYPE html> <html> <head> <meta content="text/html; charset=UTF-8" http-equiv="Content-Type"> <link rel="preconnect" href="https://fonts.googleapis.com"> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin> </head> <body style ="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;"> <div class="container" style ="background-color: #ffffff;"> <div class="logo-container" style ="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div> <div class="content-container" style ="padding: 40px;"> <div class="title-container" style ="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" /> <p class="main-title" style ="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Sửa thuê bao thành công</p> </div> <div class="main-content" style ="line-height: 22px; font-size: 14px; letter-spacing: .3px;"> <p class="mb-m" style ="margin: 0; margin-bottom: 20px;">Xin chào $USER, </p> <p style ="margin: 0;">Doanh nghiệp của bạn đã sửa thành công dịch vụ $NAME_SERVICE - $NAME_PRICING</p> <p style ="margin: 0; margin-left: 10px;"><strong>Mã giao dịch:</strong> $CODE_TRANSACTION.</p> <p style ="margin: 0; margin-left: 10px;"><strong>Người sửa:</strong>$NAME_MODIFIED.</p> <p style ="margin: 0; margin-left: 10px;"><strong>Thời gian sửa:</strong> $DATE_MODIFIED.</p> <p style ="margin: 0; margin-left: 10px;"><strong>Nội dung sửa:</strong></p> <p style ="margin: 0; margin-left: 10px;">$CONTENT_UPDATE</p> <p style ="margin: 0;"><strong>Ngày áp dụng thay đổi:</strong> $DATE_APPLY</p> <p style ="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để sử dụng dịch vụ.</p> <p style ="margin: 0;">$LINK_USE_SUBS</p> <p class="mt-m" style ="margin: 0; margin-top: 20px;">Trân trọng, </p> <p style ="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p> </div> </div> <div class="footer-container" style ="padding: 40px;">$FOOTER</div> </div> </body> </html>'
WHERE
    code = 'SC-16';

DELETE
FROM param_email
WHERE mail_template_code = 'SC-16'
	AND param_name IN ('$NAME_PRICING_OLD', '$NAME_PRICING_NEW', '$QUANTITY_PRICING_OLD', '$QUANTITY_PRICING_NEW', '$NAME_ADDON_QUANTITY', 
	'$NAME_ADDON_AMOUNT', '$PRICE_ADDON_OLD', '$PRICE_ADDON_NEW', '$AMOUNT_ADDON_OLD', '$AMOUNT_ADDON_NEW',
	'$PRICE_PRICING_OLD', '$PRICE_PRICING_NEW', '$NAME_ADDON_DELETED', '$NAME_ADDON_ADDED'
	);

INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES (118, '$CONTENT_UPDATE', '[Nội dung chỉnh sửa]', 'SC-16', NULL);