/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:48:52
*/


-- ----------------------------
-- Table structure for mc_effect_item_history_reason_desc
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_effect_item_history_reason_desc";
CREATE TABLE "vnpt_dev"."mc_effect_item_history_reason_desc" (
  "id" bigserial NOT NULL,
  "code" int4,
  "description" varchar(4096) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "created_by" int2,
  "modified_by" int2,
  "status" int2,
  "deleted_flag" int2
)
;

-- ----------------------------
-- Primary Key structure for table mc_effect_item_history_reason_desc
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_effect_item_history_reason_desc" ADD CONSTRAINT "mc_effect_item_history_reason_desc_pkey" PRIMARY KEY ("id");
