DROP TABLE IF EXISTS "vnpt_dev"."mc_send_coupon_code_history";
CREATE TABLE "vnpt_dev"."mc_send_coupon_code_history" (
  "id" bigserial,
  "coupon_set_id" int8,
  "type" int2,
  "coupon_code" varchar(50),
  "email" varchar(100),
  "total_existed" int4
);

COMMENT ON COLUMN "vnpt_dev"."mc_send_coupon_code_history"."coupon_set_id" IS 'id của coupon_set';
COMMENT ON COLUMN "vnpt_dev"."mc_send_coupon_code_history"."type" IS 'loại bộ mã KM: 1: coupon-set; 2: signle-code';
COMMENT ON COLUMN "vnpt_dev"."mc_send_coupon_code_history"."coupon_code" IS 'Mã KM';
COMMENT ON COLUMN "vnpt_dev"."mc_send_coupon_code_history"."email" IS 'Email <PERSON> nhận';
COMMENT ON COLUMN "vnpt_dev"."mc_send_coupon_code_history"."total_existed" IS 'Tổng số mã còn lại (chỉ đối với type = 2)';