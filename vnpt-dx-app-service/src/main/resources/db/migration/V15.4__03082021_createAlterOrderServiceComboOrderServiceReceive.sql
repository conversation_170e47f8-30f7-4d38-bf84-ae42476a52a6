alter table vnpt_dev.order_service_receive
add column service_id  int8,
add column combo_id  int8;
comment on column vnpt_dev.order_service_receive.service_id is 'Id trong bảng service';
comment on column vnpt_dev.order_service_receive.combo_id is 'Id của combo';

drop table if exists vnpt_dev.order_service_combo;
create table vnpt_dev.order_service_combo 
(
    id  bigserial not null,
	order_service_receive_id  int8,
	combo_id int8,
	service_id int8,
	order_status varchar(50),
	setup_status varchar(50),
	PRIMARY KEY (id)
);
comment on column vnpt_dev.order_service_combo.order_service_receive_id is 'Id trong bảng order_service_receive';
comment on column vnpt_dev.order_service_combo.combo_id is 'Id của combo';
comment on column vnpt_dev.order_service_combo.order_status is 'Trạng thái đơn hàng';
comment on column vnpt_dev.order_service_combo.setup_status is 'Trạng thái cài đặt';


alter table vnpt_dev.order_service_receive_log
add column order_service_combo_id   int8;
comment on column vnpt_dev.order_service_receive_log.order_service_combo_id is 'id của bảng order_service_combo';



