-- New query for get_combo_plan_latest_version --

DROP FUNCTION IF EXISTS "vnpt_dev"."get_combo_plan_latest_version"("i_endtime" varchar);
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_combo_plan_latest_version"("i_endtime" varchar)
    RETURNS TABLE("id" int8, "combo_code" varchar, "combo_name" varchar, "description" varchar, "payment_cycle" int2, "number_of_cycles" int2, "cycle_type" int2, "combo_id" int8, "unit_id" int8, "currency_id" int8, "amount" float8, "setup_fee" float8, "free_quantity" int4, "estimate_quantity" int4, "created_at" timestamp, "created_by" int8, "modified_at" timestamp, "modified_by" int8, "status" int2, "deleted_flag" int2, "price" float8, "list_feature_id" varchar, "combo_order" int2, "recommended_status" int2, "update_reason" varchar, "approve" int2, "combo_draft_id" int8, "approve_time" timestamp, "sme_combo_id" int8, "trial_type" int2, "number_of_trial" int2, "combo_plan_type" int2, "has_change_price" int2, "has_change_quantity" int2, "has_refund" int2, "cancel_date" int2, "active_date" int4, "duration_type" int2, "combo_plan_draft_id" int8, "discount_value" float8, "discount_type" int2, "department_id" int8, "province_id" int8, "reason_reject" text, "is_change_now" int2, "is_update_now" int2)
AS
$BODY$
DECLARE
BEGIN
    RETURN QUERY
        SELECT
            DISTINCT ON (cp.combo_draft_id)
            cp.id,
            cp.combo_code,
            cp.combo_name,
            cp.description,
            cp.payment_cycle,
            cp.number_of_cycles,
            cp.cycle_type,
            cp.combo_id,
            cp.unit_id,
            cp.currency_id,
            cp.amount,
            cp.setup_fee,
            cp.free_quantity,
            cp.estimate_quantity,
            cp.created_at,
            cp.created_by,
            cp.modified_at,
            cp.modified_by,
            cp.status,
            cp.deleted_flag,
            cp.price,
            cp.list_feature_id,
            cp.combo_order,
            cp.recommended_status,
            cp.update_reason,
            cp.approve,
            cp.combo_draft_id,
            cp.approve_time,
            cp.sme_combo_id,
            cp.trial_type,
            cp.number_of_trial,
            cp.combo_plan_type,
            cp.has_change_price,
            cp.has_change_quantity,
            cp.has_refund,
            cp.cancel_date,
            cp.active_date,
            cp.duration_type,
            cp.combo_plan_draft_id,
            cp.discount_value,
            cp.discount_type,
            cp.department_id,
            cp.province_id,
            cp.reason_reject,
            cp.is_change_now,
            cp.is_update_now
        FROM vnpt_dev.combo_plan cp
        WHERE ((cp.deleted_flag = 1) AND (cp.approve = 1)) AND cp.approve_time::date <= i_endtime::date
        ORDER BY cp.combo_draft_id, cp.approve_time DESC;

END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;