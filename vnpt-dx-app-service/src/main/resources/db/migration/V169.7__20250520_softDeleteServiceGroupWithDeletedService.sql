WITH delete_service_group_draft AS (
    SELECT service_group_pricing.service_group_id_draft
    FROM vnpt_dev.service_group_pricing
    JOIN vnpt_dev.services ON service_group_pricing.service_id = services.id
    WHERE services.deleted_flag = 0
)
UPDATE vnpt_dev.service_group
SET deleted_flag = 0
FROM delete_service_group_draft
WHERE service_group.group_service_draft_id = delete_service_group_draft.service_group_id_draft;