with insertCategory as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code")
values (1, 1, now(), 'Sim số', 1, -1, '["SERVICE"]') returning id as cat_id
    )
        , insertCategorySimSo as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Sim Số ', 2, (select insertCategory.cat_id from insertCategory), '["SERVICE"]', 210) returning id as category_id
    )
        , insertServiceSimSo as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at",
                          "customer_type_code", "email")
values ((select category_id from insertCategorySimSo), 'SS000001', 9, 1, 1, now(), 1, 'Sim số', 1, 0, current_date, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceSimSoGoiCuoc as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at",
                          "customer_type_code", "email")
values ((select category_id from insertCategorySimSo), 'GSS00001', 9, 1, 1, now(), 1, 'Gói cước kèm sim số', 1, 0, current_date, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftSimSo as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "customer_type_code", "email")
values ((select insertServiceSimSo.id from insertServiceSimSo), (select insertServiceSimSo.categories_id from insertServiceSimSo), 'SS000001', 9, 1, 1, now(), 1, now(), 'Sim số', 1, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
       , insertServiceDraftSimSoGoiCuoc as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "customer_type_code", "email")
values ((select insertServiceSimSoGoiCuoc.id from insertServiceSimSoGoiCuoc), (select insertServiceSimSoGoiCuoc.categories_id from insertServiceSimSoGoiCuoc), 'GSS00001', 9, 1, 1, now(), 1, now(), 'Gói cước kèm sim số', 1, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
insert
into vnpt_dev."mapping_services_categories"(service_id, service_draft_id, categories_id)
values
    ((select insertServiceDraftSimSo.service_id from insertServiceDraftSimSo), (select insertServiceDraftSimSo.service_draft_id from insertServiceDraftSimSo), (select insertServiceDraftSimSo.categories_id from insertServiceDraftSimSo)), ((select insertServiceDraftSimSoGoiCuoc.service_id from insertServiceDraftSimSoGoiCuoc), (select insertServiceDraftSimSoGoiCuoc.service_draft_id from insertServiceDraftSimSoGoiCuoc), (select insertServiceDraftSimSoGoiCuoc.categories_id from insertServiceDraftSimSoGoiCuoc));


--    // Telecom

with insertCategoryDiDong as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Di động', 1, -1, '["SERVICE"]', 170) returning id as category_id
    )
        , insertCategoryTichHop as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Tích hợp ', 2, (select insertCategoryDiDong.category_id from insertCategoryDiDong), '["SERVICE"]', 181) returning id as category_tich_hop_id
    )
        , insertCategoryData as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'DATA ', 2, (select insertCategoryDiDong.category_id from insertCategoryDiDong), '["SERVICE"]', 182) returning id as category_data_id
    )
        , insertServiceTichHop as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at", "migration_id",
                          "customer_type_code", "email")
values ((select category_tich_hop_id from insertCategoryTichHop), 'TICHHOP', 9, 1, 1, now(), 1, 'Tích hợp', 1, 2, current_date, 181, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftTichHop as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "created_source_migration",
                                "migration_id", "customer_type_code", "email")
values ((select insertServiceTichHop.id from insertServiceTichHop), (select insertServiceTichHop.categories_id from insertServiceTichHop), 'TICHHOP', 9, 1, 1, now(), 1, now(), 'Tích hợp', 1, 2, 181, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
        , insertServiceData as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at", "migration_id",
                          "customer_type_code", "email")
values ((select category_data_id from insertCategoryData), 'DATA', 9, 1, 1, now(), 1, 'DATA', 1, 2, current_date, 182, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftData as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "created_source_migration",
                                "migration_id", "customer_type_code", "email")
values ((select insertServiceData.id from insertServiceData), (select insertServiceData.categories_id from insertServiceData), 'DATA', 9, 1, 1, now(), 1, now(), 'DATA', 1, 2, 182, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
insert
into vnpt_dev."mapping_services_categories"(service_id, service_draft_id, categories_id)
values
    ((select insertServiceDraftTichHop.service_id from insertServiceDraftTichHop), (select insertServiceDraftTichHop.service_draft_id from insertServiceDraftTichHop), (select insertServiceDraftTichHop.categories_id from insertServiceDraftTichHop)), ((select insertServiceDraftData.service_id from insertServiceDraftData), (select insertServiceDraftData.service_draft_id from insertServiceDraftData), (select insertServiceDraftData.categories_id from insertServiceDraftData));

--    // Internet & Truyền hình

with insertCategoryInternetTruyenHinh as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Internet & Truyền hình', 1, -1, '["SERVICE"]', 180) returning id as category_id
    )
        , insertCategoryInternetTruyenHinhDiDong as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Internet, truyền hình và di động ', 2, (select insertCategoryInternetTruyenHinh.category_id from insertCategoryInternetTruyenHinh), '["SERVICE"]', 183) returning id as category_id
    )
        , insertCategoryInternetCapQuang as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Internet cáp quang ', 2, (select insertCategoryInternetTruyenHinh.category_id from insertCategoryInternetTruyenHinh), '["SERVICE"]', 198) returning id as category_id
    )
        , insertCategoryInternet as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'Internet & Truyền hình ', 2, (select insertCategoryInternetTruyenHinh.category_id from insertCategoryInternetTruyenHinh), '["SERVICE"]', 199) returning id as category_id
    )
        , insertServiceInternetTruyenHinhDiDong as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at", "migration_id",
                          "customer_type_code", "email")
values ((select category_id from insertCategoryInternetTruyenHinhDiDong), 'THDD', 7, 1, 1, now(), 1, 'Internet, truyền hình và di động', 1, 2, current_date, 183, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftInternetTruyenHinhDiDong as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "created_source_migration",
                                "migration_id", "customer_type_code", "email")
values ((select insertServiceInternetTruyenHinhDiDong.id from insertServiceInternetTruyenHinhDiDong), (select insertServiceInternetTruyenHinhDiDong.categories_id from insertServiceInternetTruyenHinhDiDong), 'THDD', 7, 1, 1, now(), 1, now(), 'Internet, truyền hình và di động', 1, 2, 183, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
        , insertServiceInternetCapQuang as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at", "migration_id",
                          "customer_type_code", "email")
values ((select category_id from insertCategoryInternetCapQuang), 'CAPQUANG', 7, 1, 1, now(), 1, 'Internet cáp quang', 1, 2, current_date, 198, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftInternetCapQuang as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "created_source_migration",
                                "migration_id", "customer_type_code", "email")
values ((select insertServiceInternetCapQuang.id from insertServiceInternetCapQuang), (select insertServiceInternetCapQuang.categories_id from insertServiceInternetCapQuang), 'CAPQUANG', 7, 1, 1, now(), 1, now(), 'Internet cáp quang', 1, 2, 198, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
        , insertServiceInternet as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at", "migration_id",
                          "customer_type_code", "email")
values ((select category_id from insertCategoryInternet), 'ETH', 7, 1, 1, now(), 1, 'Internet & Truyền hình', 1, 2, current_date, 199, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftInternet as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "created_source_migration",
                                "migration_id", "customer_type_code", "email")
values ((select insertServiceInternet.id from insertServiceInternet), (select insertServiceInternet.categories_id from insertServiceInternet), 'ETH', 7, 1, 1, now(), 1, now(), 'Internet & Truyền hình', 1, 2, 199, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
insert
into vnpt_dev."mapping_services_categories"(service_id, service_draft_id, categories_id)
values
    ((select insertServiceDraftInternetTruyenHinhDiDong.service_id from insertServiceDraftInternetTruyenHinhDiDong), (select insertServiceDraftInternetTruyenHinhDiDong.service_draft_id from insertServiceDraftInternetTruyenHinhDiDong), (select insertServiceDraftInternetTruyenHinhDiDong.categories_id from insertServiceDraftInternetTruyenHinhDiDong)), ((select insertServiceDraftInternetCapQuang.service_id from insertServiceDraftInternetCapQuang), (select insertServiceDraftInternetCapQuang.service_draft_id from insertServiceDraftInternetCapQuang), (select insertServiceDraftInternetCapQuang.categories_id from insertServiceDraftInternetCapQuang)), ((select insertServiceDraftInternet.service_id from insertServiceDraftInternet), (select insertServiceDraftInternet.service_draft_id from insertServiceDraftInternet), (select insertServiceDraftInternet.categories_id from insertServiceDraftInternet));


--   // MyTV

with insertCategoryMyTV as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'MyTV', 1, -1, '["SERVICE"]', 186) returning id as category_id
    )
        , insertCategoryMyTVIPTV as (
insert
into vnpt_dev."categories" ("status", "deleted_flag", "created_at", "name", "level", "parent_id", "apply_code", "categories_id_migration")
values (1, 1, now(), 'MyTV IPTV', 2, (select insertCategoryMyTV.category_id from insertCategoryMyTV), '["SERVICE"]', 187) returning id as category_id
    )
        , insertServiceMyTVIPTV as (
insert
into vnpt_dev."services" ("categories_id", "service_code", "product_type", "status", "deleted_flag",
                          "created_at", "approve", "service_name", "payment_method", "created_source_migration", "synced_at", "migration_id",
                          "customer_type_code", "email")
values ((select category_id from insertCategoryMyTVIPTV), 'MYTVIPTV', 7, 1, 1, now(), 1, 'MyTV IPTV', 1, 2, current_date, 187, '["CN"]', '<EMAIL>')
    returning id, categories_id
    )
        , insertServiceDraftMyTVIPTV as (
insert
into vnpt_dev."services_draft" ("service_id", "categories_id", "service_code", "product_type", "status", "deleted_flag",
                                "created_at", "approve", "approve_at", "service_name", "payment_method", "created_source_migration",
                                "migration_id", "customer_type_code", "email")
values ((select insertServiceMyTVIPTV.id from insertServiceMyTVIPTV), (select insertServiceMyTVIPTV.categories_id from insertServiceMyTVIPTV), 'MYTVIPTV', 7, 1, 1, now(), 1, now(), 'MyTV IPTV', 1, 2, 187, '["CN"]', '<EMAIL>')
    returning service_id, id as service_draft_id, categories_id
    )
insert
into vnpt_dev."mapping_services_categories"(service_id, service_draft_id, categories_id)
values
    ((select insertServiceDraftMyTVIPTV.service_id from insertServiceDraftMyTVIPTV), (select insertServiceDraftMyTVIPTV.service_draft_id from insertServiceDraftMyTVIPTV), (select insertServiceDraftMyTVIPTV.categories_id from insertServiceDraftMyTVIPTV));
