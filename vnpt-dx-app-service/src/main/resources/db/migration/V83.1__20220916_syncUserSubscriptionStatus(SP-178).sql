alter table users add column subscription_status int2 default -1;
alter table enterprise add column customer_state int2 default null;
alter table enterprise add column user_id int8 default null;
INSERT INTO schedules( "bean_name", "method_name", "method_params", "cron_expression", "remark", "job_status", "created_by", "created_at", "modified_by", "modified_at") VALUES ('user', 'updateUserSubscriptionStatus', NULL, '0 0/3 * * * ?', 'user', 1, 'batch', NULL, 'batch', NULL);

UPDATE users
SET subscription_status = 0
FROM subscriptions
WHERE users.id = subscriptions.user_id and subscriptions.status = 1 and subscriptions.deleted_flag = 1 and reg_type = 0 and confirm_status = 1;


UPDATE users
SET subscription_status = 1
FROM subscriptions
WHERE users.id = subscriptions.user_id and subscriptions.status = 2 and subscriptions.deleted_flag = 1 and reg_type = 1  and confirm_status = 1;


UPDATE enterprise
SET customer_state = view_enterprise_user_classification_new.type, user_id = view_enterprise_user_classification_new.user_id
FROM view_enterprise_user_classification_new
WHERE enterprise.id = view_enterprise_user_classification_new.enterprise_id ;
