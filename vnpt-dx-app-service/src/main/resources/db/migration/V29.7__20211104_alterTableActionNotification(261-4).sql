-- <PERSON><PERSON><PERSON><PERSON> cột is_send_telegram và allow_change_telegram
ALTER TABLE "vnpt_dev"."action_notification"
    ADD COLUMN "is_send_telegram" int2,
    ADD COLUMN "allow_change_telegram" varchar(2);
COMMENT ON COLUMN "vnpt_dev"."action_notification"."is_send_telegram" IS 'Trạng thái gửi Telegram(1: <PERSON> phép gửi Telegram 0: <PERSON>h<PERSON>ng cho phép gửi telegram)';

-- Insert d<PERSON> liệu
INSERT INTO "vnpt_dev"."action_notification" (
    "id",
    "name",
    "is_send_email",
    "is_send_sms",
    "is_notification",
    "parent_id",
    "created_by",
    "created_at",
    "modified_by",
    "modified_at",
    "receiver",
    "action_code",
    "allow_change_email",
    "allow_change_sms",
    "allow_change_notification",
    "is_send_telegram",
    "allow_change_telegram",
    "priority_order"
)
VALUES
    (
        (SELECT id FROM vnpt_dev.action_notification ORDER BY id DESC LIMIT 1) + 1,
		'Báo cáo tự động',
		1,
		0,
		0, - 1,
		'system',
		'2021-11-04 00:00:00',
		'<EMAIL>',
		'2021-11-04 00:00:00',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		1,
		'B',
		1300
	);
INSERT INTO "vnpt_dev"."action_notification" (
    "id",
    "name",
    "is_send_email",
    "is_send_sms",
    "is_notification",
    "parent_id",
    "created_by",
    "created_at",
    "modified_by",
    "modified_at",
    "receiver",
    "action_code",
    "allow_change_email",
    "allow_change_sms",
    "allow_change_notification",
    "is_send_telegram",
    "allow_change_telegram",
    "priority_order"
)
VALUES
    (
        (SELECT id FROM vnpt_dev.action_notification ORDER BY id DESC LIMIT 1) + 2,
		'Doanh thu',
		1,
		0,
		0,
		(SELECT id FROM vnpt_dev.action_notification WHERE name = 'Báo cáo tự động' LIMIT 1),
		'system',
		'2021-11-04 00:00:00',
		'<EMAIL>',
		'2021-11-04 00:00:00',
		null,
		'EXP-02',
		'B',
		'D',
		'D',
		0,
		'B',
		1302
	);
INSERT INTO "vnpt_dev"."action_notification" (
    "id",
    "name",
    "is_send_email",
    "is_send_sms",
    "is_notification",
    "parent_id",
    "created_by",
    "created_at",
    "modified_by",
    "modified_at",
    "receiver",
    "action_code",
    "allow_change_email",
    "allow_change_sms",
    "allow_change_notification",
    "is_send_telegram",
    "allow_change_telegram",
    "priority_order"
)
VALUES
    (
        (SELECT id FROM vnpt_dev.action_notification ORDER BY id DESC LIMIT 1) + 3,
		'Khách hàng',
		1,
		0,
		0,
		(SELECT id FROM vnpt_dev.action_notification WHERE name = 'Báo cáo tự động' LIMIT 1),
		'system',
		'2021-11-04 00:00:00',
		'<EMAIL>',
		'2021-11-04 00:00:00',
		null,
		'EXP-01',
		'B',
		'D',
		'D',
		0,
		'B',
	1301
	);


-- Disable thông tin của các notification ngoài Xuất hóa đơn
UPDATE vnpt_dev.action_notification
SET is_send_telegram = 0, allow_change_telegram = 'D'
WHERE
        id NOT IN
        (
            SELECT id
            FROM "vnpt_dev"."action_notification"
            WHERE
                    name LIKE 'Báo cáo tự động'
               OR
                    parent_id IN (
                    SELECT id FROM "vnpt_dev"."action_notification" WHERE name LIKE 'Báo cáo tự động'
                )
        );
