alter table vnpt_dev.affiliate_link
add column if not exists updated_code varchar(12) NULL;

comment on column "vnpt_dev"."affiliate_link"."updated_code" IS 'Mã link khi chỉnh sửa';

-- fill cột updated_code = created_code cho c<PERSON>c b<PERSON><PERSON> ghi cũ
update vnpt_dev.affiliate_link
set updated_code = created_code
where updated_code is null;

--- edit view
DROP VIEW IF EXISTS vnpt_dev.feature_view_get_affiliate_link_new;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_get_affiliate_link_new
AS SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.updated_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
    service_draft_cte.service_name AS product_name
   FROM vnpt_dev.affiliate_link
     LEFT JOIN LATERAL ( SELECT services_draft.service_name
           FROM vnpt_dev.services_draft
          WHERE services_draft.service_id = affiliate_link.source_object_id AND services_draft.deleted_flag = 1
          ORDER BY services_draft.id DESC
         LIMIT 1) service_draft_cte ON true
  WHERE affiliate_link.source_type = 1 AND affiliate_link.source_object_type = 0 AND affiliate_link.deleted_flag = 1 AND service_draft_cte.service_name::text <> ''::text
UNION ALL
 SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.updated_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
    combo.combo_name AS product_name
   FROM vnpt_dev.affiliate_link
     JOIN vnpt_dev.combo ON affiliate_link.source_object_id = combo.id AND affiliate_link.source_type = 1 AND affiliate_link.source_object_type = 1
  WHERE affiliate_link.deleted_flag = 1 AND combo.deleted_flag = 1
UNION ALL
 SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.updated_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
    ''::character varying AS product_name
   FROM vnpt_dev.affiliate_link
  WHERE (affiliate_link.source_type = ANY (ARRAY[2, 3])) AND affiliate_link.deleted_flag = 1
UNION ALL
 SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.updated_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
        CASE
            WHEN affiliate_link.source_object_type = 0 THEN service_draft_cte.service_name
            WHEN affiliate_link.source_object_type = 1 THEN combo.combo_name
            ELSE ''::character varying
        END AS product_name
   FROM vnpt_dev.affiliate_link
     LEFT JOIN vnpt_dev.pricing ON affiliate_link.source_object_id = pricing.id AND affiliate_link.source_object_type = 0 AND pricing.deleted_flag = 1
     LEFT JOIN LATERAL ( SELECT services_draft.service_name
           FROM vnpt_dev.services_draft
          WHERE services_draft.service_id = pricing.service_id AND services_draft.deleted_flag = 1
          ORDER BY services_draft.id DESC
         LIMIT 1) service_draft_cte ON true
     LEFT JOIN vnpt_dev.combo_plan ON affiliate_link.source_object_id = combo_plan.id AND affiliate_link.source_object_type = 1 AND combo_plan.deleted_flag = 1
     LEFT JOIN vnpt_dev.combo ON combo_plan.combo_id = combo.id AND combo.deleted_flag = 1
  WHERE affiliate_link.deleted_flag = 1 AND affiliate_link.source_type = 4;