-- service
ALTER TABLE vnpt_dev.services ADD IF NOT EXISTS migration_id int8 NULL;
COMMENT ON COLUMN vnpt_dev.services.migration_id IS 'Id dịch vụ đồng bộ';

ALTER TABLE vnpt_dev.services ADD IF NOT EXISTS created_source_migration int2 NULL DEFAULT 0;
COMMENT ON COLUMN vnpt_dev.services.created_source_migration IS 'Nguồn tạo (0: oneSME, 2: KHCN)';

ALTER TABLE vnpt_dev.services ADD IF NOT EXISTS synced_at date NULL DEFAULT now();
COMMENT ON COLUMN vnpt_dev.services.synced_at IS 'Thời gian đồng bộ cuối cùng';

--         service-draft
ALTER TABLE vnpt_dev.services_draft ADD IF NOT EXISTS migration_id int8 NULL;
COMMENT ON COLUMN vnpt_dev.services_draft.migration_id IS 'Id dịch vụ đồng bộ';

ALTER TABLE vnpt_dev.services_draft ADD IF NOT EXISTS created_source_migration int2 NULL DEFAULT 0;
COMMENT ON COLUMN vnpt_dev.services_draft.created_source_migration IS 'Nguồn tạo (0: oneSME, 2: KHCN)';

ALTER TABLE vnpt_dev.services_draft ADD IF NOT EXISTS synced_at date NULL DEFAULT now();
COMMENT ON COLUMN vnpt_dev.services_draft.synced_at IS 'Thời gian đồng bộ cuối cùng';

--         categories
ALTER TABLE vnpt_dev.categories ADD IF NOT EXISTS categories_id_migration int8 NULL;
COMMENT ON COLUMN vnpt_dev.categories.categories_id_migration IS 'Id categories đồng bộ KHCN';

--         pricing
ALTER TABLE vnpt_dev.pricing ADD IF NOT EXISTS migration_id int8 NULL;
COMMENT ON COLUMN vnpt_dev.pricing.migration_id IS 'Id đồng bộ KHCN';

--         pricing-draft
ALTER TABLE vnpt_dev.pricing_draft ADD IF NOT EXISTS migration_id int8 NULL;
COMMENT ON COLUMN vnpt_dev.pricing_draft.migration_id IS 'Id đồng bộ KHCN';

ALTER TABLE vnpt_dev.pricing_draft ADD IF NOT EXISTS summary varchar NULL;
COMMENT ON COLUMN vnpt_dev.pricing_draft.summary IS 'Mô tả ngắn';

--  pricing-multi-plan
ALTER TABLE vnpt_dev.pricing_multi_plan ADD IF NOT EXISTS migration_id int8 NULL;
COMMENT ON COLUMN vnpt_dev.pricing_multi_plan.migration_id IS 'Id đồng bộ KHCN';

ALTER TABLE vnpt_dev.pricing_multi_plan ADD IF NOT EXISTS description varchar NULL;
COMMENT ON COLUMN vnpt_dev.pricing_multi_plan.description IS 'Mô tả chi tiết';

ALTER TABLE vnpt_dev.pricing_multi_plan ADD IF NOT EXISTS summary varchar NULL;
COMMENT ON COLUMN vnpt_dev.pricing_multi_plan.summary IS 'Mô tả ngắn';
