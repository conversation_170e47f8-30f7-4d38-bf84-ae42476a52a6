alter table vnpt_dev.subscriptions
    add column trial_day int8,
add column reg_type int2;
comment
on column vnpt_dev.subscriptions.trial_day is 'Số ngày dùng thử. nếu reg_type = 0 thì trial_day <> null';
comment
on column vnpt_dev.subscriptions.reg_type is 'Loại đăng ký: 0 - dùng thử, 1 - ch<PERSON>h thức';

create table vnpt_dev.kafka_history
(
    id              bigserial NOT NULL,
    message_id      varchar,
    message_content json,
    status          int2,
    action_name     varchar,
    group_id        varchar,
    created_at      timestamp,
    modified_at     timestamp,
    CONSTRAINT kafka_history_pk PRIMARY KEY (id)
);
comment
on table vnpt_dev.kafka_history is 'l<PERSON>u lịch sử message bắn lên history';
comment
on column vnpt_dev.kafka_history.message_id is 'mã message trên kafka';
comment
on column vnpt_dev.kafka_history.message_content is 'nội dung message';
comment
on column vnpt_dev.kafka_history.status is 'trạng thái xử lý của message: 0-inprogress, 1-error ';
comment
on column vnpt_dev.kafka_history.action_name is 'Tên action gửi message ';
comment
on column vnpt_dev.kafka_history. group_id is 'mã group kafka';