ALTER TABLE "vnpt_dev"."page_builder"
  ADD COLUMN "version" int2,
  ADD COLUMN "customer_type" varchar(20) DEFAULT 'ALL';
COMMENT ON COLUMN "vnpt_dev"."page_builder"."version" IS 'Version của Builder Studio (version 1 dành cho giao diện ONESME trước 12/2024)';
COMMENT ON COLUMN "vnpt_dev"."page_builder"."customer_type" IS 'Loại khách hàng';
UPDATE "vnpt_dev"."page_builder" SET version = 1 WHERE version IS NULL;

ALTER TABLE "vnpt_dev"."builder_template"
  ADD COLUMN "version" int2,
  ADD COLUMN "customer_type" varchar(20) DEFAULT 'ALL';
COMMENT ON COLUMN "vnpt_dev"."builder_template"."version" IS 'Version của Builder Studio (version 1 dành cho giao diện ONESME trước 12/2024)';
COMMENT ON COLUMN "vnpt_dev"."builder_template"."customer_type" IS 'Loại khách hàng';
UPDATE "vnpt_dev"."builder_template" SET version = 1, name = 'Trang chủ' WHERE version IS NULL;