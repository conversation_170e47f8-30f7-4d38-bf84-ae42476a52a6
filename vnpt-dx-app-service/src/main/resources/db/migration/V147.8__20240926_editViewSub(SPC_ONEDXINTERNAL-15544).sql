DROP VIEW IF EXISTS vnpt_dev.feature_view_get_all_sub_group;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_get_all_sub_group
AS SELECT subscriptions.id AS sub_id,
        CASE
            WHEN subscriptions.cart_code IS NOT NULL THEN subscriptions.cart_code
            WHEN subscriptions.group_code IS NOT NULL THEN subscriptions.group_code
            ELSE concat('ID', to_char(subscriptions.id, 'FM09999999'::text))::character varying
        END AS sub_code,
        CASE
            WHEN users.customer_type::text = 'CN'::text THEN concat_ws(' '::text, users.last_name, users.first_name)::character varying
            ELSE users.name
        END AS customer_name,
    subscriptions.quantity,
    subscriptions.quantity_variant AS quantityvariant,
    pricing_multi_plan.free_quantity AS freequantity,
    COALESCE(users.customer_type, 'KHDN'::character varying) AS customer_type,
    COALESCE(combo.provider, services.provider) AS provider,
    COALESCE(combo.user_id, services.user_id) AS provider_id,
    COALESCE(combo.id * 10 + 1, services.id * 10) AS service_id,
    subscriptions.status AS sub_status,
    combo.id IS NOT NULL AS is_combo,
    COALESCE(combo.combo_owner, services.service_owner) = ANY (ARRAY[0, 1]) AS is_on,
    users.tin AS tax_code,
    users.id AS user_id,
    users.rep_personal_cert_number AS personal_cert_number,
    users.email,
    users.phone_number AS phone,
    users.rep_fullname AS rep_name,
    users.province_id,
    concat_ws(' '::text, assignee.last_name, assignee.first_name) AS assignee_name,
    enterprise.founding_date,
    enterprise.business_area_id,
    enterprise.business_size_id,
    subscriptions.created_at AS sub_created_at,
    billings.payment_date,
    sme_progress.name AS status_name,
    sme_progress.id AS status_id,
    COALESCE(pricing_multi_plan.circle_type, COALESCE(combo_plan.cycle_type, pricing.cycle_type)) AS cycle_type,
    COALESCE(pricing_multi_plan.payment_cycle, COALESCE(combo_plan.payment_cycle::bigint, pricing.payment_cycle::bigint)) AS payment_cycle,
    COALESCE(subscriptions.total_amount_after_refund, subscriptions.total_amount, 0::double precision) AS total_amount,
    subscriptions.price_variant,
    subscriptions.variant_name,
    billings.province_id AS billing_province,
        CASE
            WHEN subscriptions.created_source_migration = 1 THEN 5
            WHEN subscriptions.traffic_id IS NOT NULL THEN 3
            WHEN subscriptions.employee_code IS NOT NULL THEN 2
            WHEN subscriptions.portal_type = ANY (ARRAY[1, 2]) THEN 4
            WHEN subscriptions.created_source_migration = 4 THEN 6
            ELSE 1
        END AS create_source,
    COALESCE(combo_plan.combo_name, pricing.pricing_name) AS pricing_name,
    COALESCE(pricing_multi_plan.number_of_cycles, pricing.number_of_cycles) AS number_of_cycles,
    subscriptions.number_of_cycles AS number_of_cycles_reactive,
    subscriptions.created_at,
    COALESCE(services.service_owner, combo.combo_owner) AS service_owner,
    COALESCE(combo.combo_name, services.service_name) AS service_name,
    services.service_owner_partner,
    subscriptions.reactive_status,
    subscriptions.installed AS sub_installed,
    subscriptions.dhsxkd_sub_code,
    subscriptions.start_current_cycle,
    subscriptions.end_current_cycle,
    subscriptions.reactive_date,
    subscriptions.current_cycle,
    subscriptions.migrate_time,
    subscriptions.next_payment_time,
    subscriptions.migrate_code,
    subscriptions.is_only_service,
    pricing.is_one_time,
    migration.id AS migration_id,
    order_service_receive.transaction_code,
    billings.id AS bill_id,
    enterprise.id AS enterprise_id,
    enterprise.assignee_id AS enterprise_assignee_id,
    subscriptions.assignee_id AS sub_assignee_id,
    subscriptions.lst_assignees_id,
    users.assignee_id AS user_assignee_id,
    business_area.name AS business_area_name,
    business_size.name AS business_size_name,
    services.product_type,
    subscriptions.service_group_id,
    subscriptions.group_code,
    service_group.name AS service_group_name,
    subscriptions_extradata.khcn_metadata as sim_metadata
   FROM vnpt_dev.subscriptions
     LEFT JOIN vnpt_dev.variant_draft ON subscriptions.variant_draft_id = variant_draft.id
     LEFT JOIN vnpt_dev.billings ON subscriptions.id = billings.subscriptions_id
     LEFT JOIN vnpt_dev.migration ON migration.code::text = subscriptions.migrate_code::text
     LEFT JOIN vnpt_dev.users ON users.id = subscriptions.user_id
     LEFT JOIN vnpt_dev.enterprise ON enterprise.user_id = users.id
     LEFT JOIN vnpt_dev.pricing ON pricing.id = subscriptions.pricing_id
     LEFT JOIN vnpt_dev.combo_plan ON combo_plan.id = subscriptions.combo_plan_id
     LEFT JOIN vnpt_dev.services ON services.id = subscriptions.service_id
     LEFT JOIN vnpt_dev.combo ON combo.id = combo_plan.combo_id
     LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.id = subscriptions.pricing_multi_plan_id
     LEFT JOIN vnpt_dev.order_service_receive ON order_service_receive.subscription_id = subscriptions.id AND subscriptions.service_id IS NOT NULL AND order_service_receive.service_id IS NOT NULL AND order_service_receive.combo_id IS NULL
     LEFT JOIN vnpt_dev.order_service_status ON order_service_status.id::character varying::text = order_service_receive.order_status::text
     LEFT JOIN vnpt_dev.sme_progress ON sme_progress.id = order_service_status.sme_progress_id
     LEFT JOIN vnpt_dev.business_area ON business_area.id = enterprise.business_area_id
     LEFT JOIN vnpt_dev.business_size ON business_size.id = enterprise.business_size_id
     LEFT JOIN vnpt_dev.users assignee ON assignee.id = subscriptions.assignee_id
     LEFT JOIN vnpt_dev.service_group ON service_group.id = subscriptions.service_group_id
     LEFT JOIN vnpt_dev.subscriptions_extradata ON subscriptions.id = subscriptions_extradata.subscription_id
  WHERE subscriptions.deleted_flag = 1 AND ((billings.status = ANY (ARRAY[0, 1, 2, 4])) OR billings.status IS NULL);

