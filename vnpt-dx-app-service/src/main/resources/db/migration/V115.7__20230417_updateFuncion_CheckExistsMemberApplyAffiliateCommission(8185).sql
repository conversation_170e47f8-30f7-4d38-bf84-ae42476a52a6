drop function if exists vnpt_dev.func_check_exist_member_apply_affiliate_commission;
CREATE OR REPLACE FUNCTION vnpt_dev.func_check_exist_member_apply_affiliate_commission(i_currentuserrole character varying, i_currentuserid bigint, i_isapplyallserviceproduct boolean, i_serviceproductids bigint[], i_memberlevel smallint, i_isapplyallmember boolean, i_memberapplyids bigint[], i_currentaffiliatecommissionmemberid bigint, i_startdate date, i_enddate date)
 RETURNS TABLE(affiliatecommissionname character varying, affiliatecommisioncode character varying, affiliatecommissionstartdate date, affiliatecommissionenddate date, affiliatecommissionstatus integer)
 LANGUAGE plpgsql
AS $function$
begin
	--Trường hợp người tạo/cập nhật là FULL_ADMIN
	if i_currentUserRole = 'FULL_ADMIN' then
		begin 
			return query(
				--L<PERSON>y ra các thành viên áp dụng sản phẩm dịch vụ chuẩn bị tạo
				with member_apply as (
					select 
						aff_commission."name" as aff_commission_name, aff_commission.code as aff_commission_code,
						aff_commission.start_date as aff_commission_start_date, aff_commission.end_date as aff_commission_end_date,
						aff_commission.status as aff_commission_status,
						aff_commission.created_by, aff_commission_member."level", 
						aff_commission_member.is_apply_member_all, aff_commission_member.apply_member_ids
					from vnpt_dev.affiliate_commission aff_commission join vnpt_dev.affiliate_commission_apply_member aff_commission_member
						on aff_commission.id = aff_commission_member.affiliate_commission_id
					where (
						i_isApplyAllServiceProduct = true or 
						(
							(aff_commission.is_apply_object_all = true) or
							(aff_commission.apply_object_ids && i_serviceProductIds)
						)
					)
					and (
						(aff_commission.start_date , coalesce(aff_commission.end_date , '3000-01-01') + interval '1 day') overlaps 
		  				(i_startDate, i_endDate + interval '1 day') 
		  			)
		  			and aff_commission_member."level" = 1
					and aff_commission_member.id != i_currentAffiliateCommissionMemberId
					and aff_commission_member.deleted_flag = 1 
					and aff_commission.priority = 0 --Chỉ check tập hợp của admin
					and aff_commission.deleted_flag = 1 
				)
				select
					member_apply.aff_commission_name, member_apply.aff_commission_code,
					member_apply.aff_commission_start_date, member_apply.aff_commission_end_date,
					member_apply.aff_commission_status::int
				from member_apply
				where
					(i_isApplyAllMember = true) or 
					(i_isApplyAllMember = false and is_apply_member_all = true) or 
					(i_isApplyAllMember = false and is_apply_member_all = false and apply_member_ids && i_memberApplyIds)
					
			);
		end;
	--Trường hợp người tạo/cập nhật là DAI_LY
	else
		begin
			return query(
				--Lấy ra các thành viên áp dụng sản phẩm dịch vụ chuẩn bị tạo
				with member_apply as (
					select 
						aff_commission."name"  as aff_commission_name, aff_commission.code as aff_commission_code,
						aff_commission.start_date as aff_commission_start_date, aff_commission.end_date as aff_commission_end_date,
						aff_commission.status as aff_commission_status,
						aff_commission.created_by, aff_commission_member."level", 
						aff_commission_member.is_apply_member_all, aff_commission_member.apply_member_ids
					from vnpt_dev.affiliate_commission aff_commission join vnpt_dev.affiliate_commission_apply_member aff_commission_member
						on aff_commission.id = aff_commission_member.affiliate_commission_id
					where (
						i_isApplyAllServiceProduct = true or 
						(
							(aff_commission.is_apply_object_all = true) or
							(aff_commission.apply_object_ids && i_serviceProductIds)
						)
					)
					and (
						(aff_commission.start_date , coalesce(aff_commission.end_date , '3000-01-01') + interval '1 day') overlaps 
		  				(i_startDate, i_endDate + interval '1 day') 
		  			)
		  			and aff_commission_member."level" = i_memberLevel
					and aff_commission_member.id != i_currentAffiliateCommissionMemberId
					and aff_commission_member.deleted_flag = 1  
					and aff_commission.created_by = i_currentUserId
					and aff_commission.deleted_flag = 1
				)
				select
					member_apply.aff_commission_name, member_apply.aff_commission_code,
					member_apply.aff_commission_start_date, member_apply.aff_commission_end_date,
					member_apply.aff_commission_status::int
				from member_apply
				where 
					(i_isApplyAllMember = true) or 
					(i_isApplyAllMember = false and is_apply_member_all = true) or 
					(i_isApplyAllMember = false and is_apply_member_all = false and apply_member_ids && i_memberApplyIds)
				
			);
		end;
	end if;
end $function$

