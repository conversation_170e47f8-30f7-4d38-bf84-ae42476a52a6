UPDATE vnpt_dev.services SET created_source_migration = 2 WHERE service_code = 'SS000001' AND service_name = '<PERSON><PERSON> số' AND created_source_migration IS DISTINCT FROM 2;
DROP VIEW IF EXISTS vnpt_dev.view_report_sub_services;
CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_services AS
SELECT pricing.id,
       'SERVICE'::text AS sub_type,
        pricing.pricing_name,
       pricing.pricing_type,
       CASE
           WHEN pricing.cycle_type = 0 THEN 'DAILY'::text
           WHEN pricing.cycle_type = 1 THEN 'WEEKLY'::text
           WHEN pricing.cycle_type = 2 THEN 'MONTHLY'::text
           WHEN pricing.cycle_type = 3 THEN 'YEARLY'::text
           ELSE NULL::text
           END AS plan_cycle_type,
       pricing.payment_cycle AS plan_payment_cycle,
       services.service_name,
       CASE
           WHEN services.service_owner_partner = 0 THEN 'ON'::text
           WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
           ELSE 'OS'::text
           END AS service_owner_type,
       services.id AS service_id,
       services.categories_id,
       services.user_id AS provider_id,
       services.service_owner
FROM vnpt_dev.pricing
         LEFT JOIN vnpt_dev.services ON pricing.service_id = services.id
UNION ALL
SELECT NULL::bigint AS id,
        'SERVICE'::text AS sub_type,
        NULL::character varying AS pricing_name,
    NULL::smallint AS pricing_type,
    NULL::text AS plan_cycle_type,
    NULL::smallint AS plan_payment_cycle,
    services.service_name,
        CASE
            WHEN services.service_owner_partner = 0 THEN 'ON'::text
            WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
            ELSE 'OS'::text
END AS service_owner_type,
    services.id AS service_id,
    services.categories_id,
    services.user_id AS provider_id,
    services.service_owner
   FROM vnpt_dev.services
  WHERE services.product_type = 1 OR
    (created_source_migration = 2 AND service_code = 'SS000001'); -- Dịch vụ Sim số