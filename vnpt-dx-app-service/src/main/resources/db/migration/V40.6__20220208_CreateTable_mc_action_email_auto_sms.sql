/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:46:37
*/


-- ----------------------------
-- Table structure for mc_action_email_auto_sms
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_action_email_auto_sms";
CREATE TABLE "vnpt_dev"."mc_action_email_auto_sms" (
  "id" bigserial NOT NULL,
  "action_type" int2 NOT NULL,
  "subject" varchar(255) COLLATE "pg_catalog"."default",
  "sender" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "receiver" text COLLATE "pg_catalog"."default",
  "template_id" int8,
  "schedule_type" int2,
  "schedule_time" timestamp(6),
  "mc_id" int8,
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "receiver_cus_contact" text COLLATE "pg_catalog"."default",
  "receiver_cus_group" text COLLATE "pg_catalog"."default",
  "created_by" int2,
  "modified_by" int2,
  "deleted_flag" int2,
  "status" int2
)
;
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."action_type" IS '0: Email; 1: tự động: 2: SMS';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."subject" IS 'SMS bỏ qua trường này';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."schedule_type" IS '0: mặc định sau khi CDQC hoạt động; 1: Chọn ngày';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."mc_id" IS 'id của bảng marketing_campagin';

-- ----------------------------
-- Primary Key structure for table mc_action_email_auto_sms
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_action_email_auto_sms" ADD CONSTRAINT "mc_action_email_auto_sms_pkey" PRIMARY KEY ("id");
