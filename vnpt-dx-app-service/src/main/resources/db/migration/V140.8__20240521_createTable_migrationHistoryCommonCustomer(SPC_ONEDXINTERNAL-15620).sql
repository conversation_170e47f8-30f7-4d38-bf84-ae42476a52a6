DROP TABLE IF EXISTS "vnpt_dev"."migration_history_common_customer";
CREATE TABLE "vnpt_dev"."migration_history_common_customer" (
  "id" bigserial,
  "sme_uuid" uuid NOT NULL,
  "enterprise_id" int8 NOT NULL,
  "created_at" timestamp NOT NULL DEFAULT NOW(),
  PRIMARY KEY ("id"),
  CONSTRAINT "unique_migrationHistoryCommonCustomer_smeUUIDEnterpriseId" UNIQUE ("sme_uuid", "enterprise_id")
);

COMMENT ON COLUMN "vnpt_dev"."migration_history_common_customer"."sme_uuid" IS 'UUID của SME lấy thông tin khách hàng';
COMMENT ON COLUMN "vnpt_dev"."migration_history_common_customer"."enterprise_id" IS 'ID của khách hàng đã được lấy';
COMMENT ON CONSTRAINT "unique_migrationHistoryCommonCustomer_smeUUIDEnterpriseId" ON "vnpt_dev"."migration_history_common_customer" IS 'SME không lấy trùng thông tin doanh nghiệp';
COMMENT ON COLUMN "vnpt_dev"."migration_history_common_customer"."created_at" IS 'Thời gian lấy dữ liệu khách hàng';
COMMENT ON TABLE "vnpt_dev"."migration_history_common_customer" IS 'Bảng lưu lịch sử các khách hàng đã được đồng bộ về module Customer trên Workplace ';