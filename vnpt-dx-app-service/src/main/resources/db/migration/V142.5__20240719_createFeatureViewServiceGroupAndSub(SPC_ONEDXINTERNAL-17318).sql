DROP VIEW IF EXISTS vnpt_dev.feature_view_get_all_sub_group;
CREATE VIEW vnpt_dev.feature_view_get_all_sub_group
            (sub_id, sub_code, customer_name, customer_type, provider, provider_id, service_id, sub_status, is_combo,
             is_on, tax_code, user_id, personal_cert_number, email, phone, rep_name, province_id, assignee_name,
             founding_date, business_area_id, business_size_id, sub_created_at, payment_date, status_name, status_id,
             cycle_type, payment_cycle, total_amount, price_variant, variant_name, billing_province, create_source,
             pricing_name, number_of_cycles, number_of_cycles_reactive, created_at, service_owner, service_name,
             service_owner_partner, reactive_status, sub_installed, dhsxkd_sub_code, start_current_cycle,
             end_current_cycle, reactive_date, current_cycle, migrate_time, next_payment_time, migrate_code,
             is_only_service, is_one_time, migration_id, transaction_code, bill_id, enterprise_id,
             enterprise_assignee_id, sub_assignee_id, lst_assignees_id, user_assignee_id, business_area_name,
             business_size_name, product_type)
as
SELECT subscriptions.id AS sub_id,
       CASE
           WHEN subscriptions.cart_code IS NOT NULL THEN subscriptions.cart_code
           WHEN subscriptions.group_code IS NOT NULL THEN subscriptions.group_code
           ELSE concat('ID', to_char(subscriptions.id, 'FM09999999'::text))::character varying
END AS sub_code,
       CASE
           WHEN users.customer_type::text = 'CN'::text
               THEN concat_ws(' '::text, users.last_name, users.first_name)::character varying
           ELSE users.name
END AS customer_name,
       COALESCE(users.customer_type, 'KHDN'::character varying) AS customer_type,
       COALESCE(combo.provider, services.provider) AS provider,
       COALESCE(combo.user_id, services.user_id) AS provider_id,
       COALESCE(combo.id * 10 + 1, services.id * 10) AS service_id,
       subscriptions.status AS sub_status,
       combo.id IS NOT NULL AS is_combo,
       COALESCE(combo.combo_owner, services.service_owner) = ANY (ARRAY [0, 1]) AS is_on,
       users.tin AS tax_code,
       users.id AS user_id,
       users.rep_personal_cert_number AS personal_cert_number,
       users.email,
       users.phone_number AS phone,
       users.rep_fullname AS rep_name,
       users.province_id,
       concat_ws(' '::text, assignee.last_name, assignee.first_name) AS assignee_name,
       enterprise.founding_date,
       enterprise.business_area_id,
       enterprise.business_size_id,
       subscriptions.created_at AS sub_created_at,
       billings.payment_date,
       sme_progress.name AS status_name,
       sme_progress.id AS status_id,
       COALESCE(pricing_multi_plan.circle_type, COALESCE(combo_plan.cycle_type, pricing.cycle_type)) AS cycle_type,
       COALESCE(pricing_multi_plan.payment_cycle, COALESCE(combo_plan.payment_cycle::bigint,
                                                           pricing.payment_cycle::bigint)) AS payment_cycle,
       COALESCE(subscriptions.total_amount_after_refund, subscriptions.total_amount,
                0::double precision) AS total_amount,
       subscriptions.price_variant,
       subscriptions.variant_name,
       billings.province_id AS billing_province,
       CASE
           WHEN subscriptions.created_source_migration = 1 THEN 5
           WHEN subscriptions.traffic_id IS NOT NULL THEN 3
           WHEN subscriptions.employee_code IS NOT NULL THEN 2
           WHEN subscriptions.portal_type = ANY (ARRAY [1, 2]) THEN 4
           ELSE 1
END AS create_source,
       COALESCE(combo_plan.combo_name, pricing.pricing_name) AS pricing_name,
       COALESCE(pricing_multi_plan.number_of_cycles,
                pricing.number_of_cycles) AS number_of_cycles,
       subscriptions.number_of_cycles AS number_of_cycles_reactive,
       subscriptions.created_at,
       COALESCE(services.service_owner, combo.combo_owner) AS service_owner,
       COALESCE(combo.combo_name, services.service_name) AS service_name,
       services.service_owner_partner,
       subscriptions.reactive_status,
       subscriptions.installed AS sub_installed,
       subscriptions.dhsxkd_sub_code,
       subscriptions.start_current_cycle,
       subscriptions.end_current_cycle,
       subscriptions.reactive_date,
       subscriptions.current_cycle,
       subscriptions.migrate_time,
       subscriptions.next_payment_time,
       subscriptions.migrate_code,
       subscriptions.is_only_service,
       pricing.is_one_time,
       migration.id AS migration_id,
       order_service_receive.transaction_code,
       billings.id AS bill_id,
       enterprise.id AS enterprise_id,
       enterprise.assignee_id AS enterprise_assignee_id,
       subscriptions.assignee_id AS sub_assignee_id,
       subscriptions.lst_assignees_id,
       users.assignee_id AS user_assignee_id,
       business_area.name AS business_area_name,
       business_size.name AS business_size_name,
       services.product_type,
       subscriptions.service_group_id,
       subscriptions.group_code,
       service_group.name AS service_group_name
FROM vnpt_dev.subscriptions
         LEFT JOIN vnpt_dev.variant_draft ON subscriptions.variant_draft_id = variant_draft.id
         LEFT JOIN vnpt_dev.billings ON subscriptions.id = billings.subscriptions_id
         LEFT JOIN vnpt_dev.migration ON migration.code::text = subscriptions.migrate_code::text
         LEFT JOIN vnpt_dev.users ON users.id = subscriptions.user_id
         LEFT JOIN vnpt_dev.enterprise ON enterprise.user_id = users.id
         LEFT JOIN vnpt_dev.pricing ON pricing.id = subscriptions.pricing_id
         LEFT JOIN vnpt_dev.combo_plan ON combo_plan.id = subscriptions.combo_plan_id
         LEFT JOIN vnpt_dev.services ON services.id = subscriptions.service_id
         LEFT JOIN vnpt_dev.combo ON combo.id = combo_plan.combo_id
         LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.id = subscriptions.pricing_multi_plan_id
         LEFT JOIN vnpt_dev.order_service_receive ON order_service_receive.subscription_id = subscriptions.id AND
                                                     subscriptions.service_id IS NOT NULL AND
                                                     order_service_receive.service_id IS NOT NULL AND
                                                     order_service_receive.combo_id IS NULL
         LEFT JOIN vnpt_dev.order_service_status
                   ON order_service_status.id::character varying::text = order_service_receive.order_status::text
         LEFT JOIN vnpt_dev.sme_progress ON sme_progress.id = order_service_status.sme_progress_id
         LEFT JOIN vnpt_dev.business_area ON business_area.id = enterprise.business_area_id
         LEFT JOIN vnpt_dev.business_size ON business_size.id = enterprise.business_size_id
         LEFT JOIN vnpt_dev.users assignee ON assignee.id = subscriptions.assignee_id
         LEFT JOIN vnpt_dev.service_group ON service_group.id = subscriptions.service_group_id
WHERE subscriptions.deleted_flag = 1
  AND ((billings.status = ANY (ARRAY [0, 1, 2, 4])) OR billings.status IS NULL);

DROP VIEW IF EXISTS vnpt_dev.feature_view_service_group_pricing;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_service_group_pricing
            (service_group_id, service_group_pricing_id, service_group_draft_id, service_id, service_name, status,
             deleted_flag, allow_multi_sub ,pricing_id, pricing_item_id,
             pricing_name, pricing_multi_plan_id, payment_cycle, circle_type, variant_id, variant_item_id, variant_name,
             object_type_int,
             object_type, icon)
AS
WITH item_detail AS (SELECT sgp.id AS serviceGroupPricingId,
                            sgp.service_group_id_draft AS serviceGroupDraftId,
                            services.id AS serviceId,
                            services.service_name AS serviceName,
                            services.status AS status,
                            services.deleted_flag AS deletedFlag,
                            services.allow_multi_sub as allowMultiSub,
                            MAX(pricing.id) AS pricingId,
                            MAX(case when sgpi.object_type = 'PRICING' then sgpi.id end) AS pricingItemId,
                            MAX(pricing.pricing_name) AS pricingName,
                            MAX(pmp.id) AS pricingMultiPlanId,
                            MAX(pmp.payment_cycle) AS paymentCycle,
                            MAX(pmp.circle_type) AS circleType,
                            MAX(variant.id) AS variantId,
                            MAX(case when sgpi.object_type = 'DEVICE_VARIANT' then sgpi.id end) AS variantItemId,
                            MAX(variant.full_name) AS variantName,
                            MAX(CASE sgpi.object_type
                                    WHEN 'DEVICE_VARIANT' THEN 3
                                    WHEN 'PRICING' THEN 2
                                    ELSE 1
                                END) AS objectTypeInt
                     FROM vnpt_dev.service_group_pricing_item sgpi
                              LEFT JOIN vnpt_dev.service_group_pricing sgp ON sgpi.service_group_pricing_id = sgp.id
                              LEFT JOIN vnpt_dev.services services ON sgp.service_id = services.id
                              LEFT JOIN vnpt_dev.pricing pricing
                                        ON sgpi.object_type = 'PRICING' AND sgpi.object_id = pricing.id
                              LEFT JOIN vnpt_dev.pricing_multi_plan pmp
                                        ON sgpi.pricing_multi_plan_id = pmp.id AND sgpi.object_type = 'PRICING'
                              LEFT JOIN vnpt_dev.variant variant
                                        ON sgpi.object_type = 'DEVICE_VARIANT' AND sgpi.object_id = variant.id
                     GROUP BY sgp.id, services.id, services.service_name)
SELECT sg.id AS serviceGroupId,
       items.*,
       CASE
           WHEN items.objectTypeInt = 1 THEN 'DEVICE_NO_VARIANT'
           WHEN items.objectTypeInt = 2 THEN 'PRICING'
           WHEN items.objectTypeInt = 3 THEN 'DEVICE_VARIANT'
           ELSE 'OTHER'
           END AS objectType,
       icon.file_path as iconPath
FROM item_detail items
         JOIN vnpt_dev.service_group_pricing sgp ON items.serviceGroupPricingId = sgp.id
         LEFT JOIN vnpt_dev.service_group_draft draft ON items.serviceGroupDraftId = draft.id
         LEFT JOIN vnpt_dev.service_group sg ON sg.id = sgp.service_group_id
         LEFT JOIN vnpt_dev.file_attach icon on items.serviceId = icon.service_id and icon.object_type = 0;


---------------------------------------------------------------- Xuất dữ liệu thuê bao
drop view if exists report_view_pricing_and_combo_plan;
CREATE
OR REPLACE VIEW report_view_pricing_and_combo_plan AS
SELECT services.id,
       (concat(services.id, '0000'))::bigint AS unique_id, CASE
                                                               WHEN ((services.service_owner = 0) OR (services.service_owner = 1))
                                                                   THEN concat('(ON) ', services.service_name)
                                                               ELSE concat('(OS) ', services.service_name)
    END AS name,
       services.user_id,
       services.service_owner,
       NULL::smallint AS combo_owner, services.categories_id,
       NULL::character varying AS categories_ids
FROM vnpt_dev.services
WHERE ((services.deleted_flag = 1) AND (services.approve = 1) AND (services.status = 1))
UNION
SELECT combo.id,
       (concat(combo.id, '0001'))::bigint AS unique_id, concat('(Combo) ', combo.combo_name) AS name,
       combo.user_id,
       NULL::smallint AS service_owner, combo.combo_owner,
       NULL::bigint AS categories_id, combo.categories_id AS categories_ids
FROM vnpt_dev.combo
WHERE ((combo.deleted_flag = 1) AND (combo.approve = 1) AND (combo.status = 1))
UNION
SELECT service_group.id,
       (concat(service_group.id, '0004'))::bigint AS unique_id, concat('(Service group) ', service_group.name) AS name,
       service_group.user_id,
       service_group.group_service_owner AS service_owner,
       NULL::smallint AS combo_owner, NULL::bigint AS categories_id, REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids
FROM vnpt_dev.service_group
WHERE service_group.deleted_flag = 1
  AND service_group.status = 1
  AND service_group.approve = 1
  AND service_group.id in (SELECT MAX(id) FROM vnpt_dev.service_group GROUP BY group_service_draft_id);


DROP VIEW IF EXISTS report_view_subscription_service_group;
Create VIEW report_view_subscription_service_group as
SELECT
    subscriptions.id,
    subscriptions.service_id,
    subscriptions.quantity,
    subscriptions.total_amount,
    subscriptions.status,
    subscriptions.deleted_flag,
    subscriptions.created_by,
    subscriptions.modified_by,
    subscriptions.created_at,
    subscriptions.modified_at,
    subscriptions.user_id,
    subscriptions.from_date,
    subscriptions.cancelled_time,
    subscriptions.pricing_id,
    subscriptions.sme_subscription_id,
    subscriptions.installed,
    subscriptions.expired_time,
    subscriptions.used_quantity,
    subscriptions.registed_by,
    subscriptions.sub_registration_id,
    subscriptions.started_at,
    subscriptions.start_charge_at,
    subscriptions.trial_day,
    subscriptions.reg_type,
    subscriptions.payment_method,
    subscriptions.confirm_status,
    subscriptions.current_cycle,
    subscriptions.phone_no,
    subscriptions.contact,
    subscriptions.address,
    subscriptions.sub_code,
    subscriptions.start_current_cycle,
    subscriptions.end_current_cycle,
    subscriptions.end_current_cycle_new,
    subscriptions.current_payment_date,
    subscriptions.dhsxkd_sub_code,
    subscriptions.next_payment_time,
    subscriptions.awaiting_cancel,
    subscriptions.pre_order,
    subscriptions.number_of_cycles,
    subscriptions.cycle_type,
    subscriptions.traffic_id,
    subscriptions.combo_plan_id,
    subscriptions.canceled_by,
    subscriptions.subscription_contract_id,
    subscriptions.called_trans,
    subscriptions.change_date,
    subscriptions.change_status,
    subscriptions.update_date,
    subscriptions.update_status,
    subscriptions.portal_type,
    subscriptions.message_setup,
    subscriptions.employee_code,
    subscriptions.pricing_multi_plan_id,
    subscriptions.number_of_cycles_default,
    subscriptions.refer_subscription,
    subscriptions.traffic_user,
    null::text AS pricing_name,
    users.customer_type,
    service_group.name AS service_name,
    service_group.id AS c_service_id,
    (concat(service_group.id, '0004'))::bigint AS service_unique_id,
    null::bigint AS pricing_unique_id,
    '-1'::integer AS p_payment_cycle,
    '-1'::integer AS p_cycle_type,
    null::bigint AS price,
    4 AS subscription_type,
    CASE
       WHEN (service_group.group_service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
       WHEN (service_group.group_service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
       WHEN (service_group.group_service_owner IS NULL) THEN 'OS'::text
       ELSE NULL::text
       END AS onos,
    report_view_customer.nation_id AS c_nation_id,
    report_view_customer.nation_name AS c_nation_name,
    report_view_customer.province_id AS c_province_id,
    report_view_customer.province_name AS c_province_name,
    report_view_customer.district_id AS c_district_id,
    report_view_customer.district_name AS c_district_name,
    report_view_customer.ward_id AS c_ward_id,
    report_view_customer.ward_name AS c_ward_name,
    report_view_customer.street_name,
    report_view_customer.address AS c_address,
    report_view_customer.name AS sme_name,
    report_view_customer.email AS c_email,
    report_view_customer.phone_number AS c_phone_number,
    report_view_customer.tin,
    billings.status AS payment_status,
    billings.next_total_amount,
    bill_item.quantity AS service_quantity,
    '-1'::integer AS order_status,
    ''::text AS order_status_name,
    subscriptions.status AS c_status,
    CASE
       WHEN (subscriptions.employee_code IS NOT NULL) THEN 1
       WHEN (subscriptions.traffic_id IS NOT NULL) THEN 2
       WHEN (subscriptions.portal_type = 1) THEN 3
       WHEN (subscriptions.portal_type = 2) THEN 3
       WHEN (subscriptions.portal_type = 3) THEN 0
       ELSE NULL::integer
    END AS source,
    service_group.user_id AS provider_id,
    '-1'::integer AS categories_id,
    REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    1 AS is_one_time,
    0 as has_renew
FROM (((((vnpt_dev.subscriptions
    JOIN vnpt_dev.service_group ON ((subscriptions.service_group_id = service_group.id)))
    LEFT JOIN vnpt_dev.users ON ((users.id = subscriptions.user_id)))
    LEFT JOIN vnpt_dev.report_view_customer ON ((subscriptions.user_id = report_view_customer.id)))
    LEFT JOIN vnpt_dev.billings ON (((billings.subscriptions_id = subscriptions.id)
        AND (billings.id IN (
            SELECT max(billings.id) AS max
            FROM vnpt_dev.billings
            GROUP BY billings.subscriptions_id)))))
    LEFT JOIN vnpt_dev.bill_item ON (((bill_item.billing_id = billings.id) AND (bill_item.object_id = subscriptions.combo_plan_id) AND (bill_item.object_type = 1))))
WHERE ((subscriptions.deleted_flag = 1) AND (subscriptions.confirm_status = 1));

comment on column vnpt_dev.service_reaction.type is '1: service, 2: combo, 3: pricing, 4: plan, 5: combo pricing, 6: service group';

--------------------------- BC chi tiết thuê bao --------------------------------
DROP VIEW IF EXISTS vnpt_dev.get_report_subscriptions_details_new;
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_details_new"("is_target" int4, "i_customer_email" varchar, "i_customer_name" varchar, "i_object_id" varchar, "i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_affiliate_code" varchar, "i_bill_ids" varchar, "i_provider_ids" varchar, "i_aff_agency_user_ids" varchar)
    RETURNS TABLE("id" int8, "subcode" varchar, "createdat" timestamp, "provincename" varchar, "subsstatus" text, "smename" text, "customertype" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "provider" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifieat" timestamp, "serviceownertype" text, "registedby" text, "trafficid" varchar, "trafficuser" varchar, "employeecode" varchar, "dhsxkdsubcode" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "createdsourcemigration" int4, "setupcode" varchar, "paymentdate" timestamp, "createdexportinvoice" timestamp, "codeinvoice" text, "cancelledtime" timestamp, "isonetime" int2, "affiliatecode" varchar, "affagencyuserid" int8, "affagencyname" varchar, "affagencycode" varchar, "endcurrentcycle" date, "identityno" varchar, "billid" int8)

AS $BODY$

DECLARE
    run_query text;
    raw_query
              varchar = '
    WITH subReportCTE AS (
     SELECT
           sub.id AS id,
           sub.sub_code AS subCode,
           sub.created_at AS createdAt,
           sme.province AS provinceName,
           CASE
               WHEN sub.status = -1 THEN ''NOT_SET''
               WHEN sub.status = 0 THEN ''FUTURE''
               WHEN sub.status = 1 THEN ''IN_TRIAL''
               WHEN sub.status = 2 THEN ''ACTIVE''
               WHEN sub.status = 3 THEN ''CANCELED''
               WHEN sub.status = 4 THEN ''NON_RENEWING''
           END AS subsStatus,
           sme.name AS smeName,
           sme.customer_type AS customerType,
           sme.tin AS taxtNo,
           sme.address AS address,
           sme.street AS street,
           sme.ward AS ward,
           sme.district AS district,
           sme.province AS province,
           sme.nation AS nation,
           sme.phone_number AS phoneNo,
           sme.email AS email,
           CASE
               WHEN sg.id IS NOT NULL THEN sg.name
               ELSE COALESCE(subServices.service_name, subCombo.service_name)
           END AS serviceName,
           COALESCE(provider.name, '''') AS provider,
           COALESCE(subServices.pricing_name, subCombo.pricing_name) AS pricingName,
           sub.number_of_cycles AS numberOfCycle,
           COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle) AS planPaymentCycle,
           COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type) AS planCycleType,
           prcMultiPlan.payment_cycle AS multiPaymentCycle,
           CASE
                    WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
                    WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
                    WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
                    WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
                    ELSE ''''
           END AS multiCycleType,
           CASE
                    WHEN sub.installed IS NULL AND sub.installed = 0 THEN ''Đang cài đặt''
                    WHEN sub.installed = 1 THEN ''Đã cài đặt''
                    WHEN sub.installed = 2 THEN ''Gặp sự cố''
                    ELSE ''''
           END AS subsInstalled,
           sme_progress.name AS smeProgressName,
           COALESCE(subServices.sub_type, subCombo.sub_type) AS subscriptionType,
           sub.modified_at AS modifiedAt,
           COALESCE(subServices.service_owner_type, subCombo.service_owner_type) AS serviceOwnerType,
           CASE
                     WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                     WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                     ELSE ''OneSME''
           END AS registedBy,
           sub.traffic_id  AS trafficId,
           sub.traffic_user AS trafficUser,
           sub.employee_code AS employeeCode,
           sub.dhsxkd_sub_code AS dhsxkdSubCode,
           CASE
               WHEN sub.created_source_migration = 1 THEN ''ĐHSXKD''
               WHEN sub.traffic_source = ''accesstrade'' THEN ''Affiliate AccessTrade''
               WHEN sub.traffic_source = ''apinfo'' THEN ''Affiliate Apinfo''
               WHEN sub.affiliate_one IS NOT NULL THEN ''Affiliate Onesme''
               WHEN sub.traffic_id IS NOT NULL THEN ''Affiliate Masoffer''
               WHEN sub.employee_code IS NOT NULL THEN ''AM G.Thiệu''
               WHEN sub.portal_type IN (1,2) OR (bill.portal_type IS NOT NULL AND bill.portal_type IN (1,2)) THEN ''Dev/Admin''
               ELSE ''oneSME''
           END AS createdSource,
           sub.migrate_time AS migrateTime,
           sub.migrate_code AS migrateCode,
           CASE
               WHEN bill.status = 0 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Khởi tạo''
               WHEN bill.status = 1 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Chờ thanh toán''
               WHEN bill.status = 2 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Đã thanh toán''
               WHEN bill.status = 3 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Thanh toán thất bại''
               WHEN bill.status = 4 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN ''Quá hạn thanh toán''
               WHEN osServiceReceive.payment_status = ''1'' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Đã thanh toán''
               WHEN osServiceReceive.payment_status = ''0'' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Chờ thanh toán''
               WHEN osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL AND
                    COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN ''Chờ thanh toán''
               ELSE ''Chờ thanh toán''
           END AS billStatus,
           bill.billing_code AS billCode,
           CASE
               WHEN bill.bill_action_type = 3 THEN 0
               ELSE bill.bill_action_type
           END AS subscriptionState,
           CASE
               WHEN bill.bill_action_type = 0 then ''Đăng ký mới''
               WHEN bill.bill_action_type = 1 then ''Chỉnh sửa''
               WHEN bill.bill_action_type = 2 THEN ''Đổi gói''
               WHEN bill.bill_action_type = 3 then ''Kích hoạt''
               WHEN bill.bill_action_type = 5 then ''Gia hạn''
               ELSE ''''
           END AS state,
           CASE
               WHEN bill.bill_action_type = 0 THEN sub.created_at
               ELSE COALESCE(bill.payment_date, bill.created_at)
           END AS registrationDate,
           CASE
               WHEN bill.bill_action_type = 0 THEN sub.started_at
               ELSE COALESCE(bill.payment_date, CAST(bill.created_at AS DATE))
           END AS startAt,
           CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN sub.dhsxkd_sub_code
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN osServiceReceive.transaction_code
               ELSE ''''
           END  AS dhsxkdCode,
           CASE
               WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND subServices.plan_payment_cycle IS NULL THEN prcMultiPlan.payment_cycle
               ELSE COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle)
           END AS paymentCycle,
           CASE
               WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND subServices.plan_cycle_type IS NULL THEN
                   (
                     CASE
                            WHEN prcMultiPlan.circle_type = 0 THEN ''DAILY''
                            WHEN prcMultiPlan.circle_type = 1 THEN ''WEEKLY''
                            WHEN prcMultiPlan.circle_type = 2 THEN ''MONTHLY''
                            WHEN prcMultiPlan.circle_type = 3 THEN ''YEARLY''
                            ELSE ''''
                     END
                    )
               ELSE COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type)
           END AS cycleType,
           CASE
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' THEN (
                    CASE
                        WHEN sub.installed IS NULL OR sub.installed = 0 THEN ''Đang cài đặt''
                        WHEN sub.installed = 1 THEN ''Đã cài đặt''
                        WHEN sub.installed = 2 THEN ''Gặp sự cố''
                        ELSE ''''
                    END)
                         WHEN COALESCE(subServices.service_owner, subCombo.combo_owner) = 2 THEN (
                                    CASE
                                        WHEN sub.os_3rd_status = 1 THEN ''Tiếp nhận đơn hàng''
                                        WHEN sub.os_3rd_status = 2 THEN ''Đang triển khai''
                                        WHEN sub.os_3rd_status = 3 THEN ''Hoàn thành''
                                        WHEN sub.os_3rd_status = 4 THEN ''Huỷ đơn hàng''
                                        WHEN sub.os_3rd_status = 5 THEN ''Đặt hàng thành công''
                                        ELSE ''''
                                    END
                              )
               WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''OS'' THEN COALESCE(sme_progress.name , '''')
               ELSE ''''
           END AS installedStatus,
           CASE
               WHEN sme_progress.name = ''Hủy'' THEN ''CANCELED''
               ELSE (
                        CASE
                            WHEN sub.status = -1 THEN ''NOT_SET''
                            WHEN sub.status = 0 THEN ''FUTURE''
                            WHEN sub.status = 1 THEN ''IN_TRIAL''
                            WHEN sub.status = 2 THEN ''ACTIVE''
                            WHEN sub.status = 3 THEN ''CANCELED''
                            WHEN sub.status = 4 THEN ''NON_RENEWING''
                        END
                   )
           END AS status,
           CASE
               WHEN sub.traffic_id IS NULL THEN (
                    CASE
                         WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                         WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                         WHEN sub.portal_type = 3 THEN ''OneSME''
                         ELSE ''''
                    END
               )
               WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id
               ELSE sub.traffic_user
           END AS creator,
           billVnptPay.transaction_code AS payTransactionCode,
           CASE
                WHEN billPayments.promotion_amount < 0 THEN 0
                ELSE billPayments.promotion_amount
           END AS promotionAmount,
           CASE
                WHEN billPayments.unit_amount < 0 THEN 0
                ELSE billPayments.unit_amount
           END AS unitAmount,
           CASE
               WHEN bill.action_type = 1 THEN billPayments.amount_change
               WHEN bill.bill_action_type <> 1 AND billPayments.pre_amount_tax > 0 THEN billPayments.pre_amount_tax
               ELSE 0
               END AS preAmountTax,
           CASE
                WHEN billPayments.amount_tax > 0 then billPayments.amount_tax
                ELSE 0
           END AS amountTax,
           CASE
                WHEN billPayments.after_amount_tax > 0 then billPayments.after_amount_tax
                ELSE 0
           END AS afterAmountTax,
           CASE
			    WHEN sub.created_source_migration = 1 THEN 5
			    WHEN sub.traffic_source = ''accesstrade'' THEN 6
			    WHEN sub.traffic_source = ''apinfo'' THEN 8
			    WHEN sub.affiliate_one IS NOT NULL THEN 7
			    WHEN sub.traffic_id IS NOT NULL THEN 3
			    WHEN sub.employee_code IS NOT NULL THEN 2
			    WHEN sub.portal_type IN (1,2) THEN 4
			    ELSE 1
		   END AS createdSourceMigration,
           osServiceReceive.setup_code AS setupCode,
           CASE
               WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)
                   AND CAST(bill.created_at AS DATE) = bill.billing_date AND COALESCE(subServices.pricing_type, subCombo.pricing_type) = 0
                   THEN bill.created_at
               ELSE bill.payment_date
           END paymentDate,
           bill.created_export_invoice AS createdExportInvoice,
           bill.code AS codeInvoice,
           CASE
               WHEN sme_progress.name = ''Hủy'' OR sub.status IN (3, 4) THEN sub.cancelled_time
               END AS cancelledTime,
           sub.is_one_time AS isOneTime,
           CASE
               WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user
               WHEN sub.traffic_source = ''accesstrade'' THEN sub.traffic_user
               WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one
           END AS affiliateCode,
           bill.aff_agency_user_id AS affAgencyUserId,
           COALESCE(affiliateAgency.name, CONCAT('' '', affiliateAgency.last_name, affiliateAgency.first_name)) AS affAgencyName,
           COALESCE(bill.aff_agency_code, '''') AS affAgencyCode,
           CASE
               WHEN bill.bill_action_type = 5 THEN bill.end_date_new_renewal
               ELSE sub.end_current_cycle
           END AS endCurrentCycle,
           sme.identity_no AS identityNo,
           bill.id AS billId
        FROM vnpt_dev.subscriptions sub
            LEFT JOIN vnpt_dev.view_report_sub_sme AS sme ON sub.user_id = sme.id
            LEFT JOIN vnpt_dev.users AS register ON sub.registed_by = register.id
            LEFT JOIN vnpt_dev.pricing_multi_plan prcMultiPlan ON prcMultiPlan.id = sub.pricing_multi_plan_id
            LEFT JOIN vnpt_dev.order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id
            LEFT JOIN vnpt_dev.order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)
            LEFT JOIN vnpt_dev.sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id
            LEFT JOIN vnpt_dev.view_report_sub_services AS subServices ON sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id
            LEFT JOIN vnpt_dev.view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id
            LEFT JOIN vnpt_dev.view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id
            LEFT JOIN vnpt_dev.view_report_sub_bill_payment AS billPayments ON bill.id = billPayments.id
            LEFT JOIN vnpt_dev.view_report_sub_vnpt_pay_response AS billVnptPay ON bill.id = billVnptPay.bill_id
            LEFT JOIN vnpt_dev.service_group as sg on sg.id = sub.service_group_id
            LEFT JOIN vnpt_dev.users AS provider ON COALESCE(subServices.provider_id, subCombo.provider_id) = provider.id
            LEFT JOIN vnpt_dev.users AS affiliateAgency ON affiliateAgency.id = bill.aff_agency_user_id
        WHERE
              sub.deleted_flag = 1 AND
              sub.confirm_status = 1 AND
              ((subServices.service_owner in (0,1) OR (subCombo.combo_owner in (0,1))) -- ON lay het, OS chi lay neu co trang thai
				OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and (subServices.service_owner = 3 OR (subCombo.combo_owner = 3)))
				OR (sub.os_3rd_status is not null and (subServices.service_owner = 2 OR (subCombo.combo_owner = 2)))) AND
              ((bill.bill_action_type <> 5 OR COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = ''ON'' -- ẩn Gia hạn, OS, chưa thanh toán --
                OR NOT ((osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL) or osServiceReceive.payment_status = ''0''))) AND
              (%5$s = -1 OR sme.province_id = %5$s) AND
              (''%6$s'' = ''ALL'' OR sme.customer_type_raw = ''%6$s'') AND
              (''%19$s'' = ''ALL'' OR (''%19$s'' = ''SERVICE_GROUP'' AND sub.service_group_id IS NOT NULL) OR subServices.sub_type = ''%19$s'') AND
              (''%22$s'' = ''ALL'' OR sub.employee_code = ''%22$s'') AND
              (%13$s = -2 OR sub.status = %13$s) AND
              (%14$s = -1 OR sub.service_id = %14$s) AND
              (
                (
                     COALESCE(subServices.sub_type, subCombo.sub_type) = ''SERVICE'' AND
                     (%20$s = -1 OR subServices.categories_id = %20$s) AND
                     (%16$s = -1 OR sub.pricing_id = %16$s) AND
                     (''%17$s'' = ''-1'' OR  sub.pricing_id = ANY ((''{'' || ''%17$s'' || ''}'')::int8[]))
                 ) OR
                 (
                      COALESCE(subServices.sub_type, subCombo.sub_type) = ''COMBO'' AND
                      (''%21$s'' = ''-1'' OR string_to_array(subCombo.categories_id, '','') && string_to_array(''%21$s'', '','')) AND
                      (%16$s = -1 OR ''%18$s'' = ''-1'' OR sub.combo_plan_id = ANY ((''{'' || ''%18$s'' || ''}'')::int8[])) AND
                      (%14$s = -1 OR ''%15$s'' = ''-1'' OR subCombo.combo_id = ANY ((''{'' || ''%15$s'' || ''}'')::int8[]))
                 )
              ) AND
              (''%29$s'' = ''-1'' OR provider.id = ANY((''{'' || ''%29$s'' || ''}'')::int8[])) AND
              (''%30$s'' = ''-1'' OR bill.aff_agency_user_id = ANY((''{'' || ''%30$s'' || ''}'')::int8[]))
)
SELECT * FROM subReportCTE
        WHERE
            (''%28$s'' = ''-1'' OR subReportCTE.billId = ANY((''{'' || ''%28$s'' || ''}'')::int8[])) AND
            (%23$s = -1 OR subReportCTE.subscriptionState = %23$s) AND
            (subReportCTE.registrationDate >= CAST(''%11$s'' AS TIMESTAMP)) AND
            (subReportCTE.registrationDate <= CAST(''%12$s'' AS TIMESTAMP)) AND
            (''%24$s'' = ''-1'' OR subReportCTE.creator = ''%24$s'') AND
            (''%25$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') >= ''%25$s'') AND
            (''%26$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') <= ''%26$s'') AND
            (''%8$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%8$s'') AND
            (''%9$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%9$s'') AND
            (
                    (subReportCTE.createdSourceMigration <> 5) AND
                    ( -- Do RK lưu created_source khác với code BE --
                        %7$s = -1 OR (%7$s IN (1, 2, 3, 4, 6, 7, 8) AND %7$s = subReportCTE.createdSourceMigration)
                    )
            ) AND
            (''%27$s'' = '''' OR subReportCTE.affiliateCode = ''%27$s'') AND
            (''%3$s'' = '''' OR subReportCTE.smeName ilike (''%%'' || ''%3$s'' || ''%%'')) AND
            (''%2$s'' = '''' OR subReportCTE.email = ''%2$s'')
        ORDER BY subReportCTE.registrationDate DESC';

BEGIN
    run_query
        = format(raw_query, is_target, i_customer_email, i_customer_name, i_object_id,
                 i_province_id, i_customer_type, i_createdsource, i_migrate_start_date,
                 i_migrate_end_date, i_migrate_codes, i_start_date, i_end_date,
                 i_status, i_service_id, i_combo_ids, i_pricing_id, i_pricing_ids,
                 i_combo_plan_ids, i_subscription_type, i_category_service, i_category_combo,
                 i_employee_code, i_subscription_state, i_creator, i_cancelled_time_start,
                 i_cancelled_time_end, i_affiliate_code, i_bill_ids, i_provider_ids, i_aff_agency_user_ids);
    RAISE
        NOTICE 'Run query: %', run_query;
    RETURN QUERY EXECUTE run_query;
END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;
