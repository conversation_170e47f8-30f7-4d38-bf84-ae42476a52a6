drop table if exists vnpt_dev.action_log_service_group;
create table vnpt_dev.action_log_service_group
(
	id bigserial NOT NULL,
	icon_id int8 NULL,
	group_draft_id int8 NULL,
	group_name varchar(100) NULL, -- tên nhóm từng version
	approve int2 NULL, -- trạng thái duyệt
	updated_at timestamp NULL, -- thời gian tạo
	updated_by int8 NULL, -- người tạo
	portal int2 NULL, -- portal
	version int2 NULL, -- phiên bản
	CONSTRAINT action_log_service_group_pk PRIMARY KEY (id)
);
comment
on table vnpt_dev.action_log_service_group is '<PERSON><PERSON><PERSON> sử tác động nhóm sản phẩm';

comment on column vnpt_dev.action_log_service_group.group_draft_id is 'id nhóm SPDV';
comment on column vnpt_dev.action_log_service_group.icon_id is 'Ảnh đại diện group của version đó';
comment on column vnpt_dev.action_log_service_group.group_name is 'Tên group của version đó';
comment on column vnpt_dev.action_log_service_group.approve is 'Tr<PERSON>ng thái duyệt: 0 - chưa duyệt, 1 - đã duyệt, 2 - chờ duyệt, 3 - từ chối';
comment on column vnpt_dev.action_log_service_group.updated_at is 'Thời gian cập nhật';
comment on column vnpt_dev.action_log_service_group.updated_by is 'Người tác động';
comment on column vnpt_dev.action_log_service_group.portal is 'Portal người tác động';
comment on column vnpt_dev.action_log_service_group.version is 'Phiên bản';



