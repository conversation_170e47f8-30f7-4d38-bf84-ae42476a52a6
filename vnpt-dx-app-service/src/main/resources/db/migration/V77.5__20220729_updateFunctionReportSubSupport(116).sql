DROP FUNCTION "vnpt_dev"."get_report_subscriptions";
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions"("i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar)
  RETURNS TABLE("id" int8, "subcode" varchar, "created_at" timestamp, "provincename" varchar, "subsstatus" text, "smename" varchar, "customerType" text, "taxtno" varchar, "address" varchar, "street" varchar, "ward" varchar, "district" varchar, "province" varchar, "nation" varchar, "phoneno" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "planpaymentcycle" int2, "plancycletype" text, "multipaymentcycle" int8, "multicycletype" text, "subsinstalled" text, "smeprogressname" varchar, "subscriptiontype" text, "modifiedat" timestamp, "serviceownertype" text, "registedby" text, "traffic_id" varchar, "traffic_user" varchar, "employeecode" varchar, "dhsxkd_sub_code" varchar, "createdsource" text, "migratetime" timestamp, "migratecode" varchar, "billstatus" text, "billcode" varchar, "subscriptionstate" int4, "state" text, "registrationdate" timestamp, "startat" timestamp, "dhsxkdcode" varchar, "paymentcycle" int8, "cycletype" text, "installedstatus" text, "status" text, "creator" varchar, "paytransactioncode" varchar, "promotionamount" float8, "unitamount" float8, "preamounttax" float8, "amounttax" float8, "afteramounttax" float8, "created_source_migration" int2, "setupCode" varchar) AS $BODY$
DECLARE last_query text;
tmp_last_query text = 'SELECT DISTINCT
				t.id,
				t.subCode,
				t.created_at,
				t.provinceName,
				t.subsStatus,
				t.smeName,
				t.customerType,
				t.taxtNo,
				t.address,
				t.street,
				t.ward,
				t.district,
				t.province,
				t.nation,
				t.phoneNo,
				t.email,
				t.serviceName,
				t.pricingName,
				t.numberOfCycle,
				t.planPaymentCycle,
				t.planCycleType,
				t.multiPaymentCycle,
				t.multiCycleType,
				t.subsInstalled,
				t.smeProgressName,
				t.subscriptionType,
				t.modifiedAt,
				t.serviceOwnerType,
				t.registedBy,
				t.traffic_id,
				t.traffic_user,
				t.employeeCode,
				t.dhsxkd_sub_code,
				t.createdSource,
				t.migrateTime,
				t.migrateCode,
				t.billStatus,
				t.billCode,
				t.subscriptionState,
				t.state,
				t.registrationDate,
				t.startAt,
				t.dhsxkdCode,
				t.paymentCycle,
				t.cycleType,
				t.installedStatus,
				t.status,
				t.creator,
				t.payTransactionCode,
				t.promotionAmount,
				t.unitAmount,
				t.preAmountTax,
				t.amountTax,
				t.afterAmountTax,
				t.created_source_migration,
				t.setupCode
FROM (
        SELECT
						a.id,
						a.subCode,
						a.created_at,
						a.provinceName,
						a.subsStatus,
						a.smeName,
						a.customerType,
						a.taxtNo,
						a.address,
						a.street,
						a.ward,
						a.district,
						a.province,
						a.nation,
						a.phoneNo,
						a.email,
						a.serviceName,
						a.pricingName,
						a.registration_date,
						--a.start_at,
						a.numberOfCycle,
						a.planPaymentCycle,
						a.planCycleType,
						a.multiPaymentCycle,
						a.multiCycleType,
						a.subsInstalled,
						a.smeProgressName,
						a.subscriptionType,
						a.modifiedAt,
						a.serviceOwnerType,
						a.registedBy,
						a.traffic_id,
						a.traffic_user,
						a.employeeCode,
						a.dhsxkd_sub_code,
						a.createdSource,
						a.created_source_migration,
						a.migrateTime,
						a.migrateCode,
						a.setupCode,
            CASE
                WHEN bill.status = 0 THEN ''Khởi tạo''
                WHEN bill.status = 1 THEN ''Chờ thanh toán''
                WHEN bill.status = 2 THEN ''Đã thanh toán''
                WHEN bill.status = 3 THEN ''Thanh toán thất bại''
                WHEN bill.status = 4 THEN ''Quá hạn thanh toán''
            END AS billStatus,
            bill.billing_code as billCode,
            CASE
                WHEN bill.isRenew = 0 then 1
                ELSE 2
            END as subscriptionState,
            CASE
                WHEN bill.isRenew = 0 then ''Đăng ký mới''
                ELSE ''Gia hạn''
            END as state,
            CASE
                WHEN bill.isRenew = 0 then a.registration_date
                ELSE CAST (bill.created_at AS TIMESTAMP)
            END as registrationDate,
            CASE
                WHEN bill.isRenew = 0 then a.start_at
                ELSE COALESCE(
                    bill.payment_date,
                    CAST (bill.created_at AS Date)
                )
            END as startAt,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN a.dhsxkd_sub_code
                WHEN a.serviceOwnerType = ''OS'' THEN osr.transaction_code
            END AS dhsxkdCode,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planPaymentCycle IS NULL
                ) THEN a.multiPaymentCycle
                ELSE a.planPaymentCycle
            END AS paymentCycle,
            CASE
                WHEN (
                    a.subscriptionType = ''SERVICE''
                    AND a.planCycleType IS NULL
                ) THEN a.multiCycleType
                ELSE a.planCycleType
            END AS cycleType,
            CASE
                WHEN a.serviceOwnerType = ''ON'' THEN COALESCE(a.subsInstalled, '''')
                WHEN a.serviceOwnerType = ''OS'' THEN COALESCE(a.smeProgressName, '''')
            END AS installedStatus,
            CASE
                WHEN a.smeProgressName = ''Hủy'' THEN ''CANCELED''
                ELSE a.subsStatus
            END AS status,
            CASE
                WHEN a.traffic_id IS NULL THEN a.registedBy
                WHEN a.traffic_user IS NULL THEN a.traffic_id
                ELSE a.traffic_user
            END AS creator,
            p.transactionCode AS payTransactionCode,
            COALESCE (
                xy.promotionAmount,
                xy2.promotionAmount,
                0
            ) AS promotionAmount,
            COALESCE (xy.unitAmount, xy2.unitAmount, 0) AS unitAmount,
            COALESCE (b.preAmountTax, b2.preAmountTax, 0) AS preAmountTax,
            COALESCE (bt.amountTax, bt2.amountTax, 0) AS amountTax,
            COALESCE (
                b.afterAmountTax,
                b2.afterAmountTax,
                0
            ) AS afterAmountTax
        FROM tmp_table_all_sub a
            LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
            LEFT JOIN tmp_table_bill_vnptpay p ON p.billId = bill.id
            LEFT JOIN vnpt_dev.order_service_receive osr ON a.id = osr.subscription_id
            LEFT JOIN tmp_table_bill_vnptpay_batch b ON a.id = b.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_vnptpay_batch_t bt ON a.id = bt.subscriptions_id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_vnptpay_batch_t2 bt2 ON bill.id = bt2.id
            and bill.isRenew = 1
            LEFT JOIN tmp_table_bill_promotion_xy xy ON xy.id = a.id
            and bill.isRenew = 0
            LEFT JOIN tmp_table_bill_promotion_xy2 xy2 ON xy2.id = bill.id
            and bill.isRenew = 1
        WHERE (
                ''%1$s'' = ''ALL''
                OR a.subscriptionType = ''%1$s''
            )
    ) t
WHERE (
        %2$s = -1
        OR t.subscriptionState = %2$s
    )
    AND (
        t.registrationDate >= CAST(''%3$s'' AS timestamp)
    )
    AND (
        t.registrationDate <= CAST(''%4$s'' AS timestamp)
    )
    AND (
        ''%5$s'' = ''-1''
        OR t.creator = ''%5$s''
    )
    AND(
			(%6$s = -1) OR
			(%6$s = 0) OR
			(
				(%6$s > 0)
				AND(
					t.created_source_migration = 0 OR
					t.created_source_migration IS NULL OR
					((t.created_source_migration > 0) AND (
							t.migrateTime >= CAST(''%7$s'' AS timestamp)
					)
					AND (
							t.migrateTime <= CAST(''%8$s'' AS timestamp)
					))
				)
				AND (%9$s)
			)
		);';
tmp_all_sub_query VARCHAR = '
	DROP TABLE IF EXISTS tmp_table_all_sub;
CREATE TEMP TABLE tmp_table_all_sub AS
	SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
		CASE
			WHEN u.customer_type = ''KHDN'' THEN u.name
			WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
			WHEN u.customer_type = ''HKD'' THEN u.name
			ELSE u.name
		END as smeName,
		CASE
			WHEN u.customer_type = ''KHDN'' THEN ''Doanh nghiệp''
			WHEN u.customer_type = ''CN'' THEN ''Cá nhân''
			WHEN u.customer_type = ''HKD'' THEN ''Hộ kinh doanh''
			ELSE ''''
		END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.service_name AS serviceName,
    p2.pricing_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at AS start_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    pmp.payment_cycle AS multiPaymentCycle,
    CASE
        WHEN pmp.circle_type = 0 THEN ''DAILY''
        WHEN pmp.circle_type = 1 THEN ''WEEKLY''
        WHEN pmp.circle_type = 2 THEN ''MONTHLY''
        WHEN pmp.circle_type = 3 THEN ''YEARLY''
    END AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''SERVICE'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.service_owner IN (0, 1) THEN ''ON''
        WHEN s2.service_owner IN (2, 3) THEN ''OS''
        WHEN s2.service_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin - '', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev - '', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
		CASE
			WHEN s.created_source_migration = 1 THEN ''AM G.Thiệu''
			WHEN s.created_source_migration = 2 THEN ''Affiliate''
			WHEN s.created_source_migration = 3 THEN ''Dev/Admin''
			WHEN s.created_source_migration = 4 THEN ''ĐHSXKD''
			WHEN s.created_source_migration = 0 OR s.created_source_migration IS NULL THEN ''oneSME''
			ELSE ''''
		END as createdSource,
		s.created_source_migration,
		s.migrate_time as migrateTime,
		s.migrate_code as migrateCode,
		osr2.setup_code as setupCode
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street_detail sd ON u.street_id = sd.id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
    LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
    LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.pricing_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
				-- i_employeeCode
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
				-- i_provinceId
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
				-- i_status
        OR s.status = %3$s
    )
    AND (
        %4$s = -1
				-- i_categoryService
        OR s2.categories_id = %4$s
    )
    AND (
        %5$s = -1
				-- i_serviceId
        OR s.service_id = %5$s
    )
    AND (
        %6$s = -1
				--i_pricingId
        OR ''%6$s'' = ''-1''
				OR s.pricing_id = %6$s
				OR s.pricing_id = ANY(''{%12$s}''::int8[])
        --pricing_ids
    )
    AND (
        %11$s = -1
        OR (s.created_source_migration = 1 and  %11$s = 1)
        OR (s.created_source_migration = 2 and  %11$s = 2)
        OR (s.created_source_migration = 3 and  %11$s = 3)
        OR (s.created_source_migration = 4 and  %11$s = 4)
				OR (%11$s = 0 and (s.created_source_migration = 0 OR s.created_source_migration IS NULL))
    )
UNION ALL
SELECT s.id AS id,
    s.sub_code AS subCode,
    s.created_at AS created_at,
    p.name AS provinceName,
    CASE
        WHEN s.status = -1 THEN ''NOT_SET''
        WHEN s.status = 0 THEN ''FUTURE''
        WHEN s.status = 1 THEN ''IN_TRIAL''
        WHEN s.status = 2 THEN ''ACTIVE''
        WHEN s.status = 3 THEN ''CANCELED''
        WHEN s.status = 4 THEN ''NON_RENEWING''
    END AS subsStatus,
		CASE
			WHEN u.customer_type = ''KHDN'' THEN u.name
			WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
			WHEN u.customer_type = ''HKD'' THEN u.name
			ELSE u.name
		END as smeName,
		CASE
			WHEN u.customer_type = ''KHDN'' THEN N''Doanh nghiệp''
			WHEN u.customer_type = ''CN'' THEN N''Cá nhân''
			WHEN u.customer_type = ''HKD'' THEN N''Hộ kinh doanh''
			ELSE ''''
		END as customerType,
    u.tin AS taxtNo,
    u.address AS address,
    sd.name AS street,
    w.name AS ward,
    d.name AS district,
    p.name AS province,
    n.name AS nation,
    u.phone_number AS phoneNo,
    u.email AS email,
    s2.combo_name AS serviceName,
    p2.combo_name AS pricingName,
    CAST (s.created_at AS Date) AS registration_date,
    s.started_at,
    s.number_of_cycles AS numberOfCycle,
    p2.payment_cycle AS planPaymentCycle,
    CASE
        WHEN p2.cycle_type = 0 THEN ''DAILY''
        WHEN p2.cycle_type = 1 THEN ''WEEKLY''
        WHEN p2.cycle_type = 2 THEN ''MONTHLY''
        WHEN p2.cycle_type = 3 THEN ''YEARLY''
    END AS planCycleType,
    NULL AS multiPaymentCycle,
    NULL AS multiCycleType,
    CASE
        WHEN s.installed IS NULL THEN ''Đang cài đặt''
        WHEN s.installed = 0 THEN ''Đang cài đặt''
        WHEN s.installed = 1 THEN ''Đã cài đặt''
        WHEN s.installed = 2 THEN ''Gặp sự cố''
    END AS subsInstalled,
    sp.name AS smeProgressName,
    ''COMBO'' AS subscriptionType,
    s.modified_at AS modifiedAt,
    CASE
        WHEN s2.combo_owner IN (0, 1) THEN ''ON''
        WHEN s2.combo_owner IN (2, 3) THEN ''OS''
        WHEN s2.combo_owner IS NULL THEN ''OS''
    END AS serviceOwnerType,
    CASE
        WHEN s.portal_type = 1 THEN concat(''Admin -'', u2.email)
        WHEN s.portal_type = 2 THEN concat(''Dev -'', u2.email)
        WHEN s.portal_type = 3 THEN ''OneSME''
    END AS registedBy,
    s.traffic_id,
    s.traffic_user,
    s.employee_code as employeeCode,
    s.dhsxkd_sub_code,
		CASE
			WHEN s.created_source_migration = 1 THEN ''AM G.Thiệu''
			WHEN s.created_source_migration = 2 THEN ''Affiliate''
			WHEN s.created_source_migration = 3 THEN ''Dev/Admin''
			WHEN s.created_source_migration = 4 THEN ''ĐHSXKD''
			WHEN s.created_source_migration = 0 OR s.created_source_migration IS NULL THEN ''oneSME''
			ELSE ''''
		END as createdSource,
		s.created_source_migration,
		s.migrate_time as migrateTime,
		s.migrate_code as migrateCode,
		osr2.setup_code as setupCode
FROM vnpt_dev.subscriptions s
    LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
    LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
    LEFT JOIN vnpt_dev.street_detail sd ON u.street_id = sd.id
    AND u.province_code = sd.province_code
    LEFT JOIN vnpt_dev.ward w ON u.ward_id = w.id
    AND w.district_id = u.district_id
    AND w.province_code = u.province_code
    LEFT JOIN vnpt_dev.district d ON u.district_id = d.id
    AND d.province_id = u.province_id
    AND d.province_code = u.province_code
    LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
    LEFT JOIN vnpt_dev.nation n ON u.nation_id = n.id
    LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
    LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
    LEFT JOIN vnpt_dev.order_service_receive osr2 ON osr2.subscription_id = s.id
    LEFT JOIN vnpt_dev.order_service_status oss ON oss.id = CAST(osr2.order_status AS int8)
    LEFT JOIN vnpt_dev.sme_progress sp ON oss.sme_progress_id = sp.id
WHERE s.deleted_flag = 1
    AND s.combo_plan_id NOTNULL
    AND s.confirm_status = 1
    AND (
        ''%1$s'' = ''ALL''
        OR s.employee_code = ''%1$s''
    )
    AND (
        %2$s = -1
        OR u.province_id = %2$s
    )
    AND (
        ''%10$s'' = ''ALL''
        OR u.customer_type = ''%10$s''
    )
    AND (
        %3$s = -2
        OR s.status = %3$s
    )
    AND (
        ''%7$s'' = ''-1''
				-- i_categoryCombo
        OR (
            SELECT count(1) > 0
            FROM UNNEST(string_to_array(s2.categories_id, '', '')) sc
            WHERE sc IN (
                    SELECT *
                    FROM UNNEST(string_to_array(''%7$s'', '', '')) ss
                )
        )
    )
    AND (
        %5$s = -1
        OR ''%8$s'' = ''-1''
				OR p2.combo_id = ANY(''{%8$s}''::int8[])
				--i_comboIds
    )
    AND (
        %6$s = -1
        OR ''%9$s'' = ''-1''
        OR s.combo_plan_id = ANY(''{%9$s}''::int8[])
				-- i_comboPlanIds
    )
    AND (
        %11$s = -1
        OR (s.created_source_migration = 1 and  %11$s = 1)
        OR (s.created_source_migration = 2 and  %11$s = 2)
        OR (s.created_source_migration = 3 and  %11$s = 3)
        OR (s.created_source_migration = 4 and  %11$s = 4)
				OR (%11$s = 0 and (s.created_source_migration = 0 OR s.created_source_migration IS NULL))
    )';
all_sub_query VARCHAR;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
table_bill_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill;
		CREATE TEMP TABLE tmp_table_bill AS
		SELECT b2.id,
				b2.status,
				b2.payment_date,
				b2.created_at,
				b2.billing_code,
				b2.subscriptions_id AS sub_id,
				0 AS isRenew
		FROM (
						SELECT max(b.id) as id,
								b.subscriptions_id AS sub_id
						FROM vnpt_dev.billings b
						WHERE b.created_by <> ''batch''
								or b.created_by is null
						GROUP BY b.subscriptions_id
				) mb
				JOIN vnpt_dev.billings b2 ON b2.id = mb.id
		UNION
		-- lấy thông tin hóa đơn gia hạn
		SELECT b2.id,
				b2.status,
				b2.payment_date,
				b2.created_at,
				b2.billing_code,
				b2.subscriptions_id AS sub_id,
				1 AS isRenew
		FROM (
						SELECT b.id,
								b.subscriptions_id AS sub_id
						FROM vnpt_dev.billings b
						WHERE b.created_by = ''batch''
				) mb
				JOIN vnpt_dev.billings b2 ON b2.id = mb.id
		';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_vnptpay;
		CREATE TEMP TABLE tmp_table_bill_vnptpay AS
		SELECT vpr_m.billId AS billId,
			vpr2.vnptpay_transaction_id AS transactionCode
		FROM (
					SELECT vpr.billing_id AS billId,
							max(vpr.id) AS id
					FROM vnpt_dev.vnpt_pay_response vpr
					GROUP BY vpr.billing_id
			) vpr_m
			JOIN vnpt_dev.vnpt_pay_response vpr2 ON vpr2.id = vpr_m.id';
-- tmp_table_bill_vnptpay ///////////////////////////////
table_bill_vnptpay_batch_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch;
		CREATE TEMP TABLE tmp_table_bill_vnptpay_batch AS
		SELECT b.subscriptions_id,
					CASE
							WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
							ELSE sum(bi2.amount_pre_tax)
					END AS preAmountTax,
					CASE
							WHEN sum(bi2.amount_after_tax) < 0 THEN 0
							ELSE sum(bi2.amount_after_tax)
					END AS afterAmountTax
		FROM vnpt_dev.billings b
				LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
		WHERE b.status = 2
				and (
						b.created_by <> ''batch''
						or b.created_by is null
				)
		GROUP BY b.subscriptions_id';
-- tmp_table_bill_vnptpay_batch2/////////////////////
table_bill_vnptpay_batch2_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
		CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
		SELECT b.id,
				CASE
						WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
						ELSE sum(bi2.amount_pre_tax)
				END AS preAmountTax,
				CASE
						WHEN sum(bi2.amount_after_tax) < 0 THEN 0
						ELSE sum(bi2.amount_after_tax)
				END AS afterAmountTax
		FROM vnpt_dev.billings b
				LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
		WHERE b.status = 2
				and b.created_by = ''batch''
		GROUP BY b.id';
-- tmp_table_bill_vnptpay_batch_t///////////////
table_bill_vnptpay_batch_t_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t;
		CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t AS
		SELECT b.subscriptions_id,
    CASE
        WHEN sum(bt.amount) < 0 THEN 0
        ELSE sum(bt.amount)
    END AS amountTax
FROM vnpt_dev.billings b
    LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
    LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
WHERE b.status = 2
    and (
        b.created_by <> ''batch''
        or b.created_by is null
    )
GROUP BY b.subscriptions_id';
-- tiền thuế gia hạn
-- bill_vnptpay_batch_t2_query //////////////////////////
bill_vnptpay_batch_t2_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch_t2;
		CREATE TEMP TABLE tmp_table_bill_vnptpay_batch_t2 AS
		SELECT b.id,
				CASE
						WHEN sum(bt.amount) < 0 THEN 0
						ELSE sum(bt.amount)
				END AS amountTax
		FROM vnpt_dev.billings b
				LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
				LEFT JOIN vnpt_dev.bill_tax bt ON bt.billing_item_id = bi2.id
		WHERE b.status = 2
				and b.created_by = ''batch''
		GROUP BY b.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy ///////////////
table_bill_promotion_xy_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_promotion_xy;
		CREATE TEMP TABLE tmp_table_bill_promotion_xy AS
		SELECT x.id AS id,
				CASE
						WHEN x.promotionAmount < 0 THEN 0
						ELSE x.promotionAmount
				END AS promotionAmount,
				CASE
						WHEN y.unitAmount < 0 THEN 0
						ELSE y.unitAmount
				END AS unitAmount
		FROM (
						SELECT q.id AS id,
								COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
						FROM (
										SELECT b.subscriptions_id AS id,
												sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
										FROM vnpt_dev.billings b
												LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
												LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
										WHERE bi.object_type <> 3
												and (
														b.created_by <> ''batch''
														or b.created_by is null
												)
										GROUP BY b.subscriptions_id
								) q
								JOIN (
										SELECT b.subscriptions_id AS id,
												sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
										FROM vnpt_dev.billings b
												LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
												LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
										WHERE bi.object_type <> 3
												and (
														b.created_by <> ''batch''
														or b.created_by is null
												)
										GROUP BY b.subscriptions_id
								) w ON q.id = w.id
				) x
				JOIN (
						SELECT b.subscriptions_id AS id,
								sum(COALESCE (bi.amount, 0)) AS unitAmount
						FROM vnpt_dev.billings b
								LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
						WHERE bi.object_type <> 3
								and (
										b.created_by <> ''batch''
										or b.created_by is null
								)
						GROUP BY b.subscriptions_id
				) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi đăng ký
-- tmp_table_bill_promotion_xy2 /////////////////
table_bill_promotion_xy2_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_promotion_xy2;
		CREATE TEMP TABLE tmp_table_bill_promotion_xy2 AS
		SELECT x.id AS id,
    CASE
        WHEN x.promotionAmount < 0 THEN 0
        ELSE x.promotionAmount
    END AS promotionAmount,
    CASE
        WHEN y.unitAmount < 0 THEN 0
        ELSE y.unitAmount
    END AS unitAmount
FROM (
        SELECT q.id AS id,
            COALESCE(q.privateAmount, 0) + COALESCE(w.totalAmount, 0) AS promotionAmount
        FROM (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bcp.amount_by_cash, 0)) + sum(COALESCE (bcp.amount_by_percent, 0)) AS privateAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_private bcp ON bcp.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) q
            JOIN (
                SELECT bi.billing_id AS id,
                    sum(COALESCE (bct.amount_by_cash, 0)) + sum(COALESCE (bct.amount_by_percent, 0)) AS totalAmount
                FROM vnpt_dev.billings b
                    LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
                    LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bi.id
                WHERE bi.object_type <> 3
                    and b.created_by = ''batch''
                GROUP BY bi.billing_id
            ) w ON q.id = w.id
    ) x
    JOIN (
        SELECT bi.billing_id AS id,
            sum(COALESCE (bi.amount, 0)) AS unitAmount
        FROM vnpt_dev.billings b
            LEFT JOIN vnpt_dev.bill_item bi ON b.id = bi.billing_id
        WHERE bi.object_type <> 3
            and b.created_by = ''batch''
        GROUP BY bi.billing_id
    ) y ON x.id = y.id';
-- đơn giá và tiền khuyến mãi gia hạn
BEGIN all_sub_query = FORMAT(
    tmp_all_sub_query,
    i_employee_code,
    i_province_id,
    i_status,
    i_category_service,
    i_service_id,
    i_pricing_id,
    i_category_combo,
    i_combo_ids,
    i_combo_plan_ids,
    i_customer_type,
    i_createdsource,
    i_pricing_ids
);
--raise 'all_sub_query: %',all_sub_query;
execute all_sub_query;
-- tmp_table_bill /////////////////////////////////////////////////////////////////////////////////////////////
execute table_bill_query;
EXECUTE table_bill_vnptpay_query;
EXECUTE table_bill_vnptpay_batch_query;
EXECUTE table_bill_vnptpay_batch2_query;
EXECUTE table_bill_vnptpay_batch_t_query;
EXECUTE bill_vnptpay_batch_t2_query;
EXECUTE table_bill_promotion_xy_query;
EXECUTE table_bill_promotion_xy2_query;
EXECUTE table_bill_promotion_xy2_query;
last_query = format(
    tmp_last_query,
    i_subscription_type,
    i_subscription_state,
    i_start_date,
    i_end_date,
    i_creator,
    i_createdsource,
    i_migrate_start_date,
    i_migrate_end_date,
    i_migrate_codes
);
raise notice 'Last query: %',
last_query;
return query execute last_query;
END $BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;