DROP SEQUENCE IF EXISTS sequence_actionHistoryType_code CASCADE;
CREATE SEQUENCE sequence_actionHistoryType_code INCREMENT 1 START 9000;

DROP TABLE IF EXISTS "vnpt_dev"."action_history_type";
CREATE TABLE "vnpt_dev"."action_history_type" (
  "id" bigserial NOT NULL,
  "group" varchar(50) DEFAULT 'CUSTOM',
  "subgroup" varchar(50),
  "code" int4 DEFAULT nextval('sequence_actionHistoryType_code'),
  "name" varchar(100) NOT NULL,
  "content" text,
  "created_by" int8,
  "predefined" bool DEFAULT false,
  PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX "index_actionHistoryType_code" ON "vnpt_dev"."action_history_type" USING btree ("code");
CREATE INDEX "index_actionHistoryType_createdBy" ON "vnpt_dev"."action_history_type" USING hash ("created_by");
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."code" IS 'Mã loại thao tác phát sinh lịch sử hoạt động (1xxx: Tài khoản, 4xxx: Khách hàng, 5xxx: Liên hệ, 9xxx: Tùy chỉnh)

Trong đó:
10xx: Tài khoản SME
12xx: Tài khoản DEV
14xx: Tài khoản ADMIN
16xx: Tài khoản AFFILIATE';
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."group" IS 'Tên nhóm loại thao tác';
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."subgroup" IS 'Tên subgroup loại thao tác';
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."name" IS 'Tên loại thao tác';
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."content" IS 'Nội dung mô tả cho loại thao tác';
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."created_by" IS 'ID người tạo loại thao tác (-1: Tạo mặc định bởi hệ thống)';
COMMENT ON COLUMN "vnpt_dev"."action_history_type"."predefined" IS 'Nguồn gốc thao tác (true: Thao tác phát sinh từ hệ thống, false: Thao tác thêm được bởi người dùng)';
COMMENT ON TABLE "vnpt_dev"."action_history_type" IS 'Bảng chứa danh sách các loại thao tác phát sinh nhật ký hoạt động';
COMMENT ON INDEX "vnpt_dev"."index_actionHistoryType_code" IS 'Mã loại thao tác là duy nhất';
COMMENT ON TABLE "vnpt_dev"."action_log" IS 'Lịch sử tác động liên quan service và pricing';

INSERT INTO "vnpt_dev"."action_history_type" ("group", subgroup, code, name, content, created_by, predefined) VALUES
    ('USER', 'SME', 1000, 'Tạo tài khoản', 'Tạo tài khoản thành công cho người dùng %s', -1, true),
    ('USER', 'SME', 1001, 'Thay đổi Email đăng nhập', 'Thay đổi Email đăng nhập cho khách hàng %s thành công', -1, true),
    ('USER', 'SME', 1002, 'Đổi mật khẩu', 'Đổi mật khẩu cho khách hàng %s thành công', -1, true),
    ('USER', 'SME', 1003, 'Kích hoạt tài khoản', 'Kích hoạt tài khoản thành công', -1, true),
    ('USER', 'SME', 1004, 'Gửi email kích hoạt', 'Gửi lại email kích hoạt thành công', -1, true),
    ('USER', 'SME', 1005, 'Tạo thuê bao dùng thật', 'Tạo thuê bao %s cho khách hàng %s thành công', -1, true),
    ('USER', 'SME', 1006, 'Hủy thuê bao dùng thử', 'Hủy thuê bao dùng thử %s', -1, true),
    ('USER', 'SME', 1007, 'Tạo thuê bao dùng thử', 'Tạo thuê bao dùng thử %s cho khách hàng %s thành công', -1, true),
    ('USER', 'SME', 1008, 'Hủy thuê bao', 'Hủy thuê bao %s', -1, true),
    ('USER', 'SME', 1009, 'Kích hoạt lại thuê bao', 'Kích hoạt lại thuê bao %s', -1, true),
    ('USER', 'SME', 1010, 'Sửa số lượng gói DV bổ sung', 'Sửa số lượng gói dịch vụ bổ sung %s', -1, true),
    ('USER', 'SME', 1011, 'Thêm DV bổ sung', 'Thêm dịch vụ bổ sung %s', -1, true),
    ('USER', 'SME', 1012, 'Bỏ DV bổ sung', 'Bỏ dịch vụ bổ sung %s', -1, true),
    ('USER', 'SME', 1013, 'Thêm KM tổng', 'Thêm khuyến mại tổng %s', -1, true),
    ('USER', 'SME', 1014, 'Gia hạn thuê bao', 'Gia hạn thuê bao %s', -1, true),
    ('USER', 'SME', 1015, 'Đổi gói dịch vụ', 'Đổi gói combo dịch vụ từ %s thành %s', -1, true),
    ('USER', 'ADMIN', 1400, 'Tạo tài khoản quản trị viên', 'Tạo mới tài khoản quản trị viên %s thành công', -1, true),
    ('USER', 'ADMIN', 1401, 'Chỉnh sửa tài khoản quản trị viên', 'Thông tin %s đã được cập nhật thành công', -1, true),
    ('USER', 'ADMIN', 1402, 'Bật tài khoản quản trị viên', 'Bật tài khoản quản trị viên %s thành công', -1, true),
    ('USER', 'ADMIN', 1403, 'Tắt tài khoản quản trị viên', 'Tắt tài khoản quản trị viên %s thành công', -1, true),
    ('USER', 'ADMIN', 1404, 'Thay đổi Email đăng nhập', 'Thay đổi Email đăng nhập của quản trị viên %s thành công', -1, true),
    ('USER', 'ADMIN', 1405, 'Gửi lại thông tin tài khoản', 'Gửi lại thông tin tài khoản thành công cho quản trị viên %s', -1, true),
    ('USER', 'ADMIN', 1406, 'Tạo phiếu', 'Tạo mới phiếu hỗ trợ %s cho khách hàng %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1600, 'Tạo mới tài khoản thành viên', 'Tạo mới tài khoản thành viên %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1601, 'Cập nhật tài khoản thành viên', 'Thông tin %s đã được cập nhật thành công', -1, true),
    ('USER', 'AFFILIATE', 1602, 'Đổi cấp trên thành viên', 'Đổi cấp trên cho thành viên %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1603, 'Bật tài khoản thành viên', 'Bật tài khoản thành viên %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1604, 'Tắt tài khoản thành viên', 'Tắt tài khoản thành viên %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1605, 'Quản trị viên phê duyệt tài khoản', 'Tài khoản thành viên %s được phê duyệt thành công', -1, true),
    ('USER', 'AFFILIATE', 1606, 'Quản trị viên từ chối tài khoản', 'Quản trị viên từ chối tài khoản thành viên với lý do %s', -1, true),
    ('USER', 'AFFILIATE', 1607, 'Quản trị viên yêu cầu cập nhật thông tin tài khoản', 'Quản trị viên yêu cầu cập nhật tài khoản thành viên với các thông tin %s', -1, true),
    ('USER', 'AFFILIATE', 1608, 'Gửi lại thông tin tài khoản', 'Gửi lại thông tin tài khoản thành công cho thành viên %s', -1, true),
    ('USER', 'AFFILIATE', 1609, 'Tạo link affiliate', 'Tạo mới link affiliate %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1610, 'Cập nhật link affiliate', 'Cập nhật link affiliate %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1611, 'Tạo hoa hồng', 'Tạo mới hoa hồng %s thành công ', -1, true),
    ('USER', 'AFFILIATE', 1612, 'Cập nhật hoa hồng', 'Cập nhật hoa hồng %s thành công', -1, true),
    ('USER', 'AFFILIATE', 1613, 'Thay đổi Email đăng nhập', 'Thay đổi Email đăng nhập thành công cho thành viên %s', -1, true),
    ('USER', 'AFFILIATE', 1614, 'Đổi mật khẩu', 'Đổi mật khẩu thành công cho thành viên %s', -1, true),
    ('ENTERPRISE', null, 4000, 'Tạo khách hàng tiềm năng', 'Tạo dữ liệu cho khách hàng %s thành công', -1, true),
    ('ENTERPRISE', null, 4001, 'Chỉnh sửa thông tin khách hàng', 'Thông tin %s đã được cập nhật thành công', -1, true),
    ('ENTERPRISE', null, 4002, 'Gọi điện', 'Gọi điện cho khách hàng thành công', -1, false),
    ('ENTERPRISE', null, 4003, 'Gửi email', 'Gửi email cho khách hàng  thành công', -1, false),
    ('ENTERPRISE', null, 4004, 'Gửi SMS', 'Gửi SMS cho khách hàng thành công', -1, false),
    ('ENTERPRISE', null, 4005, 'Hẹn gặp', 'Hẹn gặp khách hàng  thành công', -1, false),
    ('ENTERPRISE', null, 4006, 'Gửi báo giá', 'Gửi báo giá cho khách hàng thành công', -1, false),
    ('ENTERPRISE', null, 4007, 'Gửi khuyến mãi', 'Gửi khuyến mãi cho khách hàng  thành công', -1, false),
    ('ENTERPRISE', null, 4008, 'Khảo sát', 'Khảo sát khách hàng thành công', -1, false),
    ('ENTERPRISE', null, 4009, 'Tư vấn zalo', 'Tư vấn zalo cho khách hàng thành công', -1, false),
    ('ENTERPRISE', null, 4010, 'Hỗ trợ mua hàng', 'Hỗ trợ mua hàng cho khách hàng thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5000, 'Tạo liên hệ', 'Tạo liên hệ %s thành công', -1, true),
    ('CUSTOMER_CONTACT', null, 5001, 'Thay đổi thông tin', 'Thông tin liên hệ %s đã được cập nhật thành công', -1, true),
    ('CUSTOMER_CONTACT', null, 5002, 'Chuyển sang khách hàng tiềm năng', 'Chuyển %s sang khách hàng tiềm năng thành công', -1, true),
    ('CUSTOMER_CONTACT', null, 5003, 'Gọi điện', 'Gọi điện cho khách hàng thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5004, 'Gửi email', 'Gửi email cho khách hàng  thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5005, 'Gửi SMS', 'Gửi SMS cho khách hàng thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5006, 'Hẹn gặp', 'Hẹn gặp khách hàng  thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5007, 'Gửi báo giá', 'Gửi báo giá cho khách hàng thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5008, 'Gửi khuyến mãi', 'Gửi khuyến mãi cho khách hàng  thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5009, 'Khảo sát', 'Khảo sát khách hàng thành công', -1, false),
    ('CUSTOMER_CONTACT', null, 5010, 'Tư vấn zalo', 'Tư vấn zalo cho khách hàng thành công', -1, false);

SELECT pg_catalog.setval(pg_get_serial_sequence('action_history_type', 'id'), 9000);

ALTER TABLE "vnpt_dev"."action_history" DROP COLUMN IF EXISTS "action_type";
ALTER TABLE "vnpt_dev"."action_history" ADD COLUMN "action_type" int4;
CREATE INDEX "index_actionHistory_actionType" ON "vnpt_dev"."action_history" USING hash ("action_type");
COMMENT ON COLUMN "vnpt_dev"."action_history"."action_type" IS 'Loại hành động phát sinh log (theo bảng action_history_type)';

UPDATE vnpt_dev.action_history SET action_type = 1200 WHERE action_code = 'ENACT2';
UPDATE vnpt_dev.action_history SET action_type = 1200 WHERE action_code = 'ADACT9';
UPDATE vnpt_dev.action_history SET action_type = 1201 WHERE action_code = 'ENACT1';
UPDATE vnpt_dev.action_history SET action_type = 1000 WHERE action_code =  'UACT1';
UPDATE vnpt_dev.action_history SET action_type = 1001 WHERE action_code =  'UACT2';
UPDATE vnpt_dev.action_history SET action_type = 1002 WHERE action_code =  'UACT3';
UPDATE vnpt_dev.action_history SET action_type = 1003 WHERE action_code = 'UACT4';
UPDATE vnpt_dev.action_history SET action_type = 1004 WHERE action_code =  'UACT5';
UPDATE vnpt_dev.action_history SET action_type = 1005 WHERE action_code = 'UACT6';
UPDATE vnpt_dev.action_history SET action_type = 1006 WHERE action_code = 'UACT7';
UPDATE vnpt_dev.action_history SET action_type = 1007 WHERE action_code = 'UACT8';
UPDATE vnpt_dev.action_history SET action_type = 1008 WHERE action_code = 'UACT9';
UPDATE vnpt_dev.action_history SET action_type = 1009 WHERE action_code = 'UACT10';
UPDATE vnpt_dev.action_history SET action_type = 1010 WHERE action_code =  'UACT11';
UPDATE vnpt_dev.action_history SET action_type = 1011 WHERE action_code = 'UACT12';
UPDATE vnpt_dev.action_history SET action_type = 1012 WHERE action_code =  'UACT13';
UPDATE vnpt_dev.action_history SET action_type = 1013 WHERE action_code = 'UACT14';
UPDATE vnpt_dev.action_history SET action_type = 1014 WHERE action_code = 'UACT15';
UPDATE vnpt_dev.action_history SET action_type = 1015 WHERE action_code = 'UACT16';
UPDATE vnpt_dev.action_history SET action_type = 5000 WHERE action_code = 'CCACT1';
UPDATE vnpt_dev.action_history SET action_type = 5001 WHERE action_code = 'CCACT2';
UPDATE vnpt_dev.action_history SET action_type = 5002 WHERE action_code =  'CCACT3';
UPDATE vnpt_dev.action_history SET action_type = 1400 WHERE action_code = 'ADACT1';
UPDATE vnpt_dev.action_history SET action_type = 1401 WHERE action_code = 'ADACT2';
UPDATE vnpt_dev.action_history SET action_type = 1402 WHERE action_code = 'ADACT3';
UPDATE vnpt_dev.action_history SET action_type = 1403 WHERE action_code = 'ADACT4';
UPDATE vnpt_dev.action_history SET action_type = 1404 WHERE action_code = 'ADACT10';
UPDATE vnpt_dev.action_history SET action_type = 1405 WHERE action_code = 'ADACT5';
UPDATE vnpt_dev.action_history SET action_type = 1406 WHERE action_code = 'ADACT6';
UPDATE vnpt_dev.action_history SET action_type = 1600 WHERE action_code = 'AFFACT1';
UPDATE vnpt_dev.action_history SET action_type = 1601 WHERE action_code = 'AFFACT2';
UPDATE vnpt_dev.action_history SET action_type = 1602 WHERE action_code = 'AFFACT3';
UPDATE vnpt_dev.action_history SET action_type = 1603 WHERE action_code = 'AFFACT4';
UPDATE vnpt_dev.action_history SET action_type = 1604 WHERE action_code = 'AFFACT5';
UPDATE vnpt_dev.action_history SET action_type = 1605 WHERE action_code = 'AFFACT6';
UPDATE vnpt_dev.action_history SET action_type = 1606 WHERE action_code = 'AFFACT7';
UPDATE vnpt_dev.action_history SET action_type = 1607 WHERE action_code = 'AFFACT8';
UPDATE vnpt_dev.action_history SET action_type = 1608 WHERE action_code = 'AFFACT9';
UPDATE vnpt_dev.action_history SET action_type = 1609 WHERE action_code = 'AFFACT10';
UPDATE vnpt_dev.action_history SET action_type = 1610 WHERE action_code = 'AFFACT11';
UPDATE vnpt_dev.action_history SET action_type = 1611 WHERE action_code = 'AFFACT12';
UPDATE vnpt_dev.action_history SET action_type = 1612 WHERE action_code = 'AFFACT13';
UPDATE vnpt_dev.action_history SET action_type = 1613 WHERE action_code = 'AFFACT14';
UPDATE vnpt_dev.action_history SET action_type = 1614 WHERE action_code = 'AFFACT15';

-- Tạo bảng action_history_note
DROP TABLE IF EXISTS "vnpt_dev"."action_history_note";
CREATE TABLE "vnpt_dev"."action_history_note" (
  "id" bigserial NOT NULL,
  "title" varchar(100) NOT NULL,
  "content" varchar(300),
  "created_by" int8 NOT NULL,
  "created_at" timestamp NOT NULL,
  "modified_by" int8,
  "modified_at" timestamp,
  PRIMARY KEY ("id")
);
CREATE INDEX "index_actionHistoryNote_id" ON "vnpt_dev"."action_history_note" USING btree ("id" DESC NULLS LAST);
CREATE INDEX "index_actionHistoryNote_createdAt" ON "vnpt_dev"."action_history_note" USING btree ("created_at" DESC NULLS LAST);
CREATE INDEX "index_actionHistoryNote_modifiedAt" ON "vnpt_dev"."action_history_note" USING btree ("modified_at" DESC NULLS LAST);
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."title" IS 'Tiêu đề ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."content" IS 'Nội dung ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."created_by" IS 'ID người tạo ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."created_at" IS 'Thời gian tạo ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."modified_by" IS 'ID người cập nhật ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."modified_at" IS 'Thời gian cập nhật ghi chú';
COMMENT ON TABLE "vnpt_dev"."action_history_note" IS 'Bảng lưu thông tin ghi chú của nhật ký hoạt động';