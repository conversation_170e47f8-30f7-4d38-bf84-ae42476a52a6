-- <PERSON><PERSON><PERSON> bảng mapping_sme_employee
DROP TABLE IF EXISTS "vnpt_dev"."mapping_sme_employee";
CREATE TABLE "vnpt_dev"."mapping_sme_employee" (
  "id" bigserial NOT NULL,
  "sme_id" int8 NOT NULL,
  "employee_id" int8 NOT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "unique_mappingSmeEmployee" UNIQUE ("sme_id", "employee_id")
);
CREATE INDEX "index_mappingSmeEmployee_employeeId" ON "vnpt_dev"."mapping_sme_employee" USING btree ("employee_id");
COMMENT ON COLUMN "vnpt_dev"."mapping_sme_employee"."sme_id" IS 'ID của tài khoản admin doanh nghiệp';
COMMENT ON COLUMN "vnpt_dev"."mapping_sme_employee"."employee_id" IS 'ID của tài khoản nhân viên doanh nghiệp';
COMMENT ON TABLE "vnpt_dev"."mapping_sme_employee" IS 'Bảng lưu thông tin mapping giữa tài khoản admin doanh nghiệp và các nhân viên của doanh nghiệp đó';
COMMENT ON CONSTRAINT "unique_mappingSmeEmployee" ON "vnpt_dev"."mapping_sme_employee" IS 'Cặp thông tin (smeId, employeeId) là duy nhất';
INSERT INTO vnpt_dev.mapping_sme_employee (sme_id, employee_id)
(SELECT parent_id, id FROM vnpt_dev.users WHERE parent_id != -1 ORDER BY parent_id, id );