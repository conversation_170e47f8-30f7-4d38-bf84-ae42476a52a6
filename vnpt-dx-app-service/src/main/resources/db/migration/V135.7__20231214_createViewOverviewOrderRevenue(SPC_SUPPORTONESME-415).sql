DROP VIEW IF EXISTS vnpt_dev.overview_order_revenue;
CREATE VIEW vnpt_dev.overview_order_revenue AS
(
SELECT
    subDateAndId.*,
    bill.billing_code as billCode,
    CASE WHEN bill.isRenew = 0 THEN subDateAndId.registration_date ELSE
        CAST (bill.created_at AS Date)
        END as registrationDate,
    COALESCE (newSubsPromotionAndUnitAmount.unitAmount, renewSubsPromotionAndUnitAmount.unitAmount, 0) AS unitAmount
FROM
    (
        SELECT
            subscriptions.id AS id,
            CAST (subscriptions.created_at AS Date) AS registration_date
        FROM
            vnpt_dev.subscriptions
        WHERE
                subscriptions.deleted_flag = 1
          AND (subscriptions.created_source_migration is null or subscriptions.created_source_migration = 0)
          AND (
            subscriptions.pricing_id NOTNULL
                OR subscriptions.combo_plan_id NOTNULL
            )
          AND subscriptions.confirm_status = 1
    ) subDateAndId
        LEFT JOIN (
        -- lấy thông tin hóa đơn đăng ký mới
        SELECT
            b2.id,
            b2.status,
            b2.payment_date,
            b2.created_at,
            b2.billing_code,
            b2.subscriptions_id AS sub_id,
            0 AS isRenew
        FROM
            (
                SELECT
                    max(b.id) as id,
                    b.subscriptions_id AS sub_id
                FROM
                    vnpt_dev.billings b
                WHERE
                        b.created_by <> 'batch'
                   or b.created_by is null
                    AND (b.created_source_migration is null or b.created_source_migration = 0)
                GROUP BY
                    b.subscriptions_id
            ) newBill
                JOIN vnpt_dev.billings b2 ON b2.id = newBill.id
        UNION
        -- lấy thông tin hóa đơn gia hạn
        SELECT
            b2.id,
            b2.status,
            b2.payment_date,
            b2.created_at,
            b2.billing_code,
            b2.subscriptions_id AS sub_id,
            1 AS isRenew
        FROM
            (
                SELECT
                    b.id,
                    b.subscriptions_id AS sub_id
                FROM
                    vnpt_dev.billings b
                WHERE
                        b.created_by = 'batch'
                  AND (b.created_source_migration is null or b.created_source_migration = 0)
            ) renewBill
                JOIN vnpt_dev.billings b2 ON b2.id = renewBill.id
    ) bill on bill.sub_id = subDateAndId.id -- đơn giá và tiền khuyến mãi đăng ký
        LEFT JOIN (
        SELECT
            subsPromotionAmount.id AS id,
            CASE WHEN subsPromotionAmount.promotionAmount < 0 THEN 0 ELSE subsPromotionAmount.promotionAmount END AS promotionAmount,
            CASE WHEN subsUnitAmount.unitAmount < 0 THEN 0 ELSE subsUnitAmount.unitAmount END AS unitAmount
        FROM
            (
                SELECT
                    subsPrivateAmount.id AS id,
                    COALESCE(subsPrivateAmount.privateAmount, 0) + COALESCE(subsTotalAmount.totalAmount, 0) AS promotionAmount
                FROM
                    (
                        SELECT
                            b.subscriptions_id AS id,
                            sum(
                                    COALESCE (bill_coupon_private.amount_by_cash, 0)
                                ) + sum(
                                    COALESCE (bill_coupon_private.amount_by_percent, 0)
                                ) AS privateAmount
                        FROM
                            vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item ON b.id = bill_item.billing_id
                                LEFT JOIN vnpt_dev.bill_coupon_private ON bill_coupon_private.billing_item_id = bill_item.id
                        WHERE
                                bill_item.object_type <> 3
                          AND (b.created_source_migration is null or b.created_source_migration = 0)
                          and (
                                    b.created_by <> 'batch'
                                or b.created_by is null
                            )
                        GROUP BY
                            b.subscriptions_id
                    ) subsPrivateAmount
                        JOIN (
                        SELECT
                            b.subscriptions_id AS id,
                            sum(
                                    COALESCE (bct.amount_by_cash, 0)
                                ) + sum(
                                    COALESCE (bct.amount_by_percent, 0)
                                ) AS totalAmount
                        FROM
                            vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item ON b.id = bill_item.billing_id
                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bill_item.id
                        WHERE
                                bill_item.object_type <> 3
                          AND (b.created_source_migration is null or b.created_source_migration = 0)
                          and (
                                    b.created_by <> 'batch'
                                or b.created_by is null
                            )
                        GROUP BY
                            b.subscriptions_id
                    ) subsTotalAmount ON subsPrivateAmount.id = subsTotalAmount.id
            ) subsPromotionAmount
                JOIN (
                SELECT
                    b.subscriptions_id AS id,
                    sum(
                            COALESCE (bill_item.amount, 0)
                        ) AS unitAmount
                FROM
                    vnpt_dev.billings b
                        LEFT JOIN vnpt_dev.bill_item ON b.id = bill_item.billing_id
                WHERE
                        bill_item.object_type <> 3
                  AND (b.created_source_migration is null or b.created_source_migration = 0)
                  and (
                            b.created_by <> 'batch'
                        or b.created_by is null
                    )
                GROUP BY
                    b.subscriptions_id
            ) subsUnitAmount ON subsPromotionAmount.id = subsUnitAmount.id
    ) newSubsPromotionAndUnitAmount ON newSubsPromotionAndUnitAmount.id = subDateAndId.id
        and bill.isRenew = 0 -- đơn giá và tiền khuyến mãi gia hạn
        LEFT JOIN (
        SELECT
            subsPromotionAmount.id AS id,
            CASE WHEN subsPromotionAmount.promotionAmount < 0 THEN 0 ELSE subsPromotionAmount.promotionAmount END AS promotionAmount,
            CASE WHEN subsUnitAmount.unitAmount < 0 THEN 0 ELSE subsUnitAmount.unitAmount END AS unitAmount
        FROM
            (
                SELECT
                    subsPrivateAmount.id AS id,
                    COALESCE(subsPrivateAmount.privateAmount, 0) + COALESCE(subsTotalAmount.totalAmount, 0) AS promotionAmount
                FROM
                    (
                        SELECT
                            bill_item.billing_id AS id,
                            sum(
                                    COALESCE (bill_coupon_private.amount_by_cash, 0)
                                ) + sum(
                                    COALESCE (bill_coupon_private.amount_by_percent, 0)
                                ) AS privateAmount
                        FROM
                            vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item ON b.id = bill_item.billing_id
                                LEFT JOIN vnpt_dev.bill_coupon_private ON bill_coupon_private.billing_item_id = bill_item.id
                        WHERE
                                bill_item.object_type <> 3
                          and b.created_by = 'batch'
                          AND (b.created_source_migration is null or b.created_source_migration = 0)
                        GROUP BY
                            bill_item.billing_id
                    ) subsPrivateAmount
                        JOIN (
                        SELECT
                            bill_item.billing_id AS id,
                            sum(
                                    COALESCE (bct.amount_by_cash, 0)
                                ) + sum(
                                    COALESCE (bct.amount_by_percent, 0)
                                ) AS totalAmount
                        FROM
                            vnpt_dev.billings b
                                LEFT JOIN vnpt_dev.bill_item ON b.id = bill_item.billing_id
                                LEFT JOIN vnpt_dev.bill_coupon_total bct ON bct.billing_item_id = bill_item.id
                        WHERE
                                bill_item.object_type <> 3
                          and b.created_by = 'batch'
                          AND (b.created_source_migration is null or b.created_source_migration = 0)
                        GROUP BY
                            bill_item.billing_id
                    ) subsTotalAmount ON subsPrivateAmount.id = subsTotalAmount.id
            ) subsPromotionAmount
                JOIN (
                SELECT
                    bill_item.billing_id AS id,
                    sum(
                            COALESCE (bill_item.amount, 0)
                        ) AS unitAmount
                FROM
                    vnpt_dev.billings b
                        LEFT JOIN vnpt_dev.bill_item ON b.id = bill_item.billing_id
                WHERE
                        bill_item.object_type <> 3
                  and b.created_by = 'batch'
                  AND (b.created_source_migration is null or b.created_source_migration = 0)
                GROUP BY
                    bill_item.billing_id
            ) subsUnitAmount ON subsPromotionAmount.id = subsUnitAmount.id
    ) renewSubsPromotionAndUnitAmount ON renewSubsPromotionAndUnitAmount.id = bill.id
        and bill.isRenew = 1
)