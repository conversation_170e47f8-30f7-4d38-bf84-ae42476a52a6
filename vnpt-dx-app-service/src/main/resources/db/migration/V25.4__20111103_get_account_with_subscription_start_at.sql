CREATE OR REPLACE FUNCTION "vnpt_dev"."get_account_with_subscription_start_at"("i_accounttype" _varchar, "i_accountstatustype" _varchar, "i_subsstartedtimestart" varchar, "i_subsstartedtimeend" varchar)
  RETURNS TABLE("id" int8, "roleName" varchar, "total_trial" numeric, "total_active" numeric, "total_future" numeric, "total_cancel" numeric, "total_end" numeric, "Loại tài khoản" text, "Họ và Tên" varchar, "Tên công ty" varchar, "<PERSON><PERSON>" varchar, "<PERSON><PERSON><PERSON>" varchar, "<PERSON>ã số thuế" varchar, "<PERSON>ã BHXH" varchar, "Tên nguòi đại diện" varchar, "Điện thoại" varchar, "Địa chỉ" varchar, "Website" varchar, "status" int4, "Trạng thái tà<PERSON>" text, "<PERSON><PERSON> quyền" varchar, "<PERSON><PERSON><PERSON> tạo" timestamp, "<PERSON><PERSON><PERSON> cập nhật" timestamp, "TechId" varchar, "Tỉnh" varchar, "<PERSON><PERSON> phận" varchar, "<PERSON><PERSON>nh vực kinh doanh" varchar, "Quy mô doanh nghiệp" varchar, "Quận/Huyện" varchar, "Phố/Đường" varchar, "Quốc gia" varchar, "Phường/xã" varchar) AS $BODY$
declare
    baseQuery         varchar = 'with _u as (with _u1 as (select u.*
                                 from vnpt_dev.users u )
                    select u.*, r.name as "roleName"
                    from _u1 u
                             inner join vnpt_dev.users_roles ur on u.id = ur.user_id
                             inner join vnpt_dev.role r on ur.role_id = r.id
                    where u.parent_id = -1 and r.name = any(''%s'')),
          	total_subs AS (
	SELECT
		tmp.ID,
		tmp."roleName",
		SUM ( tmp.trial ) AS "total_trial",
		SUM ( tmp.active ) AS "total_active",
		SUM ( tmp.future ) AS "total_future",
		SUM ( tmp.cancel ) AS "total_cancel",
	SUM ( tmp.END ) AS "total_end"
FROM
	(
	SELECT
		u22.ID,
		u22."roleName",
		SUM ( CASE WHEN s.status = 1 THEN 1 ELSE 0 END ) AS "trial",
		SUM ( CASE WHEN s.status = 2 THEN 1 ELSE 0 END ) AS "active",
		SUM ( CASE WHEN s.status = 0 THEN 1 ELSE 0 END ) AS "future",
		SUM ( CASE WHEN s.status = 3 THEN 1 ELSE 0 END ) AS "cancel",
		SUM ( CASE WHEN s.status = 4 THEN 1 ELSE 0 END ) AS "end"
	FROM
		_u u22
		LEFT JOIN vnpt_dev.subscriptions s ON u22.ID = s.user_id where s.started_at >= timestamp ''%s''  and s.started_at < timestamp ''%s''
	GROUP BY
		u22.ID,
		u22."roleName",
		s.status,
		s.ID
	) tmp
GROUP BY
	tmp.ID,
	tmp."roleName"
	),
 s_full_info as (	 SELECT T
	.*,
CASE
		WHEN T."roleName" = ''ROLE_SME'' THEN
		''SME''
		WHEN T."roleName" = ''ROLE_DEVELOPER'' THEN
		''DEV''
		WHEN T."roleName" = ''ROLE_ADMIN'' THEN
		''ADMIN''
		WHEN T."roleName" = ''ROLE_SME_EMPLOYEE'' THEN
		''EMPLOYEE''
	END AS "Loại tài khoản",
	v_user.NAME AS "Họ và Tên",
CASE
		WHEN T."roleName" = ''ROLE_SME'' THEN
		v_user.company
		WHEN T."roleName" = ''ROLE_DEVELOPER'' THEN
		v_user.NAME
		WHEN T."roleName" = ''ROLE_ADMIN'' THEN
		v_user.NAME
		WHEN T."roleName" = ''ROLE_SME_EMPLOYEE'' THEN
		v_user.NAME
	END AS "Tên công ty",
	v_user.email,
	v_user.NAME,
	v_user.tin AS "Mã số thuế",
	v_user.social_insurance_number AS "Mã BHXH",
	v_user.rep_fullname AS "Tên nguòi đại diện",
	v_user.phone_number AS "Điện thoại",
	v_user.address AS "Địa chỉ lắp đặt",
	v_user.website AS "Website",
	v_user.status,
CASE

		WHEN v_user.status = 1 THEN
		''Hoạt động''
		WHEN v_user.status = 0 THEN
		''Không hoạt động''
	END AS "Trạng thái tài khoản",
	T."roleName" AS "Phân quyền",
	v_user.created_at AS "Ngày tạo",
	v_user.modified_at AS "Ngày cập nhật",
	v_user.tech_id AS "TechId",
	v_user.province_name           as "Tỉnh",
  v_user.department_name         as "Bộ phận",
  v_user.business_area_name      as "Lĩnh vực kinh doanh",
  v_user.business_area_size_name as "Quy mô doanh nghiệp",
  v_user.district_name           as "Quận/Huyện",
  v_user.street_name             as "Phố/Đường",
  v_user.nation_name             as "Quốc gia",
  v_user.ward_name               as "Phường/xã"
FROM
	total_subs
	T INNER JOIN vnpt_dev.report_view_user_full_info v_user ON v_user.ID = T.ID )

	select * from s_full_info where 0 = 1';
    declare fullQuery varchar;
begin

    fullQuery := FORMAT(baseQuery, i_accountType, i_subsStartedTimeStart, i_subsStartedTimeEnd);

    FOR i in 1 .. array_length(i_accountStatusType, 1)
        LOOP
            case i_accountStatusType[i]
                when 'SME_ACTIVE' then fullQuery := fullQuery ||
                                                    ' union select * from s_full_info t where t.total_active != 0 and t.total_trial = 0 and t.total_future = 0 and t.total_cancel = 0 and t.total_end = 0 ';
                when 'SME_TRIAL' then fullQuery := fullQuery ||
                                                   ' union select * from s_full_info t where t.total_active = 0 and t.total_trial > 0 ';
                when 'SME_ACTIVE' then fullQuery := fullQuery ||
                                                    ' union select * from s_full_info t where t.total_active = 0 and t.total_trial = 0 and t.total_future = 0 and t.total_cancel = 0 and t.total_end = 0  ';
                when 'SME_NON_SUB' then fullQuery := fullQuery ||
                                                     ' union select * from s_full_info t where t.total_active = 0 and t.total_trial = 0 and t.total_future = 0 and t.total_cancel = 0 and t.total_end = 0 ';
                when 'SME_NON_EXTEND' then fullQuery := fullQuery ||
                                                        ' union select * from s_full_info t where t.total_active = 0 and t.total_end > 0 ';
                when 'SME_EXPIRED' then fullQuery := fullQuery ||
                                                     ' union select * from s_full_info t where t.total_active = 0 and t.total_cancel > 0 ';
                when 'SME_HAS_LEFT' then fullQuery := fullQuery ||
                                                      ' union select * from s_full_info t where (t.total_cancel > 0 or t.total_end > 0 ) and t.total_active = 0 and t.total_trial = 0 and t.total_future = 0 ';
                --                 when 'SME_POTENTIAL' then
--                 when 'SME_BLOCK' then
--                 when 'ACTIVE' then
                when 'SEM_CURRENT' then fullQuery := fullQuery ||
                                                     ' union select * from s_full_info t where t.total_cancel > 0 or t.total_end > 0 or t.total_active > 0 or t.total_trial > 0 or t.total_future > 0 ';
                when 'INACTIVE' then fullQuery := fullQuery ||
                                                  ' union select * from s_full_info t where t.status = 0';
                when 'NONE' then fullQuery := fullQuery ||
                                              ' union select * from s_full_info t ';
                END case;
        END LOOP;

    raise notice 'Value: %', fullQuery;
    return query execute fullQuery;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000