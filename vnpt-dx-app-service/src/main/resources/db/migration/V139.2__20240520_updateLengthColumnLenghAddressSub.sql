drop view if exists report_view_subscription_combo_addon;
drop view if exists report_view_subscription_combo_addon_test;
DROP VIEW IF EXISTS report_view_subscription_pricing_namnd;
DROP VIEW IF EXISTS report_view_subscription_combo_namnd;
DROP VIEW IF EXISTS report_view_subscription_pricing_addon;
DROP VIEW IF EXISTS "vnpt_dev"."report_view_subscription_pricing_test";
DROP VIEW IF EXISTS "vnpt_dev"."report_view_subscription_service_new_customer";
DROP VIEW IF EXISTS "vnpt_dev"."report_view_subscription_service";
DROP VIEW IF EXISTS "vnpt_dev"."feature_view_subscription_combo_new";
DROP VIEW IF EXISTS "vnpt_dev"."feature_view_subscription_pricing_new";
DROP VIEW IF EXISTS "vnpt_dev"."report_view_subscription_pricing_addon_test";
DROP VIEW IF EXISTS report_view_subscription_pricing;
DROP VIEW IF EXISTS report_view_subscription_combo;

ALTER TABLE "vnpt_dev"."subscriptions"
ALTER COLUMN "address" TYPE varchar(500) COLLATE "pg_catalog"."default" using "address"::varchar(500);

	CREATE OR REPLACE VIEW report_view_subscription_combo_addon AS
select s.*,sa.addons_id,
       p.combo_name as pricing_name,co.combo_name  as service_name, co.id as c_service_id,
       cast(concat(co.id,'0001') as bigint) as service_unique_id, cast(concat(p.id,'0001') as bigint) as pricing_unique_id,
       p.payment_cycle as p_payment_cycle , p.cycle_type as p_cycle_type,
       p.price, 3 as subscription_type,
       CASE
           WHEN co.combo_owner IN (0, 1) THEN 'ON'
           WHEN co.combo_owner IN (2, 3) THEN 'OS'
           WHEN co.combo_owner IS NULL THEN 'OS'
           END AS onos,
       c.nation_id as c_nation_id,
       c.nation_name as c_nation_name,
       c.province_id as c_province_id,
       c.province_name as c_province_name,
       c.district_id as c_district_id,
       c.district_name as c_district_name,
       c.ward_id as c_ward_id,
       c.ward_name as c_ward_name,
       c.street_name as street_name,
       c.address as c_address,
       c.name as sme_name,
       c.email as c_email,
       c.phone_number as c_phone_number,
       c.tin as tin,
       bi.status as payment_status,
       bi.next_total_amount,
       bt.quantity as service_quantity,
       -1 as order_status,
       '' as order_status_name,
       s.status as c_status,
       CASE
           WHEN s.employee_code is not null THEN 1
           WHEN s.traffic_id is not null THEN 2
           WHEN s.portal_type = 1 THEN 3
           WHEN s.portal_type = 2 THEN 3
           WHEN s.portal_type = 3 THEN 0
           END AS source,
       co.user_id as provider_id,
       -1 as categories_id,
       co.categories_id as categories_ids,
       null as unit,
       null as transaction_code
from subscriptions s
         inner join subscription_addons sa on s.id = sa.subscription_id
         inner join combo_plan p on s.combo_plan_id = p.id
         left join combo co on p.combo_id = co.id
         left join report_view_customer c on s.user_id = c.id
         left join billings bi on bi.subscriptions_id  = s.id and bi.id in (select max(id) from billings group by subscriptions_id)
         left join bill_item bt on bt.billing_id = bi.id and bt.object_id = s.combo_plan_id and bt.object_type = 1
where s.deleted_flag = 1 and s.confirm_status = 1;

CREATE OR REPLACE VIEW "vnpt_dev"."report_view_subscription_combo_addon_test" AS
       SELECT s.id,
         s.service_id,
         bt.quantity,
         s.total_amount,
         s.status,
         s.deleted_flag,
         s.created_by,
         s.modified_by,
         s.created_at,
         s.modified_at,
         s.user_id,
         s.from_date,
         s.cancelled_time,
         s.pricing_id,
         s.sme_subscription_id,
         s.installed,
         s.expired_time,
         s.used_quantity,
         s.registed_by,
         s.sub_registration_id,
         s.started_at,
         s.start_charge_at,
         s.trial_day,
         s.reg_type,
         s.payment_method,
         s.confirm_status,
         s.current_cycle,
         s.phone_no,
         s.contact,
         s.address,
         s.sub_code,
         s.start_current_cycle,
         s.end_current_cycle,
         s.current_payment_date,
         s.dhsxkd_sub_code,
         s.next_payment_time,
         s.awaiting_cancel,
         s.pre_order,
         s.number_of_cycles,
         s.cycle_type,
         s.traffic_id,
         s.combo_plan_id,
         s.canceled_by,
         s.subscription_contract_id,
         s.called_trans,
         s.change_date,
         s.change_status,
         s.update_date,
         s.update_status,
         s.portal_type,
         s.message_setup,
         s.employee_code,
         s.pricing_multi_plan_id,
         s.number_of_cycles_default,
         s.refer_subscription,
         s.traffic_user,
         s.company_name_invoice,
         s.address_invoice,
         s.tax_invoice,
         s.is_call_masoffer,
         s.is_swap,
         s.started_at_swap,
         s.current_cycle_swap,
         s.reference_id,
         s.installed_time,
         s.give_away_main_sub_id,
         sa.addon_id,
         p.combo_name AS pricing_name,
         co.combo_name AS service_name,
         co.id AS c_service_id,
         concat(co.id, '0001')::bigint AS service_unique_id,
          concat(p.id, '0001')::bigint AS pricing_unique_id,
          p.payment_cycle AS p_payment_cycle,
         p.cycle_type AS p_cycle_type,
         p.price,
         3 AS subscription_type,
         CASE
             WHEN co.combo_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
             WHEN co.combo_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
             WHEN co.combo_owner IS NULL THEN 'OS'::text
             ELSE NULL::text
             END AS onos,
         c.nation_id AS c_nation_id,
         c.nation_name AS c_nation_name,
         c.province_id AS c_province_id,
         c.province_name AS c_province_name,
         c.district_id AS c_district_id,
         c.district_name AS c_district_name,
         c.ward_id AS c_ward_id,
         c.ward_name AS c_ward_name,
         c.street_name,
         c.address AS c_address,
         c.name AS sme_name,
         c.email AS c_email,
         c.phone_number AS c_phone_number,
         c.tin,
         bi.status AS payment_status,
         bi.next_total_amount,
         bt.quantity AS service_quantity,
         '-1'::integer AS order_status,
          ''::text AS order_status_name,
          s.status AS c_status,
         CASE
             WHEN s.employee_code IS NOT NULL THEN 1
             WHEN s.traffic_id IS NOT NULL THEN 2
             WHEN s.portal_type = 1 THEN 3
             WHEN s.portal_type = 2 THEN 3
             WHEN s.portal_type = 3 THEN 0
             ELSE NULL::integer
END AS source,
    co.user_id AS provider_id,
    '-1'::integer AS categories_id,
    co.categories_id AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code
   FROM vnpt_dev.subscriptions s
     JOIN vnpt_dev.report_view_bill_addon_test sa ON s.id = sa.subscription_id
     JOIN vnpt_dev.combo_plan p ON s.combo_plan_id = p.id
     LEFT JOIN vnpt_dev.combo co ON p.combo_id = co.id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id))
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_type = 2;


create view report_view_subscription_combo_namnd as
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.refer_subscription,
       p.combo_name AS pricing_name,
       co.combo_name AS service_name,
       co.id AS c_service_id,
       (concat(co.id, '0001'))::bigint AS service_unique_id,
        (concat(p.id, '0001'))::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       3 AS subscription_type,
       'COMBO'::text AS onos,
        c.nation_id AS c_nation_id,
       c.nation_name AS c_nation_name,
       c.province_id AS c_province_id,
       c.province_name AS c_province_name,
       c.district_id AS c_district_id,
       c.district_name AS c_district_name,
       c.ward_id AS c_ward_id,
       c.ward_name AS c_ward_name,
       c.street_name,
       c.address AS c_address,
       c.name AS sme_name,
       c.email AS c_email,
       c.phone_number AS c_phone_number,
       c.tin,
       bi.status AS payment_status,
       bi.next_total_amount,
       bt.quantity AS service_quantity,
       '-1'::integer AS order_status,
        ''::text AS order_status_name,
        s.status AS c_status,
       CASE
           WHEN (s.employee_code IS NOT NULL) THEN 1
           WHEN (s.traffic_id IS NOT NULL) THEN 2
           WHEN (s.portal_type = 1) THEN 3
           WHEN (s.portal_type = 2) THEN 3
           WHEN (s.portal_type = 3) THEN 0
           ELSE NULL::integer
END AS source,
    co.user_id AS provider_id,
    '-1'::integer AS categories_id,
    co.categories_id AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code
   FROM (((((vnpt_dev.subscriptions s
     JOIN vnpt_dev.combo_plan p ON ((s.combo_plan_id = p.id)))
     LEFT JOIN vnpt_dev.combo co ON ((p.combo_id = co.id)))
     LEFT JOIN vnpt_dev.report_view_customer c ON ((s.user_id = c.id)))
     LEFT JOIN vnpt_dev.billings bi ON (((bi.subscriptions_id = s.id) AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id)))))
     LEFT JOIN vnpt_dev.bill_item bt ON (((bt.billing_id = bi.id) AND (bt.object_id = s.combo_plan_id) AND (bt.object_type = 1))))
  WHERE ((s.deleted_flag = 1) AND (s.confirm_status = 1));

create view report_view_subscription_pricing_addon as
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.number_of_cycles_default,
       s.refer_subscription,
       s.traffic_user,
       p.pricing_name,
       se.service_name,
       se.id AS c_service_id,
       (concat(se.id, '0000'))::bigint AS service_unique_id,
        (concat(p.id, '0000'))::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 1
           WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 2
           WHEN (se.service_owner IS NULL) THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
            WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
            WHEN (se.service_owner IS NULL) THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN (ss.id IS NULL) THEN 0
            WHEN (sr.order_status IS NULL) THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN (ss.id IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sr.order_status IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sm."order" = 1) THEN 'Tiếp nhận đơn hàng'::text
            WHEN (sm."order" = 2) THEN 'Đang triển khai'::text
            WHEN (sm."order" = 3) THEN 'Hoàn thành'::text
            WHEN (sm."order" = 4) THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN (s.status)::integer
            WHEN (ss.id IS NULL) THEN 10
            WHEN (sr.order_status IS NULL) THEN 10
            WHEN (sm."order" = '-1'::integer) THEN '-1'::integer
            ELSE (concat('1', sm."order"))::integer
END AS c_status,
        CASE
            WHEN (s.employee_code IS NOT NULL) THEN 1
            WHEN (s.traffic_id IS NOT NULL) THEN 2
            WHEN (s.portal_type = 1) THEN 3
            WHEN (s.portal_type = 2) THEN 3
            WHEN (s.portal_type = 3) THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code
   FROM (((((((((vnpt_dev.subscriptions s
     JOIN vnpt_dev.pricing p ON (((s.pricing_id = p.id) AND (s.combo_plan_id IS NULL))))
     LEFT JOIN vnpt_dev.services se ON ((se.id = p.service_id)))
     LEFT JOIN vnpt_dev.report_view_customer c ON ((s.user_id = c.id)))
     LEFT JOIN vnpt_dev.billings bi ON (((bi.subscriptions_id = s.id) AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id)))))
     LEFT JOIN vnpt_dev.bill_item bt ON (((bt.billing_id = bi.id) AND (bt.object_id = s.pricing_id) AND (bt.object_type = 0))))
     LEFT JOIN vnpt_dev.order_service_receive sr ON ((sr.subscription_id = s.id)))
     LEFT JOIN vnpt_dev.order_service_status ss ON ((ss.id = (sr.order_status)::bigint)))
     LEFT JOIN vnpt_dev.sme_progress sm ON ((sm.id = ss.sme_progress_id)))
     LEFT JOIN vnpt_dev.units un ON ((un.id = p.unit_id)))
  WHERE ((s.deleted_flag = 1) AND (s.confirm_status = 1));


create view report_view_subscription_pricing_namnd as
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.refer_subscription,
       p.pricing_name,
       se.service_name,
       se.id AS c_service_id,
       (concat(se.id, '0000'))::bigint AS service_unique_id,
        (concat(p.id, '0000'))::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 1
           WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 2
           WHEN (se.service_owner IS NULL) THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
            WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
            WHEN (se.service_owner IS NULL) THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN (ss.id IS NULL) THEN 0
            WHEN (sr.order_status IS NULL) THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN (ss.id IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sr.order_status IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sm."order" = 1) THEN 'Tiếp nhận đơn hàng'::text
            WHEN (sm."order" = 2) THEN 'Đang triển khai'::text
            WHEN (sm."order" = 3) THEN 'Hoàn thành'::text
            WHEN (sm."order" = 4) THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN (s.status)::integer
            WHEN (ss.id IS NULL) THEN 10
            WHEN (sr.order_status IS NULL) THEN 10
            WHEN (sm."order" = '-1'::integer) THEN '-1'::integer
            ELSE (concat('1', sm."order"))::integer
END AS c_status,
        CASE
            WHEN (s.employee_code IS NOT NULL) THEN 1
            WHEN (s.traffic_id IS NOT NULL) THEN 2
            WHEN (s.portal_type = 1) THEN 3
            WHEN (s.portal_type = 2) THEN 3
            WHEN (s.portal_type = 3) THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code
   FROM (((((((((vnpt_dev.subscriptions s
     JOIN vnpt_dev.pricing p ON (((s.pricing_id = p.id) AND (s.combo_plan_id IS NULL))))
     LEFT JOIN vnpt_dev.services se ON ((se.id = p.service_id)))
     LEFT JOIN vnpt_dev.report_view_customer c ON ((s.user_id = c.id)))
     LEFT JOIN vnpt_dev.billings bi ON (((bi.subscriptions_id = s.id) AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id)))))
     LEFT JOIN vnpt_dev.bill_item bt ON (((bt.billing_id = bi.id) AND (bt.object_id = s.pricing_id) AND (bt.object_type = 0))))
     LEFT JOIN vnpt_dev.order_service_receive sr ON ((sr.subscription_id = s.id)))
     LEFT JOIN vnpt_dev.order_service_status ss ON ((ss.id = (sr.order_status)::bigint)))
     LEFT JOIN vnpt_dev.sme_progress sm ON ((sm.id = ss.sme_progress_id)))
     LEFT JOIN vnpt_dev.units un ON ((un.id = p.unit_id)))
  WHERE ((s.deleted_flag = 1) AND (s.confirm_status = 1));

	CREATE OR REPLACE VIEW "vnpt_dev"."report_view_subscription_pricing_test" AS
	       SELECT s.id,
            s.service_id,
            s.quantity,
            s.total_amount,
            s.status,
            s.deleted_flag,
            s.created_by,
            s.modified_by,
            s.created_at,
            s.modified_at,
            s.user_id,
            s.from_date,
            s.cancelled_time,
            s.pricing_id,
            s.sme_subscription_id,
            s.installed,
            s.expired_time,
            s.used_quantity,
            s.registed_by,
            s.sub_registration_id,
            s.started_at,
            s.start_charge_at,
            s.trial_day,
            s.reg_type,
            s.payment_method,
            s.confirm_status,
            s.current_cycle,
            s.phone_no,
            s.contact,
            s.address,
            s.sub_code,
            s.start_current_cycle,
            s.end_current_cycle,
            s.current_payment_date,
            s.dhsxkd_sub_code,
            s.next_payment_time,
            s.awaiting_cancel,
            s.pre_order,
            s.number_of_cycles,
            s.cycle_type,
            s.traffic_id,
            s.combo_plan_id,
            s.canceled_by,
            s.subscription_contract_id,
            s.called_trans,
            s.change_date,
            s.change_status,
            s.update_date,
            s.update_status,
            s.portal_type,
            s.message_setup,
            s.employee_code,
            s.pricing_multi_plan_id,
            s.number_of_cycles_default,
            s.refer_subscription,
            s.traffic_user,
            p.pricing_name,
            se.service_name,
            se.id AS c_service_id,
            concat(se.id, '0000')::bigint AS service_unique_id,
                 concat(p.id, '0000')::bigint AS pricing_unique_id,
                 COALESCE(pi.payment_cycle, p.payment_cycle::bigint) AS p_payment_cycle,
            COALESCE(pi.circle_type, p.cycle_type) AS p_cycle_type,
            p.price,
            CASE
                WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 1
                WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 2
                WHEN se.service_owner IS NULL THEN 2
                ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
            WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
            WHEN se.service_owner IS NULL THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN ss.id IS NULL THEN 0
            WHEN sr.order_status IS NULL THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN ss.id IS NULL THEN 'Ðặt hàng thành công'::text
            WHEN sr.order_status IS NULL THEN 'Ðặt hàng thành công'::text
            WHEN sm."order" = 1 THEN 'Tiếp nhận đơn hàng'::text
            WHEN sm."order" = 2 THEN 'Ðang triển khai'::text
            WHEN sm."order" = 3 THEN 'Hoàn thành'::text
            WHEN sm."order" = 4 THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN s.status::integer
            WHEN ss.id IS NULL THEN 10
            WHEN sr.order_status IS NULL THEN 10
            WHEN sm."order" = '-1'::integer THEN '-1'::integer
            ELSE concat('1', sm."order")::integer
END AS c_status,
        CASE
            WHEN s.employee_code IS NOT NULL THEN 1
            WHEN s.traffic_id IS NOT NULL THEN 2
            WHEN s.portal_type = 1 THEN 3
            WHEN s.portal_type = 2 THEN 3
            WHEN s.portal_type = 3 THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code
   FROM vnpt_dev.subscriptions s
     JOIN vnpt_dev.pricing p ON s.pricing_id = p.id AND s.combo_plan_id IS NULL
     LEFT JOIN vnpt_dev.pricing_multi_plan pi ON pi.id = s.pricing_multi_plan_id
     LEFT JOIN vnpt_dev.services se ON se.id = p.service_id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id))
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.pricing_id AND bt.object_type = 0
     LEFT JOIN vnpt_dev.order_service_receive sr ON sr.subscription_id = s.id
     LEFT JOIN vnpt_dev.order_service_status ss ON ss.id = sr.order_status::bigint
     LEFT JOIN vnpt_dev.sme_progress sm ON sm.id = ss.sme_progress_id
     LEFT JOIN vnpt_dev.units un ON un.id = p.unit_id
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;


CREATE VIEW "vnpt_dev"."report_view_subscription_service" AS
    SELECT su.id,
                 su.service_id,
                 su.quantity,
                 su.total_amount,
                 su.status,
                 su.deleted_flag,
                 su.created_by,
                 su.modified_by,
                 su.created_at,
                 su.modified_at,
                 su.user_id,
                 su.from_date,
                 su.cancelled_time,
                 su.pricing_id,
                 su.sme_subscription_id,
                 su.installed,
                 su.expired_time,
                 su.used_quantity,
                 su.registed_by,
                 su.sub_registration_id,
                 su.started_at,
                 su.start_charge_at,
                 su.trial_day,
                 su.reg_type,
                 su.payment_method,
                 su.confirm_status,
                 su.current_cycle,
                 su.phone_no,
                 su.contact,
                 su.address,
                 su.sub_code,
                 su.start_current_cycle,
                 su.end_current_cycle,
                 su.current_payment_date,
                 su.dhsxkd_sub_code,
                 su.next_payment_time,
                 su.awaiting_cancel,
                 su.pre_order,
                 su.number_of_cycles,
                 su.cycle_type,
                 su.traffic_id,
                 su.combo_plan_id,
                 su.canceled_by,
                 su.subscription_contract_id,
                 su.called_trans,
                 su.change_date,
                 su.change_status,
                 su.update_date,
                 su.update_status,
                 su.portal_type,
                 su.message_setup,
                 su.employee_code,
                 su.pricing_multi_plan_id,
                 su.refer_subscription,
                 sv.id AS "serviceId",
                 CASE
                     WHEN ((sv.service_owner = 0) OR (sv.service_owner = 1)) THEN 'ON'::text
                     ELSE 'OS'::text
                     END AS service_type
          FROM (vnpt_dev.subscriptions su
              JOIN vnpt_dev.services sv ON ((sv.id = su.service_id)));

-- 21 ================================

CREATE VIEW "vnpt_dev"."report_view_subscription_service_new_customer" AS
    SELECT su.id,
         su.service_id,
         su.quantity,
         su.total_amount,
         su.status,
         su.deleted_flag,
         su.created_by,
         su.modified_by,
         su.created_at,
         su.modified_at,
         su.user_id,
         su.from_date,
         su.cancelled_time,
         su.pricing_id,
         su.sme_subscription_id,
         su.installed,
         su.expired_time,
         su.used_quantity,
         su.registed_by,
         su.sub_registration_id,
         su.started_at,
         su.start_charge_at,
         su.trial_day,
         su.reg_type,
         su.payment_method,
         su.confirm_status,
         su.current_cycle,
         su.phone_no,
         su.contact,
         su.address,
         su.sub_code,
         su.start_current_cycle,
         su.end_current_cycle,
         su.current_payment_date,
         su.dhsxkd_sub_code,
         su.next_payment_time,
         su.awaiting_cancel,
         su.pre_order,
         su.number_of_cycles,
         su.cycle_type,
         su.traffic_id,
         su.combo_plan_id,
         su.canceled_by,
         su.subscription_contract_id,
         su.called_trans,
         su.change_date,
         su.change_status,
         su.update_date,
         su.update_status,
         su.portal_type,
         su.message_setup,
         su.employee_code,
         su.pricing_multi_plan_id,
         su.refer_subscription,
         sv.id AS "serviceId",
         CASE
             WHEN ((sv.service_owner = 0) OR (sv.service_owner = 1)) THEN 'ON'::text
             ELSE 'OS'::text
             END AS service_type
        FROM (( WITH _events AS (
        SELECT subscriptions.id,
             subscriptions.service_id,
             subscriptions.quantity,
             subscriptions.total_amount,
             subscriptions.status,
             subscriptions.deleted_flag,
             subscriptions.created_by,
             subscriptions.modified_by,
             subscriptions.created_at,
             subscriptions.modified_at,
             subscriptions.user_id,
             subscriptions.from_date,
             subscriptions.cancelled_time,
             subscriptions.pricing_id,
             subscriptions.sme_subscription_id,
             subscriptions.installed,
             subscriptions.expired_time,
             subscriptions.used_quantity,
             subscriptions.registed_by,
             subscriptions.sub_registration_id,
             subscriptions.started_at,
             subscriptions.start_charge_at,
             subscriptions.trial_day,
             subscriptions.reg_type,
             subscriptions.payment_method,
             subscriptions.confirm_status,
             subscriptions.current_cycle,
             subscriptions.phone_no,
             subscriptions.contact,
             subscriptions.address,
             subscriptions.sub_code,
             subscriptions.start_current_cycle,
             subscriptions.end_current_cycle,
             subscriptions.current_payment_date,
             subscriptions.dhsxkd_sub_code,
             subscriptions.next_payment_time,
             subscriptions.awaiting_cancel,
             subscriptions.pre_order,
             subscriptions.number_of_cycles,
             subscriptions.cycle_type,
             subscriptions.traffic_id,
             subscriptions.combo_plan_id,
             subscriptions.canceled_by,
             subscriptions.subscription_contract_id,
             subscriptions.called_trans,
             subscriptions.change_date,
             subscriptions.change_status,
             subscriptions.update_date,
             subscriptions.update_status,
             subscriptions.portal_type,
             subscriptions.message_setup,
             subscriptions.employee_code,
             subscriptions.pricing_multi_plan_id,
             subscriptions.refer_subscription,
             row_number() OVER (PARTITION BY subscriptions.user_id ORDER BY subscriptions.created_at) AS row_number
        FROM vnpt_dev.subscriptions
        )
          SELECT _events.id,
                 _events.service_id,
                 _events.quantity,
                 _events.total_amount,
                 _events.status,
                 _events.deleted_flag,
                 _events.created_by,
                 _events.modified_by,
                 _events.created_at,
                 _events.modified_at,
                 _events.user_id,
                 _events.from_date,
                 _events.cancelled_time,
                 _events.pricing_id,
                 _events.sme_subscription_id,
                 _events.installed,
                 _events.expired_time,
                 _events.used_quantity,
                 _events.registed_by,
                 _events.sub_registration_id,
                 _events.started_at,
                 _events.start_charge_at,
                 _events.trial_day,
                 _events.reg_type,
                 _events.payment_method,
                 _events.confirm_status,
                 _events.current_cycle,
                 _events.phone_no,
                 _events.contact,
                 _events.address,
                 _events.sub_code,
                 _events.start_current_cycle,
                 _events.end_current_cycle,
                 _events.current_payment_date,
                 _events.dhsxkd_sub_code,
                 _events.next_payment_time,
                 _events.awaiting_cancel,
                 _events.pre_order,
                 _events.number_of_cycles,
                 _events.cycle_type,
                 _events.traffic_id,
                 _events.combo_plan_id,
                 _events.canceled_by,
                 _events.subscription_contract_id,
                 _events.called_trans,
                 _events.change_date,
                 _events.change_status,
                 _events.update_date,
                 _events.update_status,
                 _events.portal_type,
                 _events.message_setup,
                 _events.employee_code,
                 _events.pricing_multi_plan_id,
                 _events.refer_subscription,
                 _events.row_number
          FROM _events
          WHERE (_events.row_number = 1)) su
        JOIN vnpt_dev.services sv ON ((sv.id = su.service_id)));
-- 23 ================================


CREATE OR REPLACE VIEW vnpt_dev.feature_view_subscription_combo_new AS
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.refer_subscription,
       p.combo_name AS pricing_name,
       co.combo_name AS service_name,
       co.id AS c_service_id,
       concat(co.id, '0001')::bigint AS service_unique_id,
        concat(p.id, '0001')::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       3 AS subscription_type,
       'COMBO'::text AS onos,
        c.nation_id AS c_nation_id,
       c.nation_name AS c_nation_name,
       c.province_id AS c_province_id,
       c.province_name AS c_province_name,
       c.district_id AS c_district_id,
       c.district_name AS c_district_name,
       c.ward_id AS c_ward_id,
       c.ward_name AS c_ward_name,
       c.street_name,
       c.address AS c_address,
       c.name AS sme_name,
       c.email AS c_email,
       c.phone_number AS c_phone_number,
       c.tin,
       bi.status AS payment_status,
       bi.next_total_amount,
       bt.quantity AS service_quantity,
       '-1'::integer AS order_status,
        ''::text AS order_status_name,
        s.status AS c_status,
       CASE
           WHEN s.employee_code IS NOT NULL THEN 1
           WHEN s.traffic_id IS NOT NULL THEN 2
           WHEN s.portal_type = 1 THEN 3
           WHEN s.portal_type = 2 THEN 3
           WHEN s.portal_type = 3 THEN 0
           ELSE NULL::integer
END AS source,
    co.user_id AS provider_id,
    '-1'::integer AS categories_id,
    co.categories_id AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    u.last_name,
    u.first_name,
    p.unit_id,
    p.price AS priceee,
    NULL::bigint AS pmp_id,
    NULL::bigint AS pmp_unit_id,
    NULL::integer AS pmp_circle_type,
    NULL::integer AS pmp_payment_cycle,
    NULL::integer AS pmp_pricing_plan,
    NULL::bigint AS pmp_price
   FROM vnpt_dev.subscriptions s
     LEFT JOIN ( SELECT users.id,
            users.last_name,
            users.first_name
           FROM vnpt_dev.users) u ON s.registed_by = u.id
     JOIN vnpt_dev.combo_plan p ON s.combo_plan_id = p.id
     LEFT JOIN vnpt_dev.combo co ON p.combo_id = co.id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN ( SELECT b1.id,
            b1.subscriptions_id,
            b1.status,
            b1.next_total_amount
           FROM vnpt_dev.billings b1
             JOIN ( SELECT max(billings.id) AS id
                   FROM vnpt_dev.billings
                  GROUP BY billings.subscriptions_id) b2 ON b1.id = b2.id) bi ON bi.subscriptions_id = s.id
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.combo_plan_id AND bt.object_type = 1
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;

	CREATE OR REPLACE VIEW vnpt_dev.feature_view_subscription_pricing_new AS
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.refer_subscription,
       p.pricing_name,
       se.service_name,
       se.id AS c_service_id,
       concat(se.id, '0000')::bigint AS service_unique_id,
        concat(p.id, '0000')::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 1
           WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 2
           WHEN se.service_owner IS NULL THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
            WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
            WHEN se.service_owner IS NULL THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN ss.id IS NULL THEN 0
            WHEN sr.order_status IS NULL THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN ss.id IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sr.order_status IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sm."order" = 1 THEN 'Tiếp nhận đơn hàng'::text
            WHEN sm."order" = 2 THEN 'Đang triển khai'::text
            WHEN sm."order" = 3 THEN 'Hoàn thành'::text
            WHEN sm."order" = 4 THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN s.status::integer
            WHEN ss.id IS NULL THEN 10
            WHEN sr.order_status IS NULL THEN 10
            WHEN sm."order" = '-1'::integer THEN '-1'::integer
            ELSE concat('1', sm."order")::integer
END AS c_status,
        CASE
            WHEN s.employee_code IS NOT NULL THEN 1
            WHEN s.traffic_id IS NOT NULL THEN 2
            WHEN s.portal_type = 1 THEN 3
            WHEN s.portal_type = 2 THEN 3
            WHEN s.portal_type = 3 THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code,
    u.last_name,
    u.first_name,
    p.unit_id,
    p.price AS priceee,
    pmp.id AS pmp_id,
    pmp.unit_id AS pmp_unit_id,
    pmp.circle_type AS pmp_circle_type,
    pmp.payment_cycle AS pmp_payment_cycle,
    pmp.pricing_plan AS pmp_pricing_plan,
    pmp.price AS pmp_price
   FROM vnpt_dev.subscriptions s
     LEFT JOIN ( SELECT users.id,
            users.last_name,
            users.first_name
           FROM vnpt_dev.users) u ON s.registed_by = u.id
     JOIN vnpt_dev.pricing p ON s.pricing_id = p.id AND s.combo_plan_id IS NULL
     LEFT JOIN vnpt_dev.services se ON se.id = p.service_id
     LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id AND s.pricing_multi_plan_id IS NOT NULL
     JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN ( SELECT b1.id,
            b1.subscriptions_id,
            b1.status,
            b1.next_total_amount
           FROM vnpt_dev.billings b1
             JOIN ( SELECT max(billings.id) AS id
                   FROM vnpt_dev.billings
                  GROUP BY billings.subscriptions_id) b2 ON b1.id = b2.id) bi ON bi.subscriptions_id = s.id
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.pricing_id AND bt.object_type = 0
     LEFT JOIN vnpt_dev.order_service_receive sr ON sr.subscription_id = s.id
     LEFT JOIN vnpt_dev.order_service_status ss ON ss.id = sr.order_status::bigint
     LEFT JOIN vnpt_dev.sme_progress sm ON sm.id = ss.sme_progress_id
     LEFT JOIN vnpt_dev.units un ON un.id = p.unit_id OR pmp.unit_id = un.id
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;
	CREATE OR REPLACE VIEW vnpt_dev.report_view_subscription_pricing_addon_test
AS SELECT s.id,
          s.service_id,
          s.quantity,
          s.total_amount,
          s.status,
          s.deleted_flag,
          s.created_by,
          s.modified_by,
          s.created_at,
          s.modified_at,
          s.user_id,
          s.from_date,
          s.cancelled_time,
          s.pricing_id,
          s.sme_subscription_id,
          s.installed,
          s.expired_time,
          s.used_quantity,
          s.registed_by,
          s.sub_registration_id,
          s.started_at,
          s.start_charge_at,
          s.trial_day,
          s.reg_type,
          s.payment_method,
          s.confirm_status,
          s.current_cycle,
          s.phone_no,
          s.contact,
          s.address,
          s.sub_code,
          s.start_current_cycle,
          s.end_current_cycle,
          s.current_payment_date,
          s.dhsxkd_sub_code,
          s.next_payment_time,
          s.awaiting_cancel,
          s.pre_order,
          s.number_of_cycles,
          s.cycle_type,
          s.traffic_id,
          s.combo_plan_id,
          s.canceled_by,
          s.subscription_contract_id,
          s.called_trans,
          s.change_date,
          s.change_status,
          s.update_date,
          s.update_status,
          s.portal_type,
          s.message_setup,
          s.employee_code,
          s.pricing_multi_plan_id,
          s.number_of_cycles_default,
          s.refer_subscription,
          s.traffic_user,
          s.company_name_invoice,
          s.address_invoice,
          s.tax_invoice,
          s.is_call_masoffer,
          s.is_swap,
          s.started_at_swap,
          s.current_cycle_swap,
          s.reference_id,
          s.installed_time,
          s.give_away_main_sub_id,
          sa.addons_id,
          p.pricing_name,
          se.service_name,
          se.id AS c_service_id,
          concat(se.id, '0000')::bigint AS service_unique_id,
                          concat(p.id, '0000')::bigint AS pricing_unique_id,
                          p.payment_cycle AS p_payment_cycle,
          p.cycle_type AS p_cycle_type,
          p.price,
          CASE
              WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 1
              WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 2
              WHEN se.service_owner IS NULL THEN 2
              ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
            WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
            WHEN se.service_owner IS NULL THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN ss.id IS NULL THEN 0
            WHEN sr.order_status IS NULL THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN ss.id IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sr.order_status IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sm."order" = 1 THEN 'Tiếp nhận đơn hàng'::text
            WHEN sm."order" = 2 THEN 'Đang triển khai'::text
            WHEN sm."order" = 3 THEN 'Hoàn thành'::text
            WHEN sm."order" = 4 THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN s.status::integer
            WHEN ss.id IS NULL THEN 10
            WHEN sr.order_status IS NULL THEN 10
            WHEN sm."order" = '-1'::integer THEN '-1'::integer
            ELSE concat('1', sm."order")::integer
END AS c_status,
        CASE
            WHEN s.employee_code IS NOT NULL THEN 1
            WHEN s.traffic_id IS NOT NULL THEN 2
            WHEN s.portal_type = 1 THEN 3
            WHEN s.portal_type = 2 THEN 3
            WHEN s.portal_type = 3 THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code
   FROM vnpt_dev.subscriptions s
     JOIN vnpt_dev.subscription_addons sa ON s.id = sa.subscription_id
     JOIN vnpt_dev.pricing p ON s.pricing_id = p.id AND s.combo_plan_id IS NULL
     LEFT JOIN vnpt_dev.services se ON se.id = p.service_id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id))
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_type = 2
     LEFT JOIN vnpt_dev.order_service_receive sr ON sr.subscription_id = s.id
     LEFT JOIN vnpt_dev.order_service_status ss ON ss.id = sr.order_status::bigint
     LEFT JOIN vnpt_dev.sme_progress sm ON sm.id = ss.sme_progress_id
     LEFT JOIN vnpt_dev.units un ON un.id = p.unit_id
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;

Create VIEW report_view_subscription_pricing as
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.number_of_cycles_default,
       s.refer_subscription,
       s.traffic_user,
       p.pricing_name,
       se.service_name,
       se.id AS c_service_id,
       (concat(se.id, '0000'))::bigint AS service_unique_id,
        (concat(p.id, '0000'))::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 1
           WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 2
           WHEN (se.service_owner IS NULL) THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
            WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
            WHEN (se.service_owner IS NULL) THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN (ss.id IS NULL) THEN 0
            WHEN (sr.order_status IS NULL) THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN (ss.id IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sr.order_status IS NULL) THEN 'Đặt hàng thành công'::text
            WHEN (sm."order" = 1) THEN 'Tiếp nhận đơn hàng'::text
            WHEN (sm."order" = 2) THEN 'Đang triển khai'::text
            WHEN (sm."order" = 3) THEN 'Hoàn thành'::text
            WHEN (sm."order" = 4) THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN (s.status)::integer
            WHEN (ss.id IS NULL) THEN 10
            WHEN (sr.order_status IS NULL) THEN 10
            WHEN (sm."order" = '-1'::integer) THEN '-1'::integer
            ELSE (concat('1', sm."order"))::integer
END AS c_status,
        CASE
            WHEN (s.employee_code IS NOT NULL) THEN 1
            WHEN (s.traffic_id IS NOT NULL) THEN 2
            WHEN (s.portal_type = 1) THEN 3
            WHEN (s.portal_type = 2) THEN 3
            WHEN (s.portal_type = 3) THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code
   FROM (((((((((vnpt_dev.subscriptions s
     JOIN vnpt_dev.pricing p ON (((s.pricing_id = p.id) AND (s.combo_plan_id IS NULL))))
     LEFT JOIN vnpt_dev.services se ON ((se.id = p.service_id)))
     LEFT JOIN vnpt_dev.report_view_customer c ON ((s.user_id = c.id)))
     LEFT JOIN vnpt_dev.billings bi ON (((bi.subscriptions_id = s.id) AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id)))))
     LEFT JOIN vnpt_dev.bill_item bt ON (((bt.billing_id = bi.id) AND (bt.object_id = s.pricing_id) AND (bt.object_type = 0))))
     LEFT JOIN vnpt_dev.order_service_receive sr ON ((sr.subscription_id = s.id)))
     LEFT JOIN vnpt_dev.order_service_status ss ON ((ss.id = (sr.order_status)::bigint)))
     LEFT JOIN vnpt_dev.sme_progress sm ON ((sm.id = ss.sme_progress_id)))
     LEFT JOIN vnpt_dev.units un ON ((un.id = p.unit_id)))
  WHERE ((s.deleted_flag = 1) AND (s.confirm_status = 1));

CREATE OR REPLACE VIEW "vnpt_dev"."report_view_subscription_pricing_addon_test" AS
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.number_of_cycles_default,
       s.refer_subscription,
       s.traffic_user,
       s.company_name_invoice,
       s.address_invoice,
       s.tax_invoice,
       s.is_call_masoffer,
       s.is_swap,
       s.started_at_swap,
       s.current_cycle_swap,
       s.reference_id,
       s.installed_time,
       s.give_away_main_sub_id,
       sa.addons_id,
       p.pricing_name,
       se.service_name,
       se.id AS c_service_id,
       concat(se.id, '0000')::bigint AS service_unique_id,
            concat(p.id, '0000')::bigint AS pricing_unique_id,
            p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 1
           WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 2
           WHEN se.service_owner IS NULL THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
            WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
            WHEN se.service_owner IS NULL THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN ss.id IS NULL THEN 0
            WHEN sr.order_status IS NULL THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN ss.id IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sr.order_status IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sm."order" = 1 THEN 'Tiếp nhận đơn hàng'::text
            WHEN sm."order" = 2 THEN 'Đang triển khai'::text
            WHEN sm."order" = 3 THEN 'Hoàn thành'::text
            WHEN sm."order" = 4 THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN s.status::integer
            WHEN ss.id IS NULL THEN 10
            WHEN sr.order_status IS NULL THEN 10
            WHEN sm."order" = '-1'::integer THEN '-1'::integer
            ELSE concat('1', sm."order")::integer
END AS c_status,
        CASE
            WHEN s.employee_code IS NOT NULL THEN 1
            WHEN s.traffic_id IS NOT NULL THEN 2
            WHEN s.portal_type = 1 THEN 3
            WHEN s.portal_type = 2 THEN 3
            WHEN s.portal_type = 3 THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code
   FROM vnpt_dev.subscriptions s
     JOIN vnpt_dev.subscription_addons sa ON s.id = sa.subscription_id
     JOIN vnpt_dev.pricing p ON s.pricing_id = p.id AND s.combo_plan_id IS NULL
     LEFT JOIN vnpt_dev.services se ON se.id = p.service_id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id))
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_type = 2
     LEFT JOIN vnpt_dev.order_service_receive sr ON sr.subscription_id = s.id
     LEFT JOIN vnpt_dev.order_service_status ss ON ss.id = sr.order_status::bigint
     LEFT JOIN vnpt_dev.sme_progress sm ON sm.id = ss.sme_progress_id
     LEFT JOIN vnpt_dev.units un ON un.id = p.unit_id
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_subscription_combo" AS
SELECT s.id,
s.service_id,
s.quantity,
s.total_amount,
s.status,
s.deleted_flag,
s.created_by,
s.modified_by,
s.created_at,
s.modified_at,
s.user_id,
s.from_date,
s.cancelled_time,
s.pricing_id,
s.sme_subscription_id,
s.installed,
s.expired_time,
s.used_quantity,
s.registed_by,
s.sub_registration_id,
s.started_at,
s.start_charge_at,
s.trial_day,
s.reg_type,
s.payment_method,
s.confirm_status,
s.current_cycle,
s.phone_no,
s.contact,
s.address,
s.sub_code,
s.start_current_cycle,
s.end_current_cycle,
s.end_current_cycle_new,
s.current_payment_date,
s.dhsxkd_sub_code,
s.next_payment_time,
s.awaiting_cancel,
s.pre_order,
s.number_of_cycles,
s.cycle_type,
s.traffic_id,
s.combo_plan_id,
s.canceled_by,
s.subscription_contract_id,
s.called_trans,
s.change_date,
s.change_status,
s.update_date,
s.update_status,
s.portal_type,
s.message_setup,
s.employee_code,
s.pricing_multi_plan_id,
s.number_of_cycles_default,
s.refer_subscription,
s.traffic_user,
p.combo_name AS pricing_name,
u.customer_type,
co.combo_name AS service_name,
co.id AS c_service_id,
concat(co.id, '0001')::bigint AS service_unique_id,
   concat(p.id, '0001')::bigint AS pricing_unique_id,
   p.payment_cycle AS p_payment_cycle,
p.cycle_type AS p_cycle_type,
p.price,
3 AS subscription_type,
CASE
  WHEN co.combo_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
  WHEN co.combo_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
  WHEN co.combo_owner IS NULL THEN 'OS'::text
  ELSE NULL::text
      END AS onos,
c.nation_id AS c_nation_id,
c.nation_name AS c_nation_name,
c.province_id AS c_province_id,
c.province_name AS c_province_name,
c.district_id AS c_district_id,
c.district_name AS c_district_name,
c.ward_id AS c_ward_id,
c.ward_name AS c_ward_name,
c.street_name,
c.address AS c_address,
c.name AS sme_name,
c.email AS c_email,
c.phone_number AS c_phone_number,
c.tin,
bi.status AS payment_status,
bi.next_total_amount,
bt.quantity AS service_quantity,
'-1'::integer AS order_status,
   ''::text AS order_status_name,
   s.status AS c_status,
CASE
  WHEN s.employee_code IS NOT NULL THEN 1
  WHEN s.traffic_id IS NOT NULL THEN 2
  WHEN s.portal_type = 1 THEN 3
  WHEN s.portal_type = 2 THEN 3
  WHEN s.portal_type = 3 THEN 0
  ELSE NULL::integer
END AS source,
    co.user_id AS provider_id,
    '-1'::integer AS categories_id,
    co.categories_id AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    1 AS is_one_time,
    p.has_renew
   FROM vnpt_dev.subscriptions s
     JOIN vnpt_dev.combo_plan p ON s.combo_plan_id = p.id
     LEFT JOIN vnpt_dev.users u ON u.id = s.user_id
     LEFT JOIN vnpt_dev.combo co ON p.combo_id = co.id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
           FROM vnpt_dev.billings
          GROUP BY billings.subscriptions_id))
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.combo_plan_id AND bt.object_type = 1
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;