-- <PERSON><PERSON> sung thông tin cycle_quantity_apply
ALTER TABLE "vnpt_dev"."bill_coupon_private" DROP COLUMN IF EXISTS "cycle_quantity_apply";
ALTER TABLE "vnpt_dev"."bill_coupon_private" ADD COLUMN "cycle_quantity_apply" int4 DEFAULT 1;
COMMENT ON COLUMN "vnpt_dev"."bill_coupon_private"."cycle_quantity_apply" IS 'Số chu kỳ khách hàng gia hạn thêm';

UPDATE "vnpt_dev"."bill_coupon_private"
SET cycle_quantity_apply = 1
WHERE "vnpt_dev"."bill_coupon_private"."cycle_quantity_apply" IS NULL;

ALTER TABLE "vnpt_dev"."bill_coupon_total" DROP COLUMN IF EXISTS "cycle_quantity_apply";
ALTER TABLE "vnpt_dev"."bill_coupon_total" ADD COLUMN "cycle_quantity_apply" int4 DEFAULT 1;
COMMENT ON COLUMN "vnpt_dev"."bill_coupon_total"."cycle_quantity_apply" IS 'Số chu kỳ khách hàng gia hạn thêm';

UPDATE "vnpt_dev"."bill_coupon_total"
SET cycle_quantity_apply = 1
WHERE "vnpt_dev"."bill_coupon_total"."cycle_quantity_apply" IS NULL;