alter table vnpt_dev.affiliate_link
add column if not exists created_code varchar(12) NULL;

comment on column "vnpt_dev"."affiliate_link"."created_code" IS 'Mã link khi tạo';

-- fill cột created_code = link_code cho c<PERSON>c bảng ghi cũ
update vnpt_dev.affiliate_link
set created_code = link_code
where created_code is null;

-- SPC_SUPPORTONESME-376:code giữ nguyên của vnpt_dev.feature_view_get_affiliate_link, l<PERSON>y thêm created_code trong affiliate_link

-- NEW vnpt_dev.feature_view_get_affiliate_link source

CREATE OR REPLACE VIEW vnpt_dev.feature_view_get_affiliate_link_new
AS SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
    service_draft_cte.service_name AS product_name
   FROM vnpt_dev.affiliate_link
     LEFT JOIN LATERAL ( SELECT services_draft.service_name
           FROM vnpt_dev.services_draft
          WHERE services_draft.service_id = affiliate_link.source_object_id AND services_draft.deleted_flag = 1
          ORDER BY services_draft.id DESC
         LIMIT 1) service_draft_cte ON true
  WHERE affiliate_link.source_type = 1 AND affiliate_link.source_object_type = 0 AND affiliate_link.deleted_flag = 1 AND service_draft_cte.service_name::text <> ''::text
UNION ALL
 SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
    combo.combo_name AS product_name
   FROM vnpt_dev.affiliate_link
     JOIN vnpt_dev.combo ON affiliate_link.source_object_id = combo.id AND affiliate_link.source_type = 1 AND affiliate_link.source_object_type = 1
  WHERE affiliate_link.deleted_flag = 1 AND combo.deleted_flag = 1
UNION ALL
 SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
    ''::character varying AS product_name
   FROM vnpt_dev.affiliate_link
  WHERE (affiliate_link.source_type = ANY (ARRAY[2, 3])) AND affiliate_link.deleted_flag = 1
UNION ALL
 SELECT affiliate_link.id,
    affiliate_link.link_code,
    affiliate_link.created_code,
    affiliate_link.source_type,
    affiliate_link.source_object_type,
    affiliate_link.source_object_id,
    affiliate_link.shorten_link,
    affiliate_link.distributed_link,
    affiliate_link.start_time,
    affiliate_link.end_time,
    affiliate_link.status,
    affiliate_link.created_by,
    affiliate_link.assigned_to,
    affiliate_link.created_at,
    affiliate_link.modified_at,
    affiliate_link.marketing_channel,
    affiliate_link.distributed_domain,
    affiliate_link.source_link,
    affiliate_link.link_config,
        CASE
            WHEN affiliate_link.source_object_type = 0 THEN service_draft_cte.service_name
            WHEN affiliate_link.source_object_type = 1 THEN combo.combo_name
            ELSE ''::character varying
        END AS product_name
   FROM vnpt_dev.affiliate_link
     LEFT JOIN vnpt_dev.pricing ON affiliate_link.source_object_id = pricing.id AND affiliate_link.source_object_type = 0 AND pricing.deleted_flag = 1
     LEFT JOIN LATERAL ( SELECT services_draft.service_name
           FROM vnpt_dev.services_draft
          WHERE services_draft.service_id = pricing.service_id AND services_draft.deleted_flag = 1
          ORDER BY services_draft.id DESC
         LIMIT 1) service_draft_cte ON true
     LEFT JOIN vnpt_dev.combo_plan ON affiliate_link.source_object_id = combo_plan.id AND affiliate_link.source_object_type = 1 AND combo_plan.deleted_flag = 1
     LEFT JOIN vnpt_dev.combo ON combo_plan.combo_id = combo.id AND combo.deleted_flag = 1
  WHERE affiliate_link.deleted_flag = 1 AND affiliate_link.source_type = 4;


 -- function lấy ra affiliate link id và created_code theo user role
  drop function if exists vnpt_dev.func_get_affiliate_link_id_and_created_code_by_role;

  CREATE OR REPLACE FUNCTION vnpt_dev.func_get_affiliate_link_id_and_created_code_by_role(rolename character varying, userid bigint)
   RETURNS TABLE(affiliate_link_id bigint, affiliate_created_code character varying)
   LANGUAGE plpgsql
  AS $function$
  begin
      --Admin tổng
      if roleName like 'FULL_ADMIN' then
          begin
              --Lấy ra tất cả affiliate_link
              return query (select id, created_code from vnpt_dev.affiliate_link where deleted_flag = 1);
          end;
      --Đại lý affiliate
      elseif roleName like 'ROLE_AFFILIATE_DAILY' then
          begin
              return query (
                  --Đệ quy để lấy ra đại lý và tất cả thành viên con
                  with recursive affiliate_users_all_level_cte as(
                      select user_id, affiliate_code from vnpt_dev.affiliate_users where user_id = userId
                      union all
                      select aff_users_child.user_id, aff_users_child.affiliate_code
                      from vnpt_dev.affiliate_users aff_users_child join affiliate_users_all_level_cte aff_users_parent
                          on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
                  )
                  --Lấy ra các affiliate được tạo bởi đại lý và tất cả thành viên con
                  select aff_link.id, aff_link.created_code
                  from vnpt_dev.affiliate_link aff_link join affiliate_users_all_level_cte aff_all_level
                      on aff_link.created_by = aff_all_level.user_id
                      and aff_link.deleted_flag = 1
              );
          end;
      --Cá nhân affiliate
      else
          begin
              --Lấy ra các affiliate link được tạo bởi
              return query (select id, created_code from vnpt_dev.affiliate_link where created_by = userId and deleted_flag = 1);
          end;
      end if;
  end $function$
  ;


update vnpt_dev.apis
set api_path = '/api/admin-portal/affiliate-link/delete'
where api_code = 'XOA_LINK_AFFILIATE' and method = 'DELETE';


drop view if exists vnpt_dev.view_aff_link_statistic;
create view vnpt_dev.view_aff_link_statistic as
    -- lấy lst link code đã đc dùng để order
    with lstOrderedLinkCode as (
        select distinct
            affiliate_commission_event.link_code
        from
            vnpt_dev.affiliate_commission_event
    ),
    -- tính lượt click cho từng link
    linkClickCount as (
        select
            affiliate_link_click_event.link_code,
            count(1) as click_count
        from
            vnpt_dev.affiliate_link_click_event
        group by affiliate_link_click_event.link_code
    ),
    -- tính lượt order cho từng link
    linkOrderCount as (
        select
            billings.affiliate_link_code as link_code,
            count(1) as order_count
        from
            vnpt_dev.billings
            join lstOrderedLinkCode on lstOrderedLinkCode.link_code = billings.affiliate_link_code
        where
            commission_note is not null
        group by
            billings.affiliate_link_code
    )
select
    linkClickCount.link_code,
    linkClickCount.click_count as click_count,
    linkOrderCount.order_count as order_count
from
    linkClickCount
    left join linkOrderCount on  linkClickCount.link_code = linkOrderCount.link_code;
