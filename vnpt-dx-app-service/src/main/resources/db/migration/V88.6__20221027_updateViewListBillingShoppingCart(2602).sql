DROP VIEW IF EXISTS "vnpt_dev"."feature_view_shopping_cart_get_list_billing";
CREATE OR REPLACE VIEW "vnpt_dev"."feature_view_shopping_cart_get_list_billing" AS
WITH billing_amount AS (
		SELECT
			bill_item.billing_id,
			SUM ( bill_item.amount_after_tax ) AS amount_after_tax
		FROM vnpt_dev.bill_item GROUP BY bill_item.billing_id
		),
	subscription_billing AS (
	SELECT
	  COALESCE( billings_1.cart_code, billings_1.billing_code ) AS billing_code,
		ARRAY_AGG ( subscriptions_1.ID ) AS sub_id_array
	FROM
		 vnpt_dev.billings billings_1
		LEFT JOIN vnpt_dev.subscriptions subscriptions_1 ON billings_1.subscriptions_id = subscriptions_1.ID
	GROUP BY
		COALESCE ( billings_1.cart_code, billings_1.billing_code )
	),
	payment_date_required_billing AS (
	SELECT
		ID,
		CASE
			WHEN pricing_type = 1 AND cycle_type = 0 THEN ( created_at :: DATE + payment_cycle * INTERVAL '1 day' ) :: DATE
			WHEN pricing_type = 1 AND cycle_type = 1 THEN ( created_at :: DATE + payment_cycle * INTERVAL '1 week' ) :: DATE
			WHEN pricing_type = 1 AND cycle_type = 2 THEN ( created_at :: DATE + payment_cycle * INTERVAL '1 month' ) :: DATE
			WHEN pricing_type = 1 AND cycle_type = 3 THEN ( created_at :: DATE + payment_cycle * INTERVAL '1 year' ) :: DATE
			ELSE created_at :: DATE
		END AS required_payment_date
	FROM
			(
				SELECT
					billings.ID AS ID,
					subscriptions.created_at :: DATE AS created_at,
					COALESCE ( combo_plan.payment_cycle, pricing.payment_cycle ) AS payment_cycle,
					COALESCE ( combo_plan.cycle_type, pricing.cycle_type ) AS cycle_type,
				CASE

						WHEN COALESCE ( combo_plan.combo_plan_type, pricing.pricing_type ) = 1 THEN
						1
						WHEN COALESCE ( combo_plan.combo_plan_type, pricing.pricing_type ) = 0 THEN
						0
					END AS pricing_type
				FROM
					vnpt_dev.billings
					LEFT JOIN vnpt_dev.subscriptions ON subscriptions.ID = billings.subscriptions_id
					LEFT JOIN vnpt_dev.pricing ON pricing.ID = subscriptions.pricing_id
					LEFT JOIN vnpt_dev.combo_plan ON combo_plan.ID = subscriptions.combo_plan_id
				) AS abc
	)
SELECT
    subscription_billing.sub_id_array,
    billings.id as billings_id,
    full_data.billing_code,
    full_data.customer_name,
    full_data.service_name,
    full_data.pricing_name,
    full_data.sub_code,
    full_data.amount_after_tax,
    full_data.current_payment_date,
    full_data.payment_date,
    full_data.required_payment_date,
    full_data.payment_status,
    full_data.customer_type,
    full_data.user_id,
    full_data.province_id,
    full_data.tax_code,
    full_data.personal_cert_number,
    full_data.is_combo,
    full_data.is_on,
    full_data.service_type,
    full_data.create_source,
    full_data.migrate_time,
    full_data.migrate_code,
    full_data.billing_province,
    full_data.is_cart,
    full_data.provider_id,
    full_data.created_at

FROM
    (
        SELECT
            DISTINCT COALESCE( billings.cart_code, billings.billing_code ) AS billing_code,
                     concat_ws ( ' ' :: TEXT, users.last_name, users.first_name ) AS customer_name,
                     COALESCE ( combo.combo_name, services.service_name ) AS service_name,
                     COALESCE ( combo_plan.combo_name, pricing.pricing_name ) AS pricing_name,
                     COALESCE ( subscriptions.cart_code, ( concat ( 'ID', to_char( subscriptions.ID, 'FM09999999' :: TEXT ) ) ) :: CHARACTER VARYING ) AS sub_code,
                     billing_amount.amount_after_tax,
                     billings.current_payment_date,
                     billings.payment_date,
                     payment_date_required_billing.required_payment_date,
                     billings.status AS payment_status,
                     users.customer_type,
                     users.ID AS user_id,
                     users.province_id,
                     users.tin AS tax_code,
                     users.rep_personal_cert_number AS personal_cert_number,
                     ( combo.ID IS NOT NULL ) AS is_combo,
                     ( COALESCE ( combo.combo_owner, services.service_owner ) = ANY ( ARRAY [ 0, 1 ] ) ) AS is_on,
                     CASE

                         WHEN ( combo.ID IS NOT NULL ) THEN
                             3
                         WHEN ( services.service_owner = ANY ( ARRAY [ 0, 1 ] ) ) THEN
                             0
                         WHEN ( services.service_owner = ANY ( ARRAY [ 2, 3 ] ) ) THEN
                             1 ELSE NULL :: INTEGER
					END AS service_type,
				CASE

						WHEN ( subscriptions.created_source_migration = 1 ) THEN 5
						WHEN ( subscriptions.traffic_id IS NOT NULL ) THEN 3
						WHEN ( subscriptions.employee_code IS NOT NULL ) THEN 2
						WHEN ( subscriptions.portal_type = ANY ( ARRAY [ 1, 2 ] ) ) THEN 4
						ELSE 1
					END AS create_source,
				subscriptions.migrate_time,
				subscriptions.migrate_code,
				billings.province_id AS billing_province,
				( billings.cart_code IS NOT NULL ) AS is_cart,
				billings.ID AS billings_id,
				COALESCE ( combo.user_id, services.user_id ) AS provider_id,
				billings.created_at
        FROM vnpt_dev.billings
            LEFT JOIN billing_amount ON billing_amount.billing_id = billings.ID
            LEFT JOIN payment_date_required_billing ON payment_date_required_billing.ID = billings.ID
            LEFT JOIN vnpt_dev.subscriptions ON subscriptions.ID = billings.subscriptions_id
            LEFT JOIN vnpt_dev.users ON users.ID = subscriptions.user_id
            LEFT JOIN vnpt_dev.pricing ON pricing.ID = subscriptions.pricing_id
            LEFT JOIN vnpt_dev.combo_plan ON combo_plan.ID = subscriptions.combo_plan_id
            LEFT JOIN vnpt_dev.services ON services.ID = pricing.service_id
            LEFT JOIN vnpt_dev.combo ON combo.ID = combo_plan.combo_id
        WHERE ( subscriptions.deleted_flag = 1 )
          AND ( users.deleted_flag = 1 )
          AND ( billings.status IN ( 0, 1, 2, 3, 4 ) )
    ) AS full_data
        LEFT JOIN subscription_billing ON subscription_billing.billing_code = full_data.billing_code
        left join vnpt_dev.billings on full_data.is_cart <> true and full_data.billings_id = billings.id