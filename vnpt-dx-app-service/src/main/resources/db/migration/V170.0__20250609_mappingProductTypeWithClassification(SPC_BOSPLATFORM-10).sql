-- migrate các dữ liệu hàng hóa mapping product_type mặc định theo classification
update vnpt_dev.services
set product_type =
    case
        when classification = 3 then 1 -- PHYSICAL(new) -> DEVICE (old)
        when classification = 2 then 3 -- SERVICE (new) -> SAAS(old)
        when classification = 1 then 3 -- DIGITAL (new) -> SAAS(old)
    end
where product_type is null;

update vnpt_dev.services_draft
set product_type =
    case
        when classification = 3 then 1 -- PHYSICAL(new) -> DEVICE (old)
        when classification = 2 then 3 -- SERVICE (new) -> SAAS(old)
        when classification = 1 then 3 -- DIGITAL (new) -> SAAS(old)
    end
where product_type is null;