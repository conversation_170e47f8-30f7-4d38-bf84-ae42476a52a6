CREATE OR REPLACE VIEW vnpt_dev.feature_view_field_force_get_list_entity_apply AS
SELECT DISTINCT entity_apply_layout.name,
                entity_apply_layout.status,
                entity_apply_layout.deleted_flag,
                entity_apply_layout.type,
                entity_apply_layout.category,
                entity_apply_layout.creation_layout_id,
                entity_apply_layout.id
FROM ( SELECT 2::smallint AS type,
               'PRICING'::text AS category,
               pricing_draft.id,
              pricing_draft.status,
              pricing_draft.deleted_flag,
              COALESCE(pricing_draft.creation_layout_id, ( SELECT custom_layout.id
                                                           FROM vnpt_dev.custom_layout
                                                           WHERE custom_layout.name::text = 'Tạo gói dịch vụ (Mặc định)'::text AND custom_layout.category = 'PRICING'::text)) AS creation_layout_id,
              concat(pricing_draft.pricing_name, ' - ', services.service_name) AS name
       FROM vnpt_dev.pricing_draft
                LEFT JOIN vnpt_dev.services ON services.id = pricing_draft.service_id
       WHERE pricing_draft.deleted_flag = 1 AND pricing_draft.pricing_name IS NOT NULL AND services.service_name IS NOT NULL
       UNION ALL
       SELECT 0::smallint AS type,
               'SERVICE'::text AS category,
               services.id,
              services.status,
              services.deleted_flag,
              COALESCE(services.creation_layout_id, ( SELECT custom_layout.id
                                                      FROM vnpt_dev.custom_layout
                                                      WHERE custom_layout.name::text = 'Tạo dịch vụ (Mặc định)'::text AND custom_layout.category = 'SERVICE'::text)) AS creation_layout_id,
              services.service_name AS name
       FROM vnpt_dev.services
       WHERE services.deleted_flag = 1 AND services.service_name IS NOT NULL AND services.service_name::text <> ''::text
       UNION ALL
       SELECT 1::smallint AS type,
           'COMBO'::text AS category,
           combo.id,
           combo.status,
           combo.deleted_flag,
           NULL::bigint AS creation_layout_id,
           combo.combo_name AS name
       FROM vnpt_dev.combo
       WHERE combo.deleted_flag = 1 AND combo.combo_name IS NOT NULL
       UNION ALL
       SELECT 3::smallint AS type,
           'COMBO_PLAN'::text AS category,
           combo_plan.id,
           combo_plan.status,
           combo_plan.deleted_flag,
           NULL::bigint AS creation_layout_id,
           concat(combo_plan.combo_name, ' - ', combo.combo_name) AS name
       FROM vnpt_dev.combo_plan
           LEFT JOIN vnpt_dev.combo ON combo.id = combo_plan.combo_id
       WHERE combo.deleted_flag = 1 AND combo.combo_name IS NOT NULL AND combo_plan.combo_name IS NOT NULL
       UNION ALL
       SELECT 4::smallint AS type,
           'ADDON'::text AS category,
           addons.id,
           addons.status,
           addons.deleted_flag,
           NULL::bigint AS creation_layout_id,
           addons.name
       FROM vnpt_dev.addons
       WHERE addons.deleted_flag = 1 AND addons.name IS NOT NULL) entity_apply_layout
ORDER BY entity_apply_layout.name;

-- Thêm trường lý do cập nhật bảng dịch vụ
ALTER TABLE "vnpt_dev"."services" ADD COLUMN IF NOT EXISTS "reason" varchar;

ALTER TABLE "vnpt_dev"."services_draft" ADD COLUMN IF NOT EXISTS "reason" varchar;

COMMENT ON COLUMN "vnpt_dev"."services"."reason" IS 'Lý do cập nhật dịch vụ';
COMMENT ON COLUMN "vnpt_dev"."services_draft"."reason" IS 'Lý do cập nhật dịch vụ';