DROP FUNCTION IF EXISTS "vnpt_dev"."get_combo_plan_latest_version_namnd";
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_combo_plan_latest_version_namnd"("i_endtime" varchar)
  RETURNS TABLE("id" int8, "combo_code" varchar, "combo_name" varchar, "description" varchar, "payment_cycle" int2, "number_of_cycles" int2, "cycle_type" int2, "combo_id" int8, "unit_id" int8, "currency_id" int8, "amount" float8, "setup_fee" float8, "free_quantity" int4, "estimate_quantity" int4, "created_at" timestamp, "created_by" int8, "modified_at" timestamp, "modified_by" int8, "status" int2, "deleted_flag" int2, "price" float8, "list_feature_id" varchar, "combo_order" int2, "recommended_status" int2, "update_reason" varchar, "approve" int2, "combo_draft_id" int8, "approve_time" timestamp, "sme_combo_id" int8, "trial_type" int2, "number_of_trial" int2, "combo_plan_type" int2, "has_change_price" int2, "has_change_quantity" int2, "has_refund" int2, "cancel_date" int2, "active_date" int4, "duration_type" int2, "combo_plan_draft_id" int8, "discount_value" float8, "discount_type" int2, "department_id" int8, "province_id" int8, "reason_reject" text, "is_change_now" int2, "is_update_now" int2, "customer_type_code" varchar) AS $BODY$

	DECLARE

BEGIN
	-- Routine body goes here...

RETURN QUERY
SELECT DISTINCT
ON (cp.combo_draft_id)
    cp.id,
    cp.combo_code,
    cp.combo_name,
    cp.description,
    cp.payment_cycle,
    cp.number_of_cycles,
    cp.cycle_type,
    cp.combo_id,
    cp.unit_id,
    cp.currency_id,
    cp.amount,
    cp.setup_fee,
    cp.free_quantity,
    cp.estimate_quantity,
    cp.created_at,
    cp.created_by,
    cp.modified_at,
    cp.modified_by,
    cp.status,
    cp.deleted_flag,
    cp.price,
    cp.list_feature_id,
    cp.combo_order,
    cp.recommended_status,
    cp.update_reason,
    cp.approve,
    cp.combo_draft_id,
    cp.approve_time,
    cp.sme_combo_id,
    cp.trial_type,
    cp.number_of_trial,
    cp.combo_plan_type,
    cp.has_change_price,
    cp.has_change_quantity,
    cp.has_refund,
    cp.cancel_date,
    cp.active_date,
    cp.duration_type,
    cp.combo_plan_draft_id,
    cp.discount_value,
    cp.discount_type,
    cp.department_id,
    cp.province_id,
    cp.reason_reject,
    cp.is_change_now,
    cp.is_update_now,
    cp.customer_type_code

FROM vnpt_dev.combo_plan cp
WHERE ((cp.deleted_flag = 1) AND (cp.approve = 1)) AND cp.approve_time:: date <= i_endtime:: date
ORDER BY cp.combo_draft_id, cp.approve_time DESC;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."get_all_version_pricing_namnd";
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_all_version_pricing_namnd"("i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("id" int8, "pricing_code" varchar, "pricing_name" varchar, "description" varchar, "payment_cycle" int2, "number_of_cycles" int2, "cycle_type" int2, "pricing_plan" int2, "service_id" int8, "unit_id" int8, "currency_id" int8, "amount" float8, "setup_fee" float8, "free_quantity" int4, "estimate_quantity" int4, "created_at" timestamp, "created_by" int8, "modified_at" timestamp, "modified_by" int8, "status" int2, "deleted_flag" int2, "price" float8, "list_feature_id" varchar, "pricing_order" int2, "recommended_status" int2, "update_reason" varchar, "approve" int2, "pricing_draft_id" int8, "approve_time" timestamp, "sme_pricing_id" int8, "trial_type" int2, "number_of_trial" int2, "pricing_type" int2, "has_change_price" int2, "has_change_quantity" int2, "has_refund" int2, "cancel_date" int2, "active_date" int4, "duration_type" int2, "department_id" int8, "province_id" int8, "update_subscription_date" int2, "change_pricing_date" int2, "is_change_now" int2, "is_update_now" int2) AS $BODY$

DECLARE

BEGIN

	-- Routine body goes here...

RETURN QUERY

SELECT

    p.id,

    p.pricing_code,

    p.pricing_name,

    p.description,

    p.payment_cycle,

    p.number_of_cycles,

    p.cycle_type,

    p.pricing_plan,

    p.service_id,

    p.unit_id,

    p.currency_id,

    p.amount,

    p.setup_fee,

    p.free_quantity,

    p.estimate_quantity,

    p.created_at,

    p.created_by,

    p.modified_at,

    p.modified_by,

    p.status,

    p.deleted_flag,

    p.price,

    p.list_feature_id,

    p.pricing_order,

    p.recommended_status,

    p.update_reason,

    p.approve,

    p.pricing_draft_id,

    p.approve_time,

    p.sme_pricing_id,

    p.trial_type,

    p.number_of_trial,

    p.pricing_type,

    p.has_change_price,

    p.has_change_quantity,

    p.has_refund,

    p.cancel_date,

    p.active_date,

    p.duration_type,

    p.department_id,

    p.province_id,

    p.update_subscription_date,

    p.change_pricing_date,

    p.is_change_now,

    p.is_update_now

FROM vnpt_dev.pricing p
         LEFT JOIN vnpt_dev.services se on p.service_id=se.id

WHERE (p.deleted_flag = 1) AND (p.created_at::date >= i_starttime::date) AND (p.created_at::date <= i_endtime::date);

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000