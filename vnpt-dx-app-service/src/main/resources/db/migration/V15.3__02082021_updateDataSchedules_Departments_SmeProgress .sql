INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('dhxskd', 'syncOrderServiceReceiveApiDHSXKD', NULL, '0 0 */4 * * ?', 'orderServiceReceive', 1, 'batch', NULL, 'batch', NULL);

UPDATE
	vnpt_dev.departments d
SET
	province_id = NULL
WHERE
	d.id = (
	SELECT
		d2.id
	FROM
		vnpt_dev.departments d2
	INNER JOIN vnpt_dev.users u ON
		u.department_id = d2.id
		AND u.email = '<EMAIL>' );

comment on column vnpt_dev.billings.amount_pre_tax is 'Số tiền trước thuế của hóa đơn';
comment on column vnpt_dev.billings.amount_after_tax is 'Số tiền sau thuế của hóa đơn';
comment on column vnpt_dev.billings.amount_tax is 'Số tiền thuế của hóa đơn';

comment on column vnpt_dev.order_service_receive.order_status is 'Id map theo bảng order_service_status';
comment on column vnpt_dev.order_service_receive.setup_status is 'Id map theo bảng order_service_status';
comment on column vnpt_dev.order_service_receive.payment_status is '0 - chờ thanh toán; 1 - đã thanh toán';



alter table vnpt_dev.sme_progress
add column "order"  int4;
comment on column vnpt_dev.sme_progress."order" is 'Sắp xếp thứ tự trên giao diện khi sort theo order_status của bảng order_service_receive';


UPDATE vnpt_dev.sme_progress
SET
"order"=1
WHERE id=1;


UPDATE vnpt_dev.sme_progress
SET
"order"=2
WHERE id=2;


UPDATE vnpt_dev.sme_progress
SET
"order"=4
WHERE id=3;


UPDATE vnpt_dev.sme_progress
SET
"order"=3
WHERE id=4;


UPDATE vnpt_dev.sme_progress
SET
"order"=-1
WHERE id=5;
