DROP VIEW IF EXISTS vnpt_dev.feature_view_get_all_sub_order;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_get_all_sub_order
AS
SELECT DISTINCT
    subscriptions.id AS sub_id,
    COALESCE(subscriptions.cart_code, concat('ID', to_char(subscriptions.id, 'FM09999999'::text))::character varying) AS sub_code,
    CASE
        WHEN users.customer_type::text = 'CN'::text THEN concat_ws(' '::text, users.last_name, users.first_name)::character varying
        ELSE users.name
        END AS customer_name,
    COALESCE(users.customer_type, 'KHDN'::character varying) AS customer_type,
    COALESCE(users.tin, users.rep_personal_cert_number) AS identityno,
    COALESCE(combo.provider, services.provider) AS provider,
    COALESCE(combo.user_id, services.user_id) AS provider_id,
    COALESCE(combo.id * 10 + 1, services.id * 10) AS service_id,
    subscriptions.status AS sub_status,
    combo.id IS NOT NULL AS is_combo,
    COALESCE(combo.on_os_type, services.on_os_type) = 0 AS is_on,
    users.tin AS tax_code,
    users.id AS user_id,
    users.rep_personal_cert_number AS personal_cert_number,
    users.email,
    users.phone_number AS phone,
    users.rep_fullname AS rep_name,
    users.province_id,
    subscriptions.created_at AS sub_created_at,
    billings.payment_date,
    COALESCE(billings.cart_code, billings.billing_code) AS billing_code,
    subscriptions.status,
    sme_progress.name AS status_name,
    sme_progress.id AS status_id,
    COALESCE(pricing_multi_plan.circle_type, COALESCE(combo_plan.cycle_type, pricing.cycle_type)) AS cycle_type,
    COALESCE(pricing_multi_plan.payment_cycle, COALESCE(combo_plan.payment_cycle::bigint, pricing.payment_cycle::bigint)) AS payment_cycle,
    COALESCE(subscriptions.total_amount_after_refund, subscriptions.total_amount, 0::double precision) AS total_amount,
    subscriptions.price_variant,
    subscriptions.variant_id,
    variant.variant_draft_id,
    variant.name as variant_name,
    CASE
        WHEN pricing.id IS NOT NULL THEN 1
        ELSE 0
        END AS quantity_pricing,
    CASE
        WHEN combo_plan.id IS NOT NULL THEN 1
        ELSE 0
        END AS quantity_combo_plan,
    subscriptions.quantity_variant,
    subscriptions.message,
    billings.province_id AS billing_province,
    CASE
        WHEN subscriptions.created_source_migration = 1 THEN 5
        WHEN subscriptions.traffic_id IS NOT NULL THEN 3
        WHEN subscriptions.employee_code IS NOT NULL THEN 2
        WHEN subscriptions.portal_type = ANY (ARRAY[1, 2]) THEN 4
        ELSE 1
        END AS create_source,
    COALESCE(combo_plan.id, pricing.id) AS pricing_id,
    COALESCE(combo_plan.combo_name, pricing.pricing_name) AS pricing_name,
    COALESCE(pricing_multi_plan.number_of_cycles, pricing.number_of_cycles) AS number_of_cycles,
    subscriptions.number_of_cycles AS number_of_cycles_reactive,
    subscriptions.created_at,
    COALESCE(services.service_owner, combo.combo_owner) AS service_owner,
    COALESCE(combo.combo_name, services.service_name) AS service_name,
    services.service_owner_partner,
    subscriptions.reactive_status,
    subscriptions.installed AS sub_installed,
    subscriptions.dhsxkd_sub_code,
    subscriptions.start_current_cycle,
    subscriptions.end_current_cycle,
    subscriptions.reactive_date,
    subscriptions.current_cycle,
    subscriptions.migrate_time,
    subscriptions.next_payment_time,
    subscriptions.migrate_code,
    subscriptions.is_only_service,
    subscriptions.is_buy_service,
    pricing.is_one_time,
    order_service_receive.transaction_code,
    billings.id AS bill_id,
    subscriptions.assignee_id AS sub_assignee_id,
    subscriptions.lst_assignees_id,
    CASE
        WHEN subscriptions.variant_id IS NOT NULL THEN 'VARIANT'::text
        WHEN subscriptions.service_id IS NOT NULL AND services.product_type = 1 THEN 'DEVICE'::text
        WHEN subscriptions.combo_plan_id IS NOT NULL THEN 'COMBO_PLAN'::text
        WHEN pricing.id IS NOT NULL THEN 'PRICING'::text
        ELSE NULL::text
        END AS product_type,
    services.classification,
    subscriptions.os_3rd_status,
    CASE
        WHEN date(subscriptions.created_at) >= COALESCE(variant.discount_start, '1970-01-01'::date) AND date(subscriptions.created_at) <= COALESCE(variant.discount_end, CURRENT_DATE) THEN
            CASE
                WHEN variant.id IS NOT NULL AND variant.discount_type = 1 THEN variant.total_price - variant.discount
                WHEN variant.id IS NOT NULL AND variant.discount_type = 2 THEN variant.total_price * (1::numeric - variant.discount / 100::numeric)
                ELSE variant.total_price
                END
        ELSE variant.total_price
        END AS variant_price,
    CASE
        WHEN combo_plan.id IS NOT NULL AND cbtax.has_tax = 1 THEN combo_plan.price * (1::double precision / (1::double precision + cbtax.percent / 100::double precision))
        WHEN combo_plan.id IS NOT NULL AND (cbtax.id IS NULL OR cbtax.has_tax = 0) THEN combo_plan.price
        ELSE 0::double precision
        END AS combo_plan_price,
    CASE
        WHEN pricing_multi_plan.id IS NOT NULL AND (pricing_multi_plan.pricing_plan = ANY (ARRAY[0, 1])) THEN
            CASE
                WHEN pricing_tax.has_tax = 1 THEN pricing_multi_plan.price * (1::double precision / (1::double precision + pricing_tax.percent / 100::double precision))
                WHEN pricing_tax.id IS NULL OR pricing_tax.has_tax = 0 THEN pricing_multi_plan.price
                ELSE NULL::double precision
                END
        WHEN pricing.id IS NOT NULL AND pricing.pricing_plan = 0 THEN
            CASE
                WHEN pricing_tax.has_tax = 1 THEN pricing.price * (1::double precision / (1::double precision + pricing_tax.percent / 100::double precision))
                WHEN pricing_tax.id IS NULL OR pricing_tax.has_tax = 0 THEN pricing.price
                ELSE NULL::double precision
                END
        ELSE 0::double precision
        END AS pricing_price,
    services.price AS service_price,
    services.tax AS service_tax,
    -- bundling --
    (metadata.package_id is not null) as is_bundling,
    metadata.package_id,
    metadata.solution_id,
    prodOrder.id as product_order_id
FROM vnpt_dev.subscriptions
     LEFT JOIN vnpt_dev.variant ON subscriptions.variant_id = variant.id
     LEFT JOIN vnpt_dev.billings ON subscriptions.id = billings.subscriptions_id
     LEFT JOIN vnpt_dev.users ON users.id = subscriptions.user_id
     LEFT JOIN vnpt_dev.pricing ON pricing.id = subscriptions.pricing_id
     LEFT JOIN vnpt_dev.pricing_tax ON pricing.id = pricing_tax.pricing_id
     LEFT JOIN vnpt_dev.combo_plan ON combo_plan.id = subscriptions.combo_plan_id
     LEFT JOIN vnpt_dev.combo_pricing ON combo_pricing.id_combo_plan = subscriptions.combo_plan_id
     LEFT JOIN vnpt_dev.combo_tax cbtax ON cbtax.id_combo_plan = combo_plan.id
     LEFT JOIN vnpt_dev.services ON services.id = subscriptions.service_id
     LEFT JOIN vnpt_dev.combo ON combo.id = combo_plan.combo_id
     LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.id = subscriptions.pricing_multi_plan_id
     LEFT JOIN vnpt_dev.order_service_receive ON order_service_receive.subscription_id = subscriptions.id AND subscriptions.service_id IS NOT NULL AND order_service_receive.service_id IS NOT NULL AND order_service_receive.combo_id IS NULL
     LEFT JOIN vnpt_dev.order_service_status ON order_service_status.id::character varying::text = order_service_receive.order_status::text
     LEFT JOIN vnpt_dev.sme_progress ON sme_progress.id = order_service_status.sme_progress_id
     LEFT JOIN vnpt_dev.subscription_metadata as metadata on subscriptions.id = metadata.subscription_id
     LEFT JOIN vnpt_dev.product_orders as prodOrder on prodOrder.id = metadata.product_order_id
WHERE
    subscriptions.deleted_flag = 1 AND ((billings.status = ANY (ARRAY[0, 1, 2, 3, 4])) OR billings.status IS NULL)
    AND (
        prodOrder.id is not null OR -- physical/bundling order
        (combo_pricing.object_type = ANY (ARRAY['DEVICE_VARIANT'::text, 'DEVICE_NO_VARIANT'::text])) -- combo chứa hàng hóa vật lý
    )
;
