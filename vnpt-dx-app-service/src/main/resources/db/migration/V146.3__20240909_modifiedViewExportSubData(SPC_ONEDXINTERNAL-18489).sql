DROP VIEW IF EXISTS "vnpt_dev"."report_view_sub_service_detail";
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_sub_service_detail" as (
  SELECT s.id,
         s.service_id,
         s.quantity,
         s.total_amount,
         s.status,
         s.deleted_flag,
         s.created_by,
         s.modified_by,
         s.created_at,
         s.modified_at,
         s.user_id,
         s.from_date,
         s.cancelled_time,
         s.pricing_id,
         s.sme_subscription_id,
         s.installed,
         s.expired_time,
         s.used_quantity,
         s.registed_by,
         s.sub_registration_id,
         s.started_at,
         s.start_charge_at,
         s.trial_day,
         s.reg_type,
         s.payment_method,
         s.confirm_status,
         s.current_cycle,
         s.phone_no,
         s.contact,
         s.address,
         s.sub_code,
         s.start_current_cycle,
         s.end_current_cycle,
         s.end_current_cycle_new,
         s.current_payment_date,
         s.dhsxkd_sub_code,
         s.next_payment_time,
         s.awaiting_cancel,
         s.pre_order,
         s.number_of_cycles,
         s.cycle_type,
         s.traffic_id,
         s.combo_plan_id,
         s.canceled_by,
         s.subscription_contract_id,
         s.called_trans,
         s.change_date,
         s.change_status,
         s.update_date,
         s.update_status,
         s.portal_type,
         s.message_setup,
         s.employee_code,
         s.pricing_multi_plan_id,
         s.number_of_cycles_default,
         s.refer_subscription,
         s.traffic_user,
         p.pricing_name,
         se.service_name,
         se.id AS c_service_id,
         (concat(se.id, '0000'))::bigint AS service_unique_id,
         (concat(p.id, '0000'))::bigint AS pricing_unique_id,
         p.payment_cycle AS p_payment_cycle,
         p.cycle_type AS p_cycle_type,
         p.price,
         CASE
             WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 1
             WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 2
             WHEN (se.service_owner IS NULL) THEN 2
             ELSE NULL::integer
         END AS subscription_type,
         CASE
             WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
             WHEN (se.service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
             WHEN (se.service_owner IS NULL) THEN 'OS'::text
             ELSE NULL::text
         END AS onos,
         bi.status AS payment_status,
         bi.next_total_amount,
         bi.customer_address,
         bt.quantity AS service_quantity,
         CASE
             WHEN (ss.id IS NULL) THEN 0
             WHEN (sr.order_status IS NULL) THEN 0
             ELSE sm."order"
         END AS order_status,
         CASE
             WHEN (ss.id IS NULL) THEN 'Đặt hàng thành công'::text
             WHEN (sr.order_status IS NULL) THEN 'Đặt hàng thành công'::text
             WHEN (sm."order" = 1) THEN 'Tiếp nhận đơn hàng'::text
             WHEN (sm."order" = 2) THEN 'Đang triển khai'::text
             WHEN (sm."order" = 3) THEN 'Hoàn thành'::text
             WHEN (sm."order" = 4) THEN 'Hủy đơn hàng'::text
             ELSE NULL::text
         END AS order_status_name,
         CASE
             WHEN (se.service_owner = ANY (ARRAY[0, 1])) THEN (s.status)::integer
             WHEN (ss.id IS NULL) THEN 10
             WHEN (sr.order_status IS NULL) THEN 10
             WHEN (sm."order" = '-1'::integer) THEN '-1'::integer
             ELSE (concat('1', sm."order"))::integer
         END AS c_status,
         CASE
             WHEN (s.employee_code IS NOT NULL) THEN 1
             WHEN (s.traffic_id IS NOT NULL) THEN 2
             WHEN (s.portal_type = 1) THEN 3
             WHEN (s.portal_type = 2) THEN 3
             WHEN (s.portal_type = 3) THEN 0
             ELSE NULL::integer
         END AS source,
         se.user_id AS provider_id,
         se.categories_id,
         ''::text AS categories_ids,
         un.name AS unit,
         sr.transaction_code,
         p.is_one_time,
         p.has_renew
  FROM vnpt_dev.subscriptions s
       JOIN vnpt_dev.pricing p ON s.pricing_id = p.id AND s.combo_plan_id IS NULL
       JOIN vnpt_dev.services se ON se.id = p.service_id
       LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id
       LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND bi.id IN (SELECT max(billings.id) AS max
                                                                                  FROM vnpt_dev.billings GROUP BY billings.subscriptions_id)
       LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.pricing_id AND bt.object_type = 0
       LEFT JOIN vnpt_dev.order_service_receive sr ON sr.subscription_id = s.id
       LEFT JOIN vnpt_dev.order_service_status ss ON ss.id = (sr.order_status)::bigint
       LEFT JOIN vnpt_dev.sme_progress sm ON sm.id = ss.sme_progress_id
       LEFT JOIN vnpt_dev.units un ON un.id = p.unit_id
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1
);

DROP VIEW IF EXISTS "vnpt_dev"."report_view_sub_combo_detail";
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_sub_combo_detail" AS (
    SELECT s.id,
           s.service_id,
           s.quantity,
           s.total_amount,
           s.status,
           s.deleted_flag,
           s.created_by,
           s.modified_by,
           s.created_at,
           s.modified_at,
           s.user_id,
           s.from_date,
           s.cancelled_time,
           s.pricing_id,
           s.sme_subscription_id,
           s.installed,
           s.expired_time,
           s.used_quantity,
           s.registed_by,
           s.sub_registration_id,
           s.started_at,
           s.start_charge_at,
           s.trial_day,
           s.reg_type,
           s.payment_method,
           s.confirm_status,
           s.current_cycle,
           s.phone_no,
           s.contact,
           s.address,
           s.sub_code,
           s.start_current_cycle,
           s.end_current_cycle,
           s.end_current_cycle_new,
           s.current_payment_date,
           s.dhsxkd_sub_code,
           s.next_payment_time,
           s.awaiting_cancel,
           s.pre_order,
           s.number_of_cycles,
           s.cycle_type,
           s.traffic_id,
           s.combo_plan_id,
           s.canceled_by,
           s.subscription_contract_id,
           s.called_trans,
           s.change_date,
           s.change_status,
           s.update_date,
           s.update_status,
           s.portal_type,
           s.message_setup,
           s.employee_code,
           s.pricing_multi_plan_id,
           s.number_of_cycles_default,
           s.refer_subscription,
           s.traffic_user,
           p.combo_name AS pricing_name,
           co.combo_name AS service_name,
           co.id AS c_service_id,
           concat(co.id, '0001')::bigint AS service_unique_id,
           concat(p.id, '0001')::bigint AS pricing_unique_id,
           p.payment_cycle AS p_payment_cycle,
           p.cycle_type AS p_cycle_type,
           p.price,
           3 AS subscription_type,
           CASE
               WHEN co.combo_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
               WHEN co.combo_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
               WHEN co.combo_owner IS NULL THEN 'OS'::text
               ELSE NULL::text
           END AS onos,
           bi.status AS payment_status,
           bi.next_total_amount,
           bi.customer_address,
           bt.quantity AS service_quantity,
           '-1'::integer AS order_status,
           ''::text AS order_status_name,
           s.status AS c_status,
           CASE
               WHEN s.employee_code IS NOT NULL THEN 1
               WHEN s.traffic_id IS NOT NULL THEN 2
               WHEN s.portal_type = 1 THEN 3
               WHEN s.portal_type = 2 THEN 3
               WHEN s.portal_type = 3 THEN 0
               ELSE NULL::integer
           END AS source,
           co.user_id AS provider_id,
           '-1'::integer AS categories_id,
           co.categories_id AS categories_ids,
           NULL::text AS unit,
           NULL::text AS transaction_code,
           1 AS is_one_time,
           p.has_renew
    FROM vnpt_dev.subscriptions s
             JOIN vnpt_dev.combo_plan p ON s.combo_plan_id = p.id
             JOIN vnpt_dev.combo co ON p.combo_id = co.id
             LEFT JOIN vnpt_dev.billings bi ON bi.subscriptions_id = s.id AND (bi.id IN ( SELECT max(billings.id) AS max
                                                                                          FROM vnpt_dev.billings GROUP BY billings.subscriptions_id))
             LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.combo_plan_id AND bt.object_type = 1
    WHERE s.deleted_flag = 1 AND s.confirm_status = 1
);

DROP VIEW IF EXISTS "vnpt_dev"."report_view_sub_service_group_detail";
CREATE OR REPLACE VIEW "vnpt_dev"."report_view_sub_service_group_detail" as (
    SELECT
        subscriptions.id,
        subscriptions.service_id,
        subscriptions.quantity,
        subscriptions.total_amount,
        subscriptions.status,
        subscriptions.deleted_flag,
        subscriptions.created_by,
        subscriptions.modified_by,
        subscriptions.created_at,
        subscriptions.modified_at,
        subscriptions.user_id,
        subscriptions.from_date,
        subscriptions.cancelled_time,
        subscriptions.pricing_id,
        subscriptions.sme_subscription_id,
        subscriptions.installed,
        subscriptions.expired_time,
        subscriptions.used_quantity,
        subscriptions.registed_by,
        subscriptions.sub_registration_id,
        subscriptions.started_at,
        subscriptions.start_charge_at,
        subscriptions.trial_day,
        subscriptions.reg_type,
        subscriptions.payment_method,
        subscriptions.confirm_status,
        subscriptions.current_cycle,
        subscriptions.phone_no,
        subscriptions.contact,
        subscriptions.address,
        subscriptions.sub_code,
        subscriptions.start_current_cycle,
        subscriptions.end_current_cycle,
        subscriptions.end_current_cycle_new,
        subscriptions.current_payment_date,
        subscriptions.dhsxkd_sub_code,
        subscriptions.next_payment_time,
        subscriptions.awaiting_cancel,
        subscriptions.pre_order,
        subscriptions.number_of_cycles,
        subscriptions.cycle_type,
        subscriptions.traffic_id,
        subscriptions.combo_plan_id,
        subscriptions.canceled_by,
        subscriptions.subscription_contract_id,
        subscriptions.called_trans,
        subscriptions.change_date,
        subscriptions.change_status,
        subscriptions.update_date,
        subscriptions.update_status,
        subscriptions.portal_type,
        subscriptions.message_setup,
        subscriptions.employee_code,
        subscriptions.pricing_multi_plan_id,
        subscriptions.number_of_cycles_default,
        subscriptions.refer_subscription,
        subscriptions.traffic_user,
        null::text AS pricing_name,
        (concat(services.service_name, ' (', service_group.name, ')'))::text AS service_name,
        services.id AS c_service_id,
        (concat(service_group.id, '0004'))::bigint AS service_unique_id,
        null::bigint AS pricing_unique_id,
        '-1'::integer AS p_payment_cycle,
        '-1'::integer AS p_cycle_type,
        null::bigint AS price,
        4 AS subscription_type,
        CASE
            WHEN (service_group.group_service_owner = ANY (ARRAY[0, 1])) THEN 'ON'::text
            WHEN (service_group.group_service_owner = ANY (ARRAY[2, 3])) THEN 'OS'::text
            WHEN (service_group.group_service_owner IS NULL) THEN 'OS'::text
            ELSE NULL::text
        END AS onos,
        billings.status AS payment_status,
        billings.next_total_amount,
        billings.customer_address,
        bill_item.quantity AS service_quantity,
        '-1'::integer AS order_status,
        ''::text AS order_status_name,
        subscriptions.status AS c_status,
        CASE
            WHEN (subscriptions.employee_code IS NOT NULL) THEN 1
            WHEN (subscriptions.traffic_id IS NOT NULL) THEN 2
            WHEN (subscriptions.portal_type = 1) THEN 3
            WHEN (subscriptions.portal_type = 2) THEN 3
            WHEN (subscriptions.portal_type = 3) THEN 0
            ELSE NULL::integer
        END AS source,
        service_group.user_id AS provider_id,
        '-1'::integer AS categories_id,
        REPLACE(REPLACE(service_group.categories_id::text, '{', ''), '}', '') AS categories_ids,
        NULL::text AS unit,
        NULL::text AS transaction_code,
        1 AS is_one_time,
        0 as has_renew
    FROM vnpt_dev.subscriptions
         JOIN vnpt_dev.service_group ON subscriptions.service_group_id = service_group.id
         LEFT JOIN vnpt_dev.services ON services.id = subscriptions.service_id
         LEFT JOIN vnpt_dev.billings ON billings.subscriptions_id = subscriptions.id AND (billings.id IN (
            SELECT max(billings.id) AS max
            FROM vnpt_dev.billings
            GROUP BY billings.subscriptions_id))
         LEFT JOIN vnpt_dev.bill_item ON bill_item.billing_id = billings.id AND bill_item.object_id = subscriptions.combo_plan_id AND bill_item.object_type = 1
    WHERE subscriptions.deleted_flag = 1 AND subscriptions.confirm_status = 1
);


DROP VIEW IF EXISTS "vnpt_dev"."feature_view_customer_detail";
CREATE OR REPLACE VIEW "vnpt_dev"."feature_view_customer_detail" AS (
    SELECT u.id,
           u.created_at,
           u.created_by,
           u.deleted_flag,
           u.modified_at,
           u.modified_by,
           u.status,
           u.account_expired,
           u.account_locked,
           u.activation_key,
           u.address,
           u.avatar,
           u.birthday,
           u.company,
           u.tin,
           u.cover_image,
           u.credentials_expired,
           u.email,
           u.enabled,
           u.first_name,
           u.gender,
           u.last_name,
           u.password,
           u.password_tmp,
           u.phone_number,
           u.user_name,
           u.website,
           u.district_id,
           u.nation_id,
           u.province_id,
           u.business_area_id,
           u.business_size_id,
           u.parent_id,
           u.user_code,
           u.business_email,
           u.name,
           u.description,
           u.department_id,
           u.sme_user_id,
           u.tech_id,
           u.create_type,
           u.customer_code,
           u.customer_type,
           u.province_code,
           u.ward_id,
           u.street_id,
           u.social_insurance_number,
           u.rep_fullname,
           u.rep_gender,
           u.rep_title,
           u.rep_birthday,
           u.rep_nation_id,
           u.rep_folk_id,
           u.rep_personal_cert_type_id,
           u.rep_personal_cert_number,
           u.rep_personal_cert_date,
           u.rep_personal_cert_place,
           u.rep_registered_place,
           u.rep_address,
           u.parent_status,
           u.employee_code,
           n.name AS nation_name,
           p.name AS province_name,
           d.name AS district_name,
           w.name AS ward_name,
           s.name AS street_name
    FROM vnpt_dev.users as u
         JOIN vnpt_dev.view_role_sme as vSME on vSME.user_id = u.id
         LEFT JOIN vnpt_dev.province p ON p.id = u.province_id
         LEFT JOIN vnpt_dev.district d ON d.id = u.district_id AND d.province_code::text = p.code::text
         LEFT JOIN vnpt_dev.ward w ON w.id = u.ward_id AND w.province_code::text = p.code::text
         LEFT JOIN vnpt_dev.street_detail s ON s.id = u.street_id AND s.province_code::text = p.code::text
         LEFT JOIN vnpt_dev.nation n ON n.id = u.nation_id
);


DROP VIEW IF EXISTS "vnpt_dev"."cal_amount_billing";
CREATE OR REPLACE VIEW "vnpt_dev"."cal_amount_billing" AS (
  WITH total_amount_billing AS (
      SELECT bill_item.billing_id,
               sum(bill_item.amount)                                      AS amount,
               sum(bill_item.amount_after)                                AS amount_after,
               sum(bill_item.amount_pre_tax)                              AS amount_pre_tax,
               sum(bill_item.amount_after_tax - bill_item.amount_pre_tax) AS tax,
               sum(bill_item.amount_after_tax)                            AS amount_after_tax
        FROM vnpt_dev.bill_item
        GROUP BY bill_item.billing_id)
  SELECT b.subscriptions_id AS subscription_id,
         bi.billing_id,
         bi.amount,
         bi.amount_after,
         bi.amount_pre_tax,
         bi.tax,
         bi.amount_after_tax
  FROM vnpt_dev.billings b
       LEFT JOIN total_amount_billing bi ON b.id = bi.billing_id
);