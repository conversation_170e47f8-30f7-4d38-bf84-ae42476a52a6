DROP VIEW IF EXISTS vnpt_dev.view_report_sub_bill_payment;
CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_bill_payment AS
SELECT
    bill.id,
    SUM(COALESCE(billItem.amount_pre_tax, 0))            AS pre_amount_tax,
    SUM(COALESCE(bill.total_amount_after_adjustment, 0)) AS after_amount_tax,
    SUM(COALESCE(biTax.amount, 0))                       AS amount_tax,
    SUM(COALESCE(billItem.amount, 0))                    AS unit_amount,
    (
            SUM(COALESCE(billCouponPrivate.amount_by_cash, 0)) +
            SUM(COALESCE(billCouponPrivate.amount_by_percent, 0)) +
            SUM(COALESCE(billCouponTotal.amount_by_cash, 0)) +
            SUM(COALESCE(billCouponTotal.amount_by_percent, 0))
        ) AS promotion_amount,
    SUM(
            CASE
                WHEN bill.action_type = 1 AND billItem.amount_after_tax <> 0
                    THEN ROUND(GREATEST(billItem.amount_incurred, 0)) * (ROUND(billItem.amount_pre_tax) / ROUND(billItem.amount_after_tax))
                ELSE 0
                END
        )	AS amount_change
FROM vnpt_dev.billings AS bill
         JOIN vnpt_dev.bill_item AS billItem ON billItem.billing_id = bill.id
         LEFT JOIN (
    select SUM(amount) as amount, billing_item_id
    from vnpt_dev.bill_tax
    GROUP BY billing_item_id
) as biTax on biTax.billing_item_id = billItem.id
         LEFT JOIN vnpt_dev.bill_coupon_private AS billCouponPrivate ON billCouponPrivate.billing_item_id = billItem.id
         LEFT JOIN vnpt_dev.bill_coupon_total AS billCouponTotal ON billItem.id = billCouponTotal.billing_item_id
WHERE billItem.object_type <> 3
GROUP BY bill.id