DROP VIEW IF EXISTS "vnpt_dev"."view_enterprise_user_classification_202310";

CREATE VIEW "vnpt_dev"."view_enterprise_user_classification_202310" AS
WITH sme_role AS (
    SELECT DISTINCT users_roles.user_id
    FROM vnpt_dev.users_roles
    WHERE users_roles.role_id = 3
     OR users_roles.role_id = 11
),
user_enterprise_mapping AS (
    SELECT DISTINCT ON (enterprise.id) enterprise.id AS enterprise_id,
                                                users.id      AS user_id
             FROM vnpt_dev.enterprise enterprise
                      JOIN vnpt_dev.users
                           ON COALESCE(users.deleted_flag, 1) = 1 AND COALESCE(enterprise.deleted_flag::integer, 1) = 1 AND
                              users.parent_id = '-1'::integer AND
                              COALESCE(users.customer_type, 'KHDN'::character varying)::text =
                              COALESCE(enterprise.customer_type, 'KHDN'::character varying)::text AND
                              ((COALESCE(users.customer_type, 'KHDN'::character varying)::text = ANY
                                (ARRAY ['KHDN'::character varying::text, 'HKD'::character varying::text])) AND
                               users.tin::text = enterprise.tin::text OR
                               users.customer_type::text = 'CN'::text AND (users.provider_type = ANY (ARRAY [0, 1, 2, 4])) AND -- 4: apple_id
                               users.email::text = enterprise.email::text OR
                               users.customer_type::text = 'CN'::text AND users.provider_type = 3 AND
                               users.phone_number::text = enterprise.phone::text)
                      JOIN sme_role ON sme_role.user_id = users.id
             ORDER BY enterprise.id, users.id DESC),
user_enterprise_mapping_without_duplicating AS (SELECT DISTINCT ON (user_enterprise_mapping.user_id) user_enterprise_mapping.enterprise_id,
                                                                                      user_enterprise_mapping.user_id
                                 FROM user_enterprise_mapping
                                 ORDER BY user_enterprise_mapping.user_id, user_enterprise_mapping.enterprise_id DESC
),
user_subs AS (
    SELECT subscriptions.user_id,
      count(subscriptions.id) FILTER (WHERE subscriptions.status = ANY (ARRAY [1, 2, 5]))          AS num_active_or_trial,
      count(subscriptions.id) FILTER (WHERE subscriptions.status = 3 OR subscriptions.status = 4)  AS num_cancel_or_finish,
      count(subscriptions.id) FILTER (WHERE subscriptions.status = ANY (ARRAY [0, 1, 2, 3, 4, 5])) AS num_subs
        FROM vnpt_dev.subscriptions
        WHERE subscriptions.deleted_flag = 1
         AND subscriptions.confirm_status = 1
        GROUP BY subscriptions.user_id
),
user_classification AS
    (SELECT users.id                                AS user_id,
                COALESCE(user_subs.num_subs, 0::bigint) AS num_subs,
                CASE
                    WHEN users.customer_code IS NOT NULL THEN 0
                    WHEN mmigration.customer_state = 0 THEN 0
                    WHEN user_subs.num_active_or_trial = 0 AND user_subs.num_cancel_or_finish > 0 AND
                         users.last_login::date IS NOT NULL AND users.last_login::date < (now() - '90 days'::interval)::date
                        THEN 1
                    WHEN user_subs.num_subs > 0 THEN 0
                    ELSE NULL::integer
                    END                                 AS customer_state
         FROM vnpt_dev.users
                  LEFT JOIN user_subs ON users.id = user_subs.user_id
                  LEFT JOIN vnpt_dev.import_migration mmigration ON mmigration.id = users.import_migration_id
         WHERE users.deleted_flag = 1
           AND users.parent_id = '-1'::integer
)
SELECT mmapping.enterprise_id,
        mclassification.user_id,
        mclassification.num_subs,
        mclassification.customer_state
    FROM user_classification mclassification
    JOIN user_enterprise_mapping_without_duplicating mmapping ON mmapping.user_id = mclassification.user_id;