ALTER TABLE vnpt_dev.subscriptions
ADD COLUMN IF NOT EXISTS end_current_cycle_new date;

COMMENT ON COLUMN vnpt_dev.subscriptions.end_current_cycle_new IS 'Ngày kết thúc chu kỳ sau gia hạn';

ALTER TABLE vnpt_dev.billings
ADD COLUMN IF NOT EXISTS current_cycle_from int2;
ALTER TABLE vnpt_dev.billings
ADD COLUMN IF NOT EXISTS current_cycle_to int2;

COMMENT ON COLUMN vnpt_dev.billings.current_cycle_from IS 'Chu kỳ hiện tại sau khi gia hạn từ';
COMMENT ON COLUMN vnpt_dev.billings.current_cycle_to IS 'Chu kỳ hiện tại sau khi gia hạn đến';