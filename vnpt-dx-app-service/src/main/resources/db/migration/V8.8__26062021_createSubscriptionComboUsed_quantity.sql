drop table if exists vnpt_dev.subscription_combo_used_quantity cascade;
create table vnpt_dev.subscription_combo_used_quantity
(
    id        bigserial not null,
	subscription_id int8,
    used_quantity int8,
    pricing_id   int8,
	PRIMARY KEY (id)
);
comment on table vnpt_dev.subscription_combo_used_quantity is ' Số lượng sử dụng của gói trong thuê bao';
comment on column vnpt_dev.subscription_combo_used_quantity.used_quantity is 'Số lượng sử dụng';


drop table if exists vnpt_dev.subscription_combo_coupon cascade;
create table vnpt_dev.subscription_combo_coupon
(
    id        bigserial not null,
	subscription_id int8,
    coupon_id int8,
    pricing_id   int8,
	PRIMARY KEY (id)
);