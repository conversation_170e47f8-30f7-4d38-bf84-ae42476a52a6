drop TABLE if exists vnpt_dev.traffic_activity_log;
CREATE TABLE "vnpt_dev"."traffic_activity_log" (
                                                   "id" bigserial NOT NULL,
                                                   "active_status" int2,
                                                   "created_at" timestamp(6),
                                                   "content" varchar(1000),
                                                   "created_by" int8,
                                                   "deleted_flag" int2,
                                                   "wallet_code" varchar(255),
                                                   PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "vnpt_dev"."traffic_activity_log"."active_status" IS 'Trạng thái hoạt động  0:<PERSON><PERSON> lư<PERSON>, 1:<PERSON><PERSON> sẻ <PERSON>ư<PERSON>, 2:<PERSON><PERSON><PERSON> thực ví';

COMMENT ON COLUMN "vnpt_dev"."traffic_activity_log"."created_at" IS 'Thời gian thao tác hoạt động ghi lại lịch sử';

COMMENT ON COLUMN "vnpt_dev"."traffic_activity_log"."content" IS 'Nội dung';

COMMENT ON COLUMN "vnpt_dev"."traffic_activity_log"."created_by" IS 'id người thực hiện';

COMMENT ON COLUMN "vnpt_dev"."traffic_activity_log"."deleted_flag" IS 'Cờ đánh dấu đã xóa 0: Đã xóa, 1: Chưa xóa';

COMMENT ON COLUMN "vnpt_dev"."traffic_activity_log"."wallet_code" IS 'thông tin ví';