insert into vnpt_dev.permission(id, name, code, parent_id,  priority)
select 
    (select max(id) from vnpt_dev.permission) + 1,
    '<PERSON><PERSON><PERSON> soát ngân hàng',
    'P_BCTK_DOI_SOAT_NGAN_HANG',
    (select id from vnpt_dev.permission where code = 'BAO_CAO_THONG_KE' limit 1),
    (select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (select 1 from vnpt_dev.permission where code = 'P_BCTK_DOI_SOAT_NGAN_HANG');

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
    (select max(id) from vnpt_dev.permission_portal) + 1,
    (select id from vnpt_dev.permission where code = 'P_BCTK_DOI_SOAT_NGAN_HANG'),
    1
where not exists (
    select 1
    from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
        on per.id = per_portal.permission_id and per.code = 'P_BCTK_DOI_SOAT_NGAN_HANG'
);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
SELECT
    (SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN' LIMIT 1),
    (SELECT id FROM vnpt_dev."permission" WHERE code = 'P_BCTK_DOI_SOAT_NGAN_HANG' LIMIT 1),
    1
WHERE NOT EXISTS (
    SELECT 1
    FROM vnpt_dev.roles_permissions
    WHERE
        role_id IN (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN') AND
        permission_id IN (SELECT id FROM vnpt_dev."permission" WHERE code = 'P_BCTK_DOI_SOAT_NGAN_HANG')
);
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;