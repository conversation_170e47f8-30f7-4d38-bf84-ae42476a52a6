drop view if exists "view_user_admin_of_sme";
create or replace view "view_user_admin_of_sme" as (
   select distinct users.id as user_id
        from vnpt_dev.users
            join vnpt_dev.users_roles as usrRole on users.id = usrRole.user_id
            join vnpt_dev.role on usrRole.role_id = role.id
        where
           users.parent_id = -1 and
           array[text(role.name)] && array[text('FULL_SME'), text('ROLE_SME'), text('ROLE_SME_EMPLOYEE')]
);
