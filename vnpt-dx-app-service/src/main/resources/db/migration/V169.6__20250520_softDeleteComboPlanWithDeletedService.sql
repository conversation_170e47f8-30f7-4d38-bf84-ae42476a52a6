WITH deleted_combo_plan AS (
    SELECT combo_plan.combo_plan_draft_id AS draft_id, combo_plan.id AS comboPlanId
    FROM vnpt_dev.combo_pricing
        JOIN vnpt_dev.combo_plan ON COMBO_PLAN.id = combo_pricing.id_combo_plan
        JOIN vnpt_dev.pricing ON combo_pricing.object_id = pricing.id AND combo_pricing.object_type = 'PRICING'
        JOIN vnpt_dev.services ON pricing.service_id = services.id
    WHERE services.deleted_flag = 0
        AND services.product_type = 1
    UNION
    SELECT combo_plan.combo_plan_draft_id AS draft_id, combo_plan.id AS comboPlanId
    FROM vnpt_dev.combo_pricing
        JOIN vnpt_dev.combo_plan ON combo_plan.id = combo_pricing.id_combo_plan
        JOIN vnpt_dev.services ON combo_pricing.object_id = services.id AND combo_pricing.object_type = 'DEVICE_NO_VARIANT'
    WHERE services.deleted_flag = 0
        AND services.product_type = 1)
UPDATE vnpt_dev.combo_plan
SET deleted_flag = 0
FROM deleted_combo_plan
WHERE combo_plan.id = deleted_combo_plan.comboPlanId;