drop table if exists vnpt_dev.sme_progress;
create table vnpt_dev.sme_progress 
(
    id        int8 not null,
	name varchar(100), 
	PRIMARY KEY (id)
);
comment on column vnpt_dev.sme_progress.name is 'Trang thái tiến trình';

insert into vnpt_dev.sme_progress (id, name) values (1,'Tiếp nhận');					
insert into vnpt_dev.sme_progress (id, name) values (2,'Đã triển khai');					
insert into vnpt_dev.sme_progress (id, name) values (3,'<PERSON>ủy');					
insert into vnpt_dev.sme_progress (id, name) values (4,'<PERSON><PERSON>n thành');					
insert into vnpt_dev.sme_progress (id, name) values (5,'Đã thanh toán');	

drop table if exists vnpt_dev.order_service_status;
create table vnpt_dev.order_service_status 
(
    id        int8 not null,
	name varchar(200),
	sme_progress_id int8,
	PRIMARY KEY (id)
);
comment on column vnpt_dev.order_service_status.name is 'Trang thái của order service';
comment on column vnpt_dev.order_service_status.sme_progress_id is 'Id của trạng thái tiến trình';

insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (1,'Mới tiếp nhận',1);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (2,'Đã thanh toán',5);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (3,'Đang điều hành thi công',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (4,'Đã giao thi công',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (5,'Đã thi công xong',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (6,'Đã hoàn thành',4);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (7,'Thoái trả',3);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (8,'Đã lấy dữ liệu',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (9,'Khai báo tổng đài',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (10,'Đang chờ hoàn công',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (11,'Đối soát hồ sơ',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (14,'Xác minh nợ',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (15,'Xét duyệt nợ cước',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (16,'KDV Xử lý khóa máy',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (17,'Thoái trả tạm',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (18,'Đang chờ tư vấn',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (19,'Tư vấn không thành công',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (21,'Outbound xác minh nợ cước',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (22,'TTĐH - Kiểm tra đường truyền',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (23,'TTVT - Hoàn công dịch vụ',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (25,'VNPT-NET Khai báo tổng đài',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (26,'Đối soát hồ sơ Bán chéo',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (27,'NVKD xác minh',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (28,'Đang chờ đối tác xử lý',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (29,'Vip - Khai báo dịch vụ',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (30,'VNPT Media/IT - Khai báo dịch vụ',2);							
insert into vnpt_dev.order_service_status (id, name, sme_progress_id) values (31,'Lãnh đạo TTVT duyệt',2);							
