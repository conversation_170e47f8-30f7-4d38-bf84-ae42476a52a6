ALTER TABLE vnpt_dev.system_params
    ADD COLUMN time_value int2,
    ADD COLUMN time_type int2,
    ADD COLUMN status int2;

COMMENT ON COLUMN vnpt_dev.system_params.time_value IS '<PERSON><PERSON><PERSON> trị thời gian';
COMMENT ON COLUMN vnpt_dev.system_params.time_type IS 'Loại thời gian: <PERSON><PERSON><PERSON>(0), <PERSON><PERSON>(1), <PERSON><PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON>(3), <PERSON><PERSON>n(4)';
COMMENT ON COLUMN vnpt_dev.system_params.status IS 'Trạng thái transaction log khi cấu hình xóa: <PERSON><PERSON><PERSON> cả(-1), <PERSON><PERSON><PERSON><PERSON> công(1), <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>(0)';

INSERT INTO vnpt_dev.system_params (param_name, param_type, time_value, time_type, status)
    VALUES ('Cài đặt xóa lịch sử giao dịch', 'TRANSACTION_LOG_DELETE_CONFIG', 3, 2, -1);

INSERT INTO vnpt_dev.schedules (bean_name, method_name, cron_expression, remark, job_status, created_by, modified_by)
    VALUES ('subscription-history', 'deleteTransactionLogByBatch', '0 0 0 * * ?', 'subscription history', 1, 'batch', 'batch');

ALTER TABLE vnpt_dev.batch_kafka
    ADD COLUMN transaction_log_id int8;
COMMENT ON COLUMN vnpt_dev.batch_kafka.transaction_log_id IS 'ID của transaction_log';