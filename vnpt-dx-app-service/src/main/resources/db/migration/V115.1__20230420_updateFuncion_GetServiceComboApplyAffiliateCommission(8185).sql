drop function if exists vnpt_dev.func_get_service_combo_apply_affiliate_commission;
CREATE OR REPLACE FUNCTION vnpt_dev.func_get_service_combo_apply_affiliate_commission(
	i_currentrolename character varying, 
	i_userid bigint, i_key character varying, 
	i_limit integer
)
 RETURNS TABLE(
	objectuniqueid bigint, 
	objectname character varying
)
 LANGUAGE plpgsql
AS $function$
begin
	--Tr<PERSON><PERSON><PERSON> hợp có ít nhất 1 bản ghi chọn all sản phẩm dịch vụ thì lấy hết sản phẩm dịch vụ 
	if exists (select 1 from vnpt_dev.affiliate_commission
				where id in (
					--<PERSON><PERSON> quyền
					select affiliate_commission_id
					from vnpt_dev.func_get_affiliate_commission_by_role(i_currentRoleName, i_userId)
				) and is_apply_object_all = true 
	) then
		begin
			--<PERSON><PERSON><PERSON> ra tất cả service_combo
			return query (
				select *
				from (
					select (services.id * 10000) as objectUniqueId,
						services.service_name as objectName
					from vnpt_dev.services
					where 
						services.status = 1 and
						services.deleted_flag = 1 and 
						services.service_name ilike ('%' || i_key || '%')
					union all
					select (combo_draft.id * 10000 + 1) as objectUniqueId, combo_cte.combo_name as objectName
					from vnpt_dev.combo_draft
					left join lateral(
						select
							combo.combo_name
						from vnpt_dev.combo
						where 
							combo.status = 1 and
							combo.approve = 1 and
							combo.deleted_flag = 1 and
							combo.combo_draft_id = combo_draft.id 			
						order by combo.id desc
						limit 1
					) combo_cte on true
					where 
						combo_draft.status = 1 and
						combo_draft.deleted_flag = 1 and 
						combo_cte.combo_name ilike ('%' || i_key || '%')
				) service_combo
				limit i_limit
			);
		end;
	--Trường hợp không có bản ghi chọn tất cả sản phẩm dịch vụ
	else
		begin
			return query (
				with affiliate_commission_product_cte as (
					select affiliate_commission.id, unnest(affiliate_commission.apply_object_ids) uniqueid
					from vnpt_dev.affiliate_commission
					where id in (
						--Phân quyền
						select affiliate_commission_id
						from vnpt_dev.func_get_affiliate_commission_by_role(i_currentRoleName, i_userId)
					)
				)
				select *
				from (
					select distinct affiliate_commission_product_cte.uniqueid as objectUniqueId, services.service_name as objectName
					from affiliate_commission_product_cte
						join vnpt_dev.services on (services.id * 10000) = affiliate_commission_product_cte.uniqueid
					where 
						(affiliate_commission_product_cte.uniqueid % 10000) = 0 and 
						services.status = 1 and
						services.deleted_flag = 1 and
						services.service_name ilike ('%' || i_key || '%')	
					union all	
					select distinct affiliate_commission_product_cte.uniqueid as objectUniqueId, combo_cte.combo_name as objectName
					from affiliate_commission_product_cte 
					left join lateral(
						select
							combo.combo_name
						from vnpt_dev.combo
						where 
							combo.status = 1 and
							combo.approve = 1 and
							combo.deleted_flag = 1 and
							(combo.combo_draft_id * 10000 + 1) = affiliate_commission_product_cte.uniqueid			
						order by combo.id desc
						limit 1
					) combo_cte on true
					where 
						(affiliate_commission_product_cte.uniqueid % 10000) = 1 and 
						combo_cte.combo_name ilike ('%' || i_key || '%')		
				) service_combo
				limit i_limit
			);
		end;
	end if;
end $function$
;
