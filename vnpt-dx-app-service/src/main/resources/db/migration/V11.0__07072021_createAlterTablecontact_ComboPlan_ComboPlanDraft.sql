alter table vnpt_dev.combo_plan
add column discount_value float8,
add column discount_type int2,
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.combo_plan.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.combo_plan.province_id is 'Id của tỉnh';
comment on column vnpt_dev.combo_plan.discount_value is ' Số tiền giảm';
comment on column vnpt_dev.combo_plan.discount_type is ' 0: giảm theo giá, 1: giả<PERSON> theo %';

alter table vnpt_dev.combo_plan_draft
add column discount_value float8,
add column discount_type int2,
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.combo_plan_draft.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.combo_plan_draft.province_id is 'Id của tỉnh';
comment on column vnpt_dev.combo_plan_draft.discount_value is ' Số tiền giảm';
comment on column vnpt_dev.combo_plan_draft.discount_type is ' 0: gi<PERSON><PERSON> theo gi<PERSON>, 1: gi<PERSON><PERSON> theo %';

alter table vnpt_dev.services
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.services.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.services.province_id is 'Id của tỉnh';

alter table vnpt_dev.pricing
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.pricing.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.pricing.province_id is 'Id của tỉnh';

alter table vnpt_dev.coupons
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.coupons.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.coupons.province_id is 'Id của tỉnh';

alter table vnpt_dev.coupon_draft
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.coupon_draft.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.coupon_draft.province_id is 'Id của tỉnh';

alter table vnpt_dev.addons
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.addons.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.addons.province_id is 'Id của tỉnh';

alter table vnpt_dev.combo
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.combo.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.combo.province_id is 'Id của tỉnh';

alter table vnpt_dev.combo_draft
add column department_id int8,
add column province_id int8;
comment on column vnpt_dev.combo_draft.department_id is 'Id bộ phận, phòng ban';
comment on column vnpt_dev.combo_draft.province_id is 'Id của tỉnh';

alter table vnpt_dev.subscription_combo_used_quantity
add column installed  int2;
comment on column vnpt_dev.subscription_combo_used_quantity.installed is 'Trạng thái cài đăt: 0 - Chưa cài đặt, 1 - Đã cài đặt';

drop table if exists vnpt_dev.contact cascade;
create table vnpt_dev.contact
(
    id        int8 not null,
	name   varchar(100),
    phone_no  varchar(20),
    email   varchar(100),
	PRIMARY KEY (id)
);
comment on table vnpt_dev.contact is ' Thông tinh địa chỉ, email kèm với từng tỉnh';
comment on column vnpt_dev.contact.name is 'Tên tỉnh';
comment on column vnpt_dev.contact.phone_no   is 'Số điện thoại';
comment on column vnpt_dev.contact.email    is 'Email tỉnh';

insert into vnpt_dev.contact(id, name, phone_no, email) values (1,'An Giang','0914251423','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (2,'Bà Rịa - Vũng Tàu','0915533416','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (3,'Bắc Giang','0911433999','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (4,'Bạc Liêu','0917252423','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (5,'Bắc Ninh','0913259919','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (6,'Bến Tre','0918255779','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (7,'Bình ĐỊnh','0834246068','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (8,'Bình Dương','0911171819','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (9,'Bình Phước','02713881108','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (10,'Bình Thuận','0916850345','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (11,'Cà Mau','0888397567','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (12,'Cần Thơ','0913046881','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (13,'Cao Bằng','0917001007','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (14,'Đà Nẵng','0914900900','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (15,'Đắc Lắc','0914160666','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (16,'Đắc Nông','0918828386','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (17,'Đồng Nai','0918718728','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (18,'Đồng Tháp','0948696471','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (19,'Gia Lai','0914577068','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (20,'Hà Giang','0912135135','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (21,'Hà Nam','091 6052111','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (22,'Hà Nội','0912299988','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (23,'Hải Dương','0917704286','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (24,'Hải Phòng','0913262246','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (25,'Hậu Giang','0918931989','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (26,'Hòa Bình','0946099930','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (27,'Hưng Yên','0916263688','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (28,'Khánh Hòa','0858 800126','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (29,'Kiên Giang','0915856999','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (30,'Kon Tum','0914132868','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (31,'Lai Châu','0911700999','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (32,'Lâm Đồng','0919781780','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (33,'Lạng Sơn','0916998000','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (34,'Lào Cai','0919299923','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (35,'Long An','0918700888','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (36,'Nam Định','0915673088','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (37,'Nghệ An','0915226888','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (38,'Ninh Bình','0945828333','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (39,'Ninh Thuận','0942841566','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (40,'Phú Thọ','0915534148','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (41,'Phú Yên','0942001731','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (42,'Quảng Bình','0911313231','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (43,'Quảng Nam','0914747266','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (44,'Quảng Ngãi','0917707567','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (45,'Quảng Ninh','0912114888','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (46,'Quảng Trị','0917949567','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (47,'Sóc Trăng','0946487888','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (48,'Sơn La','0948095868','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (49,'Tây Ninh','0888226668','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (50,'Thái Bình','0888183183','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (51,'Thái Nguyên','0913286860','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (52,'Thanh Hóa','0945531236','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (53,'Thừa Thiên Huế','0942596977','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (54,'Tiền Giang','0911946333','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (55,'TP Hồ Chí Minh','0918099908','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (56,'Trà Vinh','0916167374','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (57,'Tuyên Quang','0942998898','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (58,'Vĩnh Long','0919153456','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (59,'Vĩnh Phúc','0947530909','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (60,'Yên Bái','0917331733','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (61,'Bắc Kạn','02093870400','<EMAIL>');										
insert into vnpt_dev.contact(id, name, phone_no, email) values (62,'Điện Biên','0914653838','<EMAIL>');										