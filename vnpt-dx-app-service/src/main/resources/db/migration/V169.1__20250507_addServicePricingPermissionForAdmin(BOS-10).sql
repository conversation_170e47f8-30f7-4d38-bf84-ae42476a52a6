-- thê<PERSON> quyền cho portal admin
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id IN (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'TAO_DICH_VU_1') AND portal_id = 1;
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id IN (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_DICH_VU_1') AND portal_id = 1;
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id IN (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_GOI_DICH_VU_1') AND portal_id = 1;
DELETE FROM "vnpt_dev"."permission_portal" WHERE permission_id IN (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'XOA_GOI_DICH_VU_1') AND portal_id = 1;

INSERT INTO "vnpt_dev"."permission_portal" (
    id, permission_id, portal_id)
VALUES
((SELECT max(id) + 1 FROM "vnpt_dev"."permission_portal"), (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'TAO_DICH_VU_1' LIMIT 1), 1),
((SELECT max(id) + 2 FROM "vnpt_dev"."permission_portal"), (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_DICH_VU_1' LIMIT 1), 1),
((SELECT max(id) + 3 FROM "vnpt_dev"."permission_portal"), (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_GOI_DICH_VU_1' LIMIT 1), 1),
((SELECT max(id) + 4 FROM "vnpt_dev"."permission_portal"), (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'XOA_GOI_DICH_VU_1' LIMIT 1), 1);

-- Cập nhật api_path cho api cập nhật dịch vụ
UPDATE vnpt_dev."apis" set api_code = 'ROLE_DEV_CREATE_SERVICE' where api_path = '/api/dev-portal/services' and method = 'POST';
UPDATE "vnpt_dev"."apis" set api_code = 'ROLE_DEV_UPDATE_SERVICE' where api_path = '/api/dev-portal/services/{id}' and method = 'PUT';

-- thêm api dịch vụ và gói dịch vụ cho portal admin
DELETE FROM "vnpt_dev"."apis" WHERE id IN (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_CREATE_SERVICE') AND method = 'POST';
DELETE FROM "vnpt_dev"."apis" WHERE id IN (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_UPDATE_SERVICE') AND method = 'PUT';
DELETE FROM "vnpt_dev"."apis" WHERE id IN (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_CREATED_PRICING') AND method = 'POST';
DELETE FROM "vnpt_dev"."apis" WHERE id IN (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_UPDATE_PRICING') AND method = 'PUT';
DELETE FROM "vnpt_dev"."apis" WHERE id IN (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_DELETE_PRICING') AND method = 'DELETE';

INSERT INTO "vnpt_dev"."apis" (id, api_path, api_code, method)
VALUES
((SELECT max(id) + 1 FROM "vnpt_dev"."apis"), '/api/admin-portal/services', 'ROLE_ADMIN_CREATE_SERVICE', 'POST'),
((SELECT max(id) + 2 FROM "vnpt_dev"."apis"), '/api/admin-portal/services/{serviceId}', 'ROLE_ADMIN_UPDATE_SERVICE', 'PUT'),
((SELECT max(id) + 3 FROM "vnpt_dev"."apis"), '/api/admin-portal/pricing/service/{serviceId}', 'ROLE_ADMIN_CREATED_PRICING', 'POST'),
((SELECT max(id) + 4 FROM "vnpt_dev"."apis"), '/api/admin-portal/pricing/{pricingId}/service/{serviceId}', 'ROLE_ADMIN_UPDATE_PRICING', 'PUT'),
((SELECT max(id) + 5 FROM "vnpt_dev"."apis"), '/api/admin-portal/pricing/{id}', 'ROLE_ADMIN_DELETE_PRICING', 'DELETE');

-- thêm quyền admin cho api
DELETE FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_CREATE_SERVICE' ORDER BY id DESC LIMIT 1) AND permission_portal_id = (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'TAO_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1);
DELETE FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_UPDATE_SERVICE' ORDER BY id DESC LIMIT 1) AND permission_portal_id = (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'SUA_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1);
DELETE FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_CREATED_PRICING' ORDER BY id DESC LIMIT 1) AND permission_portal_id = (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'TAO_GOI_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1);
DELETE FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_UPDATE_PRICING' ORDER BY id DESC LIMIT 1) AND permission_portal_id = (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'SUA_GOI_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1);
DELETE FROM "vnpt_dev"."api_permission" WHERE api_id = (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_DELETE_PRICING' ORDER BY id DESC LIMIT 1) AND permission_portal_id = (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XOA_GOI_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1);


INSERT INTO "vnpt_dev"."api_permission" (id, api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES
(
    (SELECT MAX(id) + 1 FROM "vnpt_dev"."api_permission"),
    (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_CREATE_SERVICE' ORDER BY id DESC LIMIT 1),
(SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'TAO_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1),
    1,
    1
    ),
(
    (SELECT MAX(id) + 2 FROM "vnpt_dev"."api_permission"),
    (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_UPDATE_SERVICE' ORDER BY id DESC LIMIT 1),
    (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'SUA_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1),
    1,
    1
),
(
    (SELECT MAX(id) + 3 FROM "vnpt_dev"."api_permission"),
    (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_CREATED_PRICING' ORDER BY id DESC LIMIT 1),
    (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'TAO_GOI_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1),
    1,
    1
),
(
    (SELECT MAX(id) + 4 FROM "vnpt_dev"."api_permission"),
    (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_UPDATE_PRICING' ORDER BY id DESC LIMIT 1),
    (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'SUA_GOI_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1),
    1,
    1
),
(
    (SELECT MAX(id) + 5 FROM "vnpt_dev"."api_permission"),
    (SELECT id FROM "vnpt_dev"."apis" WHERE api_code = 'ROLE_ADMIN_DELETE_PRICING' ORDER BY id DESC LIMIT 1),
    (SELECT pp.id FROM "vnpt_dev"."permission" p JOIN "vnpt_dev".permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'XOA_GOI_DICH_VU_1' AND pp.portal_id = 1 LIMIT 1),
    1,
    1
);

-- thêm quyền cho FULL_ADMIN
DELETE FROM "vnpt_dev"."roles_permissions" WHERE role_id = (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1) AND permission_id = (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'TAO_DICH_VU_1' LIMIT 1);
DELETE FROM "vnpt_dev"."roles_permissions" WHERE role_id = (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1) AND permission_id = (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_DICH_VU_1' LIMIT 1);
DELETE FROM "vnpt_dev"."roles_permissions" WHERE role_id = (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1) AND permission_id = (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'TAO_GOI_DICH_VU_1' LIMIT 1);
DELETE FROM "vnpt_dev"."roles_permissions" WHERE role_id = (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1) AND permission_id = (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_GOI_DICH_VU_1' LIMIT 1);
DELETE FROM "vnpt_dev"."roles_permissions" WHERE role_id = (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1) AND permission_id = (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'XOA_GOI_DICH_VU_1' LIMIT 1);


INSERT INTO "vnpt_dev"."roles_permissions" (id, role_id, permission_id, allow_edit)
VALUES
(
    (SELECT max(id) + 1 FROM "vnpt_dev"."roles_permissions"),
    (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
(SELECT id FROM "vnpt_dev"."permission" WHERE code = 'TAO_DICH_VU_1' LIMIT 1),
    1
    ),
(
    (SELECT max(id) + 2 FROM "vnpt_dev"."roles_permissions"),
    (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_DICH_VU_1' LIMIT 1),
    1
),
(
    (SELECT max(id) + 3 FROM "vnpt_dev"."roles_permissions"),
    (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'TAO_GOI_DICH_VU_1' LIMIT 1),
    1
),
(
    (SELECT max(id) + 4 FROM "vnpt_dev"."roles_permissions"),
    (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'SUA_GOI_DICH_VU_1' LIMIT 1),
    1
),
(
    (SELECT max(id) + 5 FROM "vnpt_dev"."roles_permissions"),
    (SELECT id FROM "vnpt_dev"."role" WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM "vnpt_dev"."permission" WHERE code = 'XOA_GOI_DICH_VU_1' LIMIT 1),
    1
);

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;
