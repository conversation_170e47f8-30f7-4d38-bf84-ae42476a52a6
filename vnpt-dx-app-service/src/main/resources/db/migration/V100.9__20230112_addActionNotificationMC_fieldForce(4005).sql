--================= Add action notification MC ====================
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code = 'MC';
INSERT INTO "vnpt_dev"."action_notification"("action_code", "name", "is_send_email", "is_send_sms", "is_notification", "parent_id", "created_by", "created_at", "modified_by", "modified_at", "receiver", "allow_change_email", "allow_change_sms", "allow_change_notification", "priority_order", "is_send_telegram", "allow_change_telegram") VALUES
    ('MC', 'Chiến dịch quảng cáo', 1, 1, 1, -1, 'system', '2023-01-12 00:00:00', NULL, NULL, NULL, 'B', 'B', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D');

--================= Update parent_id of action MC-01 ========================
UPDATE "vnpt_dev"."action_notification"
SET parent_id = (SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'MC' LIMIT 1)
WHERE action_code = 'MC-01';