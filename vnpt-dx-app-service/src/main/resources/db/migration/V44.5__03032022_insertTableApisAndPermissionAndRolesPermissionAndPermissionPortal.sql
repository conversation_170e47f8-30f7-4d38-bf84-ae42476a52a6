INSERT INTO vnpt_dev.apis
(id, api_path, api_code, method)
VALUES
((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/credit-note/{id}/export-pdf', 'ADMIN_EXPORT_PDF_1', 'GET'),
((SELECT max(id) + 2 FROM vnpt_dev.apis), '/api/dev-portal/credit-note/{id}/export-pdf', 'DEV_EXPORT_PDF_1', 'GET'),
((SELECT max(id) + 3 FROM vnpt_dev.apis), '/api/sme-portal/credit-note/{id}/export-pdf', 'SME_EXPORT_PDF_1', 'GET');


INSERT INTO vnpt_dev."permission"
(id, "name", code, parent_id, priority)
VALUES
((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xuất file PDF', 'XUAT_FILE_PDF_1', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_HOA_DON_1'), (SELECT max(priority) + 1 FROM vnpt_dev."permission"));


INSERT INTO vnpt_dev.api_permission
(api_id, permission_id)
VALUES
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/credit-note/{id}/export-pdf'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1')),
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/dev-portal/credit-note/{id}/export-pdf'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1')),
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/sme-portal/credit-note/{id}/export-pdf'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'));


INSERT INTO vnpt_dev.permission_portal
(permission_id, portal_id)
VALUES
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'), 1),
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'), 2),
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'), 3);


INSERT INTO vnpt_dev.roles_permissions
(role_id, permission_id, allow_edit)
VALUES
(9, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'), 1),
(10, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'), 1),
(11, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XUAT_FILE_PDF_1'), 1);


REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;