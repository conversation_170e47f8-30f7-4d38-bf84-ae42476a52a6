ALTER TABLE vnpt_dev.customer_contact
ADD COLUMN IF NOT EXISTS contact_status int2 DEFAULT 0;

COMMENT
ON COLUMN vnpt_dev.customer_contact.contact_status IS '0: tạo mới, 1: đ<PERSON> li<PERSON>n hệ, 2: đ<PERSON> li<PERSON> hệ, 3: đ<PERSON><PERSON> liên hệ';

ALTER TABLE vnpt_dev.customer_contact
ADD COLUMN IF NOT EXISTS custom_row text DEFAULT '[-1]';

COMMENT
ON COLUMN vnpt_dev.customer_contact.custom_row IS 'Cấu hình cột hiển thị màn hình chi tiết và sửa. Mặc định -1 là tất cả';

ALTER TABLE vnpt_dev.enterprise
ADD COLUMN IF NOT EXISTS custom_row text DEFAULT '[-1]';

COMMENT
ON COLUMN vnpt_dev.enterprise.custom_row IS 'Cấu hình cột hiển thị màn hình chi tiết và sửa. Mặc định -1 là tất cả';


--api lay danh sach khach hang hien huu
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
    '/api/admin-portal/crm/enterprise-mgmt/details/get-lst-enterprise-have-account',
    'ROLE_QLKH_GET_LIST_ENTERPRISE_HAVE_ACCOUNT',
    'GET'
);
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
(
    (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_LIST_ENTERPRISE_HAVE_ACCOUNT' ORDER BY id DESC LIMIT 1),
    (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
    WHERE p.code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' AND pp.portal_id = 1 LIMIT 1), 1, 1
    );


-- add api Thong tin lien he cua khach hang ca nhan tiem nang
INSERT INTO vnpt_dev.apis
(id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-active-account/{id}',
        'ROLE_ADMIN_UPDATE_ACTIVE_ACOUNT', 'POST'),
       ((SELECT max(id) + 2 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-send-email-active-account/{id}',
        'ROLE_ADMIN_UPDATE_SEND_EMAIL_ACTIVE_ACCOUNT', 'POST'),
       ((SELECT max(id) + 3 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-change-pass-account/{id}',
        'ROLE_ADMIN_CHANGE_PASS_ACCOUNT', 'POST'),
       ((SELECT max(id) + 4 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-change-email-account/{id}',
        'ROLE_ADMIN_CHANGE_EMAIL_ACCOUNT', 'POST');

INSERT INTO vnpt_dev.api_permission
(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_ACTIVE_ACOUNT' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' AND pp.portal_id = 1 LIMIT 1), 1, 1),
       ((SELECT MAX(id) + 2 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_SEND_EMAIL_ACTIVE_ACCOUNT' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' AND pp.portal_id = 1 LIMIT 1), 1, 1),
        ((SELECT MAX(id) + 3 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CHANGE_PASS_ACCOUNT' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' AND pp.portal_id = 1 LIMIT 1), 1, 1),
        ((SELECT MAX(id) + 4 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CHANGE_EMAIL_ACCOUNT' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' AND pp.portal_id = 1 LIMIT 1), 1, 1);


-- add api xóa khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
    '/api/enterprise/delete-user-by-enterprise-id',
    'ROLE_QLKH_DELETE_USER_BY_ENTERPRISE_ID',
    'DELETE'
);
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag) VALUES
(
    (SELECT MAX(id) + 4 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_DELETE_USER_BY_ENTERPRISE_ID' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' AND pp.portal_id = 1 LIMIT 1), 1, 1);

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;


UPDATE vnpt_dev.excel_field_mapping
SET excel_fields='["Tỉnh thành người liên hệ","Tỉnh thành người liên hệ (*)"]'
WHERE db_field='contact_province';

UPDATE vnpt_dev.excel_field_mapping
SET excel_fields='["Tên tỉnh","Tên tỉnh (*)"]'
WHERE db_field='province_name';

UPDATE vnpt_dev.excel_field_mapping
SET excel_fields='["Số điện thoại","Số điện thoại (*)"]'
WHERE db_field='phone';

ALTER TABLE vnpt_dev.customer_contact
    ADD COLUMN IF NOT EXISTS user_id int8 DEFAULT null;

COMMENT
ON COLUMN vnpt_dev.enterprise.user_id IS 'id của user khi được chuyển sang liên hệ';