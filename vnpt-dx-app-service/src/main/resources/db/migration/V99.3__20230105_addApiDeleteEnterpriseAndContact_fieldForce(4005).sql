-- Update API delete enterprise --

-- API Xoá khách hàng  --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Xoá khách hàng','XOA_KHACH_HANG', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_KHACH_HANG' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_KHACH_HANG' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/delete-enterprise',
        'XOA_KHACH_HANG', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='XOA_KHACH_HANG' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'XOA_KHACH_HANG' LIMIT 1) LIMIT 1), 1, 1);

-----------------------------------------------

-- API Xoá liên hệ   --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Xoá liên hệ','XOA_LIEN_HE', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LIEN_HE' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LIEN_HE' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/customer-contact/delete-lst-contact',
        'XOA_LIEN_HE', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='XOA_LIEN_HE' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'XOA_LIEN_HE' LIMIT 1) LIMIT 1), 1, 1);
----------------------------------------------------------