DROP TABLE IF EXISTS "vnpt_dev"."user_history";
CREATE TABLE "vnpt_dev"."user_history" (
  "id" bigserial NOT NULL,
  "user_id" int8 NOT NULL,
  "created_by" int8 NOT NULL,
  "created_at" timestamp NOT NULL,
  "action_type" int4 NOT NULL,
  "description" text,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."user_history"."created_at" IS 'Thời gian phát sinh nhật ký hoạt động';
COMMENT ON COLUMN "vnpt_dev"."user_history"."action_type" IS 'Tên thao tác (1: <PERSON>hay đổi <PERSON>ail đăng nhập, 2: <PERSON><PERSON><PERSON> mật kh<PERSON>u, 3: Chỉnh sửa thông tin khách hàng, 4: <PERSON><PERSON><PERSON> ho<PERSON><PERSON> tà<PERSON>ho<PERSON>, 5: <PERSON><PERSON><PERSON> email kích ho<PERSON>, 6: <PERSON><PERSON><PERSON> <PERSON>u<PERSON> bao dùng thật, 7: <PERSON><PERSON><PERSON> thu<PERSON> bao dùng thử, 8: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> bao dùng thử, 9: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> bao, 10: <PERSON><PERSON><PERSON> ho<PERSON><PERSON> lại thu<PERSON> bao, 11: <PERSON><PERSON>a số lượng gói DV bổ sung, 12: Thêm DV bổ sung, 13: Bỏ DV bổ sung, 14: Thêm KM tổng, 15: Gia hạn thuê bao, 16: Đổi gói dịch vụ)';
COMMENT ON COLUMN "vnpt_dev"."user_history"."description" IS 'Mô tả thao tác';
COMMENT ON COLUMN "vnpt_dev"."user_history"."created_by" IS 'Người thực hiện hành động';
COMMENT ON COLUMN "vnpt_dev"."user_history"."user_id" IS 'ID user phát sinh hoạt động';

DROP TABLE IF EXISTS "vnpt_dev"."enterprise_history";
CREATE TABLE "vnpt_dev"."enterprise_history" (
  "id" bigserial NOT NULL,
  "enterprise_id" int8 NOT NULL,
  "created_by" int8 NOT NULL,
  "created_at" timestamp NOT NULL,
  "action_type" int4 NOT NULL,
  "description" text,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."enterprise_history"."created_at" IS 'Thời gian phát sinh nhật ký hoạt động';
COMMENT ON COLUMN "vnpt_dev"."enterprise_history"."action_type" IS 'Tên thao tác (1: Thay đổi Email đăng nhập, 2: Đổi mật khẩu, 3: Chỉnh sửa thông tin khách hàng, 4: Kích hoạt tài khoản, 5: Gửi email kích hoạt, 6: Tạo thuê bao dùng thật, 7: Hủy thuê bao dùng thử, 8: Tạo thuê bao dùng thử, 9: Hủy thuê bao, 10: Kích hoạt lại thuê bao, 11: Sửa số lượng gói DV bổ sung, 12: Thêm DV bổ sung, 13: Bỏ DV bổ sung, 14: Thêm KM tổng, 15: Gia hạn thuê bao, 16: Đổi gói dịch vụ)';
COMMENT ON COLUMN "vnpt_dev"."enterprise_history"."description" IS 'Mô tả thao tác';
COMMENT ON COLUMN "vnpt_dev"."enterprise_history"."created_by" IS 'Người thực hiện hành động';
COMMENT ON COLUMN "vnpt_dev"."enterprise_history"."enterprise_id" IS 'ID enterprise phát sinh hoạt động';

DROP TABLE IF EXISTS "vnpt_dev"."customer_contact_history";
CREATE TABLE "vnpt_dev"."customer_contact_history" (
  "id" bigserial NOT NULL,
  "customer_contact_id" int8 NOT NULL,
  "created_by" int8 NOT NULL,
  "created_at" timestamp NOT NULL,
  "action_type" int4 NOT NULL,
  "description" text,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."customer_contact_history"."created_at" IS 'Thời gian phát sinh nhật ký hoạt động';
COMMENT ON COLUMN "vnpt_dev"."customer_contact_history"."action_type" IS 'Tên thao tác (1: Tạo liên hệ, 2: Thay đổi thông tin, 3: Chuyển sang khách hàng tiềm năng, 4: Tạo tài khoản, 5: Tạo thuê bao dùng thử, 6: Tạo thuê bao dùng thật)';
COMMENT ON COLUMN "vnpt_dev"."customer_contact_history"."description" IS 'Mô tả thao tác';
COMMENT ON COLUMN "vnpt_dev"."customer_contact_history"."created_by" IS 'Người thực hiện hành động';
COMMENT ON COLUMN "vnpt_dev"."customer_contact_history"."customer_contact_id" IS 'ID customer contact phát sinh hoạt động';