UPDATE mail_template
SET name = '<PERSON><PERSON><PERSON> thuê bao thành công',
    title = '<PERSON><PERSON>y thuê bao thành công',
    title_default = 'Hủy thuê bao thành công',
    priority_order = 24004
WHERE code = 'SCB-28';

--<PERSON>h<PERSON><PERSON> giá trị bảng mail_template
INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order) VALUES ('SCB-24', '<PERSON>ia hạn thuê bao thành công', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #ffffff;">
<div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>
<div class="content-container" style="padding: 40px;">
<div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
<p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Gia hạn thuê bao thành công</p>
</div>
<div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
<p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
<p style="margin: 0;">Doanh nghiệp của bạn đã gia hạn thuê bao thành công cho dịch vụ $NAME_COMBO - $NAME_COMBO_PRICING.</p>
<p style="margin: 0; margin-left: 10px;"><strong>Mã giao dịch:</strong> $CODE_TRANSACTION.</p>
<p style="margin: 0; margin-left: 10px;"><strong>Người gia hạn:</strong> $NAME_EXTEND</p>
<p style="margin: 0; margin-left: 10px;"><strong>Ngày gia hạn:</strong> $DATE_EXTEND</p>
<p style="margin: 0; margin-left: 10px;"><strong>Số chu kỳ:</strong> $NUM_OF_CYCLE</p>
<p style="margin: 0; margin-left: 10px; "><strong>Chu kỳ:</strong> $CYCLE</p>
<p style="margin: 0; margin-left: 10px; "><strong>Ngày kết thúc sử dụng:</strong> $DATE_END</p>
<p style="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để sử dụng dịch vụ.</p>
<p style="margin: 0;">$LINK_USE_SUBS</p>
<p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
<p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
</div>
</div>
<div class="footer-container" style="padding: 40px;">$FOOTER</div>
</div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #ffffff;">
<div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>
<div class="content-container" style="padding: 40px;">
<div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
<p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Gia hạn thuê bao thành công</p>
</div>
<div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
<p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
<p style="margin: 0;">Doanh nghiệp của bạn đã gia hạn thuê bao thành công cho dịch vụ $NAME_COMBO - $NAME_COMBO_PRICING.</p>
<p style="margin: 0; margin-left: 10px;"><strong>Mã giao dịch:</strong> $CODE_TRANSACTION.</p>
<p style="margin: 0; margin-left: 10px;"><strong>Người gia hạn:</strong> $NAME_EXTEND</p>
<p style="margin: 0; margin-left: 10px;"><strong>Ngày gia hạn:</strong> $DATE_EXTEND</p>
<p style="margin: 0; margin-left: 10px;"><strong>Số chu kỳ:</strong> $NUM_OF_CYCLE</p>
<p style="margin: 0; margin-left: 10px; "><strong>Chu kỳ:</strong> $CYCLE</p>
<p style="margin: 0; margin-left: 10px; "><strong>Ngày kết thúc sử dụng:</strong> $DATE_END</p>
<p style="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để sử dụng dịch vụ.</p>
<p style="margin: 0;">$LINK_USE_SUBS</p>
<p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
<p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
</div>
</div>
<div class="footer-container" style="padding: 40px;">$FOOTER</div>
</div>
</body>
</html>', '', '', 'SCB', 'Gia hạn thuê bao thành công', 'Gia hạn thuê bao thành công', 24003);
INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order) VALUES ('SCB-46', 'Sắp đến kỳ thanh toán Subscription combo', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #ffffff;">
<div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>
<div class="content-container" style="padding: 40px;">
<div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
<p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Sắp đến kỳ thanh toán Subscription combo</p>
</div>
<div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
<p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
<p style="margin: 0;">Combo Dịch vụ $NAME_COMBO - $NAME_COMBO_PRICING đang được sử dụng bởi khách hàng $NAME_COMPANY sắp đến kỳ thanh toán sau $REMAIN_DAYS ngày nữa. Quý khách vui lòng thanh toán để tiếp tục sử dụng combo dịch vụ.</p>
<p style="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để xem chi tiết thuê bao combo.</p>
<p style="margin: 0;">$LINK_DETAIL</p>
<p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
<p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
</div>
</div>
<div class="footer-container" style="padding: 40px;">$FOOTER</div>
</div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #ffffff;">
<div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>
<div class="content-container" style="padding: 40px;">
<div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
<p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Sắp đến kỳ thanh toán Subscription combo</p>
</div>
<div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
<p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
<p style="margin: 0;">Combo Dịch vụ $NAME_COMBO - $NAME_COMBO_PRICING đang được sử dụng bởi khách hàng $NAME_COMPANY sắp đến kỳ thanh toán sau $REMAIN_DAYS ngày nữa. Quý khách vui lòng thanh toán để tiếp tục sử dụng combo dịch vụ.</p>
<p style="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để xem chi tiết thuê bao combo.</p>
<p style="margin: 0;">$LINK_DETAIL</p>
<p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
<p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
</div>
</div>
<div class="footer-container" style="padding: 40px;">$FOOTER</div>
</div>
</body>
</html>', '', '', 'SCB', 'Sắp đến kỳ thanh toán Subscription combo', 'Sắp đến kỳ thanh toán Subscription combo', 24006);
INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order) VALUES ('SCB-43', 'Sắp đến kỳ gia hạn Subscription combo', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #ffffff;">
<div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>
<div class="content-container" style="padding: 40px;">
<div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
<p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Sắp đến kỳ gia hạn Subscription combo</p>
</div>
<div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
<p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
<p style="margin: 0;">Combo Dịch vụ $NAME_COMBO - $NAME_COMBO_PRICING sẽ hết hạn sau $REMAIN_DAYS ngày. Quý khách vui lòng gia hạn để tiếp tục sử dụng combo dịch vụ.</p>
<p style="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để gia hạn combo dịch vụ.</p>
<p style="margin: 0;">$LINK_RENEWING</p>
<p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
<p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
</div>
</div>
<div class="footer-container" style="padding: 40px;">$FOOTER</div>
</div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #ffffff;">
<div class="logo-container" style="height: 80px; display: flex; justify-content: center; align-items: center; box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">$HEADER</div>
<div class="content-container" style="padding: 40px;">
<div class="title-container" style="text-align: center; padding: 40px 0 60px;"><img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_svpc.png" alt="Dịch vụ" />
<p class="main-title" style="margin: 0; line-height: 28px; font-size: 20px; font-weight: bold; color: #2c3d94; text-transform: uppercase; margin-top: 30px;">Sắp đến kỳ gia hạn Subscription combo</p>
</div>
<div class="main-content" style="line-height: 22px; font-size: 14px; letter-spacing: .3px;">
<p class="mb-m" style="margin: 0; margin-bottom: 20px;">Xin chào $USER,</p>
<p style="margin: 0;">Combo Dịch vụ $NAME_COMBO - $NAME_COMBO_PRICING sẽ hết hạn sau $REMAIN_DAYS ngày. Quý khách vui lòng gia hạn để tiếp tục sử dụng combo dịch vụ.</p>
<p style="margin: 0; margin-top: 20px;">Truy cập đường link dưới đây để gia hạn combo dịch vụ.</p>
<p style="margin: 0;">$LINK_RENEWING</p>
<p class="mt-m" style="margin: 0; margin-top: 20px;">Trân trọng,</p>
<p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
</div>
</div>
<div class="footer-container" style="padding: 40px;">$FOOTER</div>
</div>
</body>
</html>', '', '', 'SCB', 'Sắp đến kỳ gia hạn Subscription combo', 'Sắp đến kỳ gia hạn Subscription combo', 24005);

-- Thêm giá trị bảng param_email
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$USER', '[người dùng]', 'SCB-24', 'Nguyễn Ngọc Viên');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$NAME_COMBO', '[Tên Combo dịch vụ]', 'SCB-24', 'Combo VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$NAME_COMBO_PRICING', '[Tên gói combo dịch vụ]', 'SCB-24', 'Gói nâng cao 6 tháng');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$CODE_TRANSACTION', '[Mã giao dịch]', 'SCB-24', '');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$NAME_EXTEND', '[Tên người gia hạn]', 'SCB-24', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$DATE_EXTEND', '[Ngày gia hạn]', 'SCB-24', '06/03/2021 10:27:51');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$NUM_OF_CYCLE', '[Số chu kỳ]', 'SCB-24', '1');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$CYCLE', '[Chu kỳ]', 'SCB-24', '3 tháng');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$DATE_END', '[Ngày kết thúc sử dụng thuê bao]', 'SCB-24', '06/03/2021 10:27:51');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-24'), '$LINK_USE_SUBS', '[Link sử dụng dịch vụ]', 'SCB-24', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-43'), '$USER', '[người dùng]', 'SCB-43', 'Nguyễn Ngọc Viên');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-43'), '$NAME_COMBO', '[Tên Combo dịch vụ]', 'SCB-43', 'Combo VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-43'), '$NAME_COMBO_PRICING', '[Tên gói combo dịch vụ]', 'SCB-43', 'Gói nâng cao 6 tháng');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-43'), '$REMAIN_DAYS', '[Số ngày còn hạn]', 'SCB-43', '30');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-43'), '$LINK_RENEWING', '[Link gia hạn dịch vụ]', 'SCB-43', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-46'), '$USER', '[người dùng]', 'SCB-46', 'Nguyễn Ngọc Viên');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-46'), '$NAME_COMBO', '[Tên Combo dịch vụ]', 'SCB-46', 'Combo VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-46'), '$NAME_COMBO_PRICING', '[Tên gói combo dịch vụ]', 'SCB-46', 'Gói nâng cao 6 tháng');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-46'), '$NAME_COMPANY', '[Tên công ty/ doanh nghiệp]', 'SCB-46', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-46'), '$REMAIN_DAYS', '[Số ngày còn hạn]', 'SCB-46', '30');
INSERT INTO vnpt_dev.param_email (id_mail_template, param_name, remark, mail_template_code, param_default_value) VALUES ((SELECT id FROM mail_template WHERE code = 'SCB-46'), '$LINK_DETAIL', '[Link xem chi tiết thuê bao]', 'SCB-46', 'yyDez01G5ucWXALyCnhO');
