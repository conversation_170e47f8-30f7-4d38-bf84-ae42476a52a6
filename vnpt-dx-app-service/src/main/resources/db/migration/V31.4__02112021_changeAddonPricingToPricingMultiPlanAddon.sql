-- them lai du lieu addon pricing multi plan sang bang pricing multi plan addon
WITH old_pricing_addon AS (
    SELECT
        DISTINCT pa.pricing_id AS pricing_id,
                 pa.is_required AS is_required,
                 a.id AS addon_id
    FROM
        vnpt_dev.addons a
            INNER JOIN vnpt_dev.pricing_addons pa ON
                pa.addons_id = a.id
            INNER JOIN vnpt_dev.pricing_multi_plan pmp ON
                a.id = pmp.addon_id
            INNER JOIN vnpt_dev.pricing p ON
                pa.pricing_id = p.id
    WHERE
            p.deleted_flag = 1
      AND p.approve = 1
      AND a.approve = 1
      AND a.deleted_flag = 1)
INSERT
INTO
	vnpt_dev.pricing_multi_plan_addon (addon_id,
	pricing_multi_plan_addon_id,
	is_require,
	pricing_id)
SELECT
    old_pricing_addon.addon_id,
    pmp.id,
    old_pricing_addon.is_required,
    old_pricing_addon.pricing_id
FROM
    vnpt_dev.pricing_multi_plan pmp
        INNER JOIN old_pricing_addon ON
            pmp.addon_id = old_pricing_addon.addon_id
        INNER JOIN vnpt_dev.pricing p ON
            old_pricing_addon.pricing_id = p.id
WHERE
        pmp.payment_cycle = p.payment_cycle
  AND pmp.circle_type = p.cycle_type;

-- xoa du lieu cu cua addon multi plan tỏng bang pricing_adodn
DELETE
FROM
    vnpt_dev.pricing_addons
WHERE
        id IN (
        SELECT
            pa.id
        FROM
            addons a
                INNER JOIN vnpt_dev.pricing_addons pa ON
                    pa.addons_id = a.id
                INNER JOIN vnpt_dev.pricing_multi_plan pmp ON
                    a.id = pmp.addon_id
                INNER JOIN vnpt_dev.pricing p ON
                    pa.pricing_id = p.id
        WHERE
                p.deleted_flag = 1
          AND p.approve = 1
          AND a.approve = 1
          AND a.deleted_flag = 1);
