DROP VIEW IF EXISTS "vnpt_dev"."count_user_per_all_version_combo_plan_namnd";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_user_per_all_version_combo_plan_namnd"("i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("latest_id" int8, "num_user" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT
    latest_combo_plan_user.latest_version,
    COUNT (*) AS NumUser
FROM (
         SELECT DISTINCT mapping_latest.latest_version, sub_user.UserId
         FROM vnpt_dev.get_all_version_combo_plan_namnd(i_starttime, i_endtime) as all_version
                  LEFT JOIN vnpt_dev.get_mapping_version_latest_version_combo_plan(i_endtime)
             as mapping_latest ON mapping_latest.id = all_version.id
                  INNER JOIN (
             SELECT DISTINCT sub.combo_plan_id AS ComboPlanId, sub.user_id AS UserId
             FROM vnpt_dev.subscriptions AS sub
             WHERE sub.combo_plan_id IS NOT NULL AND
                 (sub.created_at::date >= i_starttime::date AND sub.created_at::date <= i_endtime::date)
         ) AS sub_user ON sub_user.ComboPlanId = all_version.id
     ) AS latest_combo_plan_user
GROUP BY latest_combo_plan_user.latest_version;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


DROP VIEW IF EXISTS "vnpt_dev"."count_sub_per_com_plan_by_reg_type_namnd";
	CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_com_plan_by_reg_type_namnd"("register_type" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("combo_plan_id" int8, "combo_id" int8, "num_sub" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT sub.combo_plan_id AS ComboPlanId, c.id as comboId , COUNT (*) AS NumTrial
FROM vnpt_dev.subscriptions AS sub
         LEFT JOIN vnpt_dev.combo_plan cp ON sub.combo_plan_id=cp.id AND sub.combo_plan_id is not null
         LEFT JOIN vnpt_dev.combo c ON cp.combo_id=c.id
WHERE  sub.confirm_status = 1 AND
        sub.reg_type = register_type AND
    (cp.created_at::date >= i_starttime::date AND cp.created_at::date <= i_endtime::date)
GROUP BY sub.combo_plan_id, c.id;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;


DROP VIEW IF EXISTS "vnpt_dev"."count_sub_per_com_plan_by_his_namnd";
	CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_com_plan_by_his_namnd"("his_type" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("combo_plan_id" int8, "num_sub" int8) AS $BODY$

DECLARE

renew_query varchar = E'SELECT sub.combo_plan_id AS ComboPlanId , COUNT (*)::BIGINT AS NumberRenew
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT sh.id as Id, sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				LEFT JOIN vnpt_dev.billings AS b ON b.subscriptions_id = sh.subscription_id
				WHERE sh.content ILIKE \'Gia hạn thuê bao\' AND b.status = 2 AND
				    (sh.created_at::date >= \'%s\'::date AND sh.created_at::date <= \'%s\'::date)
			) AS renew_sub ON renew_sub.SubId = sub.id
			WHERE sub.combo_plan_id IS NOT NULL
			GROUP BY sub.combo_plan_id';

			
	cancel_query varchar = E'SELECT sub.combo_plan_id AS ComboPlanId , COUNT (*)::BIGINT AS NumberCancel
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT sh.id as Id, sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				WHERE sh.content ILIKE \'%%hủy thuê bao%%\' AND
				    (sh.created_at::date >= \'%s\'::date AND 
						sh.created_at::date <= \'%s\'::date)
				GROUP BY sh.subscription_id,sh.id
			) AS cancel_sub ON cancel_sub.SubId = sub.id
			WHERE sub.combo_plan_id IS NOT NULL
			GROUP BY sub.combo_plan_id';
	
	react_query varchar = E'SELECT sub.combo_plan_id AS ComboPlanId , COUNT (*)::BIGINT AS NumberReactivate
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT sh.id as Id, sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				WHERE sh.content ILIKE \'%%kích hoạt lại%%\' AND
				    (sh.created_at::date >= \'%s\'::date AND 
						sh.created_at::date <= \'%s\'::date)
			) AS react_sub ON react_sub.SubId = sub.id
			WHERE sub.combo_plan_id IS NOT NULL
			GROUP BY sub.combo_plan_id';

	change_query varchar = E'SELECT sub.combo_plan_id AS ComboPlanId , COUNT (*)::BIGINT AS NumberChange
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT sh.id as Id, sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				WHERE sh.content ILIKE \'Đổi gói dịch vụ%%\' AND
				    (sh.created_at::date >= \'%s\'::date AND 
						sh.created_at::date <= \'%s\'::date)
			) AS change_sub ON change_sub.SubId = sub.id
			WHERE sub.combo_plan_id IS NOT NULL
			GROUP BY sub.combo_plan_id';
	full_query varchar;

BEGIN
	-- Routine body goes here...
		if his_type = 0 then full_query := FORMAT(renew_query, i_startTime, i_endTime);
		elsif his_type = 1 then full_query := FORMAT(cancel_query, i_startTime, i_endTime);
		elsif his_type = 2 then full_query := FORMAT(react_query, i_startTime, i_endTime);
		elsif his_type = 3 then full_query := FORMAT(change_query, i_startTime, i_endTime);
else full_query := '';
end if;
		
		raise notice 'Value: %', full_query;

RETURN query execute full_query;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;
	
	