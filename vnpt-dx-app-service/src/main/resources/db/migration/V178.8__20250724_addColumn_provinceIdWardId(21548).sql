-- Thê<PERSON> cột địa chỉ mới vào bảng province
ALTER TABLE "vnpt_dev"."province"
  ADD COLUMN IF NOT EXISTS "id_new" int8;
COMMENT ON COLUMN "vnpt_dev"."province"."id_new" IS 'ID tỉnh thành mới (sau thay đổi địa chính 07/2025)';

UPDATE "vnpt_dev"."province" SET id_new = province_id_new
FROM vnpt_dev.geography_2025
WHERE province.id = geography_2025.province_id_old AND
    (id_new IS NULL OR id_new <> province_id_new);

-- Thêm cột địa chỉ mới vào bảng ward
ALTER TABLE "vnpt_dev"."ward"
  ADD COLUMN IF NOT EXISTS "id_new" int8;
COMMENT ON COLUMN "vnpt_dev"."ward"."id_new" IS 'ID phường xã mới (sau thay đổi địa chính 07/2025)';

DROP MATERIALIZED VIEW IF EXISTS "vnpt_dev"."mview_mapping_ward";
CREATE MATERIALIZED VIEW "vnpt_dev"."mview_mapping_ward" AS
SELECT DISTINCT geography_2025.province_id_old,
    geography_2025.district_id_old,
    geography_2025.ward_id_old,
    geography_2025.province_id_new,
    geography_2025.ward_id_new
FROM vnpt_dev.geography_2025 ;

UPDATE "vnpt_dev"."ward" SET id_new = ward_id_new
FROM vnpt_dev.mview_mapping_ward
WHERE province_id = province_id_old AND
    district_id = district_id_old AND
    id = ward_id_old AND
    ward_id_new IS NOT NULL AND
    (id_new IS NULL OR id_new <> ward_id_new);


-- Thêm cột địa chỉ mới vào bảng users
ALTER TABLE "vnpt_dev"."users"
  ADD COLUMN IF NOT EXISTS "province_id_new" int8,
  ADD COLUMN IF NOT EXISTS "ward_id_new" int8,
  ADD COLUMN IF NOT EXISTS "street_id_new" int8;
CREATE INDEX IF NOT EXISTS "index_user_provinceIdNew" ON "vnpt_dev"."users" USING hash ("province_id_new");
COMMENT ON COLUMN "vnpt_dev"."users"."province_id_new" IS 'ID tỉnh (sau thay đổi địa chính 07/2025)';
COMMENT ON COLUMN "vnpt_dev"."users"."ward_id_new" IS 'ID phường xã (sau thay đổi địa chính 07/2025)';
COMMENT ON COLUMN "vnpt_dev"."users"."street_id_new" IS 'ID phố ấp khu (sau thay đổi địa chính 07/2025)';

UPDATE "vnpt_dev"."users" SET province_id_new = id_new
FROM vnpt_dev.province
WHERE province_id = province.id AND
    id_new IS NOT NULL AND
    (province_id_new IS NULL OR province_id_new <> id_new);