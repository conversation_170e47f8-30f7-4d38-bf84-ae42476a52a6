-- vnpt_dev.feature_view_get_spdv_info --
DROP VIEW IF EXISTS vnpt_dev.feature_view_get_spdv_info;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_get_spdv_info
AS ( SELECT 0 AS calculate_type,
            services.pre_order_url,
            concat(services.id, '0000')::bigint AS service_unique_id,
            services.id AS services_id,
            pricing.id AS pricing_id,
            concat(pricing.id, '0000')::bigint AS pricing_unique_id,
            pricing_multi_plan.id AS pricing_multi_plan_id,
            services.service_name,
            services.product_type,
            services.classification as classification,
            NULL::text AS combo_type,
            NULL::text AS object_type,
            services.services_draft_id AS draft_id,
            services.pricing_default,
            pricing.pricing_draft_id,
            ARRAY[services.categories_id] AS lst_category,
            COALESCE(services.service_owner::integer, 3) = ANY (ARRAY[0, 1]) AS is_on,
            services.user_id AS provider_id,
            pricing.pricing_name,
            COALESCE(pricing.number_of_trial::integer, 0) AS number_of_trial,
            COALESCE(pricing.trial_type::integer, 0) AS trial_type,
            CASE
                WHEN COALESCE(pricing.number_of_trial::integer, 0) > 0 THEN 1
                ELSE 0
                END AS is_trial,
            services.allow_multi_sub,
            string_to_array(translate(services.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
            CASE
                WHEN pricing.id IS NOT NULL THEN string_to_array(translate(pricing.customer_type_code::text, '[]"'::text, ''::text), ','::text)
                ELSE '{HKD,KHDN,CN}'::text[]
                END AS pricing_lst_customer_type,
            CASE
                WHEN pricing_multi_plan.id IS NOT NULL THEN string_to_array(translate(pricing_multi_plan.customer_type_code::text, '[]"'::text, ''::text), ','::text)
                ELSE '{HKD,KHDN,CN}'::text[]
                END AS pricing_multi_plan_lst_customer_type,
            CASE
                WHEN pricing_multi_plan.circle_type = 0 THEN concat(pricing_multi_plan.payment_cycle, ' ngày')
                WHEN pricing_multi_plan.circle_type = 1 THEN concat(pricing_multi_plan.payment_cycle, ' tuần')
                WHEN pricing_multi_plan.circle_type = 2 THEN concat(pricing_multi_plan.payment_cycle, ' tháng')
                WHEN pricing_multi_plan.circle_type = 3 THEN concat(pricing_multi_plan.payment_cycle, ' năm')
                WHEN pricing.cycle_type = 0 THEN concat(pricing.payment_cycle, ' ngày')
                WHEN pricing.cycle_type = 1 THEN concat(pricing.payment_cycle, ' tuần')
                WHEN pricing.cycle_type = 2 THEN concat(pricing.payment_cycle, ' tháng')
                WHEN pricing.cycle_type = 3 THEN concat(pricing.payment_cycle, ' năm')
                ELSE NULL::text
                END AS payment_cycle,
            services.service_owner,
            COALESCE(pricing.is_one_time::integer, 1) AS is_one_time,
            NULL::bigint AS icon_id
     FROM vnpt_dev.services
              JOIN vnpt_dev.users ON users.id = services.created_by AND users.deleted_flag = 1 AND users.status = 1
              LEFT JOIN ( SELECT count(cpricing.id) AS count,
                                 cpricing.service_id
                          FROM ( SELECT max(pricing_1.id) AS id,
                                        pricing_1.service_id
                                 FROM vnpt_dev.pricing pricing_1
                                 WHERE pricing_1.status = 1 AND pricing_1.approve = 1 AND pricing_1.deleted_flag = 1
                                 GROUP BY pricing_1.pricing_draft_id, pricing_1.service_id) cpricing
                          GROUP BY cpricing.service_id) pcount ON pcount.service_id = services.id
              LEFT JOIN ( SELECT p.id,
                                 p.pricing_code,
                                 p.pricing_name,
                                 p.description,
                                 p.payment_cycle,
                                 p.number_of_cycles,
                                 p.cycle_type,
                                 p.pricing_plan,
                                 p.service_id,
                                 p.unit_id,
                                 p.currency_id,
                                 p.amount,
                                 p.setup_fee,
                                 p.free_quantity,
                                 p.estimate_quantity,
                                 p.created_at,
                                 p.created_by,
                                 p.modified_at,
                                 p.modified_by,
                                 p.status,
                                 p.deleted_flag,
                                 p.price,
                                 p.list_feature_id,
                                 p.pricing_order,
                                 p.recommended_status,
                                 p.update_reason,
                                 p.approve,
                                 p.pricing_draft_id,
                                 p.approve_time,
                                 p.sme_pricing_id,
                                 p.trial_type,
                                 p.number_of_trial,
                                 p.pricing_type,
                                 p.has_change_price,
                                 p.has_change_quantity,
                                 p.has_refund,
                                 p.cancel_date,
                                 p.active_date,
                                 p.duration_type,
                                 p.department_id,
                                 p.province_id,
                                 p.update_subscription_date,
                                 p.change_pricing_date,
                                 p.is_change_now,
                                 p.is_update_now,
                                 p.seo_id,
                                 p.has_renew,
                                 p.customer_type_code,
                                 p.priority,
                                 p.type_active_in_payment_type,
                                 p.payment_request,
                                 p.is_one_time,
                                 p.creation_layout_id,
                                 p.change_pricing_payment_time,
                                 p.migration_id
                          FROM vnpt_dev.pricing p
                          WHERE p.status = 1 AND p.deleted_flag = 1 AND p.approve = 1 AND (p.id IN ( SELECT max(p_1.id) AS max
                                                                                                     FROM vnpt_dev.pricing_draft pd
                                                                                                              JOIN vnpt_dev.pricing p_1 ON pd.id = p_1.pricing_draft_id AND pd.deleted_flag = 1
                                                                                                     GROUP BY pd.id))) pricing ON pcount.service_id = pricing.service_id AND (pricing.id IN ( SELECT max(pricing_1.id) AS id
                                                                                                                                                                                              FROM vnpt_dev.pricing pricing_1
                                                                                                                                                                                              WHERE pricing_1.status = 1 AND pricing_1.approve = 1 AND pricing_1.deleted_flag = 1
                                                                                                                                                                                              GROUP BY pricing_1.pricing_draft_id)) AND (pricing.pricing_draft_id IN ( SELECT pricing_draft.id
                                                                                                                                                                                                                                                                       FROM vnpt_dev.pricing_draft
                                                                                                                                                                                                                                                                       WHERE pricing_draft.deleted_flag = 1))
              LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.pricing_id = pricing.id AND pricing_multi_plan.deleted_flag = 1 AND pricing_multi_plan.display_status = 1
     WHERE
       services.deleted_flag = 1
       AND services.status = 1
       AND services.approve = 1
       AND (pricing.id IS NOT NULL OR services.classification = 3)
       AND (services.classification = 3 OR (services.classification <> 3 OR services.classification IS NULL) AND pcount.count <> 0)
       AND (services.categories_app IS NOT NULL OR services.categories_id IS NOT NULL)
     ORDER BY services.id, pricing.id, pricing_multi_plan.id)
   UNION ALL
   ( SELECT 1 AS calculate_type,
            string_agg(services.pre_order_url::text, ', '::text) AS pre_order_url,
            concat(combo.id, '0001')::bigint AS service_unique_id,
            combo.id AS services_id,
            cp.id AS pricing_id,
            concat(cp.id, '0001')::bigint AS pricing_unique_id,
            NULL::bigint AS pricing_multi_plan_id,
            combo.combo_name AS service_name,
            NULL::smallint AS product_type,
            null::smallint as classification,
            CASE
                WHEN combo.combo_owner = 0 THEN 'VNPT'::text
                WHEN combo.combo_owner = 1 THEN 'VNPT'::text
                WHEN combo.combo_owner = 2 THEN 'OTHER'::text
                WHEN combo.combo_owner = 3 THEN 'OTHER'::text
                WHEN combo.combo_owner = 4 THEN 'APP'::text
                WHEN combo.combo_owner = 5 THEN 'APP'::text
                ELSE 'OTHER'::text
                END AS combo_type,
            combo_pricing.object_type,
            combo.combo_draft_id AS draft_id,
            NULL::bigint AS pricing_default,
            NULL::bigint AS pricing_draft_id,
            string_to_array(combo.categories_id::text, ','::text)::bigint[] AS lst_category,
            COALESCE(combo.combo_owner::integer, 3) = ANY (ARRAY[0, 1]) AS is_on,
            combo.user_id AS provider_id,
            cp.combo_name AS pricing_name,
            COALESCE(cp.number_of_trial::integer, 0) AS number_of_trial,
            COALESCE(cp.trial_type::integer, 0) AS trial_type,
            CASE
                WHEN COALESCE(cp.number_of_trial::integer, 0) > 0 THEN 1
                ELSE 0
                END AS is_trial,
            combo.allow_multi_sub,
            string_to_array(translate(combo.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
            string_to_array(translate(cp.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS pricing_lst_customer_type,
            '{HKD,KHDN,CN}'::text[] AS pricing_multi_plan_lst_customer_type,
            CASE
                WHEN cp.cycle_type = 0 THEN concat(cp.payment_cycle, ' ngày')
                WHEN cp.cycle_type = 1 THEN concat(cp.payment_cycle, ' tuần')
                WHEN cp.cycle_type = 2 THEN concat(cp.payment_cycle, ' tháng')
                WHEN cp.cycle_type = 3 THEN concat(cp.payment_cycle, ' năm')
                ELSE NULL::text
                END AS payment_cycle,
            combo.combo_owner AS service_owner,
            1 AS is_one_time,
            NULL::bigint AS icon_id
     FROM vnpt_dev.combo
              JOIN vnpt_dev.users ON users.id = combo.created_by AND users.deleted_flag = 1 AND users.status = 1
              LEFT JOIN ( SELECT count(cpricing.id) AS count,
                                 cpricing.combo_id
                          FROM ( SELECT max(combo_plan.id) AS id,
                                        combo_plan.combo_id
                                 FROM vnpt_dev.combo_plan
                                 WHERE combo_plan.deleted_flag = 1 AND combo_plan.status = 1 AND combo_plan.approve = 1
                                 GROUP BY combo_plan.combo_plan_draft_id, combo_plan.combo_id) cpricing
                          GROUP BY cpricing.combo_id) cpcount ON cpcount.combo_id = combo.id
              LEFT JOIN vnpt_dev.combo_plan cp ON cpcount.combo_id = cp.combo_id AND (cp.id IN ( SELECT max(combo_plan.id) AS max
                                                                                                 FROM vnpt_dev.combo_plan
                                                                                                 WHERE combo_plan.deleted_flag = 1 AND combo_plan.status = 1 AND combo_plan.approve = 1
                                                                                                 GROUP BY combo_plan.combo_plan_draft_id))
              LEFT JOIN vnpt_dev.combo_pricing ON combo_pricing.id_combo_plan = cp.id
              LEFT JOIN vnpt_dev.pricing ON pricing.id = combo_pricing.object_id AND combo_pricing.object_type = 'PRICING'::text
              LEFT JOIN vnpt_dev.services ON services.id = pricing.service_id
     WHERE combo.deleted_flag = 1 AND combo.status = 1 AND combo.approve = 1 AND cpcount.count <> 0
     GROUP BY combo.id, cp.id, cp.combo_name, cp.number_of_trial, cp.trial_type, cp.customer_type_code, cp.cycle_type, cp.payment_cycle, combo_pricing.object_type
     ORDER BY combo.id, cp.combo_order)
   UNION ALL
   ( SELECT 4 AS calculate_type,
            NULL::character varying AS pre_order_url,
            concat(service_group.id, '0004')::bigint AS service_unique_id,
            service_group.id AS services_id,
            NULL::bigint AS pricing_id,
            NULL::bigint AS pricing_unique_id,
            NULL::bigint AS pricing_multi_plan_id,
            service_group.name AS service_name,
            null::smallint as classification,
            NULL::smallint AS product_type,
            NULL::text AS combo_type,
            NULL::text AS object_type,
            service_group.group_service_draft_id AS draft_id,
            NULL::bigint AS pricing_default,
            NULL::bigint AS pricing_draft_id,
            COALESCE(service_group.categories_id, ARRAY['-1'::integer::bigint]) AS lst_category,
            COALESCE(service_group.group_service_owner::integer, 3) = ANY (ARRAY[0, 1]) AS is_on,
            service_group.user_id AS provider_id,
            NULL::character varying AS pricing_name,
            NULL::integer AS number_of_trial,
            NULL::integer AS trial_type,
            NULL::integer AS is_trial,
            NULL::smallint AS allow_multi_sub,
            string_to_array(translate(service_group.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
            '{HKD,KHDN,CN}'::text[] AS pricing_lst_customer_type,
            '{HKD,KHDN,CN}'::text[] AS pricing_multi_plan_lst_customer_type,
            NULL::text AS payment_cycle,
            service_group.group_service_owner AS service_owner,
            NULL::integer AS is_one_time,
            service_group.icon_id
     FROM vnpt_dev.service_group
              JOIN vnpt_dev.users ON users.id = service_group.created_by AND users.deleted_flag = 1 AND users.status = 1
     WHERE service_group.deleted_flag = 1 AND service_group.status = 1 AND service_group.approve = 1 AND (service_group.id IN ( SELECT max(service_group_1.id) AS max
                                                                                                                                FROM vnpt_dev.service_group service_group_1
                                                                                                                                GROUP BY service_group_1.group_service_draft_id))
     ORDER BY service_group.id);

-- view spdv by vnpt_tech --
drop view if exists vnpt_dev.bos_feature_view_shopping_cart_get_spdv_info_vnpt_tech;
create or replace view vnpt_dev.bos_feature_view_shopping_cart_get_spdv_info_vnpt_tech as
( SELECT 0 AS calculate_type,
         services.pre_order_url,
         concat(services.id, '0000')::bigint AS service_unique_id,
         services.id AS services_id,
         pricing.id AS pricing_id,
         concat(pricing.id, '0000')::bigint AS pricing_unique_id,
         pricing_multi_plan.id AS pricing_multi_plan_id,
         services.service_name,
         services.product_type,
         services.classification,
         NULL :: TEXT AS combo_type,
         NULL :: TEXT AS object_type,
         services.services_draft_id AS draft_id,
         services.pricing_default,
         pricing.pricing_draft_id,
         ARRAY[services.categories_id] AS lst_category,
         COALESCE(services.service_owner::integer, 3) = ANY (ARRAY[0, 1]) AS is_on,
         services.user_id AS provider_id,
         pricing.pricing_name,
         COALESCE(pricing.number_of_trial::integer, 0) AS number_of_trial,
         COALESCE(pricing.trial_type::integer, 0) AS trial_type,
         CASE
             WHEN COALESCE(pricing.number_of_trial::integer, 0) > 0 THEN 1
             ELSE 0
             END AS is_trial,
         services.allow_multi_sub,
         string_to_array(translate(services.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
         CASE
             WHEN pricing.id IS NOT NULL THEN string_to_array(translate(pricing.customer_type_code::text, '[]"'::text, ''::text), ','::text)
             ELSE '{HKD,KHDN,CN}'::text[]
             END AS pricing_lst_customer_type,
         CASE
             WHEN pricing_multi_plan.id IS NOT NULL THEN string_to_array(translate(pricing_multi_plan.customer_type_code::text, '[]"'::text, ''::text), ','::text)
             ELSE '{HKD,KHDN,CN}'::text[]
             END AS pricing_multi_plan_lst_customer_type,
         CASE
             WHEN pricing_multi_plan.circle_type = 0 THEN concat(pricing_multi_plan.payment_cycle, ' ngày')
             WHEN pricing_multi_plan.circle_type = 1 THEN concat(pricing_multi_plan.payment_cycle, ' tuần')
             WHEN pricing_multi_plan.circle_type = 2 THEN concat(pricing_multi_plan.payment_cycle, ' tháng')
             WHEN pricing_multi_plan.circle_type = 3 THEN concat(pricing_multi_plan.payment_cycle, ' năm')
             WHEN pricing.cycle_type = 0 THEN concat(pricing.payment_cycle, ' ngày')
             WHEN pricing.cycle_type = 1 THEN concat(pricing.payment_cycle, ' tuần')
             WHEN pricing.cycle_type = 2 THEN concat(pricing.payment_cycle, ' tháng')
             WHEN pricing.cycle_type = 3 THEN concat(pricing.payment_cycle, ' năm')
             ELSE NULL::text
             END AS payment_cycle,
         COALESCE(pricing.is_one_time::integer, 1) AS is_one_time,
         services.service_owner
  FROM vnpt_dev.services
           LEFT JOIN vnpt_dev.users ON users.id = services.created_by AND users.deleted_flag = 1 AND users.status = 1
           LEFT JOIN ( SELECT count(cpricing.id) AS count,
                              cpricing.service_id
                       FROM ( SELECT max(pricing_1.id) AS id,
                                     pricing_1.service_id
                              FROM vnpt_dev.pricing pricing_1
                              WHERE pricing_1.status = 1 AND pricing_1.approve = 1 AND pricing_1.deleted_flag = 1
                              GROUP BY pricing_1.pricing_draft_id, pricing_1.service_id) cpricing
                       GROUP BY cpricing.service_id) pcount ON pcount.service_id = services.id
           LEFT JOIN ( SELECT p.id,
                              p.pricing_code,
                              p.pricing_name,
                              p.description,
                              p.payment_cycle,
                              p.number_of_cycles,
                              p.cycle_type,
                              p.pricing_plan,
                              p.service_id,
                              p.unit_id,
                              p.currency_id,
                              p.amount,
                              p.setup_fee,
                              p.free_quantity,
                              p.estimate_quantity,
                              p.created_at,
                              p.created_by,
                              p.modified_at,
                              p.modified_by,
                              p.status,
                              p.deleted_flag,
                              p.price,
                              p.list_feature_id,
                              p.pricing_order,
                              p.recommended_status,
                              p.update_reason,
                              p.approve,
                              p.pricing_draft_id,
                              p.approve_time,
                              p.sme_pricing_id,
                              p.trial_type,
                              p.number_of_trial,
                              p.pricing_type,
                              p.has_change_price,
                              p.has_change_quantity,
                              p.has_refund,
                              p.cancel_date,
                              p.active_date,
                              p.duration_type,
                              p.department_id,
                              p.province_id,
                              p.update_subscription_date,
                              p.change_pricing_date,
                              p.is_change_now,
                              p.is_update_now,
                              p.seo_id,
                              p.has_renew,
                              p.customer_type_code,
                              p.priority,
                              p.type_active_in_payment_type,
                              p.payment_request,
                              p.is_one_time,
                              p.creation_layout_id,
                              p.change_pricing_payment_time
                       FROM vnpt_dev.pricing p
                       WHERE p.status = 1 AND p.deleted_flag = 1 AND p.approve = 1 AND (p.id IN ( SELECT max(p_1.id) AS max
                                                                                                  FROM vnpt_dev.pricing_draft pd
                                                                                                           JOIN vnpt_dev.pricing p_1 ON pd.id = p_1.pricing_draft_id AND pd.deleted_flag = 1
                                                                                                  GROUP BY pd.id))) pricing ON pcount.service_id = pricing.service_id AND (pricing.id IN ( SELECT max(pricing_1.id) AS id
                                                                                                                                                                                           FROM vnpt_dev.pricing pricing_1
                                                                                                                                                                                           WHERE pricing_1.status = 1 AND pricing_1.approve = 1 AND pricing_1.deleted_flag = 1
                                                                                                                                                                                           GROUP BY pricing_1.pricing_draft_id)) AND (pricing.pricing_draft_id IN ( SELECT pricing_draft.id
                                                                                                                                                                                                                                                                    FROM vnpt_dev.pricing_draft
                                                                                                                                                                                                                                                                    WHERE pricing_draft.deleted_flag = 1))
           LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.pricing_id = pricing.id AND pricing_multi_plan.deleted_flag = 1 AND pricing_multi_plan.display_status = 1
  WHERE services.deleted_flag = 1 AND services.status = 1 AND services.service_owner IN (4,5) AND services.approve = 1 AND (pricing.id IS NOT NULL OR services.product_type = 1) AND (services.product_type = 1 OR (services.product_type <> 1 OR services.product_type IS NULL) AND pcount.count <> 0) AND (services.categories_app IS NOT NULL OR services.categories_id IS NOT NULL)
  ORDER BY services.id, pricing.id, pricing_multi_plan.id)
UNION ALL
( SELECT 1 AS calculate_type,
         string_agg(services.pre_order_url::text, ', '::text) AS pre_order_url,
         concat(combo.id, '0001')::bigint AS service_unique_id,
         combo.id AS services_id,
         cp.id AS pricing_id,
         concat(cp.id, '0001')::bigint AS pricing_unique_id,
         NULL::bigint AS pricing_multi_plan_id,
         combo.combo_name AS service_name,
         NULL::smallint AS product_type,
         null::smallint as classification,
         combo_pricing.object_type AS object_type,
         CASE
             WHEN combo.combo_owner = 0 THEN 'VNPT'
             WHEN combo.combo_owner = 1 THEN 'VNPT'
             WHEN combo.combo_owner = 2 THEN 'OTHER'
             WHEN combo.combo_owner = 3 THEN 'OTHER'
             WHEN combo.combo_owner = 4 THEN 'APP'
             WHEN combo.combo_owner = 5 THEN 'APP'
             ELSE 'OTHER'
             END AS combo_type,
         combo.combo_draft_id AS draft_id,
         NULL::bigint AS pricing_default,
         NULL::bigint AS pricing_draft_id,
         string_to_array(combo.categories_id::text, ','::text)::bigint[] AS lst_category,
         COALESCE(combo.combo_owner::integer, 3) = ANY (ARRAY[0, 1]) AS is_on,
         combo.user_id AS provider_id,
         cp.combo_name AS pricing_name,
         COALESCE(cp.number_of_trial::integer, 0) AS number_of_trial,
         COALESCE(cp.trial_type::integer, 0) AS trial_type,
         CASE
             WHEN COALESCE(cp.number_of_trial::integer, 0) > 0 THEN 1
             ELSE 0
             END AS is_trial,
         combo.allow_multi_sub,
         string_to_array(translate(combo.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS lst_customer_type,
         string_to_array(translate(cp.customer_type_code::text, '[]"'::text, ''::text), ','::text) AS pricing_lst_customer_type,
         '{HKD,KHDN,CN}'::text[] AS pricing_multi_plan_lst_customer_type,
         CASE
             WHEN cp.cycle_type = 0 THEN concat(cp.payment_cycle, ' ngày')
             WHEN cp.cycle_type = 1 THEN concat(cp.payment_cycle, ' tuần')
             WHEN cp.cycle_type = 2 THEN concat(cp.payment_cycle, ' tháng')
             WHEN cp.cycle_type = 3 THEN concat(cp.payment_cycle, ' năm')
             ELSE NULL::text
             END AS payment_cycle,
         1 AS is_one_time,
         combo.combo_owner AS service_owner
  FROM vnpt_dev.combo
           LEFT JOIN vnpt_dev.users ON users.id = combo.created_by AND users.deleted_flag = 1 AND users.status = 1
           LEFT JOIN ( SELECT count(cpricing.id) AS count,
                              cpricing.combo_id
                       FROM ( SELECT max(combo_plan.id) AS id,
                                     combo_plan.combo_id
                              FROM vnpt_dev.combo_plan
                              WHERE combo_plan.deleted_flag = 1 AND combo_plan.status = 1 AND combo_plan.approve = 1
                              GROUP BY combo_plan.combo_plan_draft_id, combo_plan.combo_id) cpricing
                       GROUP BY cpricing.combo_id) cpcount ON cpcount.combo_id = combo.id
           LEFT JOIN vnpt_dev.combo_plan cp ON cpcount.combo_id = cp.combo_id AND (cp.id IN ( SELECT max(combo_plan.id) AS max
                                                                                              FROM vnpt_dev.combo_plan
                                                                                              WHERE combo_plan.deleted_flag = 1 AND combo_plan.status = 1 AND combo_plan.approve = 1
                                                                                              GROUP BY combo_plan.combo_plan_draft_id))
           LEFT JOIN vnpt_dev.combo_pricing ON combo_pricing.id_combo_plan = cp.id
           LEFT JOIN vnpt_dev.pricing ON pricing.id = combo_pricing.object_id
           LEFT JOIN vnpt_dev.services ON services.id = pricing.service_id
  WHERE combo.deleted_flag = 1 AND combo.status = 1 AND combo.approve = 1 AND combo.combo_owner IN (6,7,8) AND cpcount.count <> 0
  GROUP BY combo.id, cp.id, cp.combo_name, cp.number_of_trial, cp.trial_type, cp.customer_type_code, cp.cycle_type, cp.payment_cycle, combo_pricing.object_type
  ORDER BY combo.id, cp.id);