---- BUILDER_TEMPLATE ----
ALTER TABLE vnpt_dev.builder_template
ADD COLUMN IF NOT EXISTS page_type varchar(20) NULL;
COMMENT ON COLUMN vnpt_dev.builder_template.page_type IS '<PERSON>ân loại giao diện: <PERSON>rang chủ (HOME_PAGE), Landing page (LANDING_PAGE)';

UPDATE vnpt_dev.builder_template
SET page_type = 'HOME_PAGE'
WHERE page_type is null;

-- Reset the sequence to continue from the max ID + 1
SELECT setval('vnpt_dev.builder_template_id_seq', (SELECT MAX(id) FROM vnpt_dev.builder_template), true);

INSERT INTO "vnpt_dev"."builder_template"("content", "user_id", "created_by", "modified_by", "created_at", "modified_at", "deleted_flag", "status", "name", "version", "customer_type", "page_type")
VALUES
(null, null, null, 1, null, '2025-08-08 08:56:00.000', 1, null, 'Home - Affiliate - IoT', 2, 'AF', 'LANDING_PAGE'),
(null, null, null, 1, null, '2025-08-08 08:56:00.000', 1, null, 'Home - Affiliate - oneSME', 2, 'AF', 'LANDING_PAGE'),
(null, null, null, 1, null, '2025-08-08 08:56:00.000', 1, null, 'Home  -Affiliate - GENZ', 2, 'AF', 'LANDING_PAGE'),
(null, null, null, 1, null, '2025-08-08 08:56:00.000', 1, null, 'Home - Affiliate - Hộ gia đình', 2, 'AF', 'LANDING_PAGE'),
(null, null, null, 1, null, '2025-08-08 08:56:00.000', 1, null, 'Home - Affiliate - Thanh thiếu niên', 2, 'AF', 'LANDING_PAGE');

---- PAGE_BUILDER ----
ALTER TABLE vnpt_dev.page_builder
ADD COLUMN IF NOT EXISTS page_type varchar(20) NULL;
COMMENT ON COLUMN vnpt_dev.page_builder.page_type IS 'Phân loại giao diện: Trang chủ (HOME_PAGE), Landing page (LANDING_PAGE)';

ALTER TABLE vnpt_dev.page_builder
ADD COLUMN IF NOT EXISTS owner_id int8 NULL;
COMMENT ON COLUMN vnpt_dev.page_builder.owner_id IS 'Id người sở hữu (affiliate_user.id)';

UPDATE vnpt_dev.page_builder
SET page_type = 'HOME_PAGE'
WHERE page_type is null;
