DROP VIEW IF EXISTS "vnpt_dev"."feature_view_shopping_cart_get_list_billing";
CREATE OR REPLACE VIEW "vnpt_dev"."feature_view_shopping_cart_get_list_billing" AS
WITH billing_amount AS (
    SELECT bill_item.billing_id,
           sum(bill_item.amount_after_tax) AS amount_after_tax
    FROM vnpt_dev.bill_item
    GROUP BY bill_item.billing_id
), subscription_billing AS (
    SELECT COALESCE(billings_1.cart_code, billings_1.billing_code) AS billing_code,
           array_agg(subscriptions_1.id) AS sub_id_array
    FROM (vnpt_dev.billings billings_1
        LEFT JOIN vnpt_dev.subscriptions subscriptions_1 ON ((billings_1.subscriptions_id = subscriptions_1.id)))
    GROUP BY COALESCE(billings_1.cart_code, billings_1.billing_code)
)
SELECT subscription_billing.sub_id_array,
       full_data.billings_id,
       full_data.billing_code,
       full_data.customer_name,
       full_data.service_name,
       full_data.pricing_name,
       full_data.sub_code,
       full_data.amount_after_tax,
       full_data.amount_after_refund,
       full_data.current_payment_date,
       full_data.payment_date,
       CASE
           WHEN (full_data.is_one_time = 0) THEN full_data.payment_date
           ELSE full_data.required_payment_date
           END AS required_payment_date,
       full_data.payment_status,
       full_data.customer_type,
       full_data.user_id,
       full_data.province_id,
       full_data.tax_code,
       full_data.personal_cert_number,
       full_data.is_combo,
       full_data.is_on,
       full_data.service_type,
       full_data.create_source,
       full_data.migrate_time,
       full_data.migrate_code,
       full_data.billing_province,
       full_data.is_cart,
       full_data.is_create,
       full_data.provider_id,
       full_data.created_at,
       full_data.service_owner,
       full_data.service_owner_partner,
       full_data.variant_draft_id,
       full_data.is_buy_service,
       full_data.is_only_service,
       full_data.is_one_time,
       full_data.subscriptions_id,
       full_data.payment_method
FROM (( SELECT DISTINCT COALESCE(billings.cart_code, billings.billing_code) AS billing_code,
                        CASE
                            WHEN ((users.customer_type)::text = 'CN'::text) THEN (concat_ws(' '::text, users.last_name, users.first_name))::character varying
                            ELSE users.name
                            END AS customer_name,
                        COALESCE(combo.combo_name, services.service_name) AS service_name,
                        COALESCE(combo_plan.combo_name, pricing.pricing_name) AS pricing_name,
                        COALESCE(subscriptions.cart_code, (concat('ID', to_char(subscriptions.id, 'FM09999999'::text)))::character varying) AS sub_code,
                        CASE
                            WHEN (billings.action_type = 1) THEN billings.total_amount_after_adjustment
                            ELSE billing_amount.amount_after_tax
                            END AS amount_after_tax,
                        billings.current_payment_date,
                        billings.total_amount_after_adjustment AS amount_after_refund,
                        billings.payment_date,
                        CASE
                            WHEN (billings.action_type = ANY (ARRAY[2, 5])) THEN COALESCE(billings.payment_date, (billings.current_payment_date)::timestamp without time zone)
                            WHEN ((subscriptions.reactive_date IS NOT NULL) AND (COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 0)) THEN (((subscriptions.reactive_date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '1 day'::interval)))::date)::timestamp without time zone
                            WHEN ((subscriptions.reactive_date IS NOT NULL) AND (COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 1)) THEN (((subscriptions.reactive_date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '7 days'::interval)))::date)::timestamp without time zone
                            WHEN ((subscriptions.reactive_date IS NOT NULL) AND (COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 2)) THEN (((subscriptions.reactive_date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '1 mon'::interval)))::date)::timestamp without time zone
                            WHEN ((subscriptions.reactive_date IS NOT NULL) AND (COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 3)) THEN (((subscriptions.reactive_date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '1 year'::interval)))::date)::timestamp without time zone
                            WHEN ((COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 0)) THEN ((((subscriptions.created_at)::date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '1 day'::interval)))::date)::timestamp without time zone
                            WHEN ((COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 1)) THEN ((((subscriptions.created_at)::date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '7 days'::interval)))::date)::timestamp without time zone
                            WHEN ((COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 2)) THEN ((((subscriptions.created_at)::date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '1 mon'::interval)))::date)::timestamp without time zone
                            WHEN ((COALESCE(combo_plan.combo_plan_type, pricing.pricing_type) = 1) AND (COALESCE(combo_plan.cycle_type, pricing.cycle_type) = 2)) THEN ((((subscriptions.created_at)::date + (((COALESCE(combo_plan.payment_cycle, pricing.payment_cycle) * billings.current_cycle))::double precision * '1 year'::interval)))::date)::timestamp without time zone
                            ELSE (billings.current_payment_date)::timestamp without time zone
                            END AS required_payment_date,
                        billings.status AS payment_status,
                        billings.subscriptions_id,
                        billings.payment_method,
                        users.customer_type,
                        users.id AS user_id,
                        users.province_id,
                        users.tin AS tax_code,
                        users.rep_personal_cert_number AS personal_cert_number,
                        (combo.id IS NOT NULL) AS is_combo,
                        (COALESCE(combo.combo_owner, services.service_owner) = ANY (ARRAY[0, 1])) AS is_on,
                        CASE
                            WHEN (combo.id IS NOT NULL) THEN 3
                            WHEN (services.service_owner = ANY (ARRAY[0, 1])) THEN 0
                            WHEN (services.service_owner = ANY (ARRAY[2, 3])) THEN 1
                            ELSE NULL::integer
                            END AS service_type,
                        CASE
                            WHEN (subscriptions.created_source_migration = 1) THEN 5
                            WHEN (subscriptions.traffic_id IS NOT NULL) THEN 3
                            WHEN (subscriptions.employee_code IS NOT NULL) THEN 2
                            WHEN (subscriptions.portal_type = ANY (ARRAY[1, 2])) THEN 4
                            ELSE 1
                            END AS create_source,
                        subscriptions.migrate_time,
                        subscriptions.migrate_code,
                        billings.province_id AS billing_province,
                        (billings.cart_code IS NOT NULL) AS is_cart,
                        ((billings.action_type IS NULL) OR (billings.action_type = '-1'::integer)) AS is_create,
                        billings.id AS billings_id,
                        COALESCE(combo.user_id, services.user_id) AS provider_id,
                        billings.created_at,
                        COALESCE(services.service_owner, combo.combo_owner) AS service_owner,
                        subscriptions.is_one_time,
                        subscriptions.variant_draft_id,
                        ((subscriptions.variant_draft_id IS NOT NULL) OR subscriptions.is_buy_service OR subscriptions.is_only_service) AS is_buy_service,
                        subscriptions.is_only_service,
                        services.service_owner_partner
        FROM (((((((vnpt_dev.billings
            LEFT JOIN billing_amount ON ((billing_amount.billing_id = billings.id)))
            LEFT JOIN vnpt_dev.subscriptions ON ((subscriptions.id = billings.subscriptions_id)))
            LEFT JOIN vnpt_dev.users ON ((users.id = subscriptions.user_id)))
            LEFT JOIN vnpt_dev.pricing ON ((pricing.id = subscriptions.pricing_id)))
            LEFT JOIN vnpt_dev.combo_plan ON ((combo_plan.id = subscriptions.combo_plan_id)))
            LEFT JOIN vnpt_dev.services ON ((services.id = subscriptions.service_id)))
            LEFT JOIN vnpt_dev.combo ON ((combo.id = combo_plan.combo_id)))
        WHERE ((subscriptions.deleted_flag = 1) AND (users.deleted_flag = 1) AND (billings.status = ANY (ARRAY[0, 1, 2, 3, 4])))) full_data
    LEFT JOIN subscription_billing ON (((subscription_billing.billing_code)::text = (full_data.billing_code)::text)));