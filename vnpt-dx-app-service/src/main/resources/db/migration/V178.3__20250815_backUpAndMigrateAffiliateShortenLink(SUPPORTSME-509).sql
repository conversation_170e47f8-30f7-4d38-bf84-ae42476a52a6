alter table "vnpt_dev"."affiliate_link" add column if not exists "external_shorten_link" varchar(50);
comment on column "vnpt_dev"."affiliate_link"."external_shorten_link" is 'Shorten link backup (đã tạo ra bởi các provider bên ngo<PERSON>i như bom.so, bizfly, ...)';
-- backup các shorten link cũ --
update "vnpt_dev"."affiliate_link" set "external_shorten_link" = "shorten_link";

-- reset data cột shorten_link và resize column vì domain có thể dài --
update "vnpt_dev"."affiliate_link" set "shorten_link" = null;
-- drop view before alter column size --
drop view if exists vnpt_dev.feature_view_get_affiliate_link;
DROP view if exists vnpt_dev.feature_view_get_affiliate_link_new;
alter table "vnpt_dev"."affiliate_link" alter column "shorten_link" type varchar(100);
comment on column "vnpt_dev"."affiliate_link"."shorten_link" is 'Shorten link của provider là Vnpt';

-- re-create lại view feature_view_get_affiliate_link bỏ khai báo return cụ thể --
create view vnpt_dev.feature_view_get_affiliate_link
as (
SELECT affiliate_link.id,
       affiliate_link.link_code,
       affiliate_link.source_type,
       affiliate_link.source_object_type,
       affiliate_link.source_object_id,
       affiliate_link.shorten_link,
       affiliate_link.distributed_link,
       affiliate_link.start_time,
       affiliate_link.end_time,
       affiliate_link.status,
       affiliate_link.created_by,
       affiliate_link.assigned_to,
       affiliate_link.created_at,
       affiliate_link.marketing_channel,
       affiliate_link.distributed_domain,
       affiliate_link.source_link,
       affiliate_link.link_config,
       service_draft_cte.service_name AS product_name
FROM vnpt_dev.affiliate_link
         LEFT JOIN LATERAL ( SELECT services_draft.service_name
                             FROM vnpt_dev.services_draft
                             WHERE services_draft.service_id = affiliate_link.source_object_id
                               AND services_draft.deleted_flag = 1
                               AND services_draft.approve = 1
                             ORDER BY services_draft.id DESC
                             LIMIT 1) service_draft_cte ON true
WHERE affiliate_link.source_type = 1
  AND affiliate_link.source_object_type = 0
  AND affiliate_link.deleted_flag = 1
  AND service_draft_cte.service_name::text <> ''::text
UNION ALL
SELECT affiliate_link.id,
       affiliate_link.link_code,
       affiliate_link.source_type,
       affiliate_link.source_object_type,
       affiliate_link.source_object_id,
       affiliate_link.shorten_link,
       affiliate_link.distributed_link,
       affiliate_link.start_time,
       affiliate_link.end_time,
       affiliate_link.status,
       affiliate_link.created_by,
       affiliate_link.assigned_to,
       affiliate_link.created_at,
       affiliate_link.marketing_channel,
       affiliate_link.distributed_domain,
       affiliate_link.source_link,
       affiliate_link.link_config,
       combo.combo_name AS product_name
FROM vnpt_dev.affiliate_link
         JOIN vnpt_dev.combo ON affiliate_link.source_object_id = combo.id AND affiliate_link.source_type = 1 AND
                                affiliate_link.source_object_type = 1
WHERE affiliate_link.deleted_flag = 1
  AND combo.deleted_flag = 1
UNION ALL
SELECT affiliate_link.id,
       affiliate_link.link_code,
       affiliate_link.source_type,
       affiliate_link.source_object_type,
       affiliate_link.source_object_id,
       affiliate_link.shorten_link,
       affiliate_link.distributed_link,
       affiliate_link.start_time,
       affiliate_link.end_time,
       affiliate_link.status,
       affiliate_link.created_by,
       affiliate_link.assigned_to,
       affiliate_link.created_at,
       affiliate_link.marketing_channel,
       affiliate_link.distributed_domain,
       affiliate_link.source_link,
       affiliate_link.link_config,
       ''::character varying AS product_name
FROM vnpt_dev.affiliate_link
WHERE (affiliate_link.source_type = ANY (ARRAY [2, 3]))
  AND affiliate_link.deleted_flag = 1
UNION ALL
SELECT affiliate_link.id,
       affiliate_link.link_code,
       affiliate_link.source_type,
       affiliate_link.source_object_type,
       affiliate_link.source_object_id,
       affiliate_link.shorten_link,
       affiliate_link.distributed_link,
       affiliate_link.start_time,
       affiliate_link.end_time,
       affiliate_link.status,
       affiliate_link.created_by,
       affiliate_link.assigned_to,
       affiliate_link.created_at,
       affiliate_link.marketing_channel,
       affiliate_link.distributed_domain,
       affiliate_link.source_link,
       affiliate_link.link_config,
       CASE
           WHEN affiliate_link.source_object_type = 0 THEN se.service_name
           WHEN affiliate_link.source_object_type = 1 THEN combo.combo_name
           ELSE ''::character varying
           END AS product_name
FROM vnpt_dev.affiliate_link
         LEFT JOIN vnpt_dev.pricing
                   ON affiliate_link.source_object_id = pricing.id AND affiliate_link.source_object_type = 0 AND
                      pricing.deleted_flag = 1
         LEFT JOIN vnpt_dev.services se ON pricing.service_id = se.id AND se.deleted_flag = 1
         LEFT JOIN vnpt_dev.combo_plan
                   ON affiliate_link.source_object_id = combo_plan.id AND affiliate_link.source_object_type = 1 AND
                      combo_plan.deleted_flag = 1
         LEFT JOIN vnpt_dev.combo ON combo_plan.combo_id = combo.id AND combo.deleted_flag = 1
WHERE affiliate_link.deleted_flag = 1
  AND affiliate_link.source_type = 4);


-- recreate view feature_view_get_affiliate_link_new
CREATE VIEW vnpt_dev.feature_view_get_affiliate_link_new
AS (SELECT affiliate_link.id,
          affiliate_link.link_code,
          affiliate_link.created_code,
          affiliate_link.updated_code,
          affiliate_link.source_type,
          affiliate_link.source_object_type,
          affiliate_link.source_object_id,
          affiliate_link.shorten_link,
          affiliate_link.distributed_link,
          affiliate_link.start_time,
          affiliate_link.end_time,
          affiliate_link.status,
          affiliate_link.created_by,
          affiliate_link.assigned_to,
          affiliate_link.created_at,
          affiliate_link.modified_at,
          affiliate_link.marketing_channel,
          affiliate_link.distributed_domain,
          affiliate_link.source_link,
          affiliate_link.link_config,
          service_draft_cte.service_name AS product_name
   FROM vnpt_dev.affiliate_link
            LEFT JOIN LATERAL ( SELECT services_draft.service_name
                                FROM vnpt_dev.services_draft
                                WHERE services_draft.service_id = affiliate_link.source_object_id AND services_draft.deleted_flag = 1 and services_draft.approve = 1
                                ORDER BY services_draft.id DESC
                                LIMIT 1) service_draft_cte ON true
   WHERE affiliate_link.source_type = 1 AND affiliate_link.source_object_type = 0 AND affiliate_link.deleted_flag = 1 AND service_draft_cte.service_name::text <> ''::text
   UNION ALL
   SELECT affiliate_link.id,
          affiliate_link.link_code,
          affiliate_link.created_code,
          affiliate_link.updated_code,
          affiliate_link.source_type,
          affiliate_link.source_object_type,
          affiliate_link.source_object_id,
          affiliate_link.shorten_link,
          affiliate_link.distributed_link,
          affiliate_link.start_time,
          affiliate_link.end_time,
          affiliate_link.status,
          affiliate_link.created_by,
          affiliate_link.assigned_to,
          affiliate_link.created_at,
          affiliate_link.modified_at,
          affiliate_link.marketing_channel,
          affiliate_link.distributed_domain,
          affiliate_link.source_link,
          affiliate_link.link_config,
          combo.combo_name AS product_name
   FROM vnpt_dev.affiliate_link
            JOIN vnpt_dev.combo ON affiliate_link.source_object_id = combo.id AND affiliate_link.source_type = 1 AND affiliate_link.source_object_type = 1
   WHERE affiliate_link.deleted_flag = 1 AND combo.deleted_flag = 1
   UNION ALL
   SELECT affiliate_link.id,
          affiliate_link.link_code,
          affiliate_link.created_code,
          affiliate_link.updated_code,
          affiliate_link.source_type,
          affiliate_link.source_object_type,
          affiliate_link.source_object_id,
          affiliate_link.shorten_link,
          affiliate_link.distributed_link,
          affiliate_link.start_time,
          affiliate_link.end_time,
          affiliate_link.status,
          affiliate_link.created_by,
          affiliate_link.assigned_to,
          affiliate_link.created_at,
          affiliate_link.modified_at,
          affiliate_link.marketing_channel,
          affiliate_link.distributed_domain,
          affiliate_link.source_link,
          affiliate_link.link_config,
          ''::character varying AS product_name
   FROM vnpt_dev.affiliate_link
   WHERE (affiliate_link.source_type = ANY (ARRAY[2, 3])) AND affiliate_link.deleted_flag = 1
   UNION ALL
   SELECT affiliate_link.id,
          affiliate_link.link_code,
          affiliate_link.created_code,
          affiliate_link.updated_code,
          affiliate_link.source_type,
          affiliate_link.source_object_type,
          affiliate_link.source_object_id,
          affiliate_link.shorten_link,
          affiliate_link.distributed_link,
          affiliate_link.start_time,
          affiliate_link.end_time,
          affiliate_link.status,
          affiliate_link.created_by,
          affiliate_link.assigned_to,
          affiliate_link.created_at,
          affiliate_link.modified_at,
          affiliate_link.marketing_channel,
          affiliate_link.distributed_domain,
          affiliate_link.source_link,
          affiliate_link.link_config,
          CASE
              WHEN affiliate_link.source_object_type = 0 THEN service_draft_cte.service_name
              WHEN affiliate_link.source_object_type = 1 THEN combo.combo_name
              ELSE ''::character varying
              END AS product_name
   FROM vnpt_dev.affiliate_link
            LEFT JOIN vnpt_dev.pricing ON affiliate_link.source_object_id = pricing.id AND affiliate_link.source_object_type = 0 AND pricing.deleted_flag = 1
            LEFT JOIN LATERAL ( SELECT services_draft.service_name
                                FROM vnpt_dev.services_draft
                                WHERE services_draft.service_id = pricing.service_id AND services_draft.deleted_flag = 1
                                ORDER BY services_draft.id DESC
                                LIMIT 1) service_draft_cte ON true
            LEFT JOIN vnpt_dev.combo_plan ON affiliate_link.source_object_id = combo_plan.id AND affiliate_link.source_object_type = 1 AND combo_plan.deleted_flag = 1
            LEFT JOIN vnpt_dev.combo ON combo_plan.combo_id = combo.id AND combo.deleted_flag = 1
   WHERE affiliate_link.deleted_flag = 1 AND affiliate_link.source_type = 4);
