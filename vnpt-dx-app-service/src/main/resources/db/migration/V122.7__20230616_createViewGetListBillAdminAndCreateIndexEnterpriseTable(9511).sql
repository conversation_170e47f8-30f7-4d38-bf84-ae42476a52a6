--<PERSON><PERSON><PERSON> c<PERSON>t assigned_id ở bảng enterprise
drop index if exists vnpt_dev.enterprise_assignee_id_idx;
create index enterprise_assignee_id_idx on vnpt_dev.enterprise (assignee_id);

--Tạo view mới
drop view if exists vnpt_dev.view_get_list_billing;
create or replace view vnpt_dev.view_get_list_billing as
select
    coalesce(mBill.cart_code, mBill.billing_code) as billing_code,
    mBill.status as payment_status,
    mBill.id as billings_id,
    mBill.province_id as billing_province, 
    --<PERSON><PERSON><PERSON> thông tin cần lấy ra ở bảng subscription để filter
    coalesce(mSub.cart_code, concat('ID', to_char(mSub.id, 'FM09999999'))) as sub_code,
    mSub.assignee_id as sub_assignee_id,
    mSub.lst_assignees_id as sub_lst_assignees_id,
    --<PERSON><PERSON><PERSON> thông tin cần lấy ra ở bảng user để filter 
    case
        when mUser.customer_type = 'CN' then concat_ws(' ', mUser.last_name, mUser.first_name)
        else mUser.name
    end as customer_name,
    mUser.id as user_id,
    mUser.tin as tax_code,
    mUser.rep_personal_cert_number as personal_cert_number,
    mUser.customer_type,
    mUser.assignee_id as user_assignee_id,
    --Các thông tin cần lấy ở bảng service, combo để filter
    coalesce(mCombo.user_id, mService.user_id) as provider_id,
    coalesce(mService.service_owner, mCombo.combo_owner) as service_owner,
    mService.service_owner_partner,
    case 
        when coalesce(mCombo.combo_owner, mService.service_owner) between 0 and 1 then true
        else false
    end as is_on,
    (mcombo.id is not null) as is_combo,
    coalesce(mCombo.combo_name, mService.service_name) AS service_name
from vnpt_dev.billings mBill
    left join vnpt_dev.subscriptions mSub on mSub.id = mBill.subscriptions_id
    left join vnpt_dev.pricing mPricing on mPricing.id = mSub.pricing_id
    left join vnpt_dev.services mService on mService.id = mPricing.service_id
    left join vnpt_dev.combo_plan mComboPlan on mComboPlan.id = mSub.combo_plan_id
    left join vnpt_dev.combo mCombo on mCombo.id = mComboPlan.combo_id
    left join vnpt_dev.users mUser on mUser.id = mSub.user_id 
where 
    --Điều kiện bill, sub và user theo view cũ: feature_view_shopping_cart_get_list_billing
    (mBill.status between 0 and 4) and 
    mSub.deleted_flag = 1 and mUser.deleted_flag = 1;
-- Thêm view lấy danh sách thuê bao
drop view if exists vnpt_dev.feature_view_get_all_sub ;
create or replace view vnpt_dev.feature_view_get_all_sub as
SELECT subscriptions.id AS sub_id,
       COALESCE(subscriptions.cart_code, concat('ID', to_char(subscriptions.id, 'FM09999999'::text))::character varying) AS sub_code,
       CASE
           WHEN users.customer_type::text = 'CN'::text THEN concat_ws(' '::text, users.last_name, users.first_name)::character varying
            ELSE users.name
END AS customer_name,
    COALESCE(users.customer_type, 'KHDN'::character varying) AS customer_type,
    COALESCE(combo.provider, services.provider) AS provider,
    COALESCE(combo.user_id, services.user_id) AS provider_id,
    COALESCE(combo.id * 10 + 1, services.id * 10) AS service_id,
    subscriptions.status AS sub_status,
    combo.id IS NOT NULL AS is_combo,
    COALESCE(combo.combo_owner, services.service_owner) = ANY (ARRAY[0, 1]) AS is_on,
    users.tin AS tax_code,
    users.id AS user_id,
    users.rep_personal_cert_number AS personal_cert_number,
    users.email,
    users.phone_number AS phone,
    users.rep_fullname AS rep_name,
    users.province_id,
    concat_ws(' '::text, assignee.last_name, assignee.first_name) AS assignee_name,
    enterprise.founding_date,
    enterprise.business_area_id,
    enterprise.business_size_id,
    subscriptions.created_at AS sub_created_at,
    billings.payment_date,
    sme_progress.name AS status_name,
    sme_progress.id AS status_id,
    COALESCE(pricing_multi_plan.circle_type, COALESCE(combo_plan.cycle_type, pricing.cycle_type)) AS cycle_type,
    COALESCE(pricing_multi_plan.payment_cycle, COALESCE(combo_plan.payment_cycle::bigint, pricing.payment_cycle::bigint)) AS payment_cycle,
    COALESCE(subscriptions.total_amount_after_refund, subscriptions.total_amount, 0::double precision) AS total_amount,
    billings.province_id AS billing_province,
        CASE
            WHEN subscriptions.created_source_migration = 1 THEN 5
            WHEN subscriptions.traffic_id IS NOT NULL THEN 3
            WHEN subscriptions.employee_code IS NOT NULL THEN 2
            WHEN subscriptions.portal_type = ANY (ARRAY[1, 2]) THEN 4
            ELSE 1
END AS create_source,
    COALESCE(combo_plan.combo_name, pricing.pricing_name) AS pricing_name,
    COALESCE(pricing_multi_plan.number_of_cycles, pricing.number_of_cycles) AS number_of_cycles,
    subscriptions.number_of_cycles AS number_of_cycles_reactive,
    subscriptions.created_at,
    COALESCE(services.service_owner, combo.combo_owner) AS service_owner,
    COALESCE(combo.combo_name, services.service_name) AS service_name,
    services.service_owner_partner,
    subscriptions.reactive_status,
    subscriptions.installed AS sub_installed,
    subscriptions.dhsxkd_sub_code,
    subscriptions.start_current_cycle,
    subscriptions.end_current_cycle,
    subscriptions.reactive_date,
    subscriptions.current_cycle,
    subscriptions.migrate_time,
    subscriptions.next_payment_time,
    subscriptions.migrate_code,
    subscriptions.is_one_time,
    migration.id AS migration_id,
    order_service_receive.transaction_code,
    billings.id AS bill_id,
    enterprise.id AS enterprise_id,
    enterprise.assignee_id AS enterprise_assignee_id,
    subscriptions.assignee_id AS sub_assignee_id,
    subscriptions.lst_assignees_id,
    users.assignee_id AS user_assignee_id,
    business_area.name AS business_area_name,
    business_size.name AS business_size_name
   FROM vnpt_dev.subscriptions
     LEFT JOIN vnpt_dev.billings ON subscriptions.id = billings.subscriptions_id
     LEFT JOIN vnpt_dev.migration ON migration.code::text = subscriptions.migrate_code::text
     LEFT JOIN vnpt_dev.users ON users.id = subscriptions.user_id
     LEFT JOIN vnpt_dev.enterprise ON enterprise.user_id = users.id
     LEFT JOIN vnpt_dev.pricing ON pricing.id = subscriptions.pricing_id
     LEFT JOIN vnpt_dev.combo_plan ON combo_plan.id = subscriptions.combo_plan_id
     LEFT JOIN vnpt_dev.services ON services.id = pricing.service_id
     LEFT JOIN vnpt_dev.combo ON combo.id = combo_plan.combo_id
     LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.id = subscriptions.pricing_multi_plan_id
     LEFT JOIN vnpt_dev.order_service_receive ON order_service_receive.subscription_id = subscriptions.id AND subscriptions.service_id IS NOT NULL AND order_service_receive.service_id IS NOT NULL AND order_service_receive.combo_id IS NULL
     LEFT JOIN vnpt_dev.order_service_status ON order_service_status.id::character varying::text = order_service_receive.order_status::text
     LEFT JOIN vnpt_dev.sme_progress ON sme_progress.id = order_service_status.sme_progress_id
     LEFT JOIN vnpt_dev.business_area ON business_area.id = enterprise.business_area_id
     LEFT JOIN vnpt_dev.business_size ON business_size.id = enterprise.business_size_id
     LEFT JOIN vnpt_dev.users assignee ON assignee.id = subscriptions.assignee_id
  WHERE subscriptions.deleted_flag = 1 AND ((billings.status = ANY (ARRAY[0, 1, 2, 4])) OR billings.status IS NULL);

-- Thêm view ấy thông tin pricing, combo mới
DROP VIEW IF EXISTS vnpt_dev.feature_view_subscription_pricing_new;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_subscription_pricing_new AS
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.refer_subscription,
       p.pricing_name,
       se.service_name,
       se.id AS c_service_id,
       concat(se.id, '0000')::bigint AS service_unique_id,
        concat(p.id, '0000')::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       CASE
           WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 1
           WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 2
           WHEN se.service_owner IS NULL THEN 2
           ELSE NULL::integer
END AS subscription_type,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN 'ON'::text
            WHEN se.service_owner = ANY (ARRAY[2, 3]) THEN 'OS'::text
            WHEN se.service_owner IS NULL THEN 'OS'::text
            ELSE NULL::text
END AS onos,
    c.nation_id AS c_nation_id,
    c.nation_name AS c_nation_name,
    c.province_id AS c_province_id,
    c.province_name AS c_province_name,
    c.district_id AS c_district_id,
    c.district_name AS c_district_name,
    c.ward_id AS c_ward_id,
    c.ward_name AS c_ward_name,
    c.street_name,
    c.address AS c_address,
    c.name AS sme_name,
    c.email AS c_email,
    c.phone_number AS c_phone_number,
    c.tin,
    bi.status AS payment_status,
    bi.next_total_amount,
    bt.quantity AS service_quantity,
        CASE
            WHEN ss.id IS NULL THEN 0
            WHEN sr.order_status IS NULL THEN 0
            ELSE sm."order"
END AS order_status,
        CASE
            WHEN ss.id IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sr.order_status IS NULL THEN 'Đặt hàng thành công'::text
            WHEN sm."order" = 1 THEN 'Tiếp nhận đơn hàng'::text
            WHEN sm."order" = 2 THEN 'Đang triển khai'::text
            WHEN sm."order" = 3 THEN 'Hoàn thành'::text
            WHEN sm."order" = 4 THEN 'Hủy đơn hàng'::text
            ELSE NULL::text
END AS order_status_name,
        CASE
            WHEN se.service_owner = ANY (ARRAY[0, 1]) THEN s.status::integer
            WHEN ss.id IS NULL THEN 10
            WHEN sr.order_status IS NULL THEN 10
            WHEN sm."order" = '-1'::integer THEN '-1'::integer
            ELSE concat('1', sm."order")::integer
END AS c_status,
        CASE
            WHEN s.employee_code IS NOT NULL THEN 1
            WHEN s.traffic_id IS NOT NULL THEN 2
            WHEN s.portal_type = 1 THEN 3
            WHEN s.portal_type = 2 THEN 3
            WHEN s.portal_type = 3 THEN 0
            ELSE NULL::integer
END AS source,
    se.user_id AS provider_id,
    se.categories_id,
    ''::text AS categories_ids,
    un.name AS unit,
    sr.transaction_code,
    u.last_name,
    u.first_name,
    p.unit_id,
    p.price AS priceee,
    pmp.id AS pmp_id,
    pmp.unit_id AS pmp_unit_id,
    pmp.circle_type AS pmp_circle_type,
    pmp.payment_cycle AS pmp_payment_cycle,
    pmp.pricing_plan AS pmp_pricing_plan,
    pmp.price AS pmp_price
   FROM vnpt_dev.subscriptions s
     LEFT JOIN ( SELECT users.id,
            users.last_name,
            users.first_name
           FROM vnpt_dev.users) u ON s.registed_by = u.id
     JOIN vnpt_dev.pricing p ON s.pricing_id = p.id AND s.combo_plan_id IS NULL
     LEFT JOIN vnpt_dev.services se ON se.id = p.service_id
     LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON s.pricing_multi_plan_id = pmp.id AND s.pricing_multi_plan_id IS NOT NULL
     JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN ( SELECT b1.id,
            b1.subscriptions_id,
            b1.status,
            b1.next_total_amount
           FROM vnpt_dev.billings b1
             JOIN ( SELECT max(billings.id) AS id
                   FROM vnpt_dev.billings
                  GROUP BY billings.subscriptions_id) b2 ON b1.id = b2.id) bi ON bi.subscriptions_id = s.id
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.pricing_id AND bt.object_type = 0
     LEFT JOIN vnpt_dev.order_service_receive sr ON sr.subscription_id = s.id
     LEFT JOIN vnpt_dev.order_service_status ss ON ss.id = sr.order_status::bigint
     LEFT JOIN vnpt_dev.sme_progress sm ON sm.id = ss.sme_progress_id
     LEFT JOIN vnpt_dev.units un ON un.id = p.unit_id OR pmp.unit_id = un.id
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;

DROP VIEW IF EXISTS vnpt_dev.feature_view_subscription_combo_new;
CREATE OR REPLACE VIEW vnpt_dev.feature_view_subscription_combo_new AS
SELECT s.id,
       s.service_id,
       s.quantity,
       s.total_amount,
       s.status,
       s.deleted_flag,
       s.created_by,
       s.modified_by,
       s.created_at,
       s.modified_at,
       s.user_id,
       s.from_date,
       s.cancelled_time,
       s.pricing_id,
       s.sme_subscription_id,
       s.installed,
       s.expired_time,
       s.used_quantity,
       s.registed_by,
       s.sub_registration_id,
       s.started_at,
       s.start_charge_at,
       s.trial_day,
       s.reg_type,
       s.payment_method,
       s.confirm_status,
       s.current_cycle,
       s.phone_no,
       s.contact,
       s.address,
       s.sub_code,
       s.start_current_cycle,
       s.end_current_cycle,
       s.current_payment_date,
       s.dhsxkd_sub_code,
       s.next_payment_time,
       s.awaiting_cancel,
       s.pre_order,
       s.number_of_cycles,
       s.cycle_type,
       s.traffic_id,
       s.combo_plan_id,
       s.canceled_by,
       s.subscription_contract_id,
       s.called_trans,
       s.change_date,
       s.change_status,
       s.update_date,
       s.update_status,
       s.portal_type,
       s.message_setup,
       s.employee_code,
       s.pricing_multi_plan_id,
       s.refer_subscription,
       p.combo_name AS pricing_name,
       co.combo_name AS service_name,
       co.id AS c_service_id,
       concat(co.id, '0001')::bigint AS service_unique_id,
        concat(p.id, '0001')::bigint AS pricing_unique_id,
        p.payment_cycle AS p_payment_cycle,
       p.cycle_type AS p_cycle_type,
       p.price,
       3 AS subscription_type,
       'COMBO'::text AS onos,
        c.nation_id AS c_nation_id,
       c.nation_name AS c_nation_name,
       c.province_id AS c_province_id,
       c.province_name AS c_province_name,
       c.district_id AS c_district_id,
       c.district_name AS c_district_name,
       c.ward_id AS c_ward_id,
       c.ward_name AS c_ward_name,
       c.street_name,
       c.address AS c_address,
       c.name AS sme_name,
       c.email AS c_email,
       c.phone_number AS c_phone_number,
       c.tin,
       bi.status AS payment_status,
       bi.next_total_amount,
       bt.quantity AS service_quantity,
       '-1'::integer AS order_status,
        ''::text AS order_status_name,
        s.status AS c_status,
       CASE
           WHEN s.employee_code IS NOT NULL THEN 1
           WHEN s.traffic_id IS NOT NULL THEN 2
           WHEN s.portal_type = 1 THEN 3
           WHEN s.portal_type = 2 THEN 3
           WHEN s.portal_type = 3 THEN 0
           ELSE NULL::integer
END AS source,
    co.user_id AS provider_id,
    '-1'::integer AS categories_id,
    co.categories_id AS categories_ids,
    NULL::text AS unit,
    NULL::text AS transaction_code,
    u.last_name,
    u.first_name,
    p.unit_id,
    p.price AS priceee,
    NULL::bigint AS pmp_id,
    NULL::bigint AS pmp_unit_id,
    NULL::integer AS pmp_circle_type,
    NULL::integer AS pmp_payment_cycle,
    NULL::integer AS pmp_pricing_plan,
    NULL::bigint AS pmp_price
   FROM vnpt_dev.subscriptions s
     LEFT JOIN ( SELECT users.id,
            users.last_name,
            users.first_name
           FROM vnpt_dev.users) u ON s.registed_by = u.id
     JOIN vnpt_dev.combo_plan p ON s.combo_plan_id = p.id
     LEFT JOIN vnpt_dev.combo co ON p.combo_id = co.id
     LEFT JOIN vnpt_dev.report_view_customer c ON s.user_id = c.id
     LEFT JOIN ( SELECT b1.id,
            b1.subscriptions_id,
            b1.status,
            b1.next_total_amount
           FROM vnpt_dev.billings b1
             JOIN ( SELECT max(billings.id) AS id
                   FROM vnpt_dev.billings
                  GROUP BY billings.subscriptions_id) b2 ON b1.id = b2.id) bi ON bi.subscriptions_id = s.id
     LEFT JOIN vnpt_dev.bill_item bt ON bt.billing_id = bi.id AND bt.object_id = s.combo_plan_id AND bt.object_type = 1
  WHERE s.deleted_flag = 1 AND s.confirm_status = 1;