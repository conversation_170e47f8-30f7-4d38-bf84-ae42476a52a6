CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_actual_revenue_update"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2, "sub_type" text)
  RETURNS TABLE("amount" float8, "subcode" varchar) AS $BODY$
DECLARE
mQuery text; aQuery text;
        targetTypeText text;
        mResult float8;
		table_revenue_target text;
BEGIN
aQuery = CONCAT(' subscriptions_common_info as (
						select
							billings.id as bill_id,
							subscription_info.user_id,
							subscription_info.service_id,
							subscription_info.pricing_id,
							subscription_info.employee_code,
							case
							 when billings.action_type = null then 0
							 when billings.action_type = -1 then 0
							 when billings.action_type = 5 then 1
							 else 2
						  end as target_type,
							billings.action_type,
							round(COALESCE(bill_item.amount_pre_tax,0)) as amount,
							round(COALESCE(bill_item.amount_after_tax,0)) as amount_after_tax,
							case
								when bill_item.amount_incurred < 0 then 0
								else round(bill_item.amount_incurred)
							end as amount_incurred,
							(billings.total_amount - billings.total_amount_after_adjustment) as refund
						from vnpt_dev.billings
				left join subscription_info on billings.subscriptions_id = subscription_info.id
				left join vnpt_dev.bill_item on bill_item.billing_id = billings.id
				where billings.payment_date between CAST(''',start_time,''' AS DATE) AND CAST(''',end_time + interval '1 day' ,''' AS DATE)
				AND bill_item.object_type <> 3 AND billings.status = 2),
 ');
CASE object_type
WHEN 0
    THEN mQuery = CONCAT(mQuery,'
		with parttion_info as (
				select id as user_id
				from vnpt_dev.users
				left join (
				    select lst_am_id, lst_admin_id
					from vnpt_dev.crm_data_partition
					where id = (select partition_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
				) as part on users.assignee_id = ANY(part.lst_am_id) or users.assignee_id = ANY(part.lst_admin_id)
		),
			subscription_info as (
				select id, sub_code,user_id, service_id, pricing_id,employee_code from vnpt_dev.subscriptions
				where user_id in (SELECT user_id from parttion_info)
			),
		',aQuery,'
        view_employee_business as (
				select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
	    select distinct
	        subscriptions_common_info.bill_id,
	        	case
				when subscriptions_common_info.action_type = 1 and subscriptions_common_info.amount_after_tax = 0 then 0
					when subscriptions_common_info.action_type = 1 and subscriptions_common_info.amount_after_tax <> 0 then
					round(COALESCE(subscriptions_common_info.amount_incurred*(subscriptions_common_info.amount/subscriptions_common_info.amount_after_tax),0))
					else sum(COALESCE(subscriptions_common_info.amount,0)) end as amount,
	        subscriptions_common_info.target_type
	    from parttion_info
	    left join subscriptions_common_info on subscriptions_common_info.user_id = parttion_info.user_id
		group by subscriptions_common_info.bill_id,subscriptions_common_info.amount,subscriptions_common_info.amount_incurred,subscriptions_common_info.amount_after_tax, subscriptions_common_info.target_type,subscriptions_common_info.action_type,subscriptions_common_info.refund) as bill
		where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');

WHEN 1
    THEN mQuery = CONCAT(mQuery,'
		with admin_info as (
			select id as user_id from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),
		subscription_info as (
			select id, sub_code,user_id, service_id, pricing_id,employee_code from vnpt_dev.subscriptions
			where user_id in (SELECT user_id from admin_info)
		),
		service_info as (
			select id from vnpt_dev.services
			where id = (select service_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),',aQuery,'
		view_employee_business as (
		select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
			subscriptions_common_info.bill_id,
			case
				when subscriptions_common_info.action_type = 1 and subscriptions_common_info.amount_after_tax = 0 then 0
					when subscriptions_common_info.action_type = 1 and subscriptions_common_info.amount_after_tax <> 0 then
					round(COALESCE(subscriptions_common_info.amount_incurred*(subscriptions_common_info.amount/subscriptions_common_info.amount_after_tax),0))
					else sum(COALESCE(subscriptions_common_info.amount,0)) end as amount,
			subscriptions_common_info.target_type
			from admin_info
			left join subscriptions_common_info on admin_info.user_id = subscriptions_common_info.user_id
			join service_info on subscriptions_common_info.service_id = service_info.id and subscriptions_common_info.pricing_id is not null
					group by subscriptions_common_info.bill_id,subscriptions_common_info.amount,subscriptions_common_info.amount_incurred,subscriptions_common_info.amount_after_tax, subscriptions_common_info.target_type,subscriptions_common_info.action_type,subscriptions_common_info.refund) as bill
					where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
ELSE mQuery = CONCAT(mQuery,'
		with admin_info as (
			select id as user_id from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),
		subscription_info as (
			select id, sub_code,user_id, service_id, pricing_id,employee_code from vnpt_dev.subscriptions
			where user_id in (SELECT user_id from admin_info)
		),
		',aQuery,'
		view_employee_business as (
		  select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
				subscriptions_common_info.bill_id,
				case
					when subscriptions_common_info.action_type = 1 and subscriptions_common_info.amount_after_tax = 0 then 0
					when subscriptions_common_info.action_type = 1 and subscriptions_common_info.amount_after_tax <> 0 then
					round(COALESCE(subscriptions_common_info.amount_incurred*(subscriptions_common_info.amount/subscriptions_common_info.amount_after_tax),0))
					else sum(COALESCE(subscriptions_common_info.amount,0)) end as amount,
				subscriptions_common_info.target_type
			from admin_info
			left join subscriptions_common_info on subscriptions_common_info.user_id = admin_info.user_id
			group by subscriptions_common_info.bill_id,subscriptions_common_info.amount,subscriptions_common_info.amount_incurred,subscriptions_common_info.amount_after_tax, subscriptions_common_info.target_type,subscriptions_common_info.action_type,subscriptions_common_info.refund) as bill
			where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
END CASE;

case sub_type
when '' then sub_type = '0,1,2';
else sub_type = sub_type;
end case;

CASE target_type
WHEN 0 then mQuery = CONCAT(mQuery,'select round(COALESCE(sum(view_employee_business.amount),0)), String_agg(distinct view_employee_business.bill_id :: text,'','')::varchar
									from view_employee_business
									where view_employee_business.target_type in (',sub_type,') and view_employee_business.amount > 0 ');
WHEN 1 then
mQuery = CONCAT(mQuery,'select count(*)::float8, string_agg(distinct view_employee_business.bill_id :: text,'','')::varchar
									from view_employee_business
									where view_employee_business.target_type in (',sub_type,') and view_employee_business.amount > 0 ');
END CASE;
        RAISE NOTICE 'mQuery: %', mQuery;

return Query execute mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_email_revenue_update"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "sub_type" text)
  RETURNS TABLE("email" varchar) AS $BODY$
    DECLARE
mQuery text; aQuery text;
targetTypeText text;
        mResult float8;
				table_revenue_target text;
BEGIN
aQuery = CONCAT(' subscriptions_common_info as (
					select
						billings.id as bill_id,
						subscription_info.user_id,
						subscription_info.service_id,
						subscription_info.pricing_id,
						case
							 when billings.action_type = null then 0
							 when billings.action_type = -1 then 0
							 when billings.action_type = 5 then 1
							 else 2
						end as target_type
					from vnpt_dev.billings
				    left join subscription_info on billings.subscriptions_id = subscription_info.id
				    left join vnpt_dev.bill_item on bill_item.billing_id = billings.id
				    where billings.payment_date between CAST(''',start_time,''' AS DATE) AND CAST(''',end_time + interval '1 day' ,''' AS DATE)
				    AND bill_item.object_type <> 3), ');
CASE object_type
WHEN 0
    THEN mQuery = CONCAT(mQuery,'
		with partition_info as (
			select id as user_id, email
			from vnpt_dev.users
			left join (
				select lst_am_id, lst_admin_id
				from vnpt_dev.crm_data_partition
				where id = (select partition_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
			) as part on users.assignee_id = ANY(part.lst_am_id) or users.assignee_id = ANY(part.lst_admin_id)
		),
		subscription_info as (
			select id, sub_code,user_id, service_id, pricing_id,employee_code from vnpt_dev.subscriptions
			where user_id in (SELECT user_id from partition_info)
		),
		',aQuery,'
        view_employee_business as (
	        select distinct
	            subscriptions_common_info.bill_id,
	            partition_info.email,
	            subscriptions_common_info.target_type
	        from partition_info
	        left join subscriptions_common_info on subscriptions_common_info.user_id = partition_info.user_id
			group by subscriptions_common_info.bill_id,partition_info.email, subscriptions_common_info.target_type) ');

WHEN 1
   THEN mQuery = CONCAT(mQuery,'
		with admin_info as (
			select id as user_id, email from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),
		subscription_info as (
			select id, sub_code,user_id, service_id, pricing_id,employee_code from vnpt_dev.subscriptions
			where user_id in (SELECT user_id from admin_info)
		),
		service_info as (
			select id from vnpt_dev.services
			where id = (select service_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),',aQuery,'
		view_employee_business as (
			select distinct
				subscriptions_common_info.bill_id,
				admin_info.email,
				subscriptions_common_info.target_type
			from admin_info
			left join subscriptions_common_info on admin_info.user_id = subscriptions_common_info.user_id
			join service_info on subscriptions_common_info.service_id = service_info.id and subscriptions_common_info.pricing_id is not null
			group by subscriptions_common_info.bill_id,admin_info.email, subscriptions_common_info.target_type) ');
ELSE mQuery = CONCAT(mQuery,'
	with admin_info as (
		select id as user_id, email from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),
		subscription_info as (
			select id, sub_code,user_id, service_id, pricing_id,employee_code from vnpt_dev.subscriptions
			where user_id in (SELECT user_id from admin_info)
		),
		',aQuery,'
		view_employee_business as (
			select distinct
				subscriptions_common_info.bill_id,
				admin_info.email,
				subscriptions_common_info.target_type
			from admin_info
			left join subscriptions_common_info on subscriptions_common_info.user_id = admin_info.user_id
			group by subscriptions_common_info.bill_id,admin_info.email, subscriptions_common_info.target_type) ');

END CASE;

case sub_type
when '' then sub_type = '0,1,2';
else sub_type = sub_type;
end case;
 mQuery = CONCAT(mQuery,'select string_agg(distinct view_employee_business.email, '','') ::varchar
																		from view_employee_business
																		where view_employee_business.target_type in (',sub_type,') ');

        RAISE NOTICE 'mQuery: %', mQuery;

return Query execute mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;