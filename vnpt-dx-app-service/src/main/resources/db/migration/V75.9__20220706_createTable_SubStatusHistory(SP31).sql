-- Table structure for sub_status_history
DROP TABLE IF EXISTS "vnpt_dev"."sub_status_history";
CREATE TABLE "vnpt_dev"."sub_status_history" (
  "id" bigserial NOT NULL,
  "subscription_id" int8 NOT NULL,
  "status" int2 NOT NULL,
  "created_at" timestamp(6),
  "created_by" int8,
  "user_id" int8,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "vnpt_dev"."sub_status_history"."subscription_id" IS 'ID thuê bao';
COMMENT ON COLUMN "vnpt_dev"."sub_status_history"."status" IS 'Trạng thái thuê bao';
COMMENT ON COLUMN "vnpt_dev"."sub_status_history"."created_at" IS 'Thời điểm thay đổi trạng thái';
COMMENT ON COLUMN "vnpt_dev"."sub_status_history"."created_by" IS 'ID user thực hiện thay đổi trạng thái thuê bao';
COMMENT ON COLUMN "vnpt_dev"."sub_status_history"."user_id" IS 'ID khách hàng đăng ký thuê bao';
COMMENT ON TABLE "vnpt_dev"."sub_status_history" IS 'Thông tin lịch sử thay đổi trạng thái thuê bao';

-- Khởi tạo các giá trị đầu tiên
INSERT INTO vnpt_dev.sub_status_history (subscription_id, status, user_id, created_at, created_by)
    SELECT subscriptions.id, subscriptions.status, subscriptions.user_id, subscriptions.modified_at, COALESCE(subscriptions.modified_by, subscriptions.created_by)
    FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and confirm_status = 1