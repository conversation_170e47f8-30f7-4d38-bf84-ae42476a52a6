CREATE TABLE vnpt_dev.massoffer_reason
(
    id                        bigserial NOT NULL, 	-- Mã định danh
    province_code             varchar NULL,       	-- Mã tỉnh
    cancel_reason_id          bigserial NOT NULL,   -- Lý do hủy id
    "content"                 varchar NULL,         -- <PERSON><PERSON>i dung hủy
    CONSTRAINT massoffer_reason_pk PRIMARY KEY (id)
);

COMMENT
ON TABLE vnpt_dev.massoffer_reason IS 'Bảng lý do hủy massoffer';

-- <PERSON><PERSON><PERSON> comments

COMMENT
ON COLUMN vnpt_dev.massoffer_reason.id IS 'Mã định danh';
COMMENT
ON COLUMN vnpt_dev.massoffer_reason.province_code IS 'Mã tỉnh thành';
COMMENT
ON COLUMN vnpt_dev.massoffer_reason.cancel_reason_id IS 'Mã lý do hủy';
COMMENT
ON COLUMN vnpt_dev.massoffer_reason."content" IS 'Nội dung hủy';

-- Insert data

INSERT
INTO
    vnpt_dev.massoffer_reason (province_code,
                               cancel_reason_id,
                               CONTENT)
VALUES ('BDH',3,'<PERSON><PERSON><PERSON> cước cao'),
       ('BGG',3,'<PERSON>i<PERSON> cước cao'),
       ('BKN',3,'Giá cước cao'),
       ( 'BNH',3,'Giá cước cao'),
       ('BPC',3,'STB hỏng, phải mua mới'),
       ( 'BTE',3,'Khách hàng yêu cầu'),
       ( 'BTN',3,'Giá cước cao'),
       ( 'CBG',3,'Giá cước cao'),
       ( 'CMU',3,'Khách hàng yêu cầu'),
       ( 'CTO',3,'Khách hàng yêu cầu'),
       ( 'DBN',3,'Giá cước cao'),
       ( 'DLC',3,'Giá cước cao'),
       ( 'DNG',3,'Giá cước cao'),
       ( 'DNI',3,'Khách hàng yêu cầu'),
       ( 'DNO',3,'Giá cước cao'),
       ( 'DTP',3,'Giá cước cao'),
       ( 'GLI',3,'Giá cước cao'),
       ( 'HBH',3,'Giá cước cao'),
       ( 'HCM',3,'Khách hàng yêu cầu'),
       ( 'HDG',3,'Giá cước cao'),
       ( 'HGI',3,'STB hỏng, phải mua mới'),
       ( 'HNI',3,'Giá cước cao'),
       ( 'HNM',3,'Giá cước cao'),
       ( 'HPG',3,'Giá cước cao'),
       ( 'HTH',3,'Giá cước cao'),
       ( 'HUE',3,'Giá cước cao'),
       ( 'KGG',3,'Khách hàng yêu cầu'),
       ( 'KHA',3,'Giá cước cao'),
       ( 'KTM',3,'Giá cước cao'),
       ( 'LCU',3,'Giá cước cao'),
       ( 'NAN',3,'Giá cước cao'),
       ( 'NBH',3,'Giá cước cao'),
       ( 'NDH',3,'Giá cước cao'),
       ( 'NTN',3,'Giá cước cao'),
       ( 'PTO',3,'Giá cước cao'),
       ( 'PYN',3,'Giá cước cao'),
       ( 'QBH',3,'Giá cước cao'),
       ( 'QNH',3,'Giá cước cao'),
       ( 'QNI',3,'Giá cước cao'),
       ( 'QNM',3,'Giá cước cao'),
       ( 'QTI',3,'Giá cước cao'),
       ( 'STG',3,'Khách hàng yêu cầu'),
       ( 'THA',3,'Giá cước cao'),
       ( 'TNH',3,'STB hỏng, phải mua mới'),
       ( 'TNN',3,'Giá cước cao'),
       ( 'TQG',3,'Giá cước cao'),
       ( 'VPC',3,'Giá cước cao'),
       ( 'YBI',3,'Giá cước cao');