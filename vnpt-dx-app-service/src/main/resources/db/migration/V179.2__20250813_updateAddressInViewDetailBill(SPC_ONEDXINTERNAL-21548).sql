DROP VIEW IF EXISTS "vnpt_dev"."feature_view_shopping_cart_get_detail_billing";
CREATE VIEW "vnpt_dev"."feature_view_shopping_cart_get_detail_billing" AS
WITH billing_amount AS (
    SELECT bill_item_1.billing_id,
           sum(bill_item_1.amount_after_tax) AS amount_after_tax
    FROM vnpt_dev.bill_item bill_item_1
    GROUP BY bill_item_1.billing_id
), invoice_info AS (
    SELECT e_invoice.cart_code,
           e_invoice.billing_id,
           e_invoice.code AS invoice_code,
           e_invoice.created_at AS invoice_date,
           e_invoice.total_amount AS invoice_price,
           e_invoice.fkey AS invoice_fkey
    FROM vnpt_dev.e_invoice
    ORDER BY e_invoice.cart_code
)
SELECT DISTINCT COALESCE(billings.cart_code, billings.billing_code) AS billing_code,
                concat(subscriptions.id, COALESCE(combo.id, services.id)) AS group_by_service,
                COALESCE(subscriptions.cart_code, concat('ID', to_char(subscriptions.id, 'FM09999999'::text))::character varying) AS sub_code,
                billings.cart_code IS NOT NULL AS is_cart,
                billings.id AS billing_id,
                billings.status AS payment_status,
                billings.payment_method,
                COALESCE(combo.provider, services.provider, provider_info.name) AS provider_name,
                provider_info.tin AS provider_tax,
                provider_info.address AS provider_address,
                users.id AS user_id,
                users.province_id,
                CASE
                    WHEN users.customer_type::text = 'CN '::text THEN concat_ws(' '::text, users.last_name, users.first_name)::character varying
            ELSE users.name
END AS customer_name,
    users.customer_type,
    users.tin AS customer_tax,
    users.rep_personal_cert_number AS customer_identity,
    users.address AS customer_address,
        CASE
            WHEN users.customer_type::text = 'CN'::text THEN concat_ws(' '::text, billings.personal_last_name, billings.personal_first_name)::character varying
            ELSE billings.customer_name
END AS customer_name_billing,
    billings.customer_name AS name_company_billing,
    billings.personal_last_name AS customer_billing_last_name,
    billings.personal_first_name AS customer_billing_first_name,
    billings.customer_tax_no AS customer_tax_billing,
    billings.customer_address AS customer_address_billing,
    billings.customer_personal_cert_number AS customer_identity_billing,
    COALESCE(billings.province_id_customer_new, billings.province_id_customer) AS province_id_customer,
    COALESCE(billings.province_code_customer_new, billings.province_code_customer) AS province_code_customer,
    COALESCE(billings.province_name_customer_new, billings.province_name_customer) AS province_name_customer,
    billings.district_id_customer,
    billings.district_code_customer,
    billings.district_name_customer,
    COALESCE(billings.ward_id_customer_new, billings.ward_id_customer) AS ward_id_customer,
    COALESCE(billings.ward_code_customer_new, billings.ward_code_customer) AS ward_code_customer,
    COALESCE(billings.ward_name_customer_new, billings.ward_name_customer) AS ward_name_customer,
    COALESCE(billings.street_id_customer_new, billings.street_id_customer) AS street_id_customer,
    COALESCE(billings.street_name_customer_new, billings.street_name_customer) AS street_name_customer,
    COALESCE(combo.combo_name, services.service_name) AS service_name,
    billings.total_amount AS service_price,
    bill_item.item_name,
    bill_item.amount AS item_price,
    bill_item.amount_after_tax AS item_price_after_tax,
    billing_amount.amount_after_tax AS total_after_tax,
    billings.total_amount_after_adjustment AS total_after_tax_final,
    billings.payment_date,
    billings.current_payment_date,
    billings.final_payment_term,
    invoice_info.invoice_code,
    invoice_info.invoice_date,
    invoice_info.invoice_price,
    invoice_info.invoice_fkey,
    subscriptions.is_only_service,
    bill_item.id AS bill_item_id,
    COALESCE(combo.id, services.id) AS service_id,
    billings.province_id AS billing_province,
    COALESCE(combo.user_id, services.user_id) AS provider_id,
    COALESCE(billings.created_source_migration::integer, 0) AS created_source_migration,
    billings.type_address,
    billings.address_id,
    combo.id IS NOT NULL AS is_combo,
    bill_item.object_type,
    COALESCE(combo.combo_owner, services.service_owner) = ANY (ARRAY[0, 1]) AS is_on
   FROM vnpt_dev.bill_item
     LEFT JOIN vnpt_dev.billings ON billings.id = bill_item.billing_id
     LEFT JOIN invoice_info ON invoice_info.cart_code IS NOT NULL AND invoice_info.cart_code::text = billings.cart_code::text OR invoice_info.cart_code IS NULL AND invoice_info.billing_id = billings.id
     LEFT JOIN billing_amount ON billing_amount.billing_id = billings.id
     LEFT JOIN vnpt_dev.subscriptions ON subscriptions.id = billings.subscriptions_id
     LEFT JOIN vnpt_dev.pricing ON pricing.id = subscriptions.pricing_id AND subscriptions.pricing_id IS NOT NULL
     LEFT JOIN vnpt_dev.combo_plan ON combo_plan.id = subscriptions.combo_plan_id AND subscriptions.combo_plan_id IS NOT NULL
     LEFT JOIN vnpt_dev.services ON services.id = subscriptions.service_id
     LEFT JOIN vnpt_dev.combo ON combo_plan.combo_id = combo.id
     LEFT JOIN vnpt_dev.users ON users.id = subscriptions.user_id
     LEFT JOIN ( SELECT users_1.id,
            users_1.name,
            users_1.tin,
            users_1.address
           FROM vnpt_dev.users users_1) provider_info ON provider_info.id = services.created_by OR subscriptions.combo_plan_id IS NOT NULL AND provider_info.id = combo.created_by
  WHERE subscriptions.deleted_flag = 1 AND users.deleted_flag = 1;