DELETE FROM "vnpt_dev"."activity" WHERE code = 'SME_CHECKING_STATUS';

INSERT INTO "vnpt_dev"."activity"("name", "system_name", "api_name", "api_url", "request", "response", "can_evaluate", "visible", "system_code", "api_header", "transaction_code", "code", "created_by", "created_at", "modified_by", "modified_at") VALUES ('SME checking status', 'DHSXKD', 'sme_checking_status', 'https://api-onebss.vnpt.vn/esb/onesme/sme_checking_status', NULL, NULL, 't', 1, 'SC2', NULL, 'NEW', 'SME_CHECKING_STATUS', NULL, NULL, NULL, NULL);

ALTER TABLE "vnpt_dev"."order_service_receive"
    ADD COLUMN IF NOT EXISTS "service_name" varchar(255),
    ADD COLUMN IF NOT EXISTS "pricing_name" varchar(255),
    ADD COLUMN IF NOT EXISTS "cycle" int4,
    ADD COLUMN IF NOT EXISTS "start_date" date,
    ADD COLUMN IF NOT EXISTS "end_date" date;

COMMENT ON COLUMN "vnpt_dev"."order_service_receive"."service_name" IS 'Tên dịch vụ trên BSS';

COMMENT ON COLUMN "vnpt_dev"."order_service_receive"."pricing_name" IS 'Tên gói dịch vụ trên BSS';

COMMENT ON COLUMN "vnpt_dev"."order_service_receive"."cycle" IS 'Chu kỳ tt ( mặc định là tháng )';

COMMENT ON COLUMN "vnpt_dev"."order_service_receive"."start_date" IS 'Ngày bắt đầu BSS';

COMMENT ON COLUMN "vnpt_dev"."order_service_receive"."end_date" IS 'Ngày kết thúc BSS';