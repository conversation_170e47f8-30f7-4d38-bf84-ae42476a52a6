drop function if exists vnpt_dev.func_get_affiliate_link_by_role;

CREATE OR REPLACE FUNCTION vnpt_dev.func_get_affiliate_link_by_role(rolename character varying, userid bigint)
 RETURNS TABLE(affiliate_link_id bigint, affiliate_link_code character varying)
 LANGUAGE plpgsql
AS $function$
begin
	--<PERSON>min tổng
	if roleName like 'FULL_ADMIN' then
		begin
			--Lấy ra tất cả affiliate_link
			return query (select id, link_code from vnpt_dev.affiliate_link where deleted_flag = 1);
		end;
	--Đại lý affiliate
	elseif roleName like 'ROLE_AFFILIATE_DAILY' then
		begin
			return query (
				--<PERSON><PERSON> quy để lấy ra đại lý và tất cả thành viên con
				with recursive affiliate_users_all_level_cte as(
					select user_id, affiliate_code from vnpt_dev.affiliate_users where user_id = userId
					union all
					select aff_users_child.user_id, aff_users_child.affiliate_code 
					from vnpt_dev.affiliate_users aff_users_child join affiliate_users_all_level_cte aff_users_parent 
						on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
				)
				--Lấy ra các affiliate được tạo bởi đại lý và tất cả thành viên con
				select aff_link.id, aff_link.link_code
				from vnpt_dev.affiliate_link aff_link join affiliate_users_all_level_cte aff_all_level 
					on aff_link.created_by = aff_all_level.user_id 
					and aff_link.deleted_flag = 1
			);
		end;
	--Cá nhân affiliate
	else
		begin
			--Lấy ra các affiliate link được tạo bởi 
			return query (select id, link_code from vnpt_dev.affiliate_link where created_by = userId and deleted_flag = 1);
		end;
	end if;
end $function$
;
