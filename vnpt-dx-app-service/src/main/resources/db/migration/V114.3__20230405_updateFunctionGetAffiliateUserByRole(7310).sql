drop function if exists vnpt_dev.func_get_affiliate_user_by_role;
CREATE OR REPLACE FUNCTION vnpt_dev.func_get_affiliate_user_by_role(rolename character varying, currentuserid bigint)
 RETURNS TABLE(
 	userid bigint, 
 	username character varying, 
 	useremail character varying, 
 	usercode character varying, 
 	userlevel smallint
 )
 LANGUAGE plpgsql
AS $function$
begin
	--<PERSON><PERSON> tổng
	if roleName like 'FULL_ADMIN' then
		begin
			--L<PERSON>y ra tất cả affiliate_user
			return query (
				select users.id, users."name", users.email,
					affiliate_users.affiliate_code, affiliate_users.affiliate_level
				from vnpt_dev.affiliate_users join vnpt_dev.users 
					on affiliate_users.user_id = users.id
				where users.deleted_flag = 1 and affiliate_users.affiliate_status = 2
			);
		end;
	--Đại lý affiliate
	elseif roleName like 'ROLE_AFFILIATE_DAILY' then
		begin
			return query (
				--Đ<PERSON> quy để lấy ra đại lý và tất cả thành viên con
				with recursive affiliate_users_all_level_cte as(
					select user_id, affiliate_code, affiliate_level from vnpt_dev.affiliate_users 
					where user_id = currentUserId
						and affiliate_users.affiliate_status = 2
					union all
					select aff_users_child.user_id, aff_users_child.affiliate_code, aff_users_child.affiliate_level
					from vnpt_dev.affiliate_users aff_users_child join affiliate_users_all_level_cte aff_users_parent 
						on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
					where aff_users_child.affiliate_status = 2
				)
				select users.id, users."name", users.email,
					aff_multi_level.affiliate_code, aff_multi_level.affiliate_level
				from affiliate_users_all_level_cte aff_multi_level join vnpt_dev.users 
					on aff_multi_level.user_id = users.id
				where users.deleted_flag = 1
			);
		end;
	--Cá nhân affiliate
	else
		begin
			--Lấy ra thông tin user hiện tại
			return query (
				select users.id, users."name", users.email,
					affiliate_users.affiliate_code, affiliate_users.affiliate_level
				from vnpt_dev.affiliate_users join vnpt_dev.users 
					on affiliate_users.user_id = users.id
				where users.id = userid and users.deleted_flag = 1 and affiliate_users.affiliate_status = 2
			);
		end;
	end if;
end $function$
;
