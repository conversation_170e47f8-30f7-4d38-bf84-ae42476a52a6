ALTER TABLE "vnpt_dev"."subscriptions"
    ADD COLUMN IF NOT EXISTS "service_group_id" int8;

COMMENT ON COLUMN "vnpt_dev"."subscriptions"."service_group_id" IS 'ID Nhóm SP';

ALTER TABLE "vnpt_dev"."transaction_log"
    ADD COLUMN IF NOT EXISTS "sub_group_code" varchar(255);

COMMENT ON COLUMN "vnpt_dev"."transaction_log"."sub_group_code" IS 'mã đơn hàng theo nhóm SP';

ALTER TABLE "vnpt_dev"."subscriptions"
    ADD COLUMN IF NOT EXISTS "service_group_id" int8,
    ADD COLUMN IF NOT EXISTS "group_code" varchar(50);

COMMENT ON COLUMN "vnpt_dev"."subscriptions"."service_group_id" IS 'ID Nhóm SP';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."group_code" IS 'Mã tự sinh theo nhóm SP';


ALTER TABLE "vnpt_dev"."billings"
    ADD COLUMN IF NOT EXISTS "group_code" varchar(50);

COMMENT ON COLUMN "vnpt_dev"."subscriptions"."group_code" IS 'Mã tự sinh theo nhóm SP';

drop TABLE if exists service_group CASCADE;
drop TABLE if exists service_group_draft CASCADE;
drop TABLE if exists service_group_pricing CASCADE;
drop TABLE if exists service_group_pricing_item CASCADE;

-- tạo bảng service_group
CREATE TABLE "vnpt_dev"."service_group" (
                                            "id" bigserial NOT NULL,
                                            "user_id" int8,
                                            "status" int2,
                                            "name" varchar(100) ,
                                            "code" varchar(100) ,
                                            "categories_id" int8[] default '{}' ,
                                            "phone_number" varchar(30) ,
                                            "email" varchar(100) ,
                                            "description" text ,
                                            "created_at" timestamp(6),
                                            "created_by" int8,
                                            "modified_at" timestamp(6),
                                            "modified_by" int8,
                                            "approve_by" int8,
                                            "approve_at" timestamp(6),
                                            "approve" int2,
                                            "total_amount" float8,
                                            "deleted_flag" int2,
                                            "payment_method" int2,
                                            "reason_update" varchar(500) ,
                                            "group_service_draft_id" int8,
                                            "group_service_owner" int2,
                                            "icon_id" int8,
                                            "video_id" int8,
                                            "video_guide_id" int8,
                                            "lst_doc_guide_id" int8[] default '{}',
                                            "lst_snapshot_id" int8[] default '{}',
                                            "customer_type_code" varchar(255) ,
                                            CONSTRAINT "service_group_key" PRIMARY KEY ("id")
);


COMMENT ON COLUMN "vnpt_dev"."service_group"."user_id" IS 'id Nhà cung cấp';

COMMENT ON COLUMN "vnpt_dev"."service_group"."status" IS 'Trạng thái hoạt động: 0 - deactive, 1 active';

COMMENT ON COLUMN "vnpt_dev"."service_group"."name" IS 'Tên nhóm dịch vụ';

COMMENT ON COLUMN "vnpt_dev"."service_group"."code" IS 'Mã nhóm dịch vụ';

COMMENT ON COLUMN "vnpt_dev"."service_group"."categories_id" IS 'Id của categories lưu dạng mảng ';

COMMENT ON COLUMN "vnpt_dev"."service_group"."approve" IS 'Trạng thái duyệt: 0 - chưa duyệt, 1 - đã duyệt, 2 - chờ duyệt, 3 - từ chối';

COMMENT ON COLUMN "vnpt_dev"."service_group"."total_amount" IS 'Tổng tiền sau thuế (tiền phải thanh toán)';

COMMENT ON COLUMN "vnpt_dev"."service_group"."payment_method" IS 'Phương thức thanh toán (1: VNPT Pay, 2: Tiền mặt, -1: cả 2)';

COMMENT ON COLUMN "vnpt_dev"."service_group"."reason_update" IS 'Lý do cập nhật';

COMMENT ON COLUMN "vnpt_dev"."service_group"."group_service_draft_id" IS 'Id của group_service_draft';

COMMENT ON COLUMN "vnpt_dev"."service_group"."group_service_owner" IS 'Đơn vị phát triển';

COMMENT ON COLUMN "vnpt_dev"."service_group"."customer_type_code" IS 'Loại khách hàng đăng ký tài khoản: KHDN - Khách hàng doanh nghiệp, HKD - Hộ kinh doanh, CN - cá nhân';

COMMENT ON COLUMN "vnpt_dev"."service_group"."icon_id" IS 'Id ảnh đại diện nhóm sản phẩm';

COMMENT ON COLUMN "vnpt_dev"."service_group"."video_id" IS 'Id video giới thiệu nhóm sản phẩm';

COMMENT ON COLUMN "vnpt_dev"."service_group"."video_guide_id" IS 'Id video hướng dẫn sử dụng nhóm sản phẩm';

COMMENT ON COLUMN "vnpt_dev"."service_group"."lst_doc_guide_id" IS 'Danh sách Id tài liệu hướng dẫn nhóm sản phẩm';

COMMENT ON COLUMN "vnpt_dev"."service_group"."lst_snapshot_id" IS 'Danh sách Id ảnh chụp màn hình nhóm sản phẩm';

-- tạo bảng service_group_draft
CREATE TABLE "vnpt_dev"."service_group_draft" (
                                                  "id" bigserial NOT NULL,
                                                  "user_id" int8,
                                                  "status" int2,
                                                  "name" varchar(100) ,
                                                  "code" varchar(100) ,
                                                  "categories_id" int8[] default '{}' ,
                                                  "current_group_id" int8 ,
                                                  "phone_number" varchar(30) ,
                                                  "email" varchar(100) ,
                                                  "description" text ,
                                                  "created_at" timestamp(6),
                                                  "created_by" int8,
                                                  "modified_at" timestamp(6),
                                                  "modified_by" int8,
                                                  "approve_by" int8,
                                                  "approve_at" timestamp(6),
                                                  "approve" int2,
                                                  "total_amount" float8,
                                                  "deleted_flag" int2,
                                                  "payment_method" int2,
                                                  "reason_update" varchar(500) ,
                                                  "reason_reject" text ,
                                                  "group_service_owner" int2,
                                                  "icon_id" int8,
                                                  "video_id" int8,
                                                  "video_guide_id" int8,
                                                  "lst_doc_guide_id" int8[] default '{}',
                                                  "lst_snapshot_id" int8[] default '{}',
                                                  "customer_type_code" varchar(255) ,
                                                  CONSTRAINT "service_group_draft_key" PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."service_group_draft"."current_group_id" IS 'id phiên bản hiện tại';


-- tạo bảng service_group_pricing
CREATE TABLE "vnpt_dev"."service_group_pricing" (
                                                    "id" bigserial NOT NULL,
                                                    "service_group_id" int8,
                                                    "service_group_id_draft" int8,
                                                    "service_id" int8,
                                                    CONSTRAINT "service_group_pricing_pkey" PRIMARY KEY ("id")
);

-- tạo bảng service_group_pricing_item
CREATE TABLE "vnpt_dev"."service_group_pricing_item" (
                                                         "id" bigserial NOT NULL,
                                                         "quantity" int8,
                                                         "amount_update" float8,
                                                         "service_group_pricing_id" int8,
                                                         "pricing_multi_plan_id" int8,
                                                         "object_id" int8,
                                                         "object_type" text ,
                                                         CONSTRAINT "service_group_pricing_item_pkey" PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."service_group_pricing_item"."quantity" IS 'Số lượng';

COMMENT ON COLUMN "vnpt_dev"."service_group_pricing_item"."service_group_pricing_id" IS 'Id bảng service group pricing';

COMMENT ON COLUMN "vnpt_dev"."service_group_pricing_item"."amount_update" IS 'Gía trước thuế update';

COMMENT ON COLUMN "vnpt_dev"."service_group_pricing_item"."pricing_multi_plan_id" IS 'Id của bảng pricing_multi_plan với trường hợp là pricing';

COMMENT ON COLUMN "vnpt_dev"."service_group_pricing_item"."object_id" IS 'ID của đối tượng áp dụng: DEVICE_NO_VARIANT - service_id, DEVICE_VARIANT - variant_id, PRICING - pricing_id';

COMMENT ON COLUMN "vnpt_dev"."service_group_pricing_item"."object_type" IS 'Loại đối tượng: DEVICE_NO_VARIANT - service_id, DEVICE_VARIANT - variant_id, PRICING - pricing_id';


