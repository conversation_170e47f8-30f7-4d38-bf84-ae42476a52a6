DROP VIEW IF EXISTS vnpt_dev.view_report_sub_bills;
CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_bills AS
(
    with reportSubBillCTE as (
        SELECT bill.id,
           bill.action_type,
           bill.status,
           bill.payment_date,
           bill.created_at,
           bill.billing_code,
           bill.subscriptions_id,
           bill.total_amount,
           bill.billing_date,
           bill.total_amount_after_adjustment,
           max(invoice.created_at)        AS created_export_invoice,
           string_agg(invoice.code, '; ') AS code,
           bill.portal_type               AS portal_type,
           bill.created_by                AS created_by,
           bill.end_date,
           bill.end_date_new_renewal,
           CASE
               -- bill_action_type: 0: tạo mới, 1: s<PERSON><PERSON>, 2: đ<PERSON><PERSON>, 3, 4: kích ho<PERSON>t lại,  5: gia hạn,
               WHEN bill.action_type = -1 OR (bill.action_type IS NULL AND bill.created_by <> 'batch') THEN 0 -- thuê bao tạo mới
               WHEN bill.action_type = 1 THEN 1 -- thu<PERSON> bao sửa
               WHEN bill.action_type = 2 THEN 2 -- thu<PERSON> bao đổi gói
               WHEN bill.action_type IN (3, 4) THEN 3 -- thuê bao kích hoạt lại
               WHEN bill.action_type = 5 OR (bill.action_type IS NULL AND bill.created_by = 'batch') THEN 5 -- thuê bao gia hạn
               ELSE bill.action_type
           END AS bill_action_type,
           affAgency.aff_user_id         AS aff_agency_user_id,
           affAgency.affiliate_code      AS aff_agency_code
    FROM vnpt_dev.billings AS bill
             LEFT JOIN vnpt_dev.e_invoice AS invoice ON bill.id = invoice.billing_id
             LEFT JOIN vnpt_dev.affiliate_bill_commission_note AS affAgency ON bill.id = affAgency.bill_id AND affAgency.affiliate_level = 1
    WHERE bill.status in (0, 1, 2, 3, 4)
    GROUP BY bill.id, affAgency.aff_user_id, affAgency.affiliate_code
    ) select * from reportSubBillCTE where status = 2 or (not(bill_action_type = 5) and status in (0,1,3,4))
);