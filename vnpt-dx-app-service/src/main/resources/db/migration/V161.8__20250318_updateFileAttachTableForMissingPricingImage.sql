INSERT INTO vnpt_dev.FILE_ATTACH (
    access_type,
    combo_draft_id,
    combo_id,
    created_at,
    created_by,
    description,
    ext_link,
    file_extension,
    file_name,
    file_path,
    file_size,
    file_size_library,
    file_type,
    modified_at,
    modified_by,
    object_draft_id,
    object_id,
    object_name,
    object_type,
    page_bulder_id,
    priority,
    resolution,
    seo_id,
    service_id,
    services_draft_id,
    source_id,
    source_type,
    status_display,
    title,
    user_id,
    visible
)
SELECT
    fa.access_type,
    fa.combo_draft_id,
    fa.combo_id,
    fa.created_at,
    fa.created_by,
    fa.description,
    fa.ext_link,
    fa.file_extension,
    fa.file_name,
    fa.file_path,
    fa.file_size,
    fa.file_size_library,
    fa.file_type,
    fa.modified_at,
    fa.modified_by,
    fa.object_draft_id,
    p_max.id AS object_id,  -- Đặt object_id là id mới nhất của pricing
    fa.object_name,
    fa.object_type,
    fa.page_bulder_id,
    fa.priority,
    fa.resolution,
    fa.seo_id,
    fa.service_id,
    fa.services_draft_id,
    fa.source_id,
    fa.source_type,
    fa.status_display,
    fa.title,
    fa.user_id,
    fa.visible
FROM vnpt_dev.FILE_ATTACH fa
JOIN (
     -- Lấy thông tin file_attach của object_id mới nhất với mỗi draft_id có object_type là BOS_PRICING_IMAGE(10001)
    SELECT vnpt_dev.FILE_ATTACH.object_draft_id, MAX(vnpt_dev.FILE_ATTACH.object_id) AS latest_object_id
    FROM vnpt_dev.FILE_ATTACH
    WHERE vnpt_dev.FILE_ATTACH.object_type = 10001
    GROUP BY vnpt_dev.FILE_ATTACH.object_draft_id
) latest_fa
ON fa.object_draft_id = latest_fa.object_draft_id
AND fa.object_id = latest_fa.latest_object_id
JOIN (
    -- Lấy id mới nhất của từng pricing theo pricing_draft_id
    SELECT PRICING_DRAFT_ID, MAX(id) AS id
    FROM vnpt_dev.PRICING
    GROUP BY PRICING_DRAFT_ID
) p_max
ON fa.object_draft_id = p_max.PRICING_DRAFT_ID
WHERE p_max.id != fa.object_id;