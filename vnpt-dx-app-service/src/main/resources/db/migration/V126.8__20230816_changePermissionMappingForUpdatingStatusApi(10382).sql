with wrong_permission_portal_id as (
        select id
        from vnpt_dev.permission_portal
        where
            permission_id in (select id from vnpt_dev.permission where code = 'BAT_TAT_TRANG_THAI_HOAT_DONG_CUA_COMBO_DICH_VU_1') and
            portal_id = 1
    ),
    correct_permission_portal_id as (
        select id
        from vnpt_dev.permission_portal
        where
            permission_id in (select id from vnpt_dev.permission where code = 'AN_HIEN_DICH_VU_1') and
            portal_id = 1
        limit 1
    )

update vnpt_dev.api_permission
    set permission_portal_id = (select * from correct_permission_portal_id)
where api_id in (select id from vnpt_dev.apis where api_code = 'ROLE_ADMIN_UPDATE_STATUS_SERVICE') and
    permission_portal_id in (select * from wrong_permission_portal_id);

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;