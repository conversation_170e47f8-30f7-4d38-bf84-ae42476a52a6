UPDATE vnpt_dev.role SET created_at = '2021-08-06 00:00:00.000' where created_at IS NULL;


ALTER TABLE "vnpt_dev"."services"
    ADD COLUMN IF NOT EXISTS "service_owner_vnpt" int2;

COMMENT ON COLUMN "vnpt_dev"."services"."service_owner_vnpt" IS 'hỗ trợ VNPT (0: VNPT, 1: PARTNER, 2: NON_VNPT)';

ALTER TABLE "vnpt_dev"."services_draft"
    ADD COLUMN IF NOT EXISTS "service_owner_vnpt" int2;

COMMENT ON COLUMN "vnpt_dev"."services_draft"."service_owner_vnpt" IS 'hỗ trợ VNPT (0: VNPT, 1: PARTNER, 2: NON_VNPT)';

-- Thêm bản ghi batch cấu hình quét định kì (1 hour) phân giao nhiệm vụ quy tắc gám assignee, assignees cho c<PERSON>c đối tượng
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'scanning-assignment-rule' AND method_name = 'scanningAssigningEnterprise';
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'scanning-assignment-rule' AND method_name = 'scanningAssigningContact';
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'scanning-assignment-rule' AND method_name = 'scanningAssigningTicket';
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'scanning-assignment-rule' AND method_name = 'scanningAssignmentRuleUser';
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'scanning-assignment-rule' AND method_name = 'scanningAssigningSubscription';
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('scanning-assignment-rule', 'scanningAssigningEnterprise', NULL, '0 27,57 * ? * *', 'scanningAssigningEnterprise', 1, 'batch', NULL, 'batch', NULL);
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('scanning-assignment-rule', 'scanningAssigningContact', NULL, '0 22,52 * ? * *', 'scanningAssigningContact', 1, 'batch', NULL, 'batch', NULL);
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('scanning-assignment-rule', 'scanningAssigningTicket', NULL, '0 17,47 * ? * *', 'scanningAssigningTicket', 1, 'batch', NULL, 'batch', NULL);
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('scanning-assignment-rule', 'scanningAssignmentRuleUser', NULL, '0 12,42 * ? * *', 'scanningAssignmentRuleUser', 1, 'batch', NULL, 'batch', NULL);
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('scanning-assignment-rule', 'scanningAssigningSubscription', NULL, '0 22,52 * ? * *', 'scanningAssigningSubscription', 1, 'batch', NULL, 'batch', NULL);