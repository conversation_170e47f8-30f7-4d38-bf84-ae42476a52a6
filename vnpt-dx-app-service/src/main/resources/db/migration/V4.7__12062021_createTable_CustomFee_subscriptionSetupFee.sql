-- vnpt_dev.custom_fee definition

-- Drop table

-- DROP TABLE vnpt_dev.custom_fee;
DROP TABLE IF EXISTS custom_fee CASCADE;
CREATE TABLE  vnpt_dev.custom_fee (
	id bigserial NOT NULL,
	subscription_id int8 NOT NULL,
	name varchar(50),
	price float8,
	description varchar(300),
	type int2,
	status int2,
	CONSTRAINT custom_fee_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE vnpt_dev.custom_fee IS 'Thông tin phí 1 lần';
COMMENT ON COLUMN vnpt_dev.custom_fee.subscription_id IS 'Mã đăng ký';
COMMENT ON COLUMN vnpt_dev.custom_fee.name IS 'Tên phí';
COMMENT ON COLUMN vnpt_dev.custom_fee.description IS 'Mô tả';
COMMENT ON COLUMN vnpt_dev.custom_fee.type IS '0: IMMEDIATE  1: LATER';
COMMENT ON COLUMN vnpt_dev.custom_fee.status IS '0: Chưa thanh toán 1: Đã thanh toán';

-- vnpt_dev.custom_fee subscription_setup_fee

-- Drop table

-- DROP TABLE vnpt_dev.subscription_setup_fee;
DROP TABLE IF EXISTS subscription_setup_fee CASCADE;
CREATE TABLE  vnpt_dev.subscription_setup_fee (
	id bigserial NOT NULL,
	subscription_id int8 NOT NULL,
	addon_id int8,
	price float8,
	pricing_id	int8,
	number_of_cycles int2,
	setup_fee float8,
	CONSTRAINT subscription_setup_fee_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE vnpt_dev.subscription_setup_fee IS 'Thông tin tùy chỉnh giá khi đăng ký';
COMMENT ON COLUMN vnpt_dev.subscription_setup_fee.subscription_id IS 'Mã đăng ký';
COMMENT ON COLUMN vnpt_dev.subscription_setup_fee.addon_id IS 'Mã dịch vụ bổ xung';
COMMENT ON COLUMN vnpt_dev.subscription_setup_fee.pricing_id IS 'Mã gói';
COMMENT ON COLUMN vnpt_dev.subscription_setup_fee.number_of_cycles IS 'Số chu kỳ thanh toán';
COMMENT ON COLUMN vnpt_dev.subscription_setup_fee.setup_fee IS 'Phí cài đặt mới';
COMMENT ON COLUMN vnpt_dev.subscription_setup_fee.price IS 'Giá mới';


ALTER TABLE vnpt_dev.billings
DROP COLUMN IF EXISTS current_payment_time;

ALTER TABLE vnpt_dev.users
ADD COLUMN province_code varchar(20),
ADD COLUMN ward_id int8;
COMMENT ON COLUMN vnpt_dev.users.province_code IS 'code của tỉnh';
COMMENT ON COLUMN vnpt_dev.users.ward_id IS 'Id của phường, xã';