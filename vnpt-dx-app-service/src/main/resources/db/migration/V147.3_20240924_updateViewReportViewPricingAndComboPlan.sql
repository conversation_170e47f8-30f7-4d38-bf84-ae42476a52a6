-- vnpt_dev.report_view_pricing_and_combo_plan source

CREATE OR REPLACE VIEW vnpt_dev.report_view_pricing_and_combo_plan
AS SELECT p.id,
       concat(p.id, '0000')::bigint AS unique_id,
       concat(s.service_name, '/', p.pricing_name) AS name,
       p.service_id,
       p.price,
       p.cycle_type,
       p.payment_cycle
   FROM vnpt_dev.pricing p
       LEFT JOIN vnpt_dev.services s ON p.service_id = s.id
       JOIN (
           SELECT MAX(pmax.id) AS latest_id, pmax.pricing_draft_id
           FROM vnpt_dev.pricing pmax
           WHERE pmax.deleted_flag = 1
           GROUP BY pmax.pricing_draft_id
       ) AS latest_pricing ON p.id = latest_pricing.latest_id
       WHERE p.deleted_flag = 1 AND p.approve = 1 AND p.status = 1
UNION
    SELECT combo_plan.id,
        concat(combo_plan.id, '0001')::bigint AS unique_id,
        combo_plan.combo_name AS name,
        NULL::bigint AS service_id,
        combo_plan.price,
        combo_plan.cycle_type,
        combo_plan.payment_cycle
    FROM vnpt_dev.combo_plan
        WHERE combo_plan.deleted_flag = 1 AND combo_plan.approve = 1 AND combo_plan.status = 1;