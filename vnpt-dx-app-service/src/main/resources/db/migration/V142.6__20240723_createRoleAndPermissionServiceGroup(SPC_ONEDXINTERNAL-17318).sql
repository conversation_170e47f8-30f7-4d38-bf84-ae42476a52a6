----------------------------------------------Quản lý nhóm sản phẩm dịch vụ----------------------------------------------
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU');
DELETE FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU';
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU');
DELETE FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Quản lý nhóm sản phẩm dịch vụ', 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU', -1, (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

----permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU'), 1);

---------------------------Xem danh sách sách nhóm sản phẩm dịch vụ ADMIN---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_SERVICE_GROUP_LIST';
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem danh sách nhóm sản phẩm dịch vụ',
        'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group/get-list', 'ROLE_ADMIN_VIEW_SERVICE_GROUP_LIST', 'POST');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_SERVICE_GROUP_LIST' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------Xem danh sách sách nhóm sản phẩm dịch vụ DEV---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_VIEW_SERVICE_GROUP_LIST';
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem danh sách nhóm sản phẩm dịch vụ',
        'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/dev-portal/service-group/get-list', 'ROLE_DEV_VIEW_SERVICE_GROUP_LIST', 'POST');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_VIEW_SERVICE_GROUP_LIST' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV') LIMIT 1),
    1, 1);


---------------------------Xem chi tiết nhóm sản phẩm dịch vụ ADMIN---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_DETAIL_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem chi tiết nhóm sản phẩm dịch vụ',
        'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group/get-group-draft-pricing-detail', 'ROLE_ADMIN_VIEW_DETAIL_SERVICE_GROUP', 'GET');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_DETAIL_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------Xem chi tiết nhóm sản phẩm dịch vụ DEV---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_DEV')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_VIEW_DETAIL_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_DEV';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem chi tiết nhóm sản phẩm dịch vụ',
        'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_DEV',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_DEV'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_DEV'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/dev-portal/service-group/get-detail', 'ROLE_DEV_VIEW_DETAIL_SERVICE_GROUP', 'GET');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_VIEW_DETAIL_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_NHOM_SAN_PHAM_DICH_VU_DEV' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV') LIMIT 1),
    1, 1);


---------------------------Khai báo nhóm sản phẩm dịch vụ ADMIN---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CREATE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Khai báo nhóm sản phẩm dịch vụ',
        'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group', 'ROLE_ADMIN_CREATE_SERVICE_GROUP', 'POST');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CREATE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------Khai báo nhóm sản phẩm dịch vụ DEV---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_CREATE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Khai báo nhóm sản phẩm dịch vụ',
        'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/dev-portal/service-group', 'ROLE_DEV_CREATE_SERVICE_GROUP', 'POST');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_CREATE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'KHAI_BAO_NHOM_SAN_PHAM_DICH_VU_DEV' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV') LIMIT 1),
    1, 1);


---------------------------Cập nhật nhóm sản phẩm dịch vụ ADMIN---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Cập nhật nhóm sản phẩm dịch vụ',
        'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group', 'ROLE_ADMIN_UPDATE_SERVICE_GROUP', 'PUT');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------Cập nhật nhóm sản phẩm dịch vụ DEV ---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_UPDATE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Cập nhật nhóm sản phẩm dịch vụ',
        'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/dev-portal/service-group', 'ROLE_DEV_UPDATE_SERVICE_GROUP', 'PUT');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_UPDATE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CAP_NHAT_NHOM_SAN_PHAM_DICH_VU_DEV' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV') LIMIT 1),
    1, 1);


---------------------------Xóa nhóm sản phẩm dịch vụ ADMIN---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_DELETE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xóa nhóm sản phẩm dịch vụ',
        'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group', 'ROLE_ADMIN_DELETE_SERVICE_GROUP', 'DELETE');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_DELETE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XOA_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------Xóa nhóm sản phẩm dịch vụ DEV ---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XOA_NHOM_SAN_PHAM_DICH_VU_DEV')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_DELETE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_DEV';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xóa nhóm sản phẩm dịch vụ',
        'XOA_NHOM_SAN_PHAM_DICH_VU_DEV',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_DEV'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_NHOM_SAN_PHAM_DICH_VU_DEV'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/dev-portal/service-group', 'ROLE_DEV_DELETE_SERVICE_GROUP', 'DELETE');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_DELETE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XOA_NHOM_SAN_PHAM_DICH_VU_DEV' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV') LIMIT 1),
    1, 1);


---------------------------Phê duyệt nhóm sản phẩm dịch vụ ADMIN---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_APPROVE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Phê duyệt nhóm sản phẩm dịch vụ',
        'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group/approve', 'ROLE_ADMIN_APPROVE_SERVICE_GROUP', 'PUT');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_APPROVE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------ADMIN gửi yêu cầu phê duyệt nhóm sản phẩm dịch vụ ---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_REQUEST_APPROVE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Yêu cầu phê duyệt nhóm sản phẩm dịch vụ',
        'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/admin-portal/service-group/request-approve', 'ROLE_ADMIN_REQUEST_APPROVE_SERVICE_GROUP', 'PUT');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_REQUEST_APPROVE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


---------------------------DEV gửi yêu cầu phê duyệt nhóm sản phẩm dịch vụ ---------------

DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_REQUEST_APPROVE_SERVICE_GROUP';
DELETE FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV';

----permission----
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Yêu cầu phê duyệt nhóm sản phẩm dịch vụ',
        'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal----
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV'));

----roles_permissions----
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_DEV'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV'), 1);

----apis----
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), 'api/dev-portal/service-group/request-approve', 'ROLE_DEV_REQUEST_APPROVE_SERVICE_GROUP', 'PUT');

----api_permission----
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_REQUEST_APPROVE_SERVICE_GROUP' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'YEU_CAU_PHE_DUYET_NHOM_SAN_PHAM_DICH_VU_DEV' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'DEV') LIMIT 1),
    1, 1);

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;

