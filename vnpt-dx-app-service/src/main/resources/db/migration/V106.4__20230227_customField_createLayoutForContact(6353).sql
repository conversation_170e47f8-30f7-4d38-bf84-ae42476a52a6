-- ======= CUSTOM FIELD ======= --
-- <PERSON><PERSON><PERSON> hệ kh<PERSON>ch hàng do<PERSON>h <PERSON> (KHDN) --
DELETE FROM "vnpt_dev"."custom_field" WHERE category = 'CUSTOMER_CONTACT' AND customer_type = 'KHDN' AND portal_type = 1 and is_standard = true;
INSERT INTO "vnpt_dev"."custom_field" ("name", code, is_standard, "type", category, config, lst_permission, customer_type, portal_type)
VALUES
-- Section Thông tin liên hệ --
('Thông tin liên hệ ', 'contact.enterprise.section.contactInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin liên hệ","labelEnabled":true,"hintText":null,"isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Ảnh đại diện liên hệ --
('Ảnh đại diện liên hệ', 'contact.enterprise.avatarUpload', true, 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên ảnh đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["jpg","png"],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên liên hệ  --
('Tên người liên hệ', 'contact.enterprise.name', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên người liên hệ","labelEnabled":true,"hintText":"Nhập tên người liên hệ","isUnique":true,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[],"patternCombination":0,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Số điện thoại liên hệ  --
('Số điện thoại liên hệ', 'contact.enterprise.phone', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":[],"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Email thoại liên hệ  --
('Email liên hệ', 'contact.enterprise.email', true, 'EMAIL', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Mạng xã hội  --
('Mạng xã hội', 'contact.enterprise.socialNetwork', true, 'URL', 'CUSTOMER_CONTACT', '{"label":"Mạng xã hội","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tỉnh thành liên hệ --
('Tỉnh thành liên hệ', 'contact.enterprise.province', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tỉnh thành","labelEnabled":true,"hintText":"Chọn tỉnh thành","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Hà Nội","Hồ Chí Minh","Đà Nẵng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Địa chỉ  --
('Địa chỉ liên hệ', 'contact.enterprise.address', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên tổ chức --
('Tên tổ chức', 'contact.enterprise.organization', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên tổ chức","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Chức vụ --
('Chức vụ liên hệ', 'contact.enterprise.position', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chức vụ","labelEnabled":true,"hintText":"Nhập chức vụ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Lời nhắn  --
('Lới nhắn', 'contact.enterprise.note', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'contact.enterprise.description', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  THÔNG TIN DOANH NGHIỆP --
-- Section thông tin doanh nghiệp  --
('Thông tin doanh nghiệp', 'contact.enterprise.section.businessInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin doanh nghiệp","labelEnabled":true,"hintText":null,"isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"","uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên doanh nghiệp --
('Tên doanh nghiệp', 'contact.enterprise.enterpriseInfo.name', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên doanh nghiệp","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Chi cục thuế  --
('Chi cục thuế', 'contact.enterprise.enterpriseInfo.taxDepart', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chi cục thuế","labelEnabled":true,"hintText":"Nhập chi cục thuế","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Mã số thuế --
('Mã số thuế', 'contact.enterprise.enterpriseInfo.taxNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Quốc gia  --
('Quốc gia', 'contact.enterprise.enterpriseInfo.nationality', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quốc gia","labelEnabled":true,"hintText":"Chọn quốc gia","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Việt Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Miền --
('Miền', 'contact.enterprise.enterpriseInfo.region', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Miền","labelEnabled":true,"hintText":"Chọn miền","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Miền Bắc","Miền Trung","Miền Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Mã tỉnh  --
('Mã tỉnh', 'contact.enterprise.enterpriseInfo.provinceCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã tỉnh","labelEnabled":true,"hintText":"Chọn mã tỉnh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["HNI"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên tỉnh --
('Tên tỉnh', 'contact.enterprise.enterpriseInfo.provinceName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên tỉnh","labelEnabled":true,"hintText":"Chọn tên tỉnh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Hà Nội"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Mã huyện  --
('Mã Huyện', 'contact.enterprise.enterpriseInfo.districtCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã huyện","labelEnabled":true,"hintText":"Chọn mã huyện","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["HNI-20"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên huyện --
('Tên huyện', 'contact.enterprise.enterpriseInfo.districtName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên huyện","labelEnabled":true,"hintText":"Chọn tên huyện","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Cầu Giấy"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Mã xã  --
('Mã xã', 'contact.enterprise.enterpriseInfo.communeCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã xã","labelEnabled":true,"hintText":"Chọn mã xã","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên xã --
('Tên xã', 'contact.enterprise.enterpriseInfo.communeName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên xã","labelEnabled":true,"hintText":"Chọn tên xã","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên đường phố --
('Tên đường phố', 'contact.enterprise.enterpriseInfo.streetName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên đường, phố","labelEnabled":true,"hintText":"Chọn tên đường, phố","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Địa chỉ  --
('Địa chỉ', 'contact.enterprise.enterpriseInfo.address', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Địa chỉ đăng ký kinh doanh --
('Địa chỉ đăng ký kinh doanh', 'contact.enterprise.enterpriseInfo.businessAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":[],"patternCombination":0,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Địa chỉ lắp đặt --
('Địa chỉ lắp đặt', 'contact.enterprise.enterpriseInfo.setUpAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ lắp đặt","labelEnabled":true,"hintText":"Nhập địa chỉ lắp đặt","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Số bảo hiểm xã hội --
('Số bảo hiểm xã hội', 'contact.enterprise.enterpriseInfo.socialInsNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số  BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[],"patternCombination":0,"maxLength":10,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Trạng thái hoạt động --
('Trạng thái hoạt động', 'contact.enterprise.enterpriseInfo.actStatus', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Trạng thái hoạt động","labelEnabled":true,"hintText":"Chọn trạng thái hoạt động","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Đang hoạt động","Tạm dừng","Đã đóng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Nhà mạng --
('Nhà mạng', 'contact.enterprise.enterpriseInfo.teleProvider', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nhà mạng","labelEnabled":true,"hintText":"Chọn nhà mạng","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Vinaphone","Viettel","Mobifone"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Số điện thoại --
('Số điện thoại', 'contact.enterprise.enterpriseInfo.phoneNum', true, 'NUMBER', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Fax --
('Fax', 'contact.enterprise.enterpriseInfo.fax', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Fax","labelEnabled":true,"hintText":"Nhập Fax","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Email --
('Email', 'contact.enterprise.enterpriseInfo.email', true, 'EMAIL', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"","lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Số giấy phép đăng ký kinh doanh --
('Số giấy phép ĐKKD', 'contact.enterprise.enterpriseInfo.businessNumber', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số giấy phép ĐKKD","labelEnabled":true,"hintText":"Nhập số giấy phép ĐKKD","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Quy mô --
('Quy mô doanh nghiệp', 'contact.enterprise.enterpriseInfo.businessSize', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quy mô doanh nghiệp","labelEnabled":true,"hintText":"Chọn quy mô doanh nghiệp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chỉ có bạn","2-9","10-99","100-299","300+"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên loại hình doanh nghiệp --
('Tên loại hình doanh nghiệp', 'contact.enterprise.enterpriseInfo.businessType', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên loại hình doanh nghiệp","labelEnabled":true,"hintText":"Chọn tên loại hình doanh nghiệp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Công ty Cổ phần"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
--  Ngày thành lập --
('Ngày thành lập', 'contact.enterprise.enterpriseInfo.establishDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày thành lập","labelEnabled":true,"hintText":"Ngày thành lập","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[dd//mm//yyyy]*$","lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Mã ngành nghề kinh doanh --
('Mã ngành nghề kinh doanh', 'contact.enterprise.enterpriseInfo.areaCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã ngành nghề kinh doanh","labelEnabled":true,"hintText":"Chọn mã ngành nghề kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["E","I"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên ngành nghề kinh doanh --
('Tên nghành nghề kinh doanh', 'contact.enterprise.enterpriseInfo.businessArea', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên ngàng nghề kinh doanh","labelEnabled":true,"hintText":"Chọn tên ngành nghề kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["CÔNG NGHIẾP CHẾ BIẾN, CHẾ TẠO"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'contact.enterprise.enterpriseInfo.description', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Chọn tên ngành nghề kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["CÔNG NGHIẾP CHẾ BIẾN, CHẾ TẠO"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tải lên đăng ký kinh doanh  --
('Tải lên đăng ký kinh doanh', 'contact.enterprise.enterpriseInfo.busRegCert', true, 'UPLOAD_FILE', 'CUSTOMER_CONTACT', '{"label":"Tải lên đăng ký kinh doanh","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["xlsx","xls","docx","doc","ppt","pptx","pdf"],"uploadMaxSize":10,"uploadMaxFile":1,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),

-- ============ THÔNG TIN KHÁC ============= --
-- Section Thông tin khác  --
('Thông tin khác', 'contact.enterprise.section.otherInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin khác","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Người tạo --
('Người tạo', 'contact.enterprise.otherInfo.createdBy', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Người tạo","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Nguồn khách hàng --
('Nguồn khách hàng', 'contact.enterprise.otherInfo.createdSource', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nguồn khách hàng","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Import File","Đồng bộ khách hàng doanh nghiệp","Liên hệ trên nền tảng","Liên hệ tự do","Liên hệ qua phone/email","Giới thiệu của nhân viên","Giới thiệu của khách hàng","Nguồn từ affiliate","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Nhân sự phụ trách --
('Nhân sự phụ trách','contact.enterprise.otherInfo.assignee',true,'DROPDOWN_LIST','CUSTOMER_CONTACT','{"label":"Nhân sự phụ trách","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]','KHDN',1),
-- Phân vùng
('Phân vùng','contact.enterprise.otherInfo.partition',true,'DROPDOWN_LIST','CUSTOMER_CONTACT','{"label":"Phân vùng","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]','KHDN',1),

-- ============ THÔNG TIN NGƯỜI ĐẠI DIỆN ============= --
-- Section Thông tin người đại diện --
('Thông tin người đại diện ', 'contact.enterprise.section.repInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin người đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tên người đại diện --
('Họ tên', 'contact.enterprise.rep.name', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Họ tên","labelEnabled":true,"hintText":"Nhập họ tên","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Giới tính  --
('Giới tính', 'contact.enterprise.rep.sex', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Nam","Nữ","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Chức danh --
('Chức danh', 'contact.enterprise.rep.position', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chức danh","labelEnabled":true,"hintText":"Nhập chức danh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Ngày sinh --
('Ngày sinh', 'contact.enterprise.rep.birthDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Ngày sinh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Quốc tịch người đại diện --
('Quốc tịch', 'contact.enterprise.rep.nationality', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quốc tịch","labelEnabled":true,"hintText":"Chọn quốc tịch","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Việt Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Dân tộc người đại diện --
('Dân tộc', 'contact.enterprise.rep.ethnicity', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Dân tộc","labelEnabled":true,"hintText":"Chọn dân tộc","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Kinh"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Loại giấy chứng thực --
('Loại giấy chứng thực', 'contact.enterprise.rep.certType', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Số chứng thực cá nhân --
('Số chứng thực cá nhân', 'contact.enterprise.rep.certNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số chứng thực","labelEnabled":true,"hintText":"Nhập số chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Ngày cấp chứng thực cá nhân--
('Ngày cấp', 'contact.enterprise.rep.certRegDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Nhập ngày cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[dd//mm//yyyy]*$","lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Nơi cấp --
('Ngày cấp', 'contact.enterprise.rep.certRegPlace', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nhập nơi cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Nơi đăng ký hộ khẩu --
('Ngày đăng ký hộ khẩu', 'contact.enterprise.rep.originalAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Nơi đăng ký hộ khẩu","labelEnabled":true,"hintText":"Nhập nới đăng ký hộ khẩu","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Chỗ ở hiện tại --
('Chỗ ở hiện tại', 'contact.enterprise.rep.currentAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chỗ ở hiện tại","labelEnabled":true,"hintText":"Nhập chỗ ở hiện tại","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'contact.enterprise.rep.description', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Tải lên CMND/CCCD --
('Tải lên CMND/CCCD', 'contact.enterprise.rep.indentityCertFile', true, 'UPLOAD_FILE', 'CUSTOMER_CONTACT', '{"label":"Tải CMND/CCCD","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["xlsx","xls","pptx","ppt","pdf","docx","doc"],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1),
-- Thêm liên hệ khác --
('Thêm khách hàng khác', 'contact.enterprise.addNewContact', true, 'CHECKBOX', 'CUSTOMER_CONTACT', '{"label":"Thêm khách hàng khác","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["ON","OFF"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'KHDN', 1);


-- Liên hệ khách hàng hộ kinh doanh HKD --
DELETE FROM "vnpt_dev"."custom_field" WHERE category = 'CUSTOMER_CONTACT' AND customer_type = 'HKD' AND portal_type = 1 and is_standard = true;
INSERT INTO "vnpt_dev"."custom_field" ("name", code, is_standard, "type", category, config, lst_permission, customer_type, portal_type)
-- THÔNG TIN LIÊN HỆ --
VALUES
-- Section Thông tin liên hệ --
('Thông tin liên hệ ', 'contact.household.section.contactInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin liên hệ","labelEnabled":true,"hintText":null,"isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Ảnh đại diện liên hệ --
('Ảnh đại diện liên hệ', 'contact.household.avatarUpload', true, 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên ảnh đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["jpg","png"],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên liên hệ  --
('Tên người liên hệ', 'contact.household.name', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên người liên hệ","labelEnabled":true,"hintText":"Nhập tên người liên hệ","isUnique":true,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[],"patternCombination":0,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Số điện thoại liên hệ  --
('Số điện thoại liên hệ', 'contact.household.phone', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":[],"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Email thoại liên hệ  --
('Email liên hệ', 'contact.household.email', true, 'EMAIL', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Mạng xã hội  --
('Mạng xã hội', 'contact.household.socialNetwork', true, 'URL', 'CUSTOMER_CONTACT', '{"label":"Mạng xã hội","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tỉnh thành liên hệ --
('Tỉnh thành liên hệ', 'contact.household.province', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tỉnh thành","labelEnabled":true,"hintText":"Chọn tỉnh thành","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Hà Nội","Hồ Chí Minh","Đà Nẵng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Địa chỉ  --
('Địa chỉ liên hệ', 'contact.household.address', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên tổ chức --
('Tên tổ chức', 'contact.household.organization', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên tổ chức","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Chức vụ --
('Chức vụ liên hệ', 'contact.household.position', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chức vụ","labelEnabled":true,"hintText":"Nhập chức vụ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Lời nhắn  --
('Lới nhắn', 'contact.household.note', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'contact.household.description', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  THÔNG TIN DOANH NGHIỆP --
-- Section thông tin doanh nghiệp  --
('Thông tin doanh nghiệp', 'contact.household.section.businessInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin doanh nghiệp","labelEnabled":true,"hintText":null,"isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"","uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên doanh nghiệp --
('Tên doanh nghiệp', 'contact.household.enterpriseInfo.name', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên doanh nghiệp","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Chi cục thuế  --
('Chi cục thuế', 'contact.household.enterpriseInfo.taxDepart', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chi cục thuế","labelEnabled":true,"hintText":"Nhập chi cục thuế","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Mã số thuế --
('Mã số thuế', 'contact.household.enterpriseInfo.taxNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Quốc gia  --
('Quốc gia', 'contact.household.enterpriseInfo.nationality', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quốc gia","labelEnabled":true,"hintText":"Chọn quốc gia","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Việt Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Miền --
('Miền', 'contact.household.enterpriseInfo.region', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Miền","labelEnabled":true,"hintText":"Chọn miền","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Miền Bắc","Miền Trung","Miền Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Mã tỉnh  --
('Mã tỉnh', 'contact.household.enterpriseInfo.provinceCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã tỉnh","labelEnabled":true,"hintText":"Chọn mã tỉnh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["HNI"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên tỉnh --
('Tên tỉnh', 'contact.household.enterpriseInfo.provName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên tỉnh","labelEnabled":true,"hintText":"Chọn tên tỉnh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Hà Nội"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Mã huyện  --
('Mã Huyện', 'contact.household.enterpriseInfo.districtCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã huyện","labelEnabled":true,"hintText":"Chọn mã huyện","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["HNI-20"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên huyện --
('Tên huyện', 'contact.household.enterpriseInfo.districtName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên huyện","labelEnabled":true,"hintText":"Chọn tên huyện","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Cầu Giấy"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Mã xã  --
('Mã xã', 'contact.household.enterpriseInfo.communeCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã xã","labelEnabled":true,"hintText":"Chọn mã xã","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên xã --
('Tên xã', 'contact.household.enterpriseInfo.communeName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên xã","labelEnabled":true,"hintText":"Chọn tên xã","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên đường phố --
('Tên đường phố', 'contact.household.enterpriseInfo.streetName', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên đường, phố","labelEnabled":true,"hintText":"Chọn tên đường, phố","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Địa chỉ  --
('Địa chỉ', 'contact.household.enterpriseInfo.address', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Địa chỉ đăng ký kinh doanh --
('Địa chỉ đăng ký kinh doanh', 'contact.household.enterpriseInfo.businessAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":[],"patternCombination":0,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Địa chỉ lắp đặt --
('Địa chỉ lắp đặt', 'contact.household.enterpriseInfo.setUpAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ lắp đặt","labelEnabled":true,"hintText":"Nhập địa chỉ lắp đặt","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Số bảo hiểm xã hội --
('Số bảo hiểm xã hội', 'contact.household.enterpriseInfo.socialInsNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số  BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[],"patternCombination":0,"maxLength":10,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Trạng thái hoạt động --
('Trạng thái hoạt động', 'contact.household.enterpriseInfo.actStatus', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Trạng thái hoạt động","labelEnabled":true,"hintText":"Chọn trạng thái hoạt động","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Đang hoạt động","Tạm dừng","Đã đóng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Nhà mạng --
('Nhà mạng', 'contact.household.enterpriseInfo.teleProvider', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nhà mạng","labelEnabled":true,"hintText":"Chọn nhà mạng","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Vinaphone","Viettel","Mobifone"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Số điện thoại --
('Số điện thoại', 'contact.household.enterpriseInfo.phoneNum', true, 'NUMBER', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Fax --
('Fax', 'contact.household.enterpriseInfo.fax', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Fax","labelEnabled":true,"hintText":"Nhập Fax","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Email --
('Email', 'contact.household.enterpriseInfo.email', true, 'EMAIL', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"","lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Số giấy phép đăng ký kinh doanh --
('Số giấy phép ĐKKD', 'contact.household.enterpriseInfo.businessNumber', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số giấy phép ĐKKD","labelEnabled":true,"hintText":"Nhập số giấy phép ĐKKD","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Quy mô --
('Quy mô doanh nghiệp', 'contact.household.enterpriseInfo.businessSize', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quy mô doanh nghiệp","labelEnabled":true,"hintText":"Chọn quy mô doanh nghiệp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chỉ có bạn","2-9","10-99","100-299","300+"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên loại hình doanh nghiệp --
('Tên loại hình doanh nghiệp', 'contact.household.enterpriseInfo.businessType', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên loại hình doanh nghiệp","labelEnabled":true,"hintText":"Chọn tên loại hình doanh nghiệp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Công ty Cổ phần"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
--  Ngày thành lập --
('Ngày thành lập', 'contact.household.enterpriseInfo.establishDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày thành lập","labelEnabled":true,"hintText":"Ngày thành lập","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[dd//mm//yyyy]*$","lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Mã ngành nghề kinh doanh --
('Mã ngành nghề kinh doanh', 'contact.household.enterpriseInfo.areaCode', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Mã ngành nghề kinh doanh","labelEnabled":true,"hintText":"Chọn mã ngành nghề kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["E","I"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên ngành nghề kinh doanh --
('Tên nghành nghề kinh doanh', 'contact.household.enterpriseInfo.businessArea', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tên ngàng nghề kinh doanh","labelEnabled":true,"hintText":"Chọn tên ngành nghề kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["CÔNG NGHIẾP CHẾ BIẾN, CHẾ TẠO"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'contact.household.enterpriseInfo.description', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Chọn tên ngành nghề kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["CÔNG NGHIẾP CHẾ BIẾN, CHẾ TẠO"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tải lên đăng ký kinh doanh  --
('Tải lên đăng ký kinh doanh', 'contact.household.enterpriseInfo.busRegCert', true, 'UPLOAD_FILE', 'CUSTOMER_CONTACT', '{"label":"Tải lên đăng ký kinh doanh","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["xlsx","xls","docx","doc","ppt","pptx","pdf"],"uploadMaxSize":10,"uploadMaxFile":1,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- THÔNG TIN KHÁC--
-- Section Thông tin khác  --
('Thông tin khác', 'contact.household.section.otherInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin khác","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Người tạo --
('Người tạo', 'contact.household.otherInfo.createdBy', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Người tạo","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Nguồn khách hàng --
('Nguồn khách hàng', 'contact.household.otherInfo.createdSource', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nguồn khách hàng","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Import File","Đồng bộ khách hàng doanh nghiệp","Liên hệ trên nền tảng","Liên hệ tự do","Liên hệ qua phone/email","Giới thiệu của nhân viên","Giới thiệu của khách hàng","Nguồn từ affiliate","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Nhân sự phụ trách --
('Nhân sự phụ trách','contact.household.otherInfo.assignee',true,'DROPDOWN_LIST','CUSTOMER_CONTACT','{"label":"Nhân sự phụ trách","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]','HKD',1),
-- Phân vùng
('Phân vùng','contact.household.otherInfo.partition',true,'DROPDOWN_LIST','CUSTOMER_CONTACT','{"label":"Phân vùng","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]','HKD',1),
-- THÔNG TIN NGƯỜI ĐẠI DIỆN  --
-- Section Thông tin người đại diện --
('Thông tin người đại diện ', 'contact.household.section.repInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin người đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tên người đại diện --
('Họ tên', 'contact.household.rep.name', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Họ tên","labelEnabled":true,"hintText":"Nhập họ tên","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Giới tính  --
('Giới tính', 'contact.household.rep.sex', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Nam","Nữ","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Chức danh --
('Chức danh', 'contact.household.rep.position', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chức danh","labelEnabled":true,"hintText":"Nhập chức danh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Ngày sinh --
('Ngày sinh', 'contact.household.rep.birthDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Ngày sinh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Quốc tịch người đại diện --
('Quốc tịch', 'contact.household.rep.nationality', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quốc tịch","labelEnabled":true,"hintText":"Chọn quốc tịch","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Việt Nam"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Dân tộc người đại diện --
('Dân tộc', 'contact.household.rep.ethnicity', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Dân tộc","labelEnabled":true,"hintText":"Chọn dân tộc","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Kinh"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Loại giấy chứng thực --
('Loại giấy chứng thực', 'contact.household.rep.certType', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Số chứng thực cá nhân --
('Số chứng thực cá nhân', 'contact.household.rep.certNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số chứng thực","labelEnabled":true,"hintText":"Nhập số chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Ngày cấp chứng thực cá nhân--
('Ngày cấp', 'contact.household.rep.certRegDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Nhập ngày cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[dd//mm//yyyy]*$","lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Nơi cấp --
('Ngày cấp', 'contact.household.rep.certRegPlace', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nhập nơi cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Nơi đăng ký hộ khẩu --
('Ngày đăng ký hộ khẩu', 'contact.household.rep.originalAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Nơi đăng ký hộ khẩu","labelEnabled":true,"hintText":"Nhập nới đăng ký hộ khẩu","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Chỗ ở hiện tại --
('Chỗ ở hiện tại', 'contact.household.rep.currentAdd', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chỗ ở hiện tại","labelEnabled":true,"hintText":"Nhập chỗ ở hiện tại","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'contact.household.rep.description', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Tải lên CMND/CCCD --
('Tải lên CMND/CCCD', 'contact.household.rep.indentityCertFile', true, 'UPLOAD_FILE', 'CUSTOMER_CONTACT', '{"label":"Tải CMND/CCCD","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["xlsx","xls","pptx","ppt","pdf","docx","doc"],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1),
-- Thêm liên hệ khác --
('Thêm khách hàng khác', 'contact.household.addNewContact', true, 'CHECKBOX', 'CUSTOMER_CONTACT', '{"label":"Thêm khách hàng khác","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["ON","OFF"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'HKD', 1);


---- Liên hệ khách hàng cá nhân (CN) ----
DELETE FROM "vnpt_dev"."custom_field" WHERE category = 'CUSTOMER_CONTACT' AND customer_type = 'CN' AND portal_type = 1 and is_standard = true;
INSERT INTO "vnpt_dev"."custom_field" (name, code, is_standard, type, category, config, lst_permission, customer_type, portal_type) VALUES
-- THÔNG TIN LIÊN HỆ --
-- Section thông tin liên hệ --
('Thông tin liên hệ', 'contact.personal.section.contactInf', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin liên hệ","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Ảnh đại diện liên hệ --
('Tải lên ảnh liên hệ', 'personalContactUploadId', true, 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên ảnh liên hệ","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["jpg","png"],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Tên người liên hệ --
('Tên người liên hệ', 'personalContactName', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên người liên hệ","labelEnabled":true,"hintText":"Nhập tên người liên hệ","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Số điện thoại --
('Số điện thoại', 'contact.personal.contactInfo.phone', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Email --
('Email', 'contact.personal.contactInfo.email', true, 'INPUT_TAG', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":"^[0-9]*$","lstPatternToken":null,"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Mạng xã hội --
('Mạng xã hội', 'contact.personal.contactInfo.socialNetwork', true, 'URL', 'CUSTOMER_CONTACT', '{"label":"Mạng xã hội","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Địa chỉ --
('Địa chỉ', 'personalContactAddress', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Lời nhắn --
('Lời nhắn', 'personalContactMessage', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Lời nhắn","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'personalContactGeneralField', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- THÔNG TIN CÁ NHÂN --
-- Section thông tin liên hệ --
('Thông tin cá nhân', 'contact.personal.section.personalInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin liên hệ","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Ảnh đại diện liên hệ --
('Tải lên ảnh liên hệ', 'personalContactInfoAvatarId', true, 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên ảnh đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":["jpg","png"],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Họ --
('Họ', 'personalContactInfoLastname', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Họ","labelEnabled":true,"hintText":"Nhập họ","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Tên --
('Tên', 'personalContactInfoFirstname', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên","labelEnabled":true,"hintText":"Nhập tên","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Email --
('Email', 'contact.personal.personalInfo.email', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Số điện thoại --
('Số điện thoại', 'contact.personal.personalInfo.phone', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Địa chỉ --
('Địa chỉ', 'personalContactInfoAddr', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Ngày sinh --
('Ngày sinh', 'personalContactInfoBirthdate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Chọn ngày sinh","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Giới tính --
('Giới tính', 'personalContactInfoGender', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Chọn giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Nam","Nữ"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Dân tộc --
('Dân tộc', 'contact.personal.personalInfo.ethnicity', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Dân tộc","labelEnabled":true,"hintText":"Chọn dân tộc","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Kinh"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Loại giấy chứng thực --
('Loại giấy chứng thực', 'personalContactInfoCertType', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Chứng minh thư nhân dân","Hộ chiếu","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Số giấy chứng thực --
('Số giấy chứng thực', 'personalContactInfoCertNum', true, 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số giấy chứng thực","labelEnabled":true,"hintText":"Nhập số chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":30,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Tải lên giấy chứng thực --
('Tải lên giấy chứng thực', 'personalContactInfoCertAvatarId', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Tải lên giấy chứng thực","labelEnabled":true,"hintText":"Tải file","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_FILE","uploadExtension":["xlsx","xls","docx","doc","pdf","pptx","ppt"],"uploadMaxSize":10,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Ngày cấp --
('Ngày cấp', 'personalContactInfoCertDate', true, 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),                                                                                                                                          -- Nơi cấp --
-- Nơi cấp --
('Nơi cấp', 'personalContactInfoCertAddr', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Giới thiệu chung --
('Giới thiệu chung', 'personalContactInfoGen', true, 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Thêm liên hệ khác --
('Thêm khách hàng khác', 'contact.personal.addNewContact', true, 'CHECKBOX', 'CUSTOMER_CONTACT', '{"label":"Thêm khách hàng khác","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["ON","OFF"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- THÔNG TIN KHÁC --
-- Section Thông tin khác  --
('Thông tin khác', 'contact.personal.section.otherInfo', true, 'SECTION', 'CUSTOMER_CONTACT', '{"label":"Thông tin khác","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Người tạo --
('Người tạo', 'contact.personal.otherInfo.createdBy', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Người tạo","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Nguồn khách hàng --
('Nguồn khách hàng', 'contact.personal.otherInfo.createdSource', true, 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nguồn khách hàng","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":null,"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":["Import File","Đồng bộ khách hàng doanh nghiệp","Liên hệ trên nền tảng","Liên hệ tự do","Liên hệ qua phone/email","Giới thiệu của nhân viên","Giới thiệu của khách hàng","Nguồn từ affiliate","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[],"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', '[]', 'CN', 1),
-- Nhân sự phụ trách --
('Nhân sự phụ trách','contact.personal.otherInfo.assignee',true,'DROPDOWN_LIST','CUSTOMER_CONTACT','{"label":"Nhân sự phụ trách","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]','CN',1),
-- Phân vùng
('Phân vùng','contact.personal.otherInfo.partition',true,'DROPDOWN_LIST','CUSTOMER_CONTACT','{"label":"Phân vùng","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]','CN',1);


-- CUSTOM LAYOUT --
-- ====== Tạo customer_contact template layout ====== --
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

-- Xóa layout cũ nếu đã tồn tại --
DELETE FROM "vnpt_dev"."custom_layout" WHERE vnpt_dev.custom_layout.name IN ('Liên hệ doanh nghiệp', 'Liên hệ hộ kinh doanh', 'Liên hệ cá nhân', 'Tạo liên hệ Khách hàng doanh nghiệp (Mặc định)', 'Tạo liên hệ Khách hàng hộ kinh doanh (Mặc định)', 'Tạo liên hệ Khách hàng cá nhân (Mặc định)');

-- Thêm layout mới --
INSERT INTO "vnpt_dev"."custom_layout" (name, category, is_template, template_id, is_default, sme_enabled, dev_enabled, admin_enabled, lst_permission, lst_standard_field, lst_custom_field, layout_content, detail_layout_content, data_dependency, created_at, created_by, modified_at, modified_by, status, customer_type, portal_type)
VALUES ('Liên hệ doanh nghiệp', 'CUSTOMER_CONTACT', true, null, false, false, false, true, '[]', '[]', '[]', '[]', '[]' , '[]', '2023-02-02 00:00:00',
        1, '2023-02-01 00:00:00', null, 1, 'KHDN', 1);

INSERT INTO "vnpt_dev"."custom_layout" (name, category, is_template, template_id, is_default, sme_enabled, dev_enabled, admin_enabled, lst_permission, lst_standard_field, lst_custom_field, layout_content, detail_layout_content, data_dependency, created_at, created_by, modified_at, modified_by, status, customer_type, portal_type)
VALUES ('Liên hệ hộ kinh doanh', 'CUSTOMER_CONTACT', true, null, false, false, false, true, '[]', '[]', '[]', '[]', '[]' , '[]', '2023-02-02 00:00:00',
        1, '2023-02-01 00:00:00', null, 1, 'HKD', 1);

INSERT INTO "vnpt_dev"."custom_layout" (name, category, is_template, template_id, is_default, sme_enabled, dev_enabled, admin_enabled, lst_permission, lst_standard_field, lst_custom_field, layout_content, detail_layout_content, data_dependency, created_at, created_by, modified_at, modified_by, status, customer_type, portal_type)
VALUES ('Liên hệ cá nhân', 'CUSTOMER_CONTACT', true, null, false, false, false, true, '[]', '[]', '[]', '[]', '[]', '[]', '2023-02-02 00:00:00', 1,
        '2023-02-01 00:00:00', null, 1, 'CN', 1);

-- === Tạo layout liên hệ mặc định === ---
INSERT INTO "vnpt_dev"."custom_layout" (name, category, is_template, template_id, is_default, sme_enabled, dev_enabled, admin_enabled, lst_permission, lst_standard_field, lst_custom_field, layout_content, detail_layout_content, data_dependency, created_at, created_by, modified_at, modified_by, status, customer_type, portal_type)
VALUES ('Tạo liên hệ Khách hàng Doanh Nghiệp (Mặc định)', 'CUSTOMER_CONTACT', true, (SELECT id FROM vnpt_dev.custom_layout WHERE category = 'CUSTOMER_CONTACT' and name = 'Liên hệ doanh nghiệp' and is_template = true), true, false, false, true, '[]', '[]', '[]', '[]', '[]', '[]', '2023-02-02 00:00:00', 1,
        '2023-02-01 00:00:00', null, 1, 'KHDN', 1);

INSERT INTO "vnpt_dev"."custom_layout" (name, category, is_template, template_id, is_default, sme_enabled, dev_enabled, admin_enabled, lst_permission, lst_standard_field, lst_custom_field, layout_content, detail_layout_content, data_dependency, created_at, created_by, modified_at, modified_by, status, customer_type, portal_type)
VALUES ('Tạo liên hệ Khách hàng hộ kinh doanh (Mặc định)', 'CUSTOMER_CONTACT', true, (SELECT id FROM vnpt_dev.custom_layout WHERE category = 'CUSTOMER_CONTACT' and name = 'Liên hệ hộ kinh doanh' and is_template = true), true, false, false, true, '[]', '[]', '[]', '[]', '[]', '[]', '2023-02-02 00:00:00', 1,
        '2023-02-01 00:00:00', null, 1, 'HKD', 1);

INSERT INTO "vnpt_dev"."custom_layout" (name, category, is_template, template_id, is_default, sme_enabled, dev_enabled, admin_enabled, lst_permission, lst_standard_field, lst_custom_field, layout_content, detail_layout_content, data_dependency, created_at, created_by, modified_at, modified_by, status, customer_type, portal_type)
VALUES ('Tạo liên hệ Khách hàng cá nhân (Mặc định)', 'CUSTOMER_CONTACT', true, (SELECT id FROM vnpt_dev.custom_layout WHERE category = 'CUSTOMER_CONTACT' and name = 'Liên hệ cá nhân' and is_template = true), true, false, false, true, '[]', '[]', '[]', '[]', '[]', '[]', '2023-02-02 00:00:00', 1,
        '2023-02-01 00:00:00', null, 1, 'CN', 1);

-- Cập nhật standard field và layout content --
-- Liên hệ khách hàng doanh nghiệp (KHDN) --
UPDATE "vnpt_dev"."custom_layout"
SET lst_standard_field = raw.lst_standard_field,
    layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["TyDwb2U4C4", "ZtKeyzK1SS", "hHoQRi0k_Q", "c_Zpxts8cs"], "props": {"id": "createViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "0oCnCLEQ3G": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Nơi đăng ký hộ khẩu", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6292, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập nơi đăng ký hộ khẩu", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepRegisterAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "0pBpnWlSzH": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên đăng ký kinh doanh", "configs": [], "fieldId": 6283, "formats": [".jpeg, .jpg", ".png", ".webp", ".tiff, .jfif", ".ico"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": false, "listNote": [{"id": "00ddb873-9d37-411e-a556-bd3ecdaf34d4", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyBusinessRegistrationFile", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "1atcRAYsA0": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên tổ chức", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6244, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên tổ chức", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactOrganization", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "kzfZoxGUta", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "1pxQjmt0p3": {"type": {"resolvedName": "SelectSocialNetwork"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_gHF0KDWqtoZn6XLH", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "2Fn6wI_e6_": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên người liên hệ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]"], "configs": [], "fieldId": 6240, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên người liên hệ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "34zn0f06CE": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6252, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "3chwh4LTNm": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6282, "isValid": true, "touched": false, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyDescription", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "4UL6s055bw": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "BC5VMkl7FY", "1": "la9vSD1Gop"}}, "5-L1BU4jMR": {"type": {"resolvedName": "SelectEnterpriseAddressInfo"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "selectEnterpriseAddressInfo_gPeYL0JhiSkMPMgc", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "63dOJnc1MY": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ đăng ký kinh doanh", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6253, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ đăng ký kinh doanh", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyBusinessAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "67kwu29Cfv": {"type": {"resolvedName": "DatePicker"}, "nodes": [], "props": {"label": "Ngày cấp", "configs": [], "fieldId": 6290, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "defaultDate": "2023-02-28T02:59:42.954Z", "placeholder": "Chọn ngày cấp", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "defaultDisplay": "unset", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityDate", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "zayPtybU67", "isCanvas": true, "displayName": "Date Picker", "linkedNodes": {}}, "6Jhyp_b8AL": {"type": {"resolvedName": "Container"}, "nodes": ["J6b8lTaa3h", "VPIgtAmVAz"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "8h5o9442ho", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "8h5o9442ho": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "9nf1d44RIJ", "1": "6Jhyp_b8AL"}}, "8rsbQEOV5L": {"type": {"resolvedName": "CustomCustomerOtherInfomation"}, "nodes": [], "props": {"label": "Thông tin khác của khách hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomCustomerOtherInfomation_iBJoiXfpfMcXoGwl", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "nywp2oraI3", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "9aBHItm52_": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Quy mô", "configs": [], "fieldId": 6269, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn quy mô", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanySize", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Chỉ có bạn", "label": "Chỉ có bạn", "default": false}, {"id": "2-9", "label": "2-9", "default": false}, {"id": "10-99", "label": "10-99", "default": false}, {"id": "100-299", "label": "100-299", "default": false}, {"id": "300+", "label": "300+", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "9bxlzI2L9l": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "wf4IeENcEn", "1": "rS_CNLPWM7"}}, "9nf1d44RIJ": {"type": {"resolvedName": "Container"}, "nodes": ["ZuX84NZyQ6", "9teDNjwbd9"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "8h5o9442ho", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "9teDNjwbd9": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chức danh", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6286, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chức danh", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepRole", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9nf1d44RIJ", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "9wiiVm0X3V": {"type": {"resolvedName": "Container"}, "nodes": ["S7Hfdb7Pl5", "Z7nfWm2AvO"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "fcT3iMsOjI", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "BC5VMkl7FY": {"type": {"resolvedName": "Container"}, "nodes": ["Gdwvp1iDZj"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "4UL6s055bw", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "BJrBqcN4Tn": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Lời nhắn", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]"], "configs": [], "fieldId": 6247, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập lời nhắn", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactMessage", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "Cnw6WY86TY": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Mã số thuế", "format": ["[0-9]"], "configs": [], "fieldId": 6251, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập mã số thuế", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyTaxCode", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "la9vSD1Gop", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "Gdwvp1iDZj": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chi cục thuế", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6250, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chi cục thuế", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyTaxDepartment", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "BC5VMkl7FY", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "IRVZv-yeSV": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "kzfZoxGUta", "1": "jZh_0GIeyp"}}, "IZ221sqpeu": {"type": {"resolvedName": "ProvinceList"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "ProvinceList_wUy2WYW92bjY01ai", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "kzfZoxGUta", "isCanvas": true, "displayName": "Section", "linkedNodes": {}}, "J-qgVhxO4g": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Loại giấy chứng thực", "configs": [], "fieldId": 6288, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn loại giấy chứng thực", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityType", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Chứng minh nhân dân", "label": "Chứng minh nhân dân", "default": false}, {"id": "Hộ chiếu", "label": "Hộ chiếu", "default": false}, {"id": "Thẻ căn cước công dân", "label": "Thẻ căn cước công dân", "default": false}, {"id": "Khác", "label": "Khác", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "zayPtybU67", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "J6b8lTaa3h": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Giới tính", "configs": [], "fieldId": 6285, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn giới tính", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepGender", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Nữ", "label": "Nữ", "default": false}, {"id": "Nam", "label": "Nam", "default": false}, {"id": "Khác", "label": "Khác", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "6Jhyp_b8AL", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "KBRiL2JAWT": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên ảnh đại diện", "configs": [], "fieldId": 6239, "formats": [".jpeg, .jpg", ".png", ".webp", ".tiff, .jfif", ".ico"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": false, "listNote": [{"id": "29409248-4d89-489c-b5c9-45b76b4decc1", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "contactAvatarFileId", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "NhUfMz1ne0": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6246, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "jZh_0GIeyp", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "PKlRUEmcPG": {"type": {"resolvedName": "Container"}, "nodes": ["8h5o9442ho", "aUEwyZbtwY", "fcT3iMsOjI", "0oCnCLEQ3G", "me5pG_5esF", "vZW479yqIE", "a1teUDRTeE"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "hHoQRi0k_Q", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "Po6S3RPKIE": {"type": {"resolvedName": "Container"}, "nodes": ["KBRiL2JAWT", "2Fn6wI_e6_", "1pxQjmt0p3", "IRVZv-yeSV", "BJrBqcN4Tn", "oqU4EvuqUH"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "TyDwb2U4C4", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "QB-SbM0D_C": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên doanh nghiệp", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6249, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên doanh nghiệp", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "RqmQ5I0X5B": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chức vụ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6245, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chức vụ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactRole", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "jZh_0GIeyp", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "S7Hfdb7Pl5": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số chứng thực cá nhân", "format": ["[0-9]"], "configs": [], "fieldId": 6289, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số chứng thực cá nhân", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityNo", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9wiiVm0X3V", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "Sn3SLP6yCC": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ lắp đặt", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6254, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ lắp đặt", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanySetupAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "TyDwb2U4C4": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin liên hệ", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "Po6S3RPKIE"}}, "VPIgtAmVAz": {"type": {"resolvedName": "DatePicker"}, "nodes": [], "props": {"label": "Ngày sinh", "configs": [], "fieldId": 6287, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "defaultDate": "2023-02-28T02:50:13.855Z", "placeholder": "Chọn ngày sinh", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "defaultDisplay": "unset", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepBirth", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "6Jhyp_b8AL", "isCanvas": true, "displayName": "Date Picker", "linkedNodes": {}}, "Y4YRP_90g4": {"type": {"resolvedName": "Container"}, "nodes": ["QB-SbM0D_C", "4UL6s055bw", "5-L1BU4jMR", "34zn0f06CE", "63dOJnc1MY", "Sn3SLP6yCC", "9bxlzI2L9l", "uqpNRZP1u6", "3chwh4LTNm", "0pBpnWlSzH"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "ZtKeyzK1SS", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "Z7nfWm2AvO": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Nơi cấp", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6291, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập nơi cấp", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9wiiVm0X3V", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "ZtKeyzK1SS": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin doanh nghiệp", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "Y4YRP_90g4"}}, "ZuX84NZyQ6": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên người đại diện", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]"], "configs": [], "fieldId": 6284, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên người đại diện", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9nf1d44RIJ", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "a1teUDRTeE": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên CMTND/CCCD", "configs": [], "fieldId": 6295, "formats": [".jpeg, .jpg", ".png", ".webp", ".tiff, .jfif", ".ico"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": false, "listNote": [{"id": "33c86db2-78c8-4ab0-950a-8564264bb3e4", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityFile", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "aUEwyZbtwY": {"type": {"resolvedName": "CustomerNationality"}, "nodes": [], "props": {"label": "Quốc tịch khách hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomerNationality_Fu6zOoJAZbl6A9Mu", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "c_Zpxts8cs": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin khác", "configs": [], "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "nywp2oraI3"}}, "ejnVvC4Xt9": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Trạng thái hoạt động", "configs": [], "fieldId": 6263, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn trạng thái hoạt động", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyStatus", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Đang hoạt động", "label": "Đang hoạt động", "default": false}, {"id": "Tạm dừng", "label": "Tạm dừng", "default": false}, {"id": "Đã đóng", "label": "Đã đóng", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "fcT3iMsOjI": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "zayPtybU67", "1": "9wiiVm0X3V"}}, "fi0Q_2uqVr": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số giấy phép ĐKKD", "format": ["[0-9]"], "configs": [], "fieldId": 6268, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số giấy phép ĐKKD", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyBusinessRegistration", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "hHoQRi0k_Q": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin người đại diện", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "PKlRUEmcPG"}}, "hVCbJ2TQFk": {"type": {"resolvedName": "SelectPartition"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_t4gssnADF8Z4FL60", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "nywp2oraI3", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "igB4BtQsJM": {"type": {"resolvedName": "SelectAdmin"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_qlkZKwAN7oWYi7Ej", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "nywp2oraI3", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "jZh_0GIeyp": {"type": {"resolvedName": "Container"}, "nodes": ["RqmQ5I0X5B", "NhUfMz1ne0"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "IRVZv-yeSV", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "jxTMdZC-rt": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Nhà mạng", "configs": [], "fieldId": 6264, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn nhà mạng", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyNetwork", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Vinaphone", "label": "Vinaphone", "default": false}, {"id": "Viettel", "label": "Viettel", "default": false}, {"id": "Mobifone", "label": "Mobifone", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "kzfZoxGUta": {"type": {"resolvedName": "Container"}, "nodes": ["1atcRAYsA0", "IZ221sqpeu"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "IRVZv-yeSV", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "la9vSD1Gop": {"type": {"resolvedName": "Container"}, "nodes": ["Cnw6WY86TY"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "4UL6s055bw", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "mPD4d2K9mQ": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Fax", "format": ["[0-9]"], "configs": [], "fieldId": 6266, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số fax", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyFax", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "ma7vscqY_x": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số điện thoại", "format": ["[0-9]"], "configs": [], "fieldId": 6265, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số điện thoại", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyPhone", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "me5pG_5esF": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chỗ ở hiện tại", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6293, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chỗ ở hiện tại", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "nywp2oraI3": {"type": {"resolvedName": "Container"}, "nodes": ["8rsbQEOV5L", "hVCbJ2TQFk", "igB4BtQsJM"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "c_Zpxts8cs", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "oqU4EvuqUH": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6248, "isValid": true, "touched": false, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactDescription", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "pAGPUA0S5p": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Email", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6267, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập email", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyEmail", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "rS_CNLPWM7": {"type": {"resolvedName": "Container"}, "nodes": ["ejnVvC4Xt9", "ma7vscqY_x", "pAGPUA0S5p", "9aBHItm52_"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "9bxlzI2L9l", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "uqpNRZP1u6": {"type": {"resolvedName": "CustomBusinessArea"}, "nodes": [], "props": {"label": "Ngành nghề kinh doanh", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomBusinessArea_OULe6zT1SpbpBTvX", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "vZW479yqIE": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6294, "isValid": true, "touched": false, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepDescription", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "wf4IeENcEn": {"type": {"resolvedName": "Container"}, "nodes": ["yd6nZCx3hb", "jxTMdZC-rt", "mPD4d2K9mQ", "fi0Q_2uqVr"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "9bxlzI2L9l", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "yd6nZCx3hb": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số BHXH", "format": ["[0-9]"], "configs": [], "fieldId": 6262, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số BHXH", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanySocialInsurance", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "zayPtybU67": {"type": {"resolvedName": "Container"}, "nodes": ["J-qgVhxO4g", "67kwu29Cfv"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "fcT3iMsOjI", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}'
FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field
      from "vnpt_dev"."custom_field" where category = 'CUSTOMER_CONTACT' and is_standard = true and customer_type = 'KHDN') AS raw
WHERE category = 'CUSTOMER_CONTACT' AND name IN ('Liên hệ doanh nghiệp','Tạo liên hệ Khách hàng Doanh Nghiệp (Mặc định)');

-- Liên hệ khách hàng hộ kinh doanh (HKD) --
UPDATE "vnpt_dev"."custom_layout"
SET lst_standard_field = raw.lst_standard_field,
    layout_content= '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["TyDwb2U4C4", "ZtKeyzK1SS", "hHoQRi0k_Q", "c_Zpxts8cs"], "props": {"id": "createViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "0oCnCLEQ3G": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Nơi đăng ký hộ khẩu", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6292, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập nơi đăng ký hộ khẩu", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepRegisterAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "0pBpnWlSzH": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên đăng ký kinh doanh", "configs": [], "fieldId": 6283, "formats": [".jpeg, .jpg", ".png", ".webp", ".tiff, .jfif", ".ico"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": false, "listNote": [{"id": "00ddb873-9d37-411e-a556-bd3ecdaf34d4", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyBusinessRegistrationFile", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "1atcRAYsA0": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên tổ chức", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6244, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên tổ chức", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactOrganization", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "kzfZoxGUta", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "1pxQjmt0p3": {"type": {"resolvedName": "SelectSocialNetwork"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_gHF0KDWqtoZn6XLH", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "2Fn6wI_e6_": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên người liên hệ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]"], "configs": [], "fieldId": 6240, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên người liên hệ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "34zn0f06CE": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6252, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "3chwh4LTNm": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6282, "isValid": true, "touched": false, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyDescription", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "4UL6s055bw": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "BC5VMkl7FY", "1": "la9vSD1Gop"}}, "5-L1BU4jMR": {"type": {"resolvedName": "SelectEnterpriseAddressInfo"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "selectEnterpriseAddressInfo_gPeYL0JhiSkMPMgc", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "63dOJnc1MY": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ đăng ký kinh doanh", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6253, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ đăng ký kinh doanh", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyBusinessAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "67kwu29Cfv": {"type": {"resolvedName": "DatePicker"}, "nodes": [], "props": {"label": "Ngày cấp", "configs": [], "fieldId": 6290, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "defaultDate": "2023-02-28T02:59:42.954Z", "placeholder": "Chọn ngày cấp", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "defaultDisplay": "unset", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityDate", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "zayPtybU67", "isCanvas": true, "displayName": "Date Picker", "linkedNodes": {}}, "6Jhyp_b8AL": {"type": {"resolvedName": "Container"}, "nodes": ["J6b8lTaa3h", "VPIgtAmVAz"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "8h5o9442ho", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "8h5o9442ho": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "9nf1d44RIJ", "1": "6Jhyp_b8AL"}}, "8rsbQEOV5L": {"type": {"resolvedName": "CustomCustomerOtherInfomation"}, "nodes": [], "props": {"label": "Thông tin khác của khách hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomCustomerOtherInfomation_iBJoiXfpfMcXoGwl", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "nywp2oraI3", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "9aBHItm52_": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Quy mô", "configs": [], "fieldId": 6269, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn quy mô", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanySize", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Chỉ có bạn", "label": "Chỉ có bạn", "default": false}, {"id": "2-9", "label": "2-9", "default": false}, {"id": "10-99", "label": "10-99", "default": false}, {"id": "100-299", "label": "100-299", "default": false}, {"id": "300+", "label": "300+", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "9bxlzI2L9l": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "wf4IeENcEn", "1": "rS_CNLPWM7"}}, "9nf1d44RIJ": {"type": {"resolvedName": "Container"}, "nodes": ["ZuX84NZyQ6", "9teDNjwbd9"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "8h5o9442ho", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "9teDNjwbd9": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chức danh", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6286, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chức danh", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepRole", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9nf1d44RIJ", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "9wiiVm0X3V": {"type": {"resolvedName": "Container"}, "nodes": ["S7Hfdb7Pl5", "Z7nfWm2AvO"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "fcT3iMsOjI", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "BC5VMkl7FY": {"type": {"resolvedName": "Container"}, "nodes": ["Gdwvp1iDZj"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "4UL6s055bw", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "BJrBqcN4Tn": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Lời nhắn", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]"], "configs": [], "fieldId": 6247, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập lời nhắn", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactMessage", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "Cnw6WY86TY": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Mã số thuế", "format": ["[0-9]"], "configs": [], "fieldId": 6251, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập mã số thuế", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyTaxCode", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "la9vSD1Gop", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "Gdwvp1iDZj": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chi cục thuế", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6250, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chi cục thuế", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyTaxDepartment", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "BC5VMkl7FY", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "IRVZv-yeSV": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "kzfZoxGUta", "1": "jZh_0GIeyp"}}, "IZ221sqpeu": {"type": {"resolvedName": "ProvinceList"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "ProvinceList_wUy2WYW92bjY01ai", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "kzfZoxGUta", "isCanvas": true, "displayName": "Section", "linkedNodes": {}}, "J-qgVhxO4g": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Loại giấy chứng thực", "configs": [], "fieldId": 6288, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn loại giấy chứng thực", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityType", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Chứng minh nhân dân", "label": "Chứng minh nhân dân", "default": false}, {"id": "Hộ chiếu", "label": "Hộ chiếu", "default": false}, {"id": "Thẻ căn cước công dân", "label": "Thẻ căn cước công dân", "default": false}, {"id": "Khác", "label": "Khác", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "zayPtybU67", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "J6b8lTaa3h": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Giới tính", "configs": [], "fieldId": 6285, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn giới tính", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepGender", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Nữ", "label": "Nữ", "default": false}, {"id": "Nam", "label": "Nam", "default": false}, {"id": "Khác", "label": "Khác", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "6Jhyp_b8AL", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "KBRiL2JAWT": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên ảnh đại diện", "configs": [], "fieldId": 6239, "formats": [".jpeg, .jpg", ".png", ".webp", ".tiff, .jfif", ".ico"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": false, "listNote": [{"id": "29409248-4d89-489c-b5c9-45b76b4decc1", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "contactAvatarFileId", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "NhUfMz1ne0": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6246, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "jZh_0GIeyp", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "PKlRUEmcPG": {"type": {"resolvedName": "Container"}, "nodes": ["8h5o9442ho", "aUEwyZbtwY", "fcT3iMsOjI", "0oCnCLEQ3G", "me5pG_5esF", "vZW479yqIE", "a1teUDRTeE"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "hHoQRi0k_Q", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "Po6S3RPKIE": {"type": {"resolvedName": "Container"}, "nodes": ["KBRiL2JAWT", "2Fn6wI_e6_", "1pxQjmt0p3", "IRVZv-yeSV", "BJrBqcN4Tn", "oqU4EvuqUH"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "TyDwb2U4C4", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "QB-SbM0D_C": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên doanh nghiệp", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6249, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên doanh nghiệp", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "RqmQ5I0X5B": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chức vụ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]"], "configs": [], "fieldId": 6245, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chức vụ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactRole", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "jZh_0GIeyp", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "S7Hfdb7Pl5": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số chứng thực cá nhân", "format": ["[0-9]"], "configs": [], "fieldId": 6289, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số chứng thực cá nhân", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityNo", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9wiiVm0X3V", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "Sn3SLP6yCC": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ lắp đặt", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6254, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập địa chỉ lắp đặt", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanySetupAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "TyDwb2U4C4": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin liên hệ", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "Po6S3RPKIE"}}, "VPIgtAmVAz": {"type": {"resolvedName": "DatePicker"}, "nodes": [], "props": {"label": "Ngày sinh", "configs": [], "fieldId": 6287, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "defaultDate": "2023-02-28T02:50:13.855Z", "placeholder": "Chọn ngày sinh", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "defaultDisplay": "unset", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepBirth", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "6Jhyp_b8AL", "isCanvas": true, "displayName": "Date Picker", "linkedNodes": {}}, "Y4YRP_90g4": {"type": {"resolvedName": "Container"}, "nodes": ["QB-SbM0D_C", "4UL6s055bw", "5-L1BU4jMR", "34zn0f06CE", "63dOJnc1MY", "Sn3SLP6yCC", "9bxlzI2L9l", "uqpNRZP1u6", "3chwh4LTNm", "0pBpnWlSzH"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "ZtKeyzK1SS", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "Z7nfWm2AvO": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Nơi cấp", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6291, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập nơi cấp", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9wiiVm0X3V", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "ZtKeyzK1SS": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin doanh nghiệp", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "Y4YRP_90g4"}}, "ZuX84NZyQ6": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên người đại diện", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]"], "configs": [], "fieldId": 6284, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập tên người đại diện", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "9nf1d44RIJ", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "a1teUDRTeE": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên CMTND/CCCD", "configs": [], "fieldId": 6295, "formats": [".jpeg, .jpg", ".png", ".webp", ".tiff, .jfif", ".ico"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": false, "listNote": [{"id": "33c86db2-78c8-4ab0-950a-8564264bb3e4", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepIdentityFile", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "aUEwyZbtwY": {"type": {"resolvedName": "CustomerNationality"}, "nodes": [], "props": {"label": "Quốc tịch khách hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomerNationality_Fu6zOoJAZbl6A9Mu", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "c_Zpxts8cs": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin khác", "configs": [], "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "nywp2oraI3"}}, "ejnVvC4Xt9": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Trạng thái hoạt động", "configs": [], "fieldId": 6263, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn trạng thái hoạt động", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyStatus", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Đang hoạt động", "label": "Đang hoạt động", "default": false}, {"id": "Tạm dừng", "label": "Tạm dừng", "default": false}, {"id": "Đã đóng", "label": "Đã đóng", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "fcT3iMsOjI": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "zayPtybU67", "1": "9wiiVm0X3V"}}, "fi0Q_2uqVr": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số giấy phép ĐKKD", "format": ["[0-9]"], "configs": [], "fieldId": 6268, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số giấy phép ĐKKD", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyBusinessRegistration", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "hHoQRi0k_Q": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin người đại diện", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "PKlRUEmcPG"}}, "hVCbJ2TQFk": {"type": {"resolvedName": "SelectPartition"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_t4gssnADF8Z4FL60", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "nywp2oraI3", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "igB4BtQsJM": {"type": {"resolvedName": "SelectAdmin"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_qlkZKwAN7oWYi7Ej", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "nywp2oraI3", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "jZh_0GIeyp": {"type": {"resolvedName": "Container"}, "nodes": ["RqmQ5I0X5B", "NhUfMz1ne0"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "IRVZv-yeSV", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "jxTMdZC-rt": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Nhà mạng", "configs": [], "fieldId": 6264, "isValid": true, "touched": false, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn nhà mạng", "dataInputAdd": "", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyNetwork", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Vinaphone", "label": "Vinaphone", "default": false}, {"id": "Viettel", "label": "Viettel", "default": false}, {"id": "Mobifone", "label": "Mobifone", "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "kzfZoxGUta": {"type": {"resolvedName": "Container"}, "nodes": ["1atcRAYsA0", "IZ221sqpeu"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "IRVZv-yeSV", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "la9vSD1Gop": {"type": {"resolvedName": "Container"}, "nodes": ["Cnw6WY86TY"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "4UL6s055bw", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "mPD4d2K9mQ": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Fax", "format": ["[0-9]"], "configs": [], "fieldId": 6266, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số fax", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyFax", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "ma7vscqY_x": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số điện thoại", "format": ["[0-9]"], "configs": [], "fieldId": 6265, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số điện thoại", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyPhone", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "me5pG_5esF": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Chỗ ở hiện tại", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6293, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập chỗ ở hiện tại", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "nywp2oraI3": {"type": {"resolvedName": "Container"}, "nodes": ["8rsbQEOV5L", "hVCbJ2TQFk", "igB4BtQsJM"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "c_Zpxts8cs", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "oqU4EvuqUH": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6248, "isValid": true, "touched": false, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseContactDescription", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Po6S3RPKIE", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "pAGPUA0S5p": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Email", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6267, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập email", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanyEmail", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "rS_CNLPWM7", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "rS_CNLPWM7": {"type": {"resolvedName": "Container"}, "nodes": ["ejnVvC4Xt9", "ma7vscqY_x", "pAGPUA0S5p", "9aBHItm52_"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "9bxlzI2L9l", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "uqpNRZP1u6": {"type": {"resolvedName": "CustomBusinessArea"}, "nodes": [], "props": {"label": "Ngành nghề kinh doanh", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomBusinessArea_OULe6zT1SpbpBTvX", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Y4YRP_90g4", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "vZW479yqIE": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6294, "isValid": true, "touched": false, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseRepDescription", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PKlRUEmcPG", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "wf4IeENcEn": {"type": {"resolvedName": "Container"}, "nodes": ["yd6nZCx3hb", "jxTMdZC-rt", "mPD4d2K9mQ", "fi0Q_2uqVr"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "9bxlzI2L9l", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "yd6nZCx3hb": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Số BHXH", "format": ["[0-9]"], "configs": [], "fieldId": 6262, "isValid": true, "touched": false, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "placeholder": "Nhập số BHXH", "validateType": "AND", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "enterpriseCompanySocialInsurance", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "wf4IeENcEn", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "zayPtybU67": {"type": {"resolvedName": "Container"}, "nodes": ["J-qgVhxO4g", "67kwu29Cfv"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "fcT3iMsOjI", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}'
FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field
      from "vnpt_dev"."custom_field" where category = 'CUSTOMER_CONTACT' and is_standard = true and customer_type = 'HKD') AS raw
WHERE category = 'CUSTOMER_CONTACT' AND name IN ('Tạo liên hệ Khách hàng Hộ Kinh Doanh (Mặc định)', 'Liên hệ hộ kinh doanh');

-- Liên hệ khách hàng cá nhân (CN) --
UPDATE "vnpt_dev"."custom_layout"
SET lst_standard_field = raw.lst_standard_field,
    layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["JSWqmpLWwn", "0XWeMKpcSO", "ZDuEixReRE"], "props": {"id": "createViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "0XWeMKpcSO": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin cá nhân", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "a5XXmZKB5o"}}, "1rLZzrttus": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": [], "fieldId": 6659, "isValid": true, "touched": true, "canAction": false, "maxLength": 300, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactGeneralField", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "N0jj55d85h", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "22FB5hvMG0": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Chọn giới tính", "configs": [], "fieldId": 6666, "isValid": true, "touched": true, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn giới tính", "dataInputAdd": "", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoGender", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Nam", "label": "Nam", "default": false}, {"id": "Nữ", "label": "Nữ", "default": false}, {"id": null, "label": null, "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "SozBRow-4f", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "30WekNd9nx": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên ảnh liên hệ", "configs": [], "fieldId": 6682, "formats": [".jpeg, .jpg", ".png"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": true, "listNote": [{"id": "06faa28b-1ae3-4f99-84b3-a9202d94d7dd", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "personalContactUploadId", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "N0jj55d85h", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "3QSDdC-E0i": {"type": {"resolvedName": "DropdownList"}, "nodes": [], "props": {"label": "Loại giấy chứng thực", "configs": [], "fieldId": 6668, "isValid": true, "touched": true, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "useDataApi": false, "dataApiType": "categories", "placeholder": "Chọn loại giấy chứng thực", "dataInputAdd": "", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoCertType", "isStandardField": false, "valueInputInser": "", "valueSelectItems": [{"id": "Chứng minh thư nhân dân", "label": "Chứng minh thư nhân dân", "default": false}, {"id": "Hộ chiếu", "label": "Hộ chiếu", "default": false}, {"id": "Khác", "label": "Khác", "default": false}, {"id": null, "label": null, "default": false}], "relatedFieldConfig": [{"relatedField": null, "relatedValue": null}], "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ma5ALZ0Ms0", "isCanvas": true, "displayName": "Dropdown List", "linkedNodes": {}}, "59ptMWbUvG": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Địa chỉ", "format": ["[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[0-9]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6657, "isValid": true, "touched": true, "canAction": false, "maxLength": 300, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập địa chỉ", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactAddress", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "dlD8XtM7c0", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "7j6b2Ia107": {"type": {"resolvedName": "SelectEnterpriseAddressInfo"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "selectEnterpriseAddressInfo_gNOk6HOB7P8s5p9y", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "8U8rDP_NFr": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên người liên hệ", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": ["required"], "fieldId": 6653, "isValid": true, "touched": true, "canAction": false, "maxLength": 50, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập tên người liên hệ", "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "always", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "all"}, "formControlName": "personalContactName", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "N0jj55d85h", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "8rTldV0JOB": {"type": {"resolvedName": "Container"}, "nodes": ["IWBVEYYvDR", "MMNr5E924q"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "miJXJRMswL", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "BSLFol98Ki": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "MTlFbkh0cx", "1": "SozBRow-4f"}}, "C4ZbNpGKxo": {"type": {"resolvedName": "ProvinceList"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "ProvinceList_vs48E4oSCMhxjrEq", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "aV6OEHi6G-", "isCanvas": true, "displayName": "Section", "linkedNodes": {}}, "F-I4kE5aqk": {"type": {"resolvedName": "SelectPartition"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_UnK1OMD7LMJ7TsGA", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PMkk_3rcMb", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "IWBVEYYvDR": {"type": {"resolvedName": "Number"}, "nodes": [], "props": {"label": "Số giấy chứng thực", "configs": [], "fieldId": 6669, "isValid": true, "touched": true, "canAction": false, "maxLength": 30, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập số chứng thực", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "dataInputPhone": [], "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoCertNum", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "8rTldV0JOB", "isCanvas": true, "displayName": "Number", "linkedNodes": {}}, "IWnWVU_4Ar": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Họ", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": [], "fieldId": 6661, "isValid": true, "touched": true, "canAction": false, "maxLength": 20, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập họ", "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoLastname", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "MTlFbkh0cx", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "JSWqmpLWwn": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin liên hệ", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "N0jj55d85h"}}, "Lv2ZVBHKot": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên ảnh đại diện", "configs": [], "fieldId": 6690, "formats": [".jpeg, .jpg", ".png"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": true, "listNote": [{"id": "93e2152f-6800-4a61-9cba-fa52ddb66f3f", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "DKSPDV", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoAvatarId", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "Lz07QsmK64": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Giới thiệu chung", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": [], "fieldId": 6673, "isValid": true, "touched": true, "canAction": false, "maxLength": 1000, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập giới thiệu chung", "validateType": "OR", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoGen", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "MMNr5E924q": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Nơi cấp", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": [], "fieldId": 6672, "isValid": true, "touched": true, "canAction": false, "maxLength": 50, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập nơi cấp", "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoCertAddr", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "8rTldV0JOB", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "MTlFbkh0cx": {"type": {"resolvedName": "Container"}, "nodes": ["IWnWVU_4Ar", "aJ5CHY4d5I"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "BSLFol98Ki", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "N0jj55d85h": {"type": {"resolvedName": "Container"}, "nodes": ["30WekNd9nx", "8U8rDP_NFr", "l5IXiq1JlR", "zcQbJDar87", "OLXiZusz7Y", "1rLZzrttus"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "JSWqmpLWwn", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "OLXiZusz7Y": {"type": {"resolvedName": "MultiLineText"}, "nodes": [], "props": {"label": "Lời nhắn", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": [], "fieldId": 6658, "isValid": true, "touched": true, "canAction": false, "maxLength": 500, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập lời nhắn", "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactMessage", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "N0jj55d85h", "isCanvas": true, "displayName": "Multi Line Text", "linkedNodes": {}}, "PMkk_3rcMb": {"type": {"resolvedName": "Container"}, "nodes": ["eBZzqryMnF", "F-I4kE5aqk", "QaIoY8sKKW"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "ZDuEixReRE", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "PoVnZrvEK7": {"type": {"resolvedName": "ImageUpload"}, "nodes": [], "props": {"label": "Tải lên giấy chứng thực", "configs": [], "fieldId": 6670, "formats": [".jpeg, .jpg", ".png"], "isValid": true, "maxFile": 1, "maxSize": 10, "touched": true, "listNote": [{"id": "9a34a98b-a780-40e8-b4cb-5ac9d1c8202b", "label": "File tải lên không quá 10MB"}], "urlTypes": "all", "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Tải file", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoCertAvatarId", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Tải ảnh", "linkedNodes": {}}, "QaIoY8sKKW": {"type": {"resolvedName": "SelectAdmin"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_hOgK301FbMQEY1bg", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PMkk_3rcMb", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "SozBRow-4f": {"type": {"resolvedName": "Container"}, "nodes": ["TjOWG4rNDA", "22FB5hvMG0"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "BSLFol98Ki", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "TMVoK4PNa7": {"type": {"resolvedName": "DatePicker"}, "nodes": [], "props": {"label": "Ngày cấp", "configs": [], "fieldId": 6671, "isValid": true, "touched": true, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "defaultDate": null, "placeholder": "Chọn ngày cấp", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "defaultDisplay": "unset", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoCertDate", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ma5ALZ0Ms0", "isCanvas": true, "displayName": "Date Picker", "linkedNodes": {}}, "TjOWG4rNDA": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Tên", "format": ["[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]", "[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"], "configs": [], "fieldId": 6662, "isValid": true, "touched": true, "canAction": false, "maxLength": 20, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "placeholder": "Nhập tên", "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoFirstname", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "SozBRow-4f", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "ZDuEixReRE": {"type": {"resolvedName": "Section"}, "nodes": [], "props": {"label": "Thông tin khác", "configs": [], "isValid": true, "touched": true, "showLabel": true, "noteConfig": {"noteType": ["tooltip"], "noteMessage": "", "tooltipMessage": ""}, "collapsable": true}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Section", "linkedNodes": {"section": "PMkk_3rcMb"}}, "a5XXmZKB5o": {"type": {"resolvedName": "Container"}, "nodes": ["Lv2ZVBHKot", "BSLFol98Ki", "7j6b2Ia107", "hoNvzFrkJt", "miJXJRMswL", "PoVnZrvEK7", "Lz07QsmK64"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "0XWeMKpcSO", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "aJ5CHY4d5I": {"type": {"resolvedName": "DatePicker"}, "nodes": [], "props": {"label": "Ngày sinh", "configs": [], "fieldId": 6665, "isValid": true, "touched": true, "canAction": false, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "defaultDate": null, "placeholder": "Chọn ngày sinh", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "defaultDisplay": "unset", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoBirthdate", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "MTlFbkh0cx", "isCanvas": true, "displayName": "Date Picker", "linkedNodes": {}}, "aV6OEHi6G-": {"type": {"resolvedName": "Container"}, "nodes": ["C4ZbNpGKxo"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "zcQbJDar87", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "dlD8XtM7c0": {"type": {"resolvedName": "Container"}, "nodes": ["59ptMWbUvG"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "zcQbJDar87", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "eBZzqryMnF": {"type": {"resolvedName": "CustomCustomerOtherInfomation"}, "nodes": [], "props": {"label": "Thông tin khác của khách hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "CustomCustomerOtherInfomation_zRKjxGI4EZoA8JDY", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "PMkk_3rcMb", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "hoNvzFrkJt": {"type": {"resolvedName": "SingleLineText"}, "nodes": [], "props": {"label": "Địa chỉ", "format": ["[0-9]", "[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]", "[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]", "[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"], "configs": [], "fieldId": 6687, "isValid": true, "touched": true, "canAction": false, "maxLength": 99, "showLabel": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "validateType": "OR", "displayPortal": ["admin"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "displayContent": "", "requiredConfig": {"serviceIds": [], "requiredType": "none", "requiredServices": [], "requiredCondition": "", "requiredServiceType": "custom"}, "formControlName": "personalContactInfoAddr", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Single Line Text", "linkedNodes": {}}, "l5IXiq1JlR": {"type": {"resolvedName": "SelectSocialNetwork"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categoryType", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "pricingStrategy_AcjFcnOiMoKC2Dtu", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "N0jj55d85h", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "ma5ALZ0Ms0": {"type": {"resolvedName": "Container"}, "nodes": ["3QSDdC-E0i", "TMVoK4PNa7"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "isValid": true, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "miJXJRMswL", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "miJXJRMswL": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "a5XXmZKB5o", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "ma5ALZ0Ms0", "1": "8rTldV0JOB"}}, "zcQbJDar87": {"type": {"resolvedName": "Column"}, "nodes": [], "props": {"isValid": true, "numberOfCols": 2}, "custom": {}, "hidden": false, "parent": "N0jj55d85h", "isCanvas": true, "displayName": "Column", "linkedNodes": {"0": "aV6OEHi6G-", "1": "dlD8XtM7c0"}}}'
FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field
      from "vnpt_dev"."custom_field" where category = 'CUSTOMER_CONTACT'and is_standard = true and customer_type = 'CN') AS raw
WHERE category = 'CUSTOMER_CONTACT' AND name IN ('Tạo liên hệ Khách hàng Cá Nhân (Mặc định)', 'Liên hệ cá nhân');

ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";