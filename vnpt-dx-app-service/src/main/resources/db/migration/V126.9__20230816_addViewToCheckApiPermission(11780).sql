-- T<PERSON><PERSON> view kiểm tra phân quyền API
DROP VIEW IF EXISTS "vnpt_dev"."view_api_permission_detail" CASCADE;
CREATE VIEW "vnpt_dev"."view_api_permission_detail" AS
WITH role_permission AS (
        SELECT 
            permission.id as permission_id,
            STRING_AGG(role.display_name, ', ' order by role.id DESC) as lst_role
        FROM
            permission
            LEFT JOIN roles_permissions ON roles_permissions.permission_id = permission.id
            LEFT JOIN role ON role.id = roles_permissions.role_id
        GROUP BY permission.id
    )
SELECT
    apis.api_path AS api_path,
    apis.api_code AS api_code,
    apis.method AS api_method,
    portal.name AS portal_name,
    permission.name AS permission_name,
    permission.code AS permission_code,
    role_permission.lst_role AS lst_role,
    apis.id AS api_id,
    api_permission.id as api_permission_id,
    portal.id as portal_id,
    permission.id as permission_id
FROM 
    apis
    LEFT JOIN api_permission ON api_permission.api_id = apis.id
    LEFT JOIN permission_portal ON permission_portal.id = api_permission.permission_portal_id
    LEFT JOIN portal ON portal.id = permission_portal.portal_id
    LEFT JOIN permission ON permission.id = permission_portal.permission_id
    LEFT JOIN role_permission ON role_permission.permission_id = permission.id
WHERE
    api_permission.delete_flag = 1
ORDER BY apis.api_path;


-- Bổ sung permission enable/disable service trên dev
delete from vnpt_dev.permission_portal
where
    permission_id in (select id from vnpt_dev.permission where code = 'AN_HIEN_DICH_VU_1') and
    portal_id in (select id from vnpt_dev.portal where name = 'DEV');
insert into vnpt_dev.permission_portal(id, permission_id, portal_id) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.permission_portal),
        (select id from vnpt_dev.permission where code = 'AN_HIEN_DICH_VU_1'),
        (select id from vnpt_dev.portal where name = 'DEV')
    );

delete from vnpt_dev.api_permission
where
    permission_portal_id in (
        select permission_portal.id
        from vnpt_dev.permission_portal
            join vnpt_dev.permission on permission.id = permission_portal.permission_id
            join vnpt_dev.portal on portal.id = permission_portal.portal_id
        where permission.code = 'AN_HIEN_DICH_VU_1' and portal.name = 'DEV'
    ) and
    api_id in (select id from vnpt_dev.apis where api_code = 'ROLE_DEVELOPER_HIDDEN_SERVICE');

insert into vnpt_dev.api_permission(id,api_id, permission_portal_id, map_permission_portal, delete_flag) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.api_permission),
        (select id from vnpt_dev.apis where api_code = 'ROLE_DEVELOPER_HIDDEN_SERVICE'),
        (select permission_portal.id
            from vnpt_dev.permission_portal
                join vnpt_dev.permission on permission.id = permission_portal.permission_id
                join vnpt_dev.portal on portal.id = permission_portal.portal_id
            where permission.code = 'AN_HIEN_DICH_VU_1' and portal.name = 'DEV'
            limit 1
        ), 1, 1
    );


-- Bổ sung permission enable/disable pricing trên dev
delete from vnpt_dev.permission_portal
where
    permission_id in (select id from vnpt_dev.permission where code = 'AN_HIEN_GOI_DICH_VU_1') and
    portal_id in (select id from vnpt_dev.portal where name = 'DEV');
insert into vnpt_dev.permission_portal(id, permission_id, portal_id) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.permission_portal),
        (select id from vnpt_dev.permission where code = 'AN_HIEN_GOI_DICH_VU_1'),
        (select id from vnpt_dev.portal where name = 'DEV')
    );

delete from vnpt_dev.api_permission
where
    permission_portal_id in (
        select permission_portal.id
        from vnpt_dev.permission_portal
            join vnpt_dev.permission on permission.id = permission_portal.permission_id
            join vnpt_dev.portal on portal.id = permission_portal.portal_id
        where permission.code = 'AN_HIEN_GOI_DICH_VU_1' and portal.name = 'DEV'
    ) and
    api_id in (select id from vnpt_dev.apis where api_code = 'ROLE_DEV_UPDATE_STATUS_PRICING');

insert into vnpt_dev.api_permission(id,api_id, permission_portal_id, map_permission_portal, delete_flag) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.api_permission),
        (select id from vnpt_dev.apis where api_code = 'ROLE_DEV_UPDATE_STATUS_PRICING'),
        (select permission_portal.id
            from vnpt_dev.permission_portal
                join vnpt_dev.permission on permission.id = permission_portal.permission_id
                join vnpt_dev.portal on portal.id = permission_portal.portal_id
            where permission.code = 'AN_HIEN_GOI_DICH_VU_1' and portal.name = 'DEV'
            limit 1
        ), 1, 1
    );

-- Bổ sung permission enable/disable pricing trên admin
DELETE FROM "vnpt_dev"."apis" WHERE api_code = 'API_ADMIN_BAT_TAT_GOI_DICH_VU' AND method = 'PUT';
INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method") VALUES
    (
        (SELECT max(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/pricing/update-status/{id}/{status}',
        'API_ADMIN_BAT_TAT_GOI_DICH_VU',
        'PUT'
    );
delete from vnpt_dev.permission_portal
where
    permission_id in (select id from vnpt_dev.permission where code = 'AN_HIEN_GOI_DICH_VU_1') and
    portal_id in (select id from vnpt_dev.portal where name = 'ADMIN');
insert into vnpt_dev.permission_portal(id, permission_id, portal_id) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.permission_portal),
        (select id from vnpt_dev.permission where code = 'AN_HIEN_GOI_DICH_VU_1'),
        (select id from vnpt_dev.portal where name = 'ADMIN')
    );

delete from vnpt_dev.api_permission
where
    permission_portal_id in (
        select permission_portal.id
        from vnpt_dev.permission_portal
            join vnpt_dev.permission on permission.id = permission_portal.permission_id
            join vnpt_dev.portal on portal.id = permission_portal.portal_id
        where permission.code = 'AN_HIEN_GOI_DICH_VU_1' and portal.name = 'ADMIN'
    ) and
    api_id in (select id from vnpt_dev.apis where api_code = 'API_ADMIN_BAT_TAT_GOI_DICH_VU');

insert into vnpt_dev.api_permission(id,api_id, permission_portal_id, map_permission_portal, delete_flag) values
    (
        (SELECT max(id) + 1 FROM vnpt_dev.api_permission),
        (select id from vnpt_dev.apis where api_code = 'API_ADMIN_BAT_TAT_GOI_DICH_VU'),
        (select permission_portal.id
            from vnpt_dev.permission_portal
                join vnpt_dev.permission on permission.id = permission_portal.permission_id
                join vnpt_dev.portal on portal.id = permission_portal.portal_id
            where permission.code = 'AN_HIEN_GOI_DICH_VU_1' and portal.name = 'ADMIN'
            limit 1
        ), 1, 1
    );


REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;