delete from vnpt_dev.api_permission where id in (
    select api_per.id
    from vnpt_dev.api_permission api_per
        join vnpt_dev.apis api on api_per.api_id = api.id
        join vnpt_dev.permission_portal on api_per.permission_portal_id = permission_portal.id
        join vnpt_dev.permission on permission_portal.permission_id = permission.id
    where
        api.api_code = 'ROLE_ADMIN_GET_BY_CODE_EMAIL_TEMPLATE' and
        permission.code in ('TAO_QUY_TAC_PHAN_GIAO', 'CHINH_SUA_QUY_TAC_PHAN_GIAO', 'XEM_CHI_TIET_QUY_TAC_PHAN_GIAO')
);

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
    (select max(id) from vnpt_dev.api_permission) + 1,
    (select id from vnpt_dev.apis where api_code = 'ROLE_ADMIN_GET_BY_CODE_EMAIL_TEMPLATE'),
    (
        select per_portal.id
        from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
            on per_portal.permission_id = per.id
        where per.code = 'TAO_QUY_TAC_PHAN_GIAO'
    ),
    1,
    1
;

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
    (select max(id) from vnpt_dev.api_permission) + 1,
    (select id from vnpt_dev.apis where api_code = 'ROLE_ADMIN_GET_BY_CODE_EMAIL_TEMPLATE'),
    (
        select per_portal.id
        from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
            on per_portal.permission_id = per.id
        where per.code = 'CHINH_SUA_QUY_TAC_PHAN_GIAO'
    ),
    1,
    1
;

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
    (select max(id) from vnpt_dev.api_permission) + 1,
    (select id from vnpt_dev.apis where api_code = 'ROLE_ADMIN_GET_BY_CODE_EMAIL_TEMPLATE'),
    (
        select per_portal.id
        from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
            on per_portal.permission_id = per.id
        where per.code = 'XEM_CHI_TIET_QUY_TAC_PHAN_GIAO'
    ),
    1,
    1
;
refresh materialized view concurrently vnpt_dev.role_permission_api;


update vnpt_dev.apis
set api_path = 'api/admin-portal/crm/automation-rule/get-list'
where api_code  = 'XEM_DANH_SACH_QUY_TAC';

update vnpt_dev.apis
set api_path = 'api/admin-portal/crm/automation-rule/create-rule'
where api_code  = 'TAO_QUY_TAC_PHAN_GIAO';

update vnpt_dev.apis
set api_path = 'api/admin-portal/crm/automation-rule/update-rule'
where api_code  = 'CHINH_SUA_QUY_TAC_PHAN_GIAO';

update vnpt_dev.apis
set api_path = 'api/admin-portal/crm/automation-rule/delete-rule'
where api_code  = 'XOA_QUY_TAC_PHAN_GIAO';

update vnpt_dev.apis
set api_path = 'api/admin-portal/crm/automation-rule/detail-rule'
where api_code  = 'XEM_CHI_TIET_QUY_TAC_PHAN_GIAO';
