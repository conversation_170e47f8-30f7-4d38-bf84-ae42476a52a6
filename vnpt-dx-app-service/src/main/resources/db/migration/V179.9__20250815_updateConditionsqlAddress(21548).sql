-- update tỉnh thành sql đối enterprise
----------------------------- Bảng crm_data_condition -------------------------------------
-- Trư<PERSON><PERSON> hợp chứa province_id
update vnpt_dev.crm_data_condition set condition_query = replace(replace(replace(replace(condition_query, 'province_id', 'province_id_old'), 'district_id', 'district_id_old'), 'ward_id', 'ward_id_old'), 'street_id', 'street_id_old')
where
    condition_query is not null and condition_query ilike '%enterprise.province_id %';
-- Tr<PERSON><PERSON><PERSON> hợp chứa province_name
update vnpt_dev.crm_data_condition set condition_query = data.new_condition_sql
from (
SELECT
    crm_data_condition.id,  -- id bản ghi trong your_table
    crm_data_condition.condition_query,
    'enterprise.province_id_old IN (' || string_agg(province.id::text, ', ') || ')' AS new_condition_sql
FROM vnpt_dev.crm_data_condition
JOIN LATERAL (
    SELECT trim(both '''' from unnest(string_to_array(
        regexp_replace(crm_data_condition.condition_query, '^.*\(|\)$', '', 'g'), ', ')
    )) AS province_name
) AS x ON true
JOIN vnpt_dev.province ON province.name = x.province_name
where
    condition_query is not null and condition_query ilike '%enterprise.province_name %'
GROUP BY crm_data_condition.id
) as data
where
    data.id = crm_data_condition.id;
-- update tỉnh thành sql đối customer contact
update vnpt_dev.crm_data_condition set condition_query = replace(replace(replace(replace(condition_query, 'province_id', 'province_id_old'), 'district_id', 'district_id_old'), 'ward_id', 'ward_id_old'), 'street_id', 'street_id_old')
where
    condition_query is not null and condition_query ilike '%customer_contact.province_id %';
---------------------------------------------------------
update vnpt_dev.crm_data_condition set condition_query = replace(condition_query, 'contact_province_id', 'contact_province_id_old')
where
    condition_query is not null and condition_query ilike '%customer_contact.contact_province_id %';
-----------------------------------------------------------
update vnpt_dev.crm_data_condition set condition_query = replace(replace(replace(replace(condition_query, 'personal_province_id', 'personal_province_id_old'), 'personal_district_id', 'personal_district_id_old'), 'personal_ward_id', 'personal_ward_id_old'), 'personal_street_id', 'personal_street_id_old')
where
    condition_query is not null and condition_query ilike '%customer_contact.personal_province_id %';

----------------------------- Bảng rule_condition -------------------------------------
-- Trường hợp chứa province_id
update vnpt_dev.rule_condition set condition_sql = replace(replace(replace(replace(condition_sql, 'province_id', 'province_id_old'), 'district_id', 'district_id_old'), 'ward_id', 'ward_id_old'), 'street_id', 'street_id_old')
where
    condition_sql is not null and condition_sql ilike '%enterprise.province_id %';

-- update tỉnh thành sql đối customer contact
update vnpt_dev.rule_condition set condition_sql = replace(replace(replace(replace(condition_sql, 'province_id', 'province_id_old'), 'district_id', 'district_id_old'), 'ward_id', 'ward_id_old'), 'street_id', 'street_id_old')
where
    condition_sql is not null and condition_sql ilike '%customer_contact.province_id %';
---------------------------------------------------------
update vnpt_dev.rule_condition set condition_sql = replace(condition_sql, 'contact_province_id', 'contact_province_id_old')
where
    condition_sql is not null and condition_sql ilike '%customer_contact.contact_province_id %';
-----------------------------------------------------------
update vnpt_dev.rule_condition set condition_sql = replace(replace(replace(replace(condition_sql, 'personal_province_id', 'personal_province_id_old'), 'personal_district_id', 'personal_district_id_old'), 'personal_ward_id', 'personal_ward_id_old'), 'personal_street_id', 'personal_street_id_old')
where
    condition_sql is not null and condition_sql ilike '%customer_contact.personal_province_id %';