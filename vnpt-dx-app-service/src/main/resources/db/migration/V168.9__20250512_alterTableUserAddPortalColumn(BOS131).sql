-- tạo cột portal cho bảng users nếu chưa có
ALTER TABLE vnpt_dev.users ADD COLUMN IF NOT EXISTS "portal" varchar(10);

-- Thêm dữ liệu vào cột portal dựa trên role của user
WITH role_user AS (
  SELECT
    users.id AS user_id,
    MIN(
      CASE
        WHEN role.name = ANY (ARRAY['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'FULL_ADMIN']) THEN 'ADMIN'
        WHEN role.name = ANY (ARRAY['ROLE_DEVELOPER', 'FULL_DEV']) THEN 'DEV'
        WHEN role.name = ANY (ARRAY['FULL_SME', 'ROLE_SME', 'ROLE_SME_EMPLOYEE']) THEN 'SME'
      END
    ) AS role_name
  FROM users
  JOIN users_roles ON users.id = users_roles.user_id
  JOIN role ON role.id = users_roles.role_id
  GROUP BY users.id
)
UPDATE vnpt_dev.users
SET portal = role_user.role_name
FROM role_user
WHERE users.id = role_user.user_id;
