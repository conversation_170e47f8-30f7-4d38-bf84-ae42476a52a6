-- Thêm <PERSON>ng ban ('VNPT_IT','TT_KHDN','DHKD')
DELETE FROM vnpt_dev.crm_data_partition WHERE crm_data_partition.code IN ('VNPT_IT','TT_KHDN','DHKD' ) AND parent_id = (SELECT crm_data_partition.id from vnpt_dev.crm_data_partition where crm_data_partition.code= 'DNKD');

INSERT INTO "vnpt_dev"."crm_data_partition" ( "name", "code", "parent_id", "status", "lst_am_id")
SELECT
    SUBSTRING(departments.department_name,0,50) AS NAME,
    departments.department_code AS code,
    departments.parent_id,
    1,
    ARRAY_AGG ( DISTINCT users.ID ) As lst_am_id
FROM
    vnpt_dev.departments
        Left JOIN vnpt_dev.users ON users.department_id = departments.ID
        Left JOIN vnpt_dev.users_roles users_roles ON users_roles.user_id = users.id
WHERE
        departments.department_code IN ('VNPT_IT','TT_KHDN','DHKD')
GROUP BY
    departments.id,
    users.department_id ON CONFLICT DO NOTHING;

UPDATE vnpt_dev.crm_data_partition SET parent_id = (SELECT crm_data_partition.id from vnpt_dev.crm_data_partition where crm_data_partition.code= 'DNKD')  WHERE crm_data_partition.code IN ('VNPT_IT','TT_KHDN','DHKD' ) AND ((parent_id = -1 AND crm_data_partition.code <> 'DNKD') OR parent_id IS NULL) ;

-- Thêm dữ liệu vào bảng crm_permission_am
DELETE
FROM
    vnpt_dev.crm_permission_am
WHERE
        crm_permission_am.partition_id IN
        ( SELECT crm_data_partition.id
          FROM vnpt_dev.departments
                   LEFT JOIN vnpt_dev.crm_data_partition ON crm_data_partition.code = departments.department_code
          WHERE crm_data_partition.code IN
                ('VNPT_IT','TT_KHDN','DHKD' ));

INSERT INTO "vnpt_dev"."crm_permission_am" ("partition_id", "am_id", "am_am", "object", "child_admin_am", "child_object")

with am_permission as (
    SELECT
        crm_data_partition.id,
        UNNEST(crm_data_partition.lst_am_id) as am_id,
        array[1,2,3],
    array[1,2,3],
    array[1,2,3],
    array[1,2,3]
from vnpt_dev.crm_data_partition
WHERE crm_data_partition.code IN ('VNPT_IT','TT_KHDN','DHKD' )
    )
select * from  am_permission  where am_permission.am_id is not null ON CONFLICT DO NOTHING;

--------------------------------

INSERT INTO "vnpt_dev"."crm_data_condition" ( "partition_id","object_type","condition","lst_object_id", "condition_query" )
WITH vObjectType AS ( SELECT * FROM generate_series (0,3 ) AS type ),
     vCondition AS
         (select distinct crm_data_partition.id,
                          concat ( '[{"id":1,"key":"1","ifconds":[{"id":',COALESCE(province.id,1), ',"key":1,"data":{"value":["', COALESCE(province.NAME, 'Tất cả'),'"]},"operator":1,"operandId":33}]}]' ):: jsonb AS CONDITION,
                  array[]::int8[] as lst_object_type,
                  concat(COALESCE(province.name,'Tất cả'),''')') AS condition_query
          from vnpt_dev.crm_data_partition
                   left join vnpt_dev.departments on departments.department_code = crm_data_partition.code and crm_data_partition.id <> 1
                   left join vnpt_dev.province on province.id = departments.province_id where crm_data_partition.code IN ('VNP_KHDN','VNPT_IT','TT_KHDN','DHKD') or departments.department_name = 'BAN KHDN')
select distinct vCondition.id, vObjectType.type, vCondition.condition, array[]::int8[],
        case
            when vObjectType.type = 0 then true
            when vObjectType.type = 1 then true
            when vObjectType.type = 2 then true
            when vObjectType.type = 3 then true

            end as condition_query
from vCondition, vObjectType ON CONFLICT DO NOTHING;