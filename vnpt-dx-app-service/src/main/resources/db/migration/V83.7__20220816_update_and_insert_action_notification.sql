--  UPDATE THÔNG TIN CÓ SẴN

UPDATE vnpt_dev.action_notification
SET name             = 'Thông báo khi có sự cố cập nhật thuê bao',
    created_at       = '2022-08-15 00:00:00.000000',
    modified_by      = '<EMAIL>',
    modified_at      = '2022-08-20 00:00:00.000000',
    receiver         = '<PERSON><PERSON> tổng, Admin tỉnh thành của khách hàng, Ban KHDN',
    action_code      = 'SLA-05',
    allow_change_sms = 'D'
WHERE action_code = 'MG-03';

UPDATE vnpt_dev.action_notification
SET name             = 'Thông báo khi có sự cố đổi gói dịch vụ',
    created_at       = '2022-08-15 00:00:00.000000',
    modified_by      = '<EMAIL>',
    modified_at      = '22022-08-20 00:00:00.000000',
    receiver         = '<PERSON><PERSON> tổng, Ad<PERSON> tỉnh thành của khách hàng, Ban KHDN',
    action_code      = 'SLA-07',
    allow_change_sms = 'D'
WHERE action_code = 'MG-04';

UPDATE vnpt_dev.action_notification
SET name             = 'Thông báo khi có sự cố đăng ký mới',
    created_at       = '2022-08-15 00:00:00.000000',
    modified_by      = '<EMAIL>',
    modified_at      = '2022-08-20 00:00:00.000000',
    receiver         = 'Admin tổng, Admin tỉnh thành của khách hàng, Ban KHDN',
    action_code      = 'SLA-01',
    allow_change_sms = 'D'
WHERE action_code = 'MG-01';

UPDATE vnpt_dev.action_notification
SET name             = 'Thông báo khi có sự cố gia hạn',
    created_at       = '2022-08-15 00:00:00.000000',
    modified_by      = '<EMAIL>',
    modified_at      = '2022-08-20 00:00:00.000000',
    receiver         = 'Admin tổng, Admin tỉnh thành của khách hàng, Ban KHDN',
    action_code      = 'SLA-03',
    allow_change_sms = 'D'
WHERE action_code = 'MG-02';

--  INSERT THÔNG TIN MỚI

INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                        modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                        priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Thông báo khi có sự cố mua mới', 1, 0, 1, 125, 'system', '2022-08-15 00:00:00.000000', '<EMAIL>',
        '2022-08-20 00:00:00.000000', 'Dev của SPDV', 'SLA-02', 'B', 'D', 'B', 12005, 0, 'D');

INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                          modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Thông báo khi có sự cố gia hạn', 1, 0, 1, 125, 'system', '2022-08-15 00:00:00.000000', '<EMAIL>',
        '2022-08-20 00:00:00.000000', 'Dev của SPDV', 'SLA-04', 'B', 'D', 'B', 12006, 0, 'D');

INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                          modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Thông báo khi có sự cố cập nhật thuê bao', 1, 0, 1, 125, 'system', '2022-08-15 00:00:00.000000', '<EMAIL>',
        '2022-08-20 00:00:00.000000', 'Dev của SPDV', 'SLA-06', 'B', 'D', 'B', 12007, 0, 'D');

INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                          modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Thông báo khi có sự cố đổi gói dịch vụ', 1, 0, 1, 125, 'system', '2022-08-15 00:00:00.000000', '<EMAIL>',
        '2022-08-20 00:00:00.000000', 'Dev của SPDV', 'SLA-08', 'B', 'D', 'B', 12008, 0, 'D');


--  Sự cố hủy gói

INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                        modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                        priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Thông báo khi có sự cố hủy gói', 1, 0, 1, 125, 'system', '2022-08-15 00:00:00.000000', '<EMAIL>',
        '2022-08-20 00:00:00.000000', 'Admin tổng, Admin tỉnh thành của khách hàng, Ban KHDN', 'SLA-09', 'B', 'D', 'B', 12009, 0, 'D');


INSERT INTO vnpt_dev.action_notification (name, is_send_email, is_send_sms, is_notification, parent_id, created_by, created_at, modified_by,
                                          modified_at, receiver, action_code, allow_change_email, allow_change_sms, allow_change_notification,
                                          priority_order, is_send_telegram, allow_change_telegram)
VALUES ('Thông báo khi có sự cố hủy gói', 1, 0, 1, 125, 'system', '2022-08-15 00:00:00.000000', '<EMAIL>',
        '2022-08-20 00:00:00.000000', 'Dev của khách hàng', 'SLA-10', 'B', 'D', 'B', 12010, 0, 'D');