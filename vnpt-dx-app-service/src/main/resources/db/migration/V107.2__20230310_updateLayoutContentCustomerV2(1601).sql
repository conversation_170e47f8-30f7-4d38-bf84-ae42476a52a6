
Update vnpt_dev.custom_field set is_standard = false where code in
       ('enterprise.info.1','enterprise.name.1','enterprise.taxDepartment.1','enterprise.taxCode.1','enterprise.nation.1','enterprise.region.1','enterprise.provinceCode.1','enterprise.provinceName.1','enterprise.districtCode.1','enterprise.districtName.1','enterprise.wardCode.1','enterprise.wardName.1','enterprise.streetName.1','enterprise.address.1','enterprise.registerBusinessAddress.1','enterprise.setupAddress.1','enterprise.insuranceNumber.1','enterprise.activeStatus.1','enterprise.telecomOperator.1','enterprise.phoneNumber.1','enterprise.fax.1','enterprise.email.1','enterprise.businessLicenseNumber.1','enterprise.businessSize.1','enterprise.businessType.1','enterprise.foundingDate.1','enterprise.businessSectorCode.1','enterprise.businessSectorName.1','enterprise.introduction.1','enterprise.businessRegistrationFileIds.1','enterprise.representativeInfo.1','enterprise.repName.1','enterprise.repSex.1','enterprise.repTitle.1','enterprise.repDob.1','enterprise.repNationality.1','enterprise.repEthnic.1','enterprise.repIdentityType.1','enterprise.repIdentityNumber.1','enterprise.repIssueDate.1','enterprise.repIssueBy.1','enterprise.repHouseholdRegistration.1','enterprise.repCurrentAccommodation.1','enterprise.repIdentityFileIds.1','enterprise.repIdentityFileIdsUrl.1','enterprise.contactInfo.1','enterprise.contactName.1','enterprise.contactPhoneNumber.1','enterprise.contactSocialNetwork.1','enterprise.contactProvince.1','enterprise.contactAddress.1','enterprise.contactOrganization.1','enterprise.contactTitle.1','enterprise.contactMessage.1','enterprise.contactIntroduction.1','enterprise.contactOther.1','enterprise.otherInfo.1','enterprise.customerSource.1','enterprise.lstPartitionId.1','enterprise.lstAdminId.1','enterprise.contactEmail.1','enterprise.repIntroduction.1');
DELETE FROM vnpt_dev.custom_field where code in ('repSex','repIdentityNo','generalDesc','contactMessage','companyName','repPosition','operator','repGeneralDesc','repIdentityDate','companyEmail','businessRegistrationNo','tin','repIdentityFileIds','contactAddress','fax','setupAddress','taxDepartment','businessRegistrationFileIds','contactOrganization','socialInsuranceNo','repBirthday','address','contactName','contactPosition','repName','SingleLineText','businessRegistrationAddress','repRegisterResidence','repIdentityAddress','repCurrentResidence','businessSizeId','repIdentityType','phone','contactGeneralDesc','status','emailContact' , 'phoneNumContact');
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Nơi đăng ký hộ khẩu', 'repRegisterResidence', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Nơi đăng ký hộ khẩu","labelEnabled":true,"hintText":"Nhập nơi đăng ký hộ khẩu","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chức vụ', 'contactPosition', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Chức vụ","labelEnabled":true,"hintText":"Nhập chức vụ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Ngày sinh', 'repBirthday', 'f', 'DATE_PICKER', 'CUSTOMER', '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Chọn ngày sinh","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"2023-02-23T07:23:54.707Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Nhà mạng', 'operator', 'f', 'DROPDOWN_LIST', 'CUSTOMER', '{"label":"Nhà mạng","labelEnabled":true,"hintText":"Chọn nhà mạng","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Vinaphone","Viettel","Mobifone"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Nơi cấp', 'repIdentityAddress', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ lắp đặt', 'setupAddress', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Địa chỉ lắp đặt","labelEnabled":true,"hintText":"Nhập địa chỉ lắp đặt","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Email', 'companyEmail', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz])(?=.*[\\d])(?=.*[\\W])[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d\\W]*$","lstPatternToken":[1,3,4],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số BHXH', 'socialInsuranceNo', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Số BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chi cục thuế', 'taxDepartment', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Chi cục thuế","labelEnabled":true,"hintText":"Nhập chi cục thuế","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Ngày cấp', 'repIdentityDate', 'f', 'DATE_PICKER', 'CUSTOMER', '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"2023-02-23T08:53:53.910Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên tổ chức', 'contactOrganization', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Tên tổ chức","labelEnabled":true,"hintText":"Nhập tên tổ chức","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\Waàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[2,3,4,1],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới thiệu chung', 'contactGeneralDesc', 'f', 'MULTI_LINE_TEXT', 'CUSTOMER', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\W\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[4,3,2,1],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên người liên hệ', 'contactName', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Tên người liên hệ","labelEnabled":true,"hintText":"Nhập tên người liên hệ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[2,1],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới thiệu chung', 'generalDesc', 'f', 'MULTI_LINE_TEXT', 'CUSTOMER', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Trạng thái hoạt động', 'status', 'f', 'DROPDOWN_LIST', 'CUSTOMER', '{"label":"Trạng thái hoạt động","labelEnabled":true,"hintText":"Chọn trạng thái hoạt động","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Đang hoạt động","Tạm dừng","Đã đóng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số giấy phép ĐKKD', 'businessRegistrationNo', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Số giấy phép ĐKKD","labelEnabled":true,"hintText":"Nhập số giấy tờ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số điện thoại', 'phone', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên người đại diện', 'repName', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Tên người đại diện","labelEnabled":true,"hintText":"Nhập tên người đại diện","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d]*$","lstPatternToken":[2,1,3],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ', 'contactAddress', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\W\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[4,3,2,1],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tải lên Đăng ký kinh doanh', 'businessRegistrationFileIds', 'f', 'UPLOAD_IMAGE', 'CUSTOMER', '{"label":"Tải lên Đăng ký kinh doanh","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Mã số thuế', 'tin', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chỗ ở hiện tại', 'repCurrentResidence', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Chỗ ở hiện tại","labelEnabled":true,"hintText":"Nhập chỗ ở hiện tại","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\W\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[4,3,2,1],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ', 'address', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới tính', 'repSex', 'f', 'DROPDOWN_LIST', 'CUSTOMER', '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Nam","Nữ","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ đăng ký kinh doanh', 'businessRegistrationAddress', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Quy mô', 'businessSizeId', 'f', 'DROPDOWN_LIST', 'CUSTOMER', '{"label":"Quy mô","labelEnabled":true,"hintText":"Chọn quy mô","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chỉ có bạn","2-9","10-99","100-299","300+"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tải lên CMND/CCCD', 'repIdentityFileIds', 'f', 'UPLOAD_IMAGE', 'CUSTOMER', '{"label":"Tải lên CMND/CCCD","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chức danh', 'repPosition', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Chức danh","labelEnabled":true,"hintText":"Nhập chức danh","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\W\\d]*$","lstPatternToken":[1,2,4,3],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số chứng thực cá nhân', 'repIdentityNo', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Số chứng thực cá nhân","labelEnabled":true,"hintText":"Nhập số chứng thực cá nhân","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới thiệu chung', 'repGeneralDesc', 'f', 'MULTI_LINE_TEXT', 'CUSTOMER', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Lời nhắn', 'contactMessage', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\W\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[4,3,2,1],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Fax', 'fax', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Fax","labelEnabled":true,"hintText":"Nhập số fax","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Single line text', 'SingleLineText', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Single line text","labelEnabled":true,"hintText":"","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz])[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[1],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Loại giấy chứng thực', 'repIdentityType', 'f', 'DROPDOWN_LIST', 'CUSTOMER', '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chứng minh nhân dân","Thẻ căn cước công dân","Hộ chiếu","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên doanh nghiệp', 'companyName', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Tên doanh nghiệp","labelEnabled":true,"hintText":"Nhập tên doanh nghiệp","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[2,3,1],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Email', 'emailContact', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số điện thoại', 'phoneNumContact', 'f', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', NULL, NULL);
Update vnpt_dev.custom_field set is_standard = true, customer_type ='KHDN', portal_type = 1 where code in
      ('repSex','repIdentityNo','generalDesc','contactMessage','companyName','repPosition','operator','repGeneralDesc','repIdentityDate','companyEmail','businessRegistrationNo','tin','repIdentityFileIds','contactAddress','fax','setupAddress','taxDepartment','businessRegistrationFileIds','contactOrganization','socialInsuranceNo','repBirthday','address','contactName','contactPosition','repName','SingleLineText','businessRegistrationAddress','repRegisterResidence','repIdentityAddress','repCurrentResidence','businessSizeId','repIdentityType','phone','contactGeneralDesc','status','emailContact' , 'phoneNumContact');
UPDATE vnpt_dev.custom_field SET is_standard = false WHERE
        code in ('contact.enterprise.enterpriseInfo.email','contact.enterprise.enterpriseInfo.establishDate','contact.enterprise.enterpriseInfo.areaCode','contact.enterprise.enterpriseInfo.businessNumber','contact.enterprise.rep.originalAdd','contact.enterprise.rep.currentAdd','contact.enterprise.otherInfo.createdBy','contact.enterprise.rep.sex','contact.enterprise.position','contact.enterprise.addNewContact','contact.enterprise.section.contactInfo','contact.enterprise.avatarUpload','contact.enterprise.name','contact.enterprise.phone','contact.enterprise.email','contact.enterprise.address','contact.enterprise.note','contact.enterprise.description','contact.enterprise.enterpriseInfo.name','contact.enterprise.enterpriseInfo.taxDepart','contact.enterprise.enterpriseInfo.taxNum','contact.enterprise.enterpriseInfo.region','contact.enterprise.enterpriseInfo.provinceCode','contact.enterprise.enterpriseInfo.provinceName','contact.enterprise.enterpriseInfo.districtCode','contact.enterprise.enterpriseInfo.districtName','contact.enterprise.enterpriseInfo.streetName','contact.enterprise.enterpriseInfo.address','contact.enterprise.enterpriseInfo.businessAdd','contact.enterprise.enterpriseInfo.setUpAdd','contact.enterprise.enterpriseInfo.socialInsNum','contact.enterprise.enterpriseInfo.actStatus','contact.enterprise.enterpriseInfo.teleProvider','contact.enterprise.enterpriseInfo.phoneNum','contact.enterprise.enterpriseInfo.fax','contact.enterprise.enterpriseInfo.businessSize','contact.enterprise.enterpriseInfo.businessType','contact.enterprise.enterpriseInfo.businessArea','contact.enterprise.enterpriseInfo.description','contact.enterprise.enterpriseInfo.busRegCert','contact.enterprise.section.otherInfo','contact.enterprise.otherInfo.createdSource','contact.enterprise.otherInfo.assignee','contact.enterprise.otherInfo.partition','contact.enterprise.section.repInfo','contact.enterprise.rep.position','contact.enterprise.rep.birthDate','contact.enterprise.rep.nationality','contact.enterprise.rep.ethnicity','contact.enterprise.rep.certType','contact.enterprise.rep.certNum','contact.enterprise.rep.certRegPlace','contact.enterprise.socialNetwork','contact.enterprise.province','contact.enterprise.section.businessInfo','contact.enterprise.enterpriseInfo.nationality','contact.enterprise.enterpriseInfo.communeCode','contact.enterprise.enterpriseInfo.communeName','contact.enterprise.rep.name','contact.enterprise.rep.certRegDate','contact.enterprise.rep.description','contact.enterprise.rep.indentityCertFile');
DELETE FROM vnpt_dev.custom_field where code in ('enterpriseRepIdentityAddress','enterpriseRepRegisterAddress','enterpriseCompanyAddress','enterpriseCompanyTaxCode','enterpriseRepName','enterpriseContactRole','enterpriseCompanyBusinessAddress','enterpriseCompanySocialInsurance','enterpriseCompanyDescription','enterpriseContactDescription','enterpriseCompanyEmail','enterpriseCompanyTaxDepartment','enterpriseRepIdentityNo','enterpriseRepIdentityType','contactAvatarFileId','enterpriseContactOrganization','enterpriseCompanyName','enterpriseCompanyFax','enterpriseCompanyStatus','enterpriseRepAddress','enterpriseContactAddress','enterpriseRepGender','enterpriseCompanySetupAddress','enterpriseRepDescription','enterpriseRepIdentityFile','enterpriseCompanySize','enterpriseCompanyBusinessRegistration','enterpriseCompanyNetwork','enterpriseContactMessage','enterpriseCompanyPhone','enterpriseRepIdentityDate','enterpriseRepRole','enterpriseRepBirth','enterpriseCompanyBusinessRegistrationFile','enterpriseContactName');
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên tổ chức', 'enterpriseContactOrganization', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên tổ chức","labelEnabled":true,"hintText":"Nhập tên tổ chức","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên người liên hệ', 'enterpriseContactName', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên người liên hệ","labelEnabled":true,"hintText":"Nhập tên người liên hệ","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d\\W]*$","lstPatternToken":[2,1,3,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới thiệu chung', 'enterpriseCompanyDescription', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Ngày cấp', 'enterpriseRepIdentityDate', 't', 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày cấp","labelEnabled":true,"hintText":"Chọn ngày cấp","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"2023-02-28T02:59:42.954Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Quy mô', 'enterpriseCompanySize', 't', 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Quy mô","labelEnabled":true,"hintText":"Chọn quy mô","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chỉ có bạn","2-9","10-99","100-299","300+"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ', 'enterpriseCompanyAddress', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\Waàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[2,3,4,1],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Mã số thuế', 'enterpriseCompanyTaxCode', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tải lên ảnh đại diện', 'contactAvatarFileId', 't', 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên ảnh đại diện","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ đăng ký kinh doanh', 'enterpriseCompanyBusinessAddress', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên doanh nghiệp', 'enterpriseCompanyName', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên doanh nghiệp","labelEnabled":true,"hintText":"Nhập tên doanh nghiệp","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Lời nhắn', 'enterpriseContactMessage', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chức vụ', 'enterpriseContactRole', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chức vụ","labelEnabled":true,"hintText":"Nhập chức vụ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ', 'enterpriseContactAddress', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Nơi cấp', 'enterpriseRepIdentityAddress', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d]*$","lstPatternToken":[2,3],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số giấy phép ĐKKD', 'enterpriseCompanyBusinessRegistration', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số giấy phép ĐKKD","labelEnabled":true,"hintText":"Nhập số giấy phép ĐKKD","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số điện thoại', 'enterpriseCompanyPhone', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\d\\W]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":12,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chỗ ở hiện tại', 'enterpriseRepAddress', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chỗ ở hiện tại","labelEnabled":true,"hintText":"Nhập chỗ ở hiện tại","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Email', 'enterpriseCompanyEmail', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":100,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới thiệu chung', 'enterpriseRepDescription', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chức danh', 'enterpriseRepRole', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chức danh","labelEnabled":true,"hintText":"Nhập chức danh","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Địa chỉ lắp đặt', 'enterpriseCompanySetupAddress', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Địa chỉ lắp đặt","labelEnabled":true,"hintText":"Nhập địa chỉ lắp đặt","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới tính', 'enterpriseRepGender', 't', 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Giới tính","labelEnabled":true,"hintText":"Chọn giới tính","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Nữ","Nam","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tải lên CMTND/CCCD', 'enterpriseRepIdentityFile', 't', 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên CMTND/CCCD","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số chứng thực cá nhân', 'enterpriseRepIdentityNo', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số chứng thực cá nhân","labelEnabled":true,"hintText":"Nhập số chứng thực cá nhân","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^(?=.*[\\d])[\\d]*$","lstPatternToken":[3],"patternCombination":0,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Loại giấy chứng thực', 'enterpriseRepIdentityType', 't', 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Loại giấy chứng thực","labelEnabled":true,"hintText":"Chọn loại giấy chứng thực","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Chứng minh nhân dân","Hộ chiếu","Thẻ căn cước công dân","Khác"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Nhà mạng', 'enterpriseCompanyNetwork', 't', 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Nhà mạng","labelEnabled":true,"hintText":"Chọn nhà mạng","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Vinaphone","Viettel","Mobifone"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số BHXH', 'enterpriseCompanySocialInsurance', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Số BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d]*$","lstPatternToken":[1,2,3],"patternCombination":1,"maxLength":10,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Nơi đăng ký hộ khẩu', 'enterpriseRepRegisterAddress', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Nơi đăng ký hộ khẩu","labelEnabled":true,"hintText":"Nhập nơi đăng ký hộ khẩu","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Giới thiệu chung', 'enterpriseContactDescription', 't', 'MULTI_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\Waàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[2,3,4,1],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Ngày sinh', 'enterpriseRepBirth', 't', 'DATE_PICKER', 'CUSTOMER_CONTACT', '{"label":"Ngày sinh","labelEnabled":true,"hintText":"Chọn ngày sinh","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"2023-02-28T02:50:13.855Z","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":"unset","uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Trạng thái hoạt động', 'enterpriseCompanyStatus', 't', 'DROPDOWN_LIST', 'CUSTOMER_CONTACT', '{"label":"Trạng thái hoạt động","labelEnabled":true,"hintText":"Chọn trạng thái hoạt động","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":["Đang hoạt động","Tạm dừng","Đã đóng"],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Chi cục thuế', 'enterpriseCompanyTaxDepartment', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Chi cục thuế","labelEnabled":true,"hintText":"Nhập chi cục thuế","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\daàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[2,3,1,4],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tên người đại diện', 'enterpriseRepName', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Tên người đại diện","labelEnabled":true,"hintText":"Nhập tên người đại diện","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\d\\W]*$","lstPatternToken":[2,1,3,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Tải lên đăng ký kinh doanh', 'enterpriseCompanyBusinessRegistrationFile', 't', 'UPLOAD_IMAGE', 'CUSTOMER_CONTACT', '{"label":"Tải lên đăng ký kinh doanh","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":"UPLOAD_URL","uploadExtension":[".jpeg, .jpg",".png",".webp",".tiff, .jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":1,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Fax', 'enterpriseCompanyFax', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER_CONTACT', '{"label":"Fax","labelEnabled":true,"hintText":"Nhập số fax","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\d\\W]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', NULL);

UPDATE vnpt_dev.custom_field SET is_standard = true, customer_type = 'KHDN' WHERE
        code in ('enterpriseRepIdentityAddress','enterpriseRepRegisterAddress','enterpriseCompanyAddress','enterpriseCompanyTaxCode','enterpriseRepName','enterpriseContactRole','enterpriseCompanyBusinessAddress','enterpriseCompanySocialInsurance','enterpriseCompanyDescription','enterpriseContactDescription','enterpriseCompanyEmail','enterpriseCompanyTaxDepartment','enterpriseRepIdentityNo','enterpriseRepIdentityType','contactAvatarFileId','enterpriseContactOrganization','enterpriseCompanyName','enterpriseCompanyFax','enterpriseCompanyStatus','enterpriseRepAddress','enterpriseContactAddress','enterpriseRepGender','enterpriseCompanySetupAddress','enterpriseRepDescription','enterpriseRepIdentityFile','enterpriseCompanySize','enterpriseCompanyBusinessRegistration','enterpriseCompanyNetwork','enterpriseContactMessage','enterpriseCompanyPhone','enterpriseRepIdentityDate','enterpriseRepRole','enterpriseRepBirth','enterpriseCompanyBusinessRegistrationFile','enterpriseContactName');
DELETE FROM vnpt_dev.custom_field where code in ('enterprise.avatarId.3', 'enterprise.repAvatarId.3', 'enterprise.personalAvatar.3', 'household.personalAvatar.3', 'household.repAvatarId.3', 'household.avatarId.3', 'household.taxCode.3', 'household.phoneNumber.3');
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Thay đổi ảnh đại diện', 'enterprise.avatarId.3', 't', 'AVATAR', 'CUSTOMER', '{"label":"Thay đổi ảnh đại diện","labelEnabled":true,"hintText":"Chọn thay đổi ảnh đại diện","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[".jpeg,.jpg",".png",".webp",".jfif",".ico"],"uploadMaxSize":10485760,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"\",\"showingConfig\":{\"serviceType\":[],\"categoryService\":\"\",\"serviceSelected\":[]}}"}', '[]', 'KHDN', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Thay đổi ảnh đại diện', 'enterprise.repAvatarId.3', 't', 'AVATAR', 'CUSTOMER', '{"label":"Thay đổi ảnh đại diện","labelEnabled":true,"hintText":"Chọn thay đổi ảnh đại diện","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":["jpg","jpeg","webp"],"uploadMaxSize":1000000,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Thay đổi ảnh đại diện', 'enterprise.personalAvatar.3', 't', 'AVATAR', 'CUSTOMER', '{"label":"Thay đổi ảnh đại diện","labelEnabled":true,"hintText":"Chọn thay đổi ảnh đại diện","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":["jpg","jpeg","webp"],"uploadMaxSize":1000000,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'KHDN', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Thay đổi ảnh đại diện', 'household.personalAvatar.3', 't', 'AVATAR', 'CUSTOMER', '{"label":"Thay đổi ảnh đại diện","labelEnabled":true,"hintText":"Chọn thay đổi ảnh đại diện","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":[".jpeg,.jpg",".webp"],"uploadMaxSize":1048576,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'HKD', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Thay đổi ảnh đại diện', 'household.repAvatarId.3', 't', 'AVATAR', 'CUSTOMER', '{"label":"Thay đổi ảnh đại diện","labelEnabled":false,"hintText":"Chọn thay đổi ảnh đại diện","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":["jpg","jpeg","webp"],"uploadMaxSize":1000000,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'HKD', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Thay đổi ảnh đại diện', 'household.avatarId.3', 't', 'AVATAR', 'CUSTOMER', '{"label":"Thay đổi ảnh đại diện","labelEnabled":false,"hintText":"Chọn thay đổi ảnh đại diện","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":["jpg","jpeg","webp"],"uploadMaxSize":1000000,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'HKD', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Mã số thuế', 'household.taxCode.3', 't', 'SINGLE_LINE_TEXT', 'CUSTOMER', '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":true,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d]*$","lstPatternToken":[2,3],"patternCombination":1,"maxLength":13,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'HKD', 3);
INSERT INTO "vnpt_dev"."custom_field" ("name", "code", "is_standard", "type", "category", "config", "lst_permission", "customer_type", "portal_type") VALUES ('Số điện thoại', 'household.phoneNumber.3', 't', 'NUMBER', 'CUSTOMER', '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":11,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', '[]', 'HKD', 3);