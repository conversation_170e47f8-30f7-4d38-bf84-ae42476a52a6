-- update customField
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{adminEnabled}','true') WHERE custom_field.category in ('CUSTOMER_CONTACT') and custom_field.is_standard = true;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{devEnabled}','false') WHERE custom_field.category in ('CUSTOMER_CONTACT') and custom_field.is_standard = true;
UPDATE vnpt_dev.custom_field SET config = jsonb_set(config::jsonb,'{smeEnabled}','false') WHERE custom_field.category in ('CUSTOMER_CONTACT') and custom_field.is_standard = true;
UPDATE  vnpt_dev.custom_layout set sme_enabled = null, dev_enabled = null
where category = 'CUSTOMER' and portal_type = 1;
UPDATE  vnpt_dev.custom_layout set admin_enabled = null, dev_enabled = null
where category = 'CUSTOMER' and portal_type = 3;
UPDATE  vnpt_dev.custom_layout set sme_enabled = null, dev_enabled = null
where category = 'CUSTOMER_CONTACT' and portal_type = 1;

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả gói', "code" = 'descriptionPricing', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'PRICING', "config" = '{"label":"Mô tả gói","labelEnabled":true,"hintText":"Mô tả gói","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'descriptionPricing';


UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterprise.repIntroduction.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'enterprise.repIntroduction.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterprise.introduction.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'enterprise.introduction.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterprise.contactIntroduction.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'enterprise.contactIntroduction.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'tech.description.0', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'SERVICE', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"Nhập mô tả","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":140,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'tech.description.0';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'household.repIntroduction.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 1 WHERE "code" = 'household.repIntroduction.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'household.introduction.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"[\\D\\S\\WaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ]{1,1000}","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 3 WHERE "code" = 'household.introduction.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'personal.generalDesc.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"[\\D\\S\\WaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ]{1,1000}","lstPatternToken":[1,2,1,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.generalDesc.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Lời nhắn', "code" = 'personal.contactMessage.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Lời nhắn","labelEnabled":true,"hintText":"Nhập lời nhắn","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":500,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.contactMessage.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'personal.contactGeneralDesc.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":"","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personal.contactGeneralDesc.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'layout.description.0', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'SERVICE', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":350,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'layout.description.0';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'feature.description.0', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'SERVICE', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"Nhập mô tả tính năng","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":true,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":100,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'feature.description.0';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'household.introduction.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[]*$","lstPatternToken":[],"patternCombination":1,"maxLength":99,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 1 WHERE "code" = 'household.introduction.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'household.contactIntroduction.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":true,"hintText":"Nhập giới thiệu chung","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}', "lst_permission" = '[]', "customer_type" = 'HKD', "portal_type" = 1 WHERE "code" = 'household.contactIntroduction.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'tech.description.1', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'SERVICE', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"Nhập mô tả","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":140,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'tech.description.1';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'tech.description.2', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'SERVICE', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"Nhập mô tả","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":140,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'tech.description.2';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Giới thiệu chung', "code" = 'enterprise.introduction.3', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER', "config" = '{"label":"Giới thiệu chung","labelEnabled":false,"hintText":"","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":false,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":1000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"\",\"showingConfig\":{\"serviceType\":[],\"categoryService\":\"\",\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 3 WHERE "code" = 'enterprise.introduction.3';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mô tả', "code" = 'addonDescription', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'ADDON', "config" = '{"label":"Mô tả","labelEnabled":true,"hintText":"Mô tả dịch vụ bổ sung","isUnique":null,"mandatory":"ALWAYS","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":5000,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = NULL, "portal_type" = NULL WHERE "code" = 'addonDescription';

-- update field lỗi phần liên hệ
UPDATE vnpt_dev.custom_field SET is_standard = false WHERE
        code in ('contact.enterprise.enterpriseInfo.email','contact.enterprise.enterpriseInfo.establishDate','contact.enterprise.enterpriseInfo.areaCode','contact.enterprise.enterpriseInfo.businessNumber','contact.enterprise.rep.originalAdd','contact.enterprise.rep.currentAdd','contact.enterprise.otherInfo.createdBy','contact.enterprise.rep.sex','contact.enterprise.position','contact.enterprise.addNewContact','contact.enterprise.section.contactInfo','contact.enterprise.avatarUpload','contact.enterprise.name','contact.enterprise.phone','contact.enterprise.email','contact.enterprise.address','contact.enterprise.note','contact.enterprise.description','contact.enterprise.enterpriseInfo.name','contact.enterprise.enterpriseInfo.taxDepart','contact.enterprise.enterpriseInfo.taxNum','contact.enterprise.enterpriseInfo.region','contact.enterprise.enterpriseInfo.provinceCode','contact.enterprise.enterpriseInfo.provinceName','contact.enterprise.enterpriseInfo.districtCode','contact.enterprise.enterpriseInfo.districtName','contact.enterprise.enterpriseInfo.streetName','contact.enterprise.enterpriseInfo.address','contact.enterprise.enterpriseInfo.businessAdd','contact.enterprise.enterpriseInfo.setUpAdd','contact.enterprise.enterpriseInfo.socialInsNum','contact.enterprise.enterpriseInfo.actStatus','contact.enterprise.enterpriseInfo.teleProvider','contact.enterprise.enterpriseInfo.phoneNum','contact.enterprise.enterpriseInfo.fax','contact.enterprise.enterpriseInfo.businessSize','contact.enterprise.enterpriseInfo.businessType','contact.enterprise.enterpriseInfo.businessArea','contact.enterprise.enterpriseInfo.description','contact.enterprise.enterpriseInfo.busRegCert','contact.enterprise.section.otherInfo','contact.enterprise.otherInfo.createdSource','contact.enterprise.otherInfo.assignee','contact.enterprise.otherInfo.partition','contact.enterprise.section.repInfo','contact.enterprise.rep.position','contact.enterprise.rep.birthDate','contact.enterprise.rep.nationality','contact.enterprise.rep.ethnicity','contact.enterprise.rep.certType','contact.enterprise.rep.certNum','contact.enterprise.rep.certRegPlace','contact.enterprise.socialNetwork','contact.enterprise.province','contact.enterprise.section.businessInfo','contact.enterprise.enterpriseInfo.nationality','contact.enterprise.enterpriseInfo.communeCode','contact.enterprise.enterpriseInfo.communeName','contact.enterprise.rep.name','contact.enterprise.rep.certRegDate','contact.enterprise.rep.description','contact.enterprise.rep.indentityCertFile');

UPDATE vnpt_dev.custom_field SET is_standard = true, customer_type = 'KHDN' WHERE
        code in ('enterpriseRepIdentityAddress','enterpriseRepRegisterAddress','enterpriseCompanyAddress','enterpriseCompanyTaxCode','enterpriseRepName','enterpriseContactRole','enterpriseCompanyBusinessAddress','enterpriseCompanySocialInsurance','enterpriseCompanyDescription','enterpriseContactDescription','enterpriseCompanyEmail','enterpriseCompanyTaxDepartment','enterpriseRepIdentityNo','enterpriseRepIdentityType','contactAvatarFileId','enterpriseContactOrganization','enterpriseCompanyName','enterpriseCompanyFax','enterpriseCompanyStatus','enterpriseRepAddress','enterpriseContactAddress','enterpriseRepGender','enterpriseCompanySetupAddress','enterpriseRepDescription','enterpriseRepIdentityFile','enterpriseCompanySize','enterpriseCompanyBusinessRegistration','enterpriseCompanyNetwork','enterpriseContactMessage','enterpriseCompanyPhone','enterpriseRepIdentityDate','enterpriseRepRole','enterpriseRepBirth','enterpriseCompanyBusinessRegistrationFile','enterpriseContactName');

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'contact.personal.personalInfo.email', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Email", "other": null, "canEdit": true, "pattern": null, "hintText": "Nhập email", "isUnique": null, "lstValue": [], "mandatory": "NONE", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": null, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'contact.personal.personalInfo.email';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'contact.personal.contactInfo.email', "is_standard" = 't', "type" = 'INPUT_TAG', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Email", "other": null, "canEdit": true, "pattern": "^[0-9]*$", "hintText": "", "isUnique": null, "lstValue": [], "mandatory": "ALWAYS", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": null, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'contact.personal.contactInfo.email';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'contact.enterprise.enterpriseInfo.email', "is_standard" = 't', "type" = 'EMAIL', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Email", "other": null, "canEdit": true, "pattern": "", "hintText": "Nhập email", "isUnique": null, "lstValue": [], "mandatory": "ALWAYS", "maxLength": null, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "lstPatternToken": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": null, "patternCombination": null, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'contact.enterprise.enterpriseInfo.email';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mã số thuế', "code" = 'contact.enterprise.enterpriseInfo.taxNum', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label": "Mã số thuế", "other": null, "canEdit": true, "hintText": "Nhập mã số thuế", "isUnique": null, "lstValue": [], "mandatory": "ALWAYS","pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":50, "urlMaxNum": null, "devEnabled": false, "smeEnabled": false, "uploadType": null, "urlPattern": null, "noteContent": "", "noteEnabled": false, "adminEnabled": true, "defaultValue": "", "labelEnabled": true, "displayFormat": null, "uploadMaxFile": null, "uploadMaxSize": null, "defaultDisplay": null, "tooltipsContent": "", "tooltipsEnabled": false, "uploadExtension": null, "getUploadMaxFile": null, "mandatoryCondition": {}, "displayOnDetailPage": null}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = 1 WHERE "code" = 'contact.enterprise.enterpriseInfo.taxNum';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Mã số thuế', "code" = 'enterpriseCompanyTaxCode', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Mã số thuế","labelEnabled":true,"hintText":"Nhập mã số thuế","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":50,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyTaxCode';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ đăng ký kinh doanh', "code" = 'enterpriseCompanyBusinessAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Địa chỉ đăng ký kinh doanh","labelEnabled":true,"hintText":"Nhập địa chỉ đăng ký kinh doanh","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyBusinessAddress';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ lắp đặt', "code" = 'enterpriseCompanySetupAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Địa chỉ lắp đặt","labelEnabled":true,"hintText":"Nhập địa chỉ lắp đặt","isUnique":null,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanySetupAddress';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Chỗ ở hiện tại', "code" = 'enterpriseRepAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Chỗ ở hiện tại","labelEnabled":true,"hintText":"Nhập chỗ ở hiện tại","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseRepAddress';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số BHXH', "code" = 'enterpriseCompanySocialInsurance', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số BHXH","labelEnabled":true,"hintText":"Nhập số BHXH","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d]*$","lstPatternToken":[1,2,3],"patternCombination":1,"maxLength":10,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanySocialInsurance';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Fax', "code" = 'enterpriseCompanyFax', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Fax","labelEnabled":true,"hintText":"Nhập số fax","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[+()\\d]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":20,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyFax';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ', "code" = 'personalContactAddress', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":300,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactAddress';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Địa chỉ', "code" = 'personalContactInfoAddr', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Địa chỉ","labelEnabled":true,"hintText":"Nhập địa chỉ","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[\\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz\\W]*$","lstPatternToken":[3,2,1,4],"patternCombination":1,"maxLength":200,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoAddr';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Email', "code" = 'enterpriseCompanyEmail', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Email","labelEnabled":true,"hintText":"Nhập email","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$","lstPatternToken":[1,2,3,4],"patternCombination":1,"maxLength":100,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyEmail';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Số điện thoại', "code" = 'enterpriseCompanyPhone', "is_standard" = 't', "type" = 'SINGLE_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Số điện thoại","labelEnabled":true,"hintText":"Nhập số điện thoại","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"CREATE_SUBSCRIPTION","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":true,"devEnabled":true,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[()+-. \\d]*$","lstPatternToken":[3,4],"patternCombination":1,"maxLength":12,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":\"categoryType\",\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'KHDN', "portal_type" = NULL WHERE "code" = 'enterpriseCompanyPhone';

UPDATE "vnpt_dev"."custom_field" SET "name" = 'Nơi cấp', "code" = 'personalContactInfoCertAddr', "is_standard" = 't', "type" = 'MULTI_LINE_TEXT', "category" = 'CUSTOMER_CONTACT', "config" = '{"label":"Nơi cấp","labelEnabled":true,"hintText":"Nhập nơi cấp","isUnique":false,"mandatory":"NONE","mandatoryCondition":{"action":"","applyFor":[],"listServices":[],"requiredServiceType":null},"tooltipsEnabled":false,"tooltipsContent":"","noteEnabled":false,"noteContent":"","smeEnabled":false,"devEnabled":false,"adminEnabled":true,"displayOnDetailPage":null,"canEdit":false,"lstValue":null,"defaultValue":null,"pattern":"^[ \\dAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZaàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵz]*$","lstPatternToken":[3,2,1],"patternCombination":1,"maxLength":150,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null,"other":"{\"showingFor\":null,\"showingConfig\":{\"serviceType\":[\"ON\",\"OS\"],\"categoryService\":[\"TatCaDanhMuc\"],\"serviceSelected\":[]}}"}', "lst_permission" = '[]', "customer_type" = 'CN', "portal_type" = 1 WHERE "code" = 'personalContactInfoCertAddr';

UPDATE vnpt_dev.custom_field
SET config = jsonb_set(config::jsonb,'{adminEnabled}','true')
WHERE custom_field.category in ('PRICING','SERVICE') and custom_field.is_standard = true;

DELETE FROM vnpt_dev.custom_layout WHERE name in('Tạo khách hàng','Tạo liên hệ');
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";