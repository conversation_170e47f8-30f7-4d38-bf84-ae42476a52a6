-- C<PERSON><PERSON> nhật func tính doanh thu thực tế theo Target ID
DROP FUNCTION IF EXISTS "vnpt_dev"."func_get_actual_revenue_by_target_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_actual_revenue_by_target_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2)
  RETURNS TABLE("amount" float8, "subcode" varchar) AS $BODY$
DECLARE
sub_type text;
is_employee_code_apply varchar;
is_creator_apply varchar;
is_assignee_apply varchar;
mQuery text;
subApplyQuery text;
subCommonQuery text;
targetTypeText text;
mResult float8;
table_revenue_target text;
BEGIN
CASE
	WHEN object_id IS NULL THEN object_id = -1;
ELSE object_id = object_id;
END CASE;

execute 'SELECT param_text_value
         FROM vnpt_dev.system_params
         WHERE system_params.param_name::text = ''Cấu hình loại thuê bao hiển thị doanh thu mục tiêu''::text' into sub_type;
execute 'SELECT
		     coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isEmployeeCodeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu''' into is_employee_code_apply;
execute 'SELECT
			 coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAccountApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu''' into is_creator_apply;
execute 'SELECT
	         coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAssigneeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu''' into is_assignee_apply;

subApplyQuery = CONCAT('
allAdminDuplicate as (
select
    distinct mValue.admin_id as id
from vnpt_dev.crm_revenue_target as mTarget
    join vnpt_dev.crm_revenue_target_value as mValue on mValue.target_id = mTarget.id and mValue.admin_id is not null
where
    (CAST(''',start_time,''' AS DATE) between mTarget.start_date and mTarget.end_date) or (mTarget.start_date between CAST(''',start_time,''' AS DATE) and CAST(''',end_time,''' AS DATE))
),
subCreatorApply as (
  select distinct subscriptions.id
	from vnpt_dev.subscriptions
	  join allAdminDuplicate on allAdminDuplicate.id = subscriptions.created_by and ', is_creator_apply,'
	where
	  subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
),
mSubApply as (
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join mUserInfo on mUserInfo.user_id = subscriptions.user_id and ', is_assignee_apply, ' = true
	where
			(subscriptions.employee_code is null or (', is_employee_code_apply,' = false)) and
		 ((subscriptions.id not in (select id from subCreatorApply) and ', is_creator_apply, ' = true) or (', is_creator_apply,' = false))
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join mAdminIdApply on mAdminIdApply.admin_code = subscriptions.employee_code and ', is_employee_code_apply, ' = true
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
	join mAdminIdApply on subscriptions.created_by = mAdminIdApply.id and ', is_creator_apply,'
	where
	subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
), ');

subCommonQuery = CONCAT(
'mSubCommonInfo as (
	select
		billings.id as bill_id,
		case when mSubApply.employee_code is not null then -1
		else mSubApply.user_id end as user_id,
		mSubApply.service_id,
		mSubApply.pricing_id,
		mSubApply.employee_code,
		case
			when billings.action_type = null then 0
			when billings.action_type = -1 then 0
			when billings.action_type = 5 then 1
			else 2
		end as target_type,
		billings.action_type,
		round(COALESCE(bill_item.amount_pre_tax,0)) as amount,
		round(COALESCE(bill_item.amount_after_tax,0)) as amount_after_tax,
		case
			when bill_item.amount_incurred < 0 then 0
			else round(bill_item.amount_incurred)
		end as amount_incurred,
		(billings.total_amount - billings .total_amount_after_adjustment) as refund
	from mSubApply
		left join vnpt_dev.billings on billings.subscriptions_id = mSubApply.id
		left join vnpt_dev.bill_item on bill_item.billing_id = billings.id
	where
	  billings.payment_date between CAST(''',start_time,''' AS DATE) AND CAST(''',end_time + interval '1 day' ,''' AS DATE)
		AND bill_item.object_type <> 3 AND billings.status = 2 AND mSubApply.pricing_id is not null), ');

CASE object_type
WHEN 0
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
				select id as user_id
				from vnpt_dev.users
			      join (
				         select lst_am_id, lst_admin_id
					       from vnpt_dev.crm_data_partition
					       where id in (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =',object_id ,' )
						) as part on users.assignee_id = ANY(part.lst_am_id) or users.assignee_id = ANY(part.lst_admin_id)
		),
		mAdminIdApply as (
				select id, admin_code as admin_code
				from vnpt_dev.users
			      join (
				        select lst_am_id, lst_admin_id
					      from vnpt_dev.crm_data_partition
					      where id in (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =',object_id ,' )
				    ) as part on users.id = ANY(part.lst_am_id) or users.id = ANY(part.lst_admin_id)
		),
		',subApplyQuery,'
		',subCommonQuery,'
        mEmployeeRevenue as (
				select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
	    select distinct
	        mSubCommonInfo.bill_id,
	        	case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
	        mSubCommonInfo.target_type
	    from mSubCommonInfo
		group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
		where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');

WHEN 1
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
			select id as user_id from vnpt_dev.users
			where assignee_id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' )
		),
		mAdminIdApply as (
					select id, admin_code from vnpt_dev.users
			where id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' )
		),
		',subApplyQuery,'
		service_info as (
			select id from vnpt_dev.services
			where id in (select service_id from vnpt_dev.crm_revenue_target_value where target_id =',object_id ,' )
		),',subCommonQuery,'
		mEmployeeRevenue as (
		select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
			mSubCommonInfo.bill_id,
			case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
			mSubCommonInfo.target_type
			from mSubCommonInfo
			join service_info on mSubCommonInfo.service_id = service_info.id and mSubCommonInfo.pricing_id is not null
					group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
					where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
ELSE mQuery = CONCAT(mQuery,
'with mUserInfo as (
   select id as user_id from vnpt_dev.users
   where assignee_id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' )),
 mAdminIdApply as (
   select id, admin_code from vnpt_dev.users
   where id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' ) and admin_code is not null),
',subApplyQuery,'
',subCommonQuery,'
		mEmployeeRevenue as (
		  select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
				mSubCommonInfo.bill_id,
				case
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
				mSubCommonInfo.target_type
			from mSubCommonInfo
			group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
			where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
END CASE;

case sub_type
when '' then sub_type = '0,1,2';
else sub_type = sub_type;
end case;

CASE target_type
WHEN 0 then mQuery = CONCAT(mQuery,'select round(COALESCE(sum(mEmployeeRevenue.amount),0)), String_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
WHEN 1 then
mQuery = CONCAT(mQuery,'select count(*)::float8, string_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
END CASE;
        RAISE NOTICE 'mQuery: %', mQuery;

return Query execute mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

    -- Cập nhật doanh func tính doạnh thu thực tế theo targetValue ID

DROP FUNCTION IF EXISTS "vnpt_dev"."func_get_actual_revenue_by_target_value_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_actual_revenue_by_target_value_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2)
  RETURNS TABLE("amount" float8, "subcode" varchar) AS $BODY$
DECLARE
sub_type text;
is_employee_code_apply varchar;
is_creator_apply varchar;
is_assignee_apply varchar;
mQuery text;
subApplyQuery text;
subCommonQuery text;
targetTypeText text;
mResult float8;
table_revenue_target text;
BEGIN
CASE
	WHEN object_id IS NULL THEN object_id = -1;
ELSE object_id = object_id;
END CASE;
execute 'SELECT param_text_value
         FROM vnpt_dev.system_params
         WHERE system_params.param_name::text = ''Cấu hình loại thuê bao hiển thị doanh thu mục tiêu''::text' into sub_type;
execute 'SELECT
				   coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isEmployeeCodeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu'''	into is_employee_code_apply;
execute 'SELECT
				   coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAccountApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu'''	into is_creator_apply;
execute 'SELECT
				   coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAssigneeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu'''	into is_assignee_apply;


subApplyQuery = CONCAT(
'allAdminDuplicate as (
select
    distinct mValue.admin_id as id
from vnpt_dev.crm_revenue_target as mTarget
    join vnpt_dev.crm_revenue_target_value as mValue on mValue.target_id = mTarget.id and mValue.admin_id is not null
where
    (CAST(''',start_time,''' AS DATE) between mTarget.start_date and mTarget.end_date) or (mTarget.start_date between CAST(''',start_time,''' AS DATE) and CAST(''',end_time,''' AS DATE))
),
subCreatorApply as (
		select distinct subscriptions.id
		from vnpt_dev.subscriptions
		join allAdminDuplicate on allAdminDuplicate.id = subscriptions.created_by and ', is_creator_apply,'
		where
		subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
),
mSubApply as (
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join mUserInfo on mUserInfo.user_id = subscriptions.user_id and ', is_assignee_apply, ' = true
	where
	   (subscriptions.employee_code is null or (', is_employee_code_apply,' = false)) and
		 ((subscriptions.id not in (select id from subCreatorApply) and ', is_creator_apply, ' = true) or (', is_creator_apply,' = false))
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join adminApplyInfo on adminApplyInfo.admin_code = subscriptions.employee_code and ', is_employee_code_apply, ' = true
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
	join adminApplyInfo on subscriptions.created_by = adminApplyInfo.id and ', is_creator_apply,'
	where
	subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
), ');

subCommonQuery = CONCAT(
'mSubCommonInfo as (
	select
		billings.id as bill_id,
		case when mSubApply.employee_code is not null then -1
		else mSubApply.user_id end as user_id,
		mSubApply.service_id,
		mSubApply.pricing_id,
		mSubApply.employee_code,
		case
			when billings.action_type = null then 0
			when billings.action_type = -1 then 0
			when billings.action_type = 5 then 1
			else 2
		end as target_type,
		billings.action_type,
		round(COALESCE(bill_item.amount_pre_tax,0)) as amount,
		round(COALESCE(bill_item.amount_after_tax,0)) as amount_after_tax,
		case
			when bill_item.amount_incurred < 0 then 0
			else round(bill_item.amount_incurred)
		end as amount_incurred,
		(billings.total_amount - billings .total_amount_after_adjustment) as refund
	from mSubApply
		left join vnpt_dev.billings on billings.subscriptions_id = mSubApply.id
		left join vnpt_dev.bill_item on bill_item.billing_id = billings.id
	where billings.payment_date between CAST(''',start_time,''' AS DATE) AND CAST(''',end_time + interval '1 day' ,''' AS DATE)
		AND bill_item.object_type <> 3 AND billings.status = 2 AND mSubApply.pricing_id is not null), ');

CASE object_type
WHEN 0
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
				select id as user_id
				from vnpt_dev.users
			      join (
				         select lst_am_id, lst_admin_id
					       from vnpt_dev.crm_data_partition
					       where id = (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =',object_id ,' )
						) as part on users.assignee_id = ANY(part.lst_am_id) or users.assignee_id = ANY(part.lst_admin_id)
		),
		adminApplyInfo as (
				select id, admin_code as admin_code
				from vnpt_dev.users
			      join (
				        select lst_am_id, lst_admin_id
					      from vnpt_dev.crm_data_partition
					      where id = (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =',object_id ,' )
				    ) as part on users.id = ANY(part.lst_am_id) or users.id = ANY(part.lst_admin_id)
		),
		',subApplyQuery,'
		',subCommonQuery,'
        mEmployeeRevenue as (
				select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
	    select distinct
	        mSubCommonInfo.bill_id,
	        	case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
	        mSubCommonInfo.target_type
	    from mSubCommonInfo
		group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
		where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');

WHEN 1
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
			select id as user_id from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =',object_id ,' )
		),
		adminApplyInfo as (
					select id, admin_code from vnpt_dev.users
			where id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =', object_id ,' )
		),
		',subApplyQuery,'
		service_info as (
			select id from vnpt_dev.services
			where id = (select service_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),',subCommonQuery,'
		mEmployeeRevenue as (
		select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
			mSubCommonInfo.bill_id,
			case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
			mSubCommonInfo.target_type
			from mSubCommonInfo
			join service_info on mSubCommonInfo.service_id = service_info.id and mSubCommonInfo.pricing_id is not null
					group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
					where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
ELSE mQuery = CONCAT(mQuery,'
		with mUserInfo as (
			select id as user_id from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =', object_id ,' )
		),
		adminApplyInfo as (
					select id, admin_code from vnpt_dev.users
			where id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =', object_id ,' )
		),
		',subApplyQuery,'
		',subCommonQuery,'
		mEmployeeRevenue as (
		  select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
				mSubCommonInfo.bill_id,
				case
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
				mSubCommonInfo.target_type
			from mSubCommonInfo
			group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
			where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
END CASE;

case sub_type
when '' then sub_type = '0,1,2';
else sub_type = sub_type;
end case;

CASE target_type
WHEN 0 then mQuery = CONCAT(mQuery,'select round(COALESCE(sum(mEmployeeRevenue.amount),0)), String_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
WHEN 1 then
mQuery = CONCAT(mQuery,'select count(*)::float8, string_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
END CASE;
        RAISE NOTICE 'mQuery: %', mQuery;

return Query execute mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;