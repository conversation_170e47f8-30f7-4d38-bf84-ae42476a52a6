DROP TABLE IF EXISTS categories_application;

CREATE TABLE vnpt_dev.categories_application
(
    id           bigserial NOT NULL,
    description  text NULL,      -- <PERSON><PERSON> tả danh mục
    status       int2 NULL,      -- Tr<PERSON>ng thái: 0 - <PERSON><PERSON><PERSON> hi<PERSON>, 1 - <PERSON><PERSON> l<PERSON>
    deleted_flag int2 NULL,      -- Tr<PERSON><PERSON> thái xóa : 0 - <PERSON><PERSON> xóa, 1 - <PERSON><PERSON><PERSON> xóa
    created_by   int4 NULL,      -- ng<PERSON><PERSON><PERSON> tạo
    created_at   timestamp NULL, -- ngày tạo
    modified_by  int4 NULL,      -- người sửa
    modified_at  timestamp NULL, -- ng<PERSON><PERSON> sửa
    "name"       varchar(200) NULL,
    CONSTRAINT categories_application_pkey PRIMARY KEY (id)
);

-- <PERSON><PERSON>n comments

COMMENT
ON COLUMN vnpt_dev.categories_application.description IS 'Mô tả danh mục';
COMMENT
ON COLUMN vnpt_dev.categories_application.status IS 'Trạng thái: 0 - <PERSON><PERSON><PERSON> hi<PERSON> l<PERSON>, 1 - <PERSON><PERSON> hiệu lự<PERSON>';
COMMENT
ON COLUMN vnpt_dev.categories_application.deleted_flag IS 'Trạng thái xóa : 0 - <PERSON><PERSON> xóa, 1 - Chưa xóa';
COMMENT
ON COLUMN vnpt_dev.categories_application.created_by IS 'người tạo';
COMMENT
ON COLUMN vnpt_dev.categories_application.created_at IS 'ngày tạo';
COMMENT
ON COLUMN vnpt_dev.categories_application.modified_by IS 'người sửa';
COMMENT
ON COLUMN vnpt_dev.categories_application.modified_at IS 'ngày sửa';


INSERT INTO vnpt_dev.categories_application (description, status, deleted_flag, created_by, created_at, modified_by, modified_at, name)
VALUES (NULL, 1, 1, NULL, '2021-04-28 11:51:05.732283', NULL, '2021-04-28 10:29:23.887', 'Quản lý chung'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:06.732283', NULL, '2021-04-28 10:51:40.301', 'HRM'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:07.732283', NULL, '2021-04-28 10:01:11.203', 'CRM'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:08.732283', NULL, '2021-04-28 10:50:38.449', 'Sales'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:09.732283', NULL, '2021-04-28 10:23:49.883', 'ERP'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:10.732283', NULL, '2021-04-28 10:43:56.424', 'Giáo Dục'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:12.732283', NULL, '2021-04-28 10:01:00.473', 'Y Tế'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:13.732283', NULL, '2021-04-28 09:59:16.36', 'Du lịch'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:14.732283', NULL, '2021-04-28 10:43:52.184', 'Bán lẻ'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:21.732283', NULL, '2021-04-28 10:29:21.686', 'Sản xuất'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:21.732283', NULL, '2021-04-28 10:29:21.686', 'Nông nghiệp'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:21.732283', NULL, '2021-04-28 10:29:21.686', 'Tài chính - Ngân hàng'),
       (NULL, 1, 1, NULL, '2021-04-28 11:51:21.732283', NULL, '2021-04-28 10:29:21.686', 'Giao thông - Vận Tải');

ALTER TABLE "vnpt_dev"."services"
    ADD COLUMN IF NOT EXISTS "service_type_application" int2,
    ADD COLUMN IF NOT EXISTS "categories_app" int8[];

COMMENT
ON COLUMN vnpt_dev.services.service_type_application IS 'Loại ứng dụng';

COMMENT
ON COLUMN vnpt_dev.services.categories_app IS 'Danh mục của ứng dụng';

ALTER TABLE "vnpt_dev"."services_draft"
    ADD COLUMN IF NOT EXISTS "service_type_application" int2,
    ADD COLUMN IF NOT EXISTS "categories_app" int8[];