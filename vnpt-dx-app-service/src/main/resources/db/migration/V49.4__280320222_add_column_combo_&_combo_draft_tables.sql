ALTER TABLE vnpt_dev.combo_draft ADD COLUMN customer_type_code VARCHAR(255);
COMMENT ON COLUMN vnpt_dev.combo_draft.customer_type_code IS 'Loại khách hàng đăng ký tài khoản: KHDN - <PERSON><PERSON><PERSON><PERSON> hàng <PERSON>, HKD <PERSON> <PERSON><PERSON>, CN - c<PERSON> nhân';

ALTER TABLE vnpt_dev.combo ADD COLUMN customer_type_code VARCHAR(255);
COMMENT ON COLUMN vnpt_dev.combo.customer_type_code IS 'Loại khách hàng đăng ký tài khoản: KHDN - <PERSON><PERSON><PERSON><PERSON> h<PERSON> do<PERSON>hi<PERSON>, HKD - <PERSON><PERSON> kinh doanh, CN - cá nhân';

UPDATE vnpt_dev.combo_draft SET customer_type_code = '["KHDN"]' WHERE customer_type_code IS NULL;

UPDATE vnpt_dev.combo SET customer_type_code = '["KHDN"]' WHERE customer_type_code IS NULL;

