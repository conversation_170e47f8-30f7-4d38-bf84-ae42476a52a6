-- admin
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id)
                    + 1
         FROM vnpt_dev.apis),
        '/api/admin-portal/rating/combo/{id}',
        'ROLE_ADMIN_SUA_THONG_TIN_NHAN_XET_COMBO',
        'PUT');
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id)
                    + 1
         FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_SUA_THONG_TIN_NHAN_XET_COMBO' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'SUA_THONG_TIN_NHAN_XET_CUA_KHACH_HANG' AND pp.portal_id = 1 LIMIT 1)
        ,1,1
    );
-- dev
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id)
                    + 1
         FROM vnpt_dev.apis),
        '/api/dev-portal/rating/combo/{id}',
        'ROLE_DEV_SUA_THONG_TIN_NHAN_XET_COMBO',
        'PUT');

INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id)
                    + 1
         FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_SUA_THONG_TIN_NHAN_XET_COMBO' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'SUA_THONG_TIN_NHAN_XET_CUA_KHACH_HANG' AND pp.portal_id = 1 LIMIT 1)
        ,1,1
    );