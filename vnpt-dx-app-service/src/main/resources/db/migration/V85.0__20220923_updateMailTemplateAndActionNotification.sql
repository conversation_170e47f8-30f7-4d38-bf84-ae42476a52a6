UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố đăng ký mới', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-01';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố đăng ký mới', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-02';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố gia hạn', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-03';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố gia hạn', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-04';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố cập nhật thuê bao', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-05';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố cập nhật thuê bao', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-06';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố đổi gói dịch vụ', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-07';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố đổi gói dịch vụ', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-08';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố hủy gói', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-09';

UPDATE vnpt_dev.action_notification
SET "name"='Thông báo khi có sự cố hủy gói', parent_id=(SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'SLA' LIMIT 1)
WHERE action_code = 'SLA-10';

UPDATE vnpt_dev.mail_template
SET  "name"='Sự cố mua mới'
WHERE code IN ('SLA-01', 'SLA-02');

UPDATE vnpt_dev.mail_template
SET  "name"='Sự cố gia hạn'
WHERE code IN ('SLA-03', 'SLA-04');

UPDATE vnpt_dev.mail_template
SET  "name"='Sự cố cập nhật thuê bao'
WHERE code IN ('SLA-05', 'SLA-06');

UPDATE vnpt_dev.mail_template
SET  "name"='Sự cố đổi gói dịch vụ'
WHERE code IN ('SLA-07', 'SLA-08');

UPDATE vnpt_dev.mail_template
SET  "name"='Sự cố hủy gói dịch vụ'
WHERE code IN ('SLA-09', 'SLA-10');


