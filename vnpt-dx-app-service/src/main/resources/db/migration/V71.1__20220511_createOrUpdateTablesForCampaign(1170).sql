-- NgoNC: <PERSON><PERSON><PERSON><PERSON> mới bảng bill_mc_private
DROP TABLE IF EXISTS "vnpt_dev"."bill_mc_private";
CREATE TABLE "vnpt_dev"."bill_mc_private" (
    "id" bigserial NOT NULL,
    "bill_item_id" int8,
    "amount_by_cash" float8,
    "amount_by_percent" float8,
    "mc_id" int8,
    "activity_idx" int8,
    "rule_idx" int8,
    "effect_set_idx" int8,
    "effect_item_idx" int8,
    "object_type" int2,
    "object_id" int8,
    "percent" float8,
    "max_amount" float8,
    "amount" float8,
    "number_apply_unit" int4,
    "created_at" timestamp(6),
    "created_by" int8,
    "modified_at" timestamp(6),
    "modified_by" int8
);
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."bill_item_id" IS 'Id của bảng bill_item';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."amount_by_cash" IS 'Số tiền khuyến mại riêng theo số tiền';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."amount_by_percent" IS 'Số tiền khuyến mại riêng theo %';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."mc_id" IS 'ID của CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."activity_idx" IS 'Index của activity trong CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."rule_idx" IS 'Index của rule trong activity CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."effect_set_idx" IS 'Index của effect set CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."effect_item_idx" IS 'Index của effect item CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."object_type" IS 'Loại đối tượng áp dụng (0: pricing, 1: combo plan, 2: addon)';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."object_id" IS 'ID của đối tượng áp dụng CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."percent" IS 'Phần trăm khuyến mại của effect CDQC loại phần trăm';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."max_amount" IS 'Số tiền khuyến mại tối đa của effect CDQC loại phần trăm';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."amount" IS 'Số tiền khuyến mại của effect CDQC loại giá tiền';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_private"."number_apply_unit" IS 'Số lượng đơn vị áp dụng effect CDQC';
COMMENT ON TABLE "vnpt_dev"."bill_mc_private" IS 'Bảng lưu thông tin số tiền khuyến mại riêng từ CDQC của từng item trong bảng bill_item';

-- NgoNC: Thêm mới bảng bill_mc_total
DROP TABLE IF EXISTS "vnpt_dev"."bill_mc_total";
CREATE TABLE "vnpt_dev"."bill_mc_total" (
    "id" bigserial NOT NULL,
    "bill_item_id" int8,
    "amount_by_cash" float8,
    "amount_by_percent" float8,
    "mc_id" int8,
    "activity_idx" int8,
    "rule_idx" int8,
    "effect_set_idx" int8,
    "effect_item_idx" int8,
    "object_type" int2,
    "object_id" int8,
    "percent" float8,
    "max_amount" float8,
    "amount" float8,
    "number_apply_unit" int4,
    "created_at" timestamp(6),
    "created_by" int8,
    "modified_at" timestamp(6),
    "modified_by" int8
);
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."bill_item_id" IS 'Id của bảng bill_item';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."amount_by_cash" IS 'Số tiền khuyến mại tổng hóa đơn theo số tiền';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."amount_by_percent" IS 'Số tiền khuyến mại tổng hóa đơn theo %';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."mc_id" IS 'ID của CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."activity_idx" IS 'Index của activity trong CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."rule_idx" IS 'Index của rule trong activity CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."effect_set_idx" IS 'Index của effect set CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."effect_item_idx" IS 'Index của effect item CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."object_type" IS 'Loại đối tượng áp dụng (0: pricing, 1: combo plan, 2: addon)';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."object_id" IS 'ID của đối tượng áp dụng CDQC';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."percent" IS 'Phần trăm khuyến mại của effect CDQC loại phần trăm';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."max_amount" IS 'Số tiền khuyến mại tối đa của effect CDQC loại phần trăm';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."amount" IS 'Số tiền khuyến mại của effect CDQC loại giá tiền';
COMMENT ON COLUMN "vnpt_dev"."bill_mc_total"."number_apply_unit" IS 'Số lượng đơn vị áp dụng effect CDQC';
COMMENT ON TABLE "vnpt_dev"."bill_mc_total" IS 'Bảng lưu thông tin số tiền khuyến mại tổng hóa đơn từ CDQC của từng item trong bảng bill_item';

-- NgoNC: Thêm thông tin billing_id vào bảng mc_event_applied_info
ALTER TABLE "vnpt_dev"."mc_event_applied_info"
  ADD COLUMN "billing_id" int8;
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."billing_id" IS 'ID của billing ứng với lần áp dụng CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."status" IS 'Trạng thái thống kê doanh thu của event (0: Chưa được thống kê, 1: Đã được thống kê)';

--DienNQ: Cập nhật dữ liệu config

DELETE FROM vnpt_dev.mc_operator;
INSERT INTO vnpt_dev.mc_operator(name, code, created_at)
VALUES
	('Bằng', 1, NOW()::timestamp),
	('Khác', 2, NOW()::timestamp),
	('Chứa', 3, NOW()::timestamp),
	('Không chứa', 4, NOW()::timestamp),
	('Bắt đầu bằng', 5, NOW()::timestamp),
	('Kết thúc bằng', 6, NOW()::timestamp),
	('Không bắt đầu bằng', 7, NOW()::timestamp),
	('Không kết thúc bằng', 8, NOW()::timestamp),
	('Không có giá trị', 9, NOW()::timestamp),
	('Có giá trị', 10, NOW()::timestamp),
	('Nhỏ hơn', 11, NOW()::timestamp),
	('Lớn hơn', 12, NOW()::timestamp),
	('Lớn hơn hoặc bằng', 13, NOW()::timestamp),
	('Nhỏ hơn hoặc bằng', 14, NOW()::timestamp),
	('Nằm giữa', 15, NOW()::timestamp),
	('Không nằm giữa', 16, NOW()::timestamp),
	('Nằm trong danh sách', 17, NOW()::timestamp),
	('Không nằm trong danh sách', 18, NOW()::timestamp),
	('Không giới hạn', 19, NOW()::timestamp),
	('Trong vòng', 20, NOW()::timestamp),
	('Không áp dụng', 21, NOW()::timestamp),
	('Áp dụng với', 22, NOW()::timestamp),
	('Theo SPDV đã mua', 23, NOW()::timestamp),
	('Giới hạn', 24, NOW()::timestamp);
	

DELETE FROM vnpt_dev.mc_data_type;
INSERT INTO vnpt_dev.mc_data_type(code, name, created_at)
VALUES	
( 1, 'String', NOW()::timestamp ),
( 2, 'List String', NOW()::timestamp ),
( 3, 'Integer', NOW()::timestamp ),
( 4, 'Integer Range', NOW()::timestamp ),
( 5, 'List Integer', NOW()::timestamp ),
( 6, 'DateTime', NOW()::timestamp ),
( 7, 'DateTime Range', NOW()::timestamp ),
( 8, 'List DateTime', NOW()::timestamp ),
( 9, 'Time Duration', NOW()::timestamp ),
( 10, 'Double', NOW()::timestamp ),
( 11, 'Double Range', NOW()::timestamp ),
( 12, 'List Double', NOW()::timestamp ),
( 13, 'Customer Type', NOW()::timestamp ),
( 14, 'Discount', NOW()::timestamp ),
( 15, 'Discount Scope', NOW()::timestamp ),
( 16, 'Purchase Time', NOW()::timestamp ),
( 17, 'Identifier Number', NOW()::timestamp ),
( 18, 'List ID', NOW()::timestamp ),
( 19, 'Service Type', NOW()::timestamp ),
( 20, 'List Service Type', NOW()::timestamp ),
( 21, 'Coupon and Marketing Campaign', NOW()::timestamp ),
( 22, 'Apply Time', NOW()::timestamp ),
( 23, 'Null', NOW()::timestamp ),
( 24, 'Free Product', NOW()::timestamp ),
( 25, 'Fix String', NOW()::timestamp ),
( 26, 'List Fix String', NOW()::timestamp ),
( 27, 'Payment Method', NOW()::timestamp ),
( 28, 'List Payment Method', NOW()::timestamp ),
( 29, 'Service Combo Addon Id', NOW()::timestamp ),
( 30, 'List Service Combo Addon Id', NOW()::timestamp ),
( 31, 'Pricing Combo_Plan Id', NOW()::timestamp ),
( 32, 'List Pricing Combo_Plan Id', NOW()::timestamp ),
( 33, 'Payment Cycle', NOW()::timestamp ),
( 34, 'List Payment Cycle', NOW()::timestamp ),
( 35, 'Number Apply MC', NOW()::timestamp ),
( 36, 'Discount Amount', NOW()::timestamp ),
( 37, 'Register Service N', NOW()::timestamp ),
( 38, 'Number Unit Apply', NOW()::timestamp ),
( 39, 'List Customer Type', NOW()::timestamp ),
( 40, 'Addon Type', NOW()::timestamp ),
( 41, 'List Addon Type', NOW()::timestamp ),
( 42, 'Payment Status', NOW()::timestamp ),
( 43, 'List Payment Status', NOW()::timestamp ),
( 44, 'Pricing Plan', NOW()::timestamp ),
( 45, 'List Pricing Plan', NOW()::timestamp ),
( 46, 'Time Unit', NOW()::timestamp ),
( 47, 'List Time Unit', NOW()::timestamp ),
( 48, 'Time Unit Range', NOW()::timestamp ),
( 49, 'Service Category', NOW()::timestamp ),
( 50, 'List Service Category', NOW()::timestamp ),
( 51, 'Giveaway Time', NOW()::timestamp ),
( 52, 'Number Gift', NOW()::timestamp ),
( 53, 'Number Unit of Gift', NOW()::timestamp ),
( 54, 'Time of Using Gift', NOW()::timestamp ),
( 55, 'List Coupon and Marketing Campaign', NOW()::timestamp ),
( 56, 'Give Away Service', NOW()::timestamp ),
( 57, 'Give Away Pricing', NOW()::timestamp ),
( 58, 'Give Away Payment Cycle', NOW()::timestamp ),
( 59, 'Discount Service Payment Cycle', NOW()::timestamp ),
( 60, 'List Discount Service Payment Cycle', NOW()::timestamp ),
( 61, 'Customer Organization Type', NOW()::timestamp ),
( 62, 'List Customer Organization Type', NOW()::timestamp );



DELETE FROM vnpt_dev.mc_operand;
INSERT INTO vnpt_dev.mc_operand(name, code, object_type, mc_data_type_code, created_at)
VALUES
('Khách hàng đăng ký tài khoản', 1, 0, 6, NOW()::timestamp),
('Khách hàng đăng ký dịch vụ lần đầu', 2, 0, 1, NOW()::timestamp),
('Số thuê bao đã đăng ký', 3, 0, 3, NOW()::timestamp),
('Số thuê bao đang sử dụng', 4, 0, 3, NOW()::timestamp),
('Số lần đăng ký dùng thử', 5, 0, 3, NOW()::timestamp),
('Số lần đăng ký dùng chính thức', 6, 0, 3, NOW()::timestamp),
('Số lần đang chờ sử dụng', 7, 0, 3, NOW()::timestamp),
('Số lần gia hạn', 8, 0, 3, NOW()::timestamp),
('Số lần hủy', 9, 0, 3, NOW()::timestamp),
('Số lần kích hoạt lại', 10, 0, 3, NOW()::timestamp),
('Số lần đổi gói', 11, 0, 3, NOW()::timestamp),
('Tổng số tiền đã thanh toán', 12, 0, 10, NOW()::timestamp),
('Tổng số tiền khuyến mại đã nhận', 13, 0, 10, NOW()::timestamp),
('CTKM đã áp dụng', 14, 0, 1, NOW()::timestamp),
('Bộ mã KM đã áp dụng', 15, 0, 1, NOW()::timestamp),
('Mã KM đã áp dụng', 16, 0, 1, NOW()::timestamp),
('Khách hàng đã đánh giá sản phẩm', 17, 0, 1, NOW()::timestamp),
('Khách hàng đã tìm kiếm từ khóa', 18, 0, 1, NOW()::timestamp),
('Khách hàng đã tìm kiếm sản phẩm ', 19, 0, 1, NOW()::timestamp),
('Khách hàng đã thích SPDV', 20, 0, 1, NOW()::timestamp),
('Khách hàng đã thích gói cước', 21, 0, 1, NOW()::timestamp),
('Danh mục sản phẩm đã mua', 22, 0, 1, NOW()::timestamp),
('Loại dịch vụ đã mua', 23, 0, 19, NOW()::timestamp),
('SPDV đã mua', 24, 0, 1, NOW()::timestamp),
('Gói dịch vụ đã mua', 25, 0, 1, NOW()::timestamp),
('Mã khách hàng', 26, 0, 25, NOW()::timestamp),
('Email ', 27, 0, 25, NOW()::timestamp),
('Ngày sinh', 28, 0, 6, NOW()::timestamp),
('Tên đầu', 29, 0, 25, NOW()::timestamp),
('Tên cuối', 30, 0, 25, NOW()::timestamp),
('Tên doanh nghiệp', 31, 0, 25, NOW()::timestamp),
('Quốc gia', 32, 0, 25, NOW()::timestamp),
('Thành phố/Tỉnh', 33, 0, 25, NOW()::timestamp),
('Quận/Huyện', 34, 0, 25, NOW()::timestamp),
('Phường/xã ', 35, 0, 25, NOW()::timestamp),
('Phố/Đường ', 36, 0, 25, NOW()::timestamp),
('Địa chỉ', 37, 0, 25, NOW()::timestamp),
('Số điện thoại', 38, 0, 25, NOW()::timestamp),
('Mã số BHXH', 39, 0, 25, NOW()::timestamp),
('Website', 40, 0, 25, NOW()::timestamp),
('Mã số thuế', 41, 0, 25, NOW()::timestamp),
('Quy mô doanh nghiệp', 42, 0, 17, NOW()::timestamp),
('Ngành nghề kinh doanh', 43, 0, 17, NOW()::timestamp),
('Người đại diện', 44, 0, 25, NOW()::timestamp),
('Địa chi lắp đặt', 45, 0, 25, NOW()::timestamp),
('Địa chỉ đăng ký kinh doanh', 46, 0, 25, NOW()::timestamp),
('Ngày hủy tài khoản', 47, 0, 6, NOW()::timestamp),
('Ngày tạo tài khoản', 48, 0, 6, NOW()::timestamp),
('Ngày kích hoạt lại tài khoản', 49, 0, 6, NOW()::timestamp),
('Phương thức thanh toán', 50, 0, 27, NOW()::timestamp),
('Mã nhân viên giới thiệu', 51, 0, 1, NOW()::timestamp),
('Nhóm khách hàng', 52, 0, 1, NOW()::timestamp),
('Khách hàng không đăng nhập trong thời gian', 53, 0, 6, NOW()::timestamp),
('Loại khách hàng', 54, 0, 13, NOW()::timestamp),
('Họ và tên liên hệ', 55, 0, 25, NOW()::timestamp),
('Email liên hệ', 56, 0, 25, NOW()::timestamp),
('Phân loại khách hàng', 57, 0, 61, NOW()::timestamp),
('Số tiền thuê bao bao gồm cả các loại thuế phí', 1001, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao không bao gồm các loại thuế phí', 1002, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao bao gồm phí thiết lập trước thuế', 1003, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao bao gồm phí thiết lập sau thuế', 1004, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao bao gồm các addons bắt buộc không tính thuế phí', 1005, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao bao gồm các addon bắt buộc và thuế phí', 1006, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao bao gồm các addons (cả bắt buộc và không bắt buộc) không tính thuế phí', 1007, 1, 10, NOW()::timestamp ),
('Số tiền thuê bao bao gồm các addons (cả bắt buộc và không bắt buộc) tính thuế phí', 1008, 1, 10, NOW()::timestamp ),
('Tổng số tiền phải thanh toán của thuê bao', 1009, 1, 10, NOW()::timestamp ),
('Số tiền khuyến mại (số tiền khuyến mại gói chính)', 1010, 1, 10, NOW()::timestamp ),
('Số tiền khuyến mại (số tiền khuyến mại gói addon)', 1011, 1, 10, NOW()::timestamp ),
('Số tiền khuyến mại (số tiền khuyến mại tổng hoá đơn)', 1012, 1, 10, NOW()::timestamp ),
('Số lượt gia hạn', 1013, 1, 3, NOW()::timestamp ),
('Số lượng thuê bao đã đăng ký', 1014, 1, 3, NOW()::timestamp ),
('Danh mục sản phẩm đã đăng ký', 1015, 1, 1, NOW()::timestamp ),
('Loại sản phẩm dịch vụ', 1016, 1, 19, NOW()::timestamp ),
('Số lượt đổi gói', 1017, 1, 3, NOW()::timestamp ),
('Ngày tạo thuê bao', 1018, 1, 6, NOW()::timestamp ),
('SPDV đã mua', 1019, 1, 1, NOW()::timestamp ),
('Gói dịch vụ đã mua', 1020, 1, 1, NOW()::timestamp ),
('Mã thuê bao', 1021, 1, 1, NOW()::timestamp ),
('Chu kỳ thanh toán hiện tại của thuê bao', 1022, 1, 33, NOW()::timestamp ),
('Số chu kỳ của gói cước đã mua', 1023, 1, 3, NOW()::timestamp ),
('Phương thức thanh toán', 1024, 1, 27, NOW()::timestamp ),
('Trạng thái thanh toán hóa đơn mới nhất', 1025, 1, 42, NOW()::timestamp ),
('Mã nhân viên giới thiệu', 1026, 1, 1, NOW()::timestamp ),
('Ngày cập nhật thuê bao', 1027, 1, 6, NOW()::timestamp ),
('Ngày hết hạn thuê bao', 1028, 1, 6, NOW()::timestamp ),
('Ngày gia hạn thuê bao', 1029, 1, 6, NOW()::timestamp ),
('Loại addon (một lần hoặc theo chu kỳ)', 1030, 1, 40, NOW()::timestamp ),
('Số tiền addon', 1031, 1, 10, NOW()::timestamp ),
('Phí thiết lập', 1032, 1, 10, NOW()::timestamp ),
('Thuê bao mua số addon', 1033, 1, 3, NOW()::timestamp ),
('Thuê bao mua số lượng đơn vị (unit)', 1034, 1, 3, NOW()::timestamp ),
('Thuê bao hủy', 1035, 1, 1, NOW()::timestamp ),
('Thuê bao hết hạn', 1036, 1, 1, NOW()::timestamp ),
('Thuê bao gia hạn', 1037, 1, 1, NOW()::timestamp ),
('Thuê bao quay lại', 1038, 1, 1, NOW()::timestamp ),
('Ngày quay lại', 1039, 1, 6, NOW()::timestamp ),
('Kế hoạch định giá', 1040, 1, 44, NOW()::timestamp ),
('Thời điểm thanh toán', 1041, 1, 6, NOW()::timestamp ),
('Email', 1042, 1, 25, NOW()::timestamp ),
('Tên doanh nghiệp', 1043, 1, 25, NOW()::timestamp ),
('Địa chỉ', 1044, 1, 25, NOW()::timestamp ),
('Số điện thoại', 1045, 1, 25, NOW()::timestamp ),
('Mã số BHXH', 1046, 1, 25, NOW()::timestamp ),
('Website', 1047, 1, 25, NOW()::timestamp ),
('Mã số thuế', 1048, 1, 25, NOW()::timestamp ),
('Quy mô doanh nghiệp', 1049, 1, 17, NOW()::timestamp ),
('Lĩnh vực doanh nghiệp', 1050, 1, 17, NOW()::timestamp ),
('Người đại diện', 1051, 1, 25, NOW()::timestamp ),
('Địa chi lắp đặt', 1052, 1, 25, NOW()::timestamp ),
('Địa chỉ đăng ký kinh doanh', 1053, 1, 25, NOW()::timestamp ),
('Số lượng SPDV đã mua', 2001, 2, 3, NOW()::timestamp ),
('Danh mục sản phẩm đã mua', 2002, 2, 1, NOW()::timestamp ),
('Loại SPDV đã mua', 2003, 2, 19, NOW()::timestamp ),
('Tên SPDV đã mua', 2004, 2, 1, NOW()::timestamp ),
('Tên Gói SPDV đã mua', 2005, 2, 1, NOW()::timestamp ),
('ID sản phẩm đã mua', 2006, 2, 1, NOW()::timestamp ),
('Chu kỳ thanh toán của gói cước đã mua', 2007, 2, 33, NOW()::timestamp ),
('Kế hoạch định giá', 2008, 2, 44, NOW()::timestamp ),
('Số lượng đơn vị đã mua', 2009, 2, 3, NOW()::timestamp ),
('Thời gian dùng thử', 2010, 2, 46, NOW()::timestamp ),
('Phí thiết lập', 2011, 2, 10, NOW()::timestamp ),
('Thời điểm thanh toán', 2012, 2, 6, NOW()::timestamp ),
('Đơn vi phát triển', 2013, 2, 1, NOW()::timestamp ),
('Giá gói dịch vụ', 2014, 2, 10, NOW()::timestamp ),
('Sản phẩm có điểm đánh giá', 2015, 2, 10, NOW()::timestamp ),
('Thời gian', 4001, 4, 6, NOW()::timestamp),
('Chiết khấu', 5001, 5, 14, NOW()::timestamp ),
('Hình thức chiết khấu', 5002, 5, 15, NOW()::timestamp ),
('Áp dụng cho lần mua hàng', 5003, 5, 16, NOW()::timestamp ),
('Danh mục sản phẩm', 5004, 5, 49, NOW()::timestamp ),
('Loại dịch vụ', 5005, 5, 19, NOW()::timestamp ),
('SPDV áp dụng', 5006, 5, 29, NOW()::timestamp ),
('Gói dịch vụ áp dụng', 5007, 5, 31, NOW()::timestamp ),
('Chu kỳ thanh toán', 5008, 5, 59, NOW()::timestamp ),
('Áp dụng CDQC/CTKM khác', 5009, 5, 21, NOW()::timestamp ),
('Thời gian áp dụng', 5010, 5, 22, NOW()::timestamp ),
('Số lần 1 khách hàng được áp dụng CDQC', 5011, 5, 35, NOW()::timestamp ),
('Trong thời gian', 5012, 5, 6, NOW()::timestamp ),
('Số tiền chiết khấu tối đa', 5013, 5, 36, NOW()::timestamp ),
('Mua sản phẩm thứ', 5014, 5, 37, NOW()::timestamp ),
('Số lượng đơn vị áp dụng', 5015, 5, 38, NOW()::timestamp ),
('Tặng sản phẩm', 5016, 5, 24, NOW()::timestamp ),
('Thời gian tặng', 5017, 5, 51, NOW()::timestamp ),
('Số lượng tặng', 5018, 5, 52, NOW()::timestamp ),
('Số đơn vị tặng', 5019, 5, 53, NOW()::timestamp ),
('Thời gian sử dụng SPDV tặng', 5020, 5, 54, NOW()::timestamp ),
('SPDV áp dụng (Tặng sản phẩm khác loại)', 5021, 5, 56, NOW()::timestamp ),
('Gói dịch vụ áp dụng (Tặng sản phẩm khác loại)', 5022, 5, 57, NOW()::timestamp ),
('Chu kỳ thanh toán (Tặng sản phẩm khác loại)', 5023, 5, 58, NOW()::timestamp );
	

DELETE FROM vnpt_dev.mc_operand_operator_mapping;
INSERT INTO vnpt_dev.mc_operand_operator_mapping(operand_data_type_code, operator_code, value_data_type_code, created_at)
VALUES
( 1 , 1 , 2 , NOW()::timestamp ),
( 1 , 2 , 2 , NOW()::timestamp ),
( 1 , 3 , 1 , NOW()::timestamp ),
( 1 , 4 , 1 , NOW()::timestamp ),
( 1 , 5 , 1 , NOW()::timestamp ),
( 1 , 6 , 1 , NOW()::timestamp ),
( 1 , 7 , 1 , NOW()::timestamp ),
( 1 , 8 , 1 , NOW()::timestamp ),
( 1 , 9 , 23 , NOW()::timestamp ),
( 1 , 10 , 23 , NOW()::timestamp ),
( 6 , 19 , 23 , NOW()::timestamp ),
( 6 , 1 , 6 , NOW()::timestamp ),
( 6 , 15 , 7 , NOW()::timestamp ),
( 6 , 16 , 7 , NOW()::timestamp ),
( 6 , 17 , 8 , NOW()::timestamp ),
( 6 , 18 , 8 , NOW()::timestamp ),
( 6 , 20 , 9 , NOW()::timestamp ),
( 3 , 1 , 3 , NOW()::timestamp ),
( 3 , 2 , 3 , NOW()::timestamp ),
( 3 , 11 , 3 , NOW()::timestamp ),
( 3 , 12 , 3 , NOW()::timestamp ),
( 3 , 13 , 3 , NOW()::timestamp ),
( 3 , 14 , 3 , NOW()::timestamp ),
( 3 , 15 , 4 , NOW()::timestamp ),
( 3 , 16 , 4 , NOW()::timestamp ),
( 3 , 9 , 23 , NOW()::timestamp ),
( 3 , 10 , 23 , NOW()::timestamp ),
( 3 , 17 , 5 , NOW()::timestamp ),
( 3 , 18 , 5 , NOW()::timestamp ),
( 10 , 1 , 10 , NOW()::timestamp ),
( 10 , 2 , 10 , NOW()::timestamp ),
( 10 , 11 , 10 , NOW()::timestamp ),
( 10 , 12 , 10 , NOW()::timestamp ),
( 10 , 13 , 10 , NOW()::timestamp ),
( 10 , 14 , 10 , NOW()::timestamp ),
( 10 , 15 , 11 , NOW()::timestamp ),
( 10 , 16 , 11 , NOW()::timestamp ),
( 10 , 9 , 23 , NOW()::timestamp ),
( 10 , 10 , 23 , NOW()::timestamp ),
( 10 , 17 , 12 , NOW()::timestamp ),
( 10 , 18 , 12 , NOW()::timestamp ),
( 13 , 1 , 39 , NOW()::timestamp ),
( 14 , 1 , 14 , NOW()::timestamp ),
( 15 , 1 , 15 , NOW()::timestamp ),
( 16 , 1 , 16 , NOW()::timestamp ),
( 17 , 1 , 18 , NOW()::timestamp ),
( 17 , 2 , 18 , NOW()::timestamp ),
( 19 , 1 , 20 , NOW()::timestamp ),
( 21 , 21 , 23 , NOW()::timestamp ),
( 21 , 22 , 55 , NOW()::timestamp ),
( 22 , 1 , 22 , NOW()::timestamp ),
( 25 , 1 , 26 , NOW()::timestamp ),
( 25 , 2 , 26 , NOW()::timestamp ),
( 25 , 3 , 25 , NOW()::timestamp ),
( 25 , 4 , 25 , NOW()::timestamp ),
( 25 , 5 , 25 , NOW()::timestamp ),
( 25 , 6 , 25 , NOW()::timestamp ),
( 25 , 7 , 25 , NOW()::timestamp ),
( 25 , 8 , 25 , NOW()::timestamp ),
( 27 , 1 , 28 , NOW()::timestamp ),
( 27 , 2 , 28 , NOW()::timestamp ),
( 29 , 1 , 30 , NOW()::timestamp ),
( 31 , 1 , 32 , NOW()::timestamp ),
( 33 , 1 , 34 , NOW()::timestamp ),
( 33 , 2 , 34 , NOW()::timestamp ),
( 33 , 9 , 23 , NOW()::timestamp ),
( 33 , 10 , 23 , NOW()::timestamp ),
( 35 , 1 , 35 , NOW()::timestamp ),
( 36 , 1 , 36 , NOW()::timestamp ),
( 37 , 1 , 37 , NOW()::timestamp ),
( 37 , 14 , 37 , NOW()::timestamp ),
( 38 , 1 , 38 , NOW()::timestamp ),
( 40 , 1 , 41 , NOW()::timestamp ),
( 40 , 2 , 41 , NOW()::timestamp ),
( 42 , 1 , 43 , NOW()::timestamp ),
( 42 , 2 , 43 , NOW()::timestamp ),
( 44 , 1 , 45 , NOW()::timestamp ),
( 44 , 2 , 45 , NOW()::timestamp ),
( 46 , 1 , 46 , NOW()::timestamp ),
( 46 , 2 , 46 , NOW()::timestamp ),
( 46 , 11 , 46 , NOW()::timestamp ),
( 46 , 12 , 46 , NOW()::timestamp ),
( 46 , 13 , 46 , NOW()::timestamp ),
( 46 , 14 , 46 , NOW()::timestamp ),
( 46 , 15 , 48 , NOW()::timestamp ),
( 46 , 16 , 48 , NOW()::timestamp ),
( 46 , 9 , 23 , NOW()::timestamp ),
( 46 , 10 , 23 , NOW()::timestamp ),
( 46 , 17 , 47 , NOW()::timestamp ),
( 46 , 18 , 47 , NOW()::timestamp ),
( 49 , 1 , 50 , NOW()::timestamp ),
( 24 , 1 , 24 , NOW()::timestamp ),
( 51 , 1 , 51 , NOW()::timestamp ),
( 52 , 1 , 52 , NOW()::timestamp ),
( 53 , 23 , 23 , NOW()::timestamp ),
( 53 , 24 , 53 , NOW()::timestamp ),
( 54 , 1 , 54 , NOW()::timestamp ),
( 56 , 1 , 56 , NOW()::timestamp ),
( 57 , 1 , 57 , NOW()::timestamp ),
( 58 , 1 , 58 , NOW()::timestamp ),
( 59 , 1 , 60 , NOW()::timestamp ),
( 61 , 1 , 62 , NOW()::timestamp );

-- ----------------------------
-- Table structure for subscription_addon_mc
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."subscription_addon_mc";
CREATE TABLE "vnpt_dev"."subscription_addon_mc" (
  "id" bigserial NOT NULL,
  "subscription_id" int8,
  "addon_id" int8,
  "mc_id" int8,
  "acitivity_idx" int8,
  "rule_idx" int8,
  "item_set_idx" int8,
  "item_idx" int8
);