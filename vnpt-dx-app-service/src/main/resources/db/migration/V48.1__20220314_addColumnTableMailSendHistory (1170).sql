ALTER TABLE "vnpt_dev"."mail_send_history"
    ADD COLUMN "mc_id" int8,
    ADD COLUMN "activity_id" int8,
    ADD COLUMN "action_id" int8,
    ADD COLUMN "object_type" int2,
    ADD COLUMN "object_id" int8,
    ADD COLUMN "scanned" int2,
    ADD COLUMN "sender_info" varchar(200);

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."mc_id" IS 'id của marketing campain';

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."activity_id" IS 'id của bảng mc_ativity';

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."action_id" IS 'id của bảng mc_action_email_auto_sms';

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."object_type" IS 'Loại đối tượng xem/click quảng cáo (0: <PERSON><PERSON><PERSON> nghi<PERSON>, 1: <PERSON><PERSON><PERSON>)';

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."object_id" IS 'ID của đối tượng xem/click quảng cáo';

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."scanned" IS 'đánh dấu đã quét để cập nhật vào bảng lịch của của MC';

COMMENT
ON COLUMN "vnpt_dev"."mail_send_history"."sender_info" IS 'email người gửi';

ALTER TABLE "vnpt_dev"."mail_send_history" RENAME COLUMN "activity_id" TO "activity_idx";
ALTER TABLE "vnpt_dev"."mail_send_history" RENAME COLUMN "action_id" TO "action_idx";

COMMENT ON COLUMN "vnpt_dev"."mail_send_history"."activity_idx" IS 'index của bảng mc_ativity';
COMMENT ON COLUMN "vnpt_dev"."mail_send_history"."action_idx" IS 'index của bảng mc_action_email_auto_sms';