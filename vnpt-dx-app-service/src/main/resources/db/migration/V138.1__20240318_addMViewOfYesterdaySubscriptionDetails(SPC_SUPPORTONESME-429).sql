drop view if exists vnpt_dev.view_spc_supportonesme_429;

create view vnpt_dev.view_spc_supportonesme_429 as
select
    'Tỉnh thành' as provinceName, 
    '<PERSON><PERSON><PERSON> thuê bao' as subType, 
    'Tên khách hàng' as customerName, 
    '<PERSON><PERSON><PERSON> khách hàng' as customerType, 
    '<PERSON><PERSON> số thuế' as taxNo, 
    '<PERSON><PERSON> chứng từ' as identityNo, 
    'Địa chỉ' as address, 
    'SĐT' as phoneNo, 
    'Email' as email,   
    'Mã nhân viên giới thiệu' as employeeCode, 
    'Dịch vụ' as serviceName, 
    'Nhà cung cấp' as providerName, 
    'Trạng thái dịch vụ' as subscriptionStatus, 
    'Thời gian hoàn thành đơn hàng' as subscriptionCompletionTime, 
    '<PERSON><PERSON>i dịch vụ' as pricingName, 
    'ON/OS' as onOs, 
    '<PERSON><PERSON><PERSON> đăng ký/ <PERSON>ày gia hạn' as registrationDate, 
    '<PERSON><PERSON><PERSON> bắt đầu sử dụng' as startUsingDate, 
    '<PERSON><PERSON><PERSON>ế<PERSON> thú<PERSON> (oneSME)' as endDate, 
    '<PERSON><PERSON><PERSON> đến hạn gia hạn (DHSXKD)' as renewalDate, 
    'Tr<PERSON>ng thái gia hạn' renewalStatus, 
    'Ngày thanh toán' as paymentDate, 
    'Ngày hủy' as cancelledDate, 
    'Số chu kỳ' as numCycle, 
    'Chu kỳ thanh toán' as paymentCycle, 
    'Giá cước dịch vụ/gói cước' as price, 
    'Số tiền khuyến mại' as promotionAmount, 
    'Số tiền đã thanh toán (chưa thuế)' as preTaxAmount, 
    'Số tiền nộp thuế' as taxAmount, 
    'Số tiền thanh toán (đã có thuế)' as afterTaxAmount, 
    'Mã giao dịch với PAY' as PAYTransactionCode, 
    'Mã giao dịch với DHSX' as DHSXTransactionCode, 
    'Ngày xuất hóa đơn điện tử' as eInvoiceCreationDate, 
    'Số hóa đơn điện tử' as eInvoiceNo, 
    'Đơn hàng tạo bởi' as createdBy, 
    'Mã đơn hàng' as subscriptionCode, 
    'Mã lắp đặt' as setupCode, 
    'Mã hóa đơn' as billingCode,
    'Trạng thái thanh toán' as paymentStatus, 
    'Nguồn tạo' as creationSource, 
    'Tên đại lý affiliate' as affiliateAgentName, 
    'Mã đại lý cấp 1' as affiliate1stAgentName, 
    'Mã thành viên affiliate giới thiệu' as affiliateMemberCode, 
    'Mã đồng bộ' as migrationCode, 
    'Thời gian đồng bộ' as migrationTime
union all
select 
    provinceName as provinceName, -- Tỉnh thành 
    state as subType, -- Loại thuê bao 
    smeName as customerName, -- Tên khách hàng 
    customerType as customerType, -- Loại khách hàng 
    taxtNo as taxNo, -- Mã số thuế 
    identityNo as identityNo, -- Số chứng từ 
    address as address, -- Địa chỉ 
    phoneNo as phoneNo, -- SĐT 
    email ,  -- Email    
    employeeCode as email, -- Mã nhân viên giới thiệu 
    serviceName as employeeCode, -- Dịch vụ 
    provider as serviceName, -- Nhà cung cấp 
    status as providerName, -- Trạng thái dịch vụ 
    null as subscriptionStatus, -- Thời gian hoàn thành đơn hàng 
    pricingName as subscriptionCompletionTime, -- Gói dịch vụ 
    serviceOwnerType as pricingName, -- ON/OS 
    to_char(registrationDate , 'dd/MM/yyyy HH24:MI:SS') as onOs, -- Ngày đăng ký/ Ngày gia hạn 
    to_char(startAt , 'dd/MM/yyyy HH24:MI:SS') as registrationDate, -- Ngày bắt đầu sử dụng 
    to_char(endCurrentCycle , 'dd/MM/yyyy HH24:MI:SS') as startUsingDate, -- Ngày kết thúc (oneSME) 
    null as endDate, -- Ngày đến hạn gia hạn (DHSXKD) 
    null as renewalDate, -- Trạng thái gia hạn 
    to_char(paymentDate , 'dd/MM/yyyy HH24:MI:SS') as paymentDate, -- Ngày thanh toán 
    to_char(cancelledTime , 'dd/MM/yyyy HH24:MI:SS') as cancelledDate, -- Ngày hủy 
    case
        when numberOfCycle = -1 then 'Không giới hạn'
        else cast(numberOfCycle as text)
    end as numCycle, -- Số chu kỳ 
    case
        when planCycleType = 'DAILY' then concat(planPaymentCycle , ' ngày') 
        when planCycleType = 'WEEKLY' then concat(planPaymentCycle , ' tuần') 
        when planCycleType = 'MONTHLY' then concat(planPaymentCycle , ' tháng') 
        when planCycleType = 'YEARLY' then concat(planPaymentCycle , ' năm') 
        when multiCycleType = 'DAILY' then concat(multiPaymentCycle , ' ngày') 
        when multiCycleType = 'WEEKLY' then concat(multiPaymentCycle , ' tuần') 
        when multiCycleType = 'MONTHLY' then concat(multiPaymentCycle , ' tháng') 
        when multiCycleType = 'YEARLY' then concat(multiPaymentCycle , ' năm') 
    end as paymentCycle, -- Chu kỳ thanh toán 
    cast(unitAmount as text) as price, -- Giá cước dịch vụ/gói cước 
    cast(promotionAmount as text) as promotionAmount, -- Số tiền khuyến mại 
    cast(preAmountTax as text) as preTaxAmount, -- Số tiền đã thanh toán (chưa thuế) 
    cast(amountTax as text) as taxAmount, -- Số tiền nộp thuế 
    cast(afterAmountTax as text) as afterTaxAmount, -- Số tiền thanh toán (đã có thuế) 
    payTransactionCode as PAYTransactionCode, -- Mã giao dịch với PAY 
    dhsxkdCode as DHSXTransactionCode, -- Mã giao dịch với DHSX 
    to_char(createdExportInvoice , 'dd/MM/yyyy HH24:MI:SS') as eInvoiceCreationDate, -- Ngày xuất hóa đơn điện tử 
    codeInvoice as eInvoiceNo, -- Số hóa đơn điện tử 
    creator as createdBy, -- Đơn hàng tạo bởi 
    subCode as subscriptionCode, -- Mã đơn hàng 
    setupCode as setupCode, -- Mã lắp đặt 
    billCode as billingCode, -- Mã hóa đơn ,
    billStatus as paymentStatus, -- Trạng thái thanh toán 
    createdSource as creationSource, -- Nguồn tạo 
    affagencyname as affiliateAgentName, -- Tên đại lý affiliate 
    affagencycode as affiliate1stAgentName, -- Mã đại lý cấp 1 
    cast(affagencyuserid as text) as affiliateMemberCode, -- Mã thành viên affiliate giới thiệu 
    migrateCode as migrationCode, -- Mã đồng bộ 
    to_char(migrateTime , 'dd/MM/yyyy HH24:MI:SS') as migrationTime -- Thời gian đồng bộ 
from vnpt_dev.get_report_subscriptions_details_new(0, '', '', 'ALL',-1,'ALL', -1,'','','',
    to_char(date_trunc('day', CURRENT_TIMESTAMP) - interval '1' day, 'yyyy-MM-dd HH24:MI:ss'), 
    to_char(date_trunc('day', CURRENT_TIMESTAMP) - interval '1' second, 'yyyy-MM-dd HH24:MI:ss'), 
    -2,-1,'-1',-1, '-1','-1','ALL','-1','-1','ALL',-1,'-1', '', '', '', '-1', '-1', '-1');


DELETE FROM vnpt_dev.schedules WHERE bean_name = 'batch-subscription-history' AND method_name = 'createDailySubscriptionReport';
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('batch-subscription-history', 'createDailySubscriptionReport', NULL, '30 5 3 ? * *', 'createDailySubscriptionReport', 1, 'batch', NULL, 'batch', NULL);
