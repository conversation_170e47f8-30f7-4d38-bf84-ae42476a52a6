DROP FUNCTION IF EXISTS "vnpt_dev"."get_all_version_pricing";
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_all_version_pricing"("i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("id" int8, "pricing_code" varchar, "pricing_name" varchar, "description" varchar, "payment_cycle" int2, "number_of_cycles" int2, "cycle_type" int2, "pricing_plan" int2, "service_id" int8, "unit_id" int8, "currency_id" int8, "amount" float8, "setup_fee" float8, "free_quantity" int4, "estimate_quantity" int4, "created_at" timestamp, "created_by" int8, "modified_at" timestamp, "modified_by" int8, "status" int2, "deleted_flag" int2, "price" float8, "list_feature_id" varchar, "pricing_order" int2, "recommended_status" int2, "update_reason" varchar, "approve" int2, "pricing_draft_id" int8, "approve_time" timestamp, "sme_pricing_id" int8, "trial_type" int2, "number_of_trial" int2, "pricing_type" int2, "has_change_price" int2, "has_change_quantity" int2, "has_refund" int2, "cancel_date" int2, "active_date" int4, "duration_type" int2, "department_id" int8, "province_id" int8, "update_subscription_date" int2, "change_pricing_date" int2, "is_change_now" int2, "is_update_now" int2) AS $BODY$

DECLARE

BEGIN

	-- Routine body goes here...

RETURN QUERY

SELECT

    p.id,

    p.pricing_code,

    p.pricing_name,

    p.description,

    p.payment_cycle,

    p.number_of_cycles,

    p.cycle_type,

    p.pricing_plan,

    p.service_id,

    p.unit_id,

    p.currency_id,

    p.amount,

    p.setup_fee,

    p.free_quantity,

    p.estimate_quantity,

    p.created_at,

    p.created_by,

    p.modified_at,

    p.modified_by,

    p.status,

    p.deleted_flag,

    p.price,

    p.list_feature_id,

    p.pricing_order,

    p.recommended_status,

    p.update_reason,

    p.approve,

    p.pricing_draft_id,

    p.approve_time,

    p.sme_pricing_id,

    p.trial_type,

    p.number_of_trial,

    p.pricing_type,

    p.has_change_price,

    p.has_change_quantity,

    p.has_refund,

    p.cancel_date,

    p.active_date,

    p.duration_type,

    p.department_id,

    p.province_id,

    p.update_subscription_date,

    p.change_pricing_date,

    p.is_change_now,

    p.is_update_now

FROM vnpt_dev.pricing p
         LEFT JOIN vnpt_dev.services se on p.service_id=se.id

WHERE (p.deleted_flag = 1) AND (se.created_at::date >= i_starttime::date) AND (se.created_at::date <= i_endtime::date);

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."count_sub_per_pricing_by_status";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_pricing_by_status"("i_status" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("pricing_id" int8, "service_id" int8, "num_sub" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT sub.pricing_id AS pricing_id , se.id as service_id, COUNT (*) AS num_sub
FROM vnpt_dev.subscriptions AS sub
         LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
         LEFT JOIN vnpt_dev.services se on p.service_id=se.id
WHERE   sub.confirm_status = 1 AND
        sub.status = i_status AND
    (se.created_at::date >= i_starttime::date AND se.created_at::date <= i_endtime::date)
GROUP BY sub.pricing_id, se.id;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."count_sub_per_pricing_by_reg_type";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_pricing_by_reg_type"("register_type" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("pricing_id" int8, "service_id" int8, "num_sub" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT sub.pricing_id AS pricing_id, se.id as service_id, COUNT (*) AS num_sub
FROM vnpt_dev.subscriptions AS sub
         LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
         LEFT JOIN vnpt_dev.services se on p.service_id=se.id
WHERE sub.reg_type = register_type AND sub.confirm_status = 1 AND
    (se.created_at::date >= i_starttime::date AND se.created_at::date <= i_endtime::date)
GROUP BY sub.pricing_id, se.id;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."count_sub_per_pricing_by_his";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_pricing_by_his"("his_type" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("pricing_id" int8, "service_id" int8, "num_sub" int8) AS $BODY$

DECLARE
renew_query varchar = E'SELECT sub.pricing_id AS PricingId, se.id as serviceId , COUNT (*)::BIGINT AS NumberRenew
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT DISTINCT sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				LEFT JOIN vnpt_dev.billings AS b ON b.subscriptions_id = sh.subscription_id
				WHERE sh.content ILIKE \'Gia hạn thuê bao\' AND b.status = 2
			) AS renew_sub ON renew_sub.SubId = sub.id
			LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
		  LEFT JOIN vnpt_dev.services se on p.service_id=se.id
			WHERE se.created_at::date >= \'%s\'::date AND se.created_at::date <= \'%s\'::date
			GROUP BY sub.pricing_id, se.id';

	cancel_query varchar = E'SELECT sub.pricing_id AS PricingId, se.id as serviceId , COUNT (*)::BIGINT AS NumberCancel
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT DISTINCT sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				WHERE sh.content ILIKE \'%%hủy thuê bao%%\'
			) AS cancel_sub ON cancel_sub.SubId = sub.id
			LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
		  LEFT JOIN vnpt_dev.services se on p.service_id=se.id
			WHERE se.created_at::date >= \'%s\'::date AND se.created_at::date <= \'%s\'::date
			GROUP BY sub.pricing_id, se.id';

	react_query varchar = E'SELECT sub.pricing_id AS PricingId, se.id as serviceId , COUNT (*)::BIGINT AS NumberReactivate
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT DISTINCT sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				WHERE sh.content ILIKE \'%%kích hoạt lại%%\'
			) AS react_sub ON react_sub.SubId = sub.id
			LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
		  LEFT JOIN vnpt_dev.services se on p.service_id=se.id
			WHERE se.created_at::date >= \'%s\'::date AND se.created_at::date <= \'%s\'::date
			GROUP BY sub.pricing_id, se.id';

	change_query varchar = E'SELECT sub.pricing_id AS PricingId, se.id as serviceId , COUNT (*)::BIGINT AS NumberChange
			FROM vnpt_dev.subscriptions AS sub
			INNER JOIN (
				SELECT DISTINCT sh.subscription_id AS SubId
				FROM vnpt_dev.subscription_history AS sh
				WHERE sh.content ILIKE \'Đổi gói dịch vụ%%\'
			) AS change_sub ON change_sub.SubId = sub.id
			LEFT JOIN vnpt_dev.pricing p on p.id=sub.pricing_id AND sub.pricing_id IS NOT NULL
		  LEFT JOIN vnpt_dev.services se on p.service_id=se.id
			WHERE se.created_at::date >= \'%s\'::date AND se.created_at::date <= \'%s\'::date
			GROUP BY sub.pricing_id, se.id';

	full_query varchar;

BEGIN
	-- Routine body goes here...
		if his_type = 0 then full_query := FORMAT(renew_query, i_startTime, i_endTime);
		elsif his_type = 1 then full_query := FORMAT(cancel_query, i_startTime, i_endTime);
		elsif his_type = 2 then full_query := FORMAT(react_query, i_startTime, i_endTime);
		elsif his_type = 3 then full_query := FORMAT(change_query, i_startTime, i_endTime);
else full_query := '';
end if;

		raise notice 'Value: %', full_query;

RETURN query execute full_query;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."count_sub_per_com_plan_by_reg_type";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_sub_per_com_plan_by_reg_type"("register_type" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("combo_plan_id" int8, "combo_id" int8, "num_sub" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT sub.combo_plan_id AS ComboPlanId, c.id as comboId , COUNT (*) AS NumTrial
FROM vnpt_dev.subscriptions AS sub
         LEFT JOIN vnpt_dev.combo_plan cp ON sub.combo_plan_id=cp.id AND sub.combo_plan_id is not null
         LEFT JOIN vnpt_dev.combo c ON cp.combo_id=c.id
WHERE  sub.confirm_status = 1 AND
        sub.reg_type = register_type AND
    (c.created_at::date >= i_starttime::date AND c.created_at::date <= i_endtime::date)
GROUP BY sub.combo_plan_id, c.id;

END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

DROP FUNCTION IF EXISTS "vnpt_dev"."count_user_per_combo_by_sub_status";
CREATE OR REPLACE FUNCTION "vnpt_dev"."count_user_per_combo_by_sub_status"("i_status" int2, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("combo_id" int8, "num_user" int8) AS $BODY$BEGIN
	-- Routine body goes here...

	RETURN QUERY
SELECT combo_user.ComboId, COUNT (*) NumUser

FROM (

         SELECT DISTINCT cp.combo_id AS ComboId, user_combo_plan.UserId AS UserId

         FROM (

                  SELECT DISTINCT sub.combo_plan_id AS ComboPlanId, sub.user_id AS UserId

                  FROM vnpt_dev.subscriptions AS sub

                  WHERE sub.status = i_status AND sub.combo_plan_id IS NOT NULL

              ) AS user_combo_plan

                  LEFT JOIN vnpt_dev.combo_plan AS cp ON cp.id = user_combo_plan.ComboPlanId
                  LEFT JOIN vnpt_dev.combo c ON cp.combo_id=c.id

         WHERE (c.created_at::date >= i_starttime::date AND c.created_at::date <= i_endtime::date)

     ) AS combo_user

GROUP BY( combo_user.ComboId);


END$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;