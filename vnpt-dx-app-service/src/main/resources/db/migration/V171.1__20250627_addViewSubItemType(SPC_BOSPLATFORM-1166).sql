drop view if exists "vnpt_dev"."view_subscription_order_item_type";
create or replace view "vnpt_dev"."view_subscription_order_item_type" as (
 select
     sub.id as sub_id,
     COALESCE(sub.cart_code, concat('ID', to_char(sub.id, 'FM09999999'))) as sub_code,
     case
         when services.classification in (0, 1, 2) and services.on_os_type = 0 then 'ON_SAAS'
         when services.classification in (0, 1, 2) and services.on_os_type = 1 then 'OS_SAAS'
         when services.classification = 3 and coalesce(prodOrder.has_installation, false) is false then 'NOT_INSTALL_PHYSICAL_PRODUCT'
         when services.classification = 3 and coalesce(prodOrder.has_installation, false) is true then 'INSTALL_PHYSICAL_PRODUCT'
     end as item_type
 from vnpt_dev.subscriptions as sub
      left join vnpt_dev.services on services.id = sub.service_id
      left join vnpt_dev.pricing on sub.pricing_id = pricing.id
      left join vnpt_dev.variant on sub.variant_id = variant.id
      left join vnpt_dev.subscription_metadata as metadata on metadata.subscription_id = sub.id
      left join vnpt_dev.product_orders as prodOrder on prodOrder.id = metadata.product_order_id
 where sub.combo_plan_id is null
 union
 select
     sub.id as subId,
     COALESCE(sub.cart_code, concat('ID', to_char(sub.id, 'FM09999999'))) as subCode,
     'COMBO' as itemType
 from vnpt_dev.subscriptions as sub
      left join vnpt_dev.combo_plan as cbPlan on sub.combo_plan_id = cbPlan.id
      left join vnpt_dev.combo on cbPlan.combo_id = combo.id
      left join vnpt_dev.subscription_metadata as metadata on metadata.subscription_id = sub.id
      left join vnpt_dev.product_orders as prodOrder on prodOrder.id = metadata.product_order_id
 where sub.combo_plan_id is not null
);