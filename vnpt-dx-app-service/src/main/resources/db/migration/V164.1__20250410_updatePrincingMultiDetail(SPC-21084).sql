-- l<PERSON>y thông tin chi tiết bậc giá của gói cước cũ
WITH pricing_plan_detail_update AS (
    SELECT
        pricing_multi_plan.id as pricing_multi_plan_id,
        unit_limited.*
    FROM vnpt_dev.pricing_multi_plan
    -- L<PERSON>y thông tin chi tiết bậc giá của gói cước cũ
    JOIN vnpt_dev.unit_limited ON unit_limited.pricing_id = pricing_multi_plan.pricing_id
    WHERE pricing_multi_plan.id NOT IN (
        -- Lấy danh sách id các gói cước đã có thông tin chi tiết bậc giá
        SELECT DISTINCT (pricing_plan_detail.pricing_multi_plan_id) FROM vnpt_dev.pricing_plan_detail
    )
      AND pricing_multi_plan.pricing_plan NOT IN (0,1)
      AND unit_limited.subscription_setup_fee_id is null
)

-- update lại thông tin chi tiết bậc giá của gó<PERSON> cước cũ
INSERT INTO vnpt_dev.pricing_plan_detail (
    pricing_multi_plan_id,
    price,
    unit_from,
    unit_to
)
SELECT
    pricing_multi_plan_id,
    price,
    unit_from,
    unit_to
FROM pricing_plan_detail_update