-- 1. Permission
-- <PERSON><PERSON><PERSON> bảng quản lý các permission
drop table if exists vnpt_dev.wp_permission;
create table vnpt_dev.wp_permission(
       id bigserial primary key not null,
       code int4 not null,
       display_name varchar(500) not null,
       api_key varchar(500) not null,
       name varchar(500) not null,
       parent_code int4 not null,
       constraint unique_wppermission_code unique (code)
);

comment on column vnpt_dev.wp_permission.code is 'Mã quyền';
comment on column vnpt_dev.wp_permission.name is 'Tên quyền để Back-end phân quyền';
comment on column vnpt_dev.wp_permission.display_name is 'Tên quyền để Front-end hiển thị';
comment on column vnpt_dev.wp_permission.api_key is 'Mã key của từng ứng dụng';
comment on constraint unique_wppermission_code on vnpt_dev.wp_permission is 'Mã quyền trong workplace là duy nhất';
comment on table vnpt_dev.wp_permission is '<PERSON><PERSON>ng quản lý các quyền của workplace';

-- <PERSON><PERSON><PERSON><PERSON> các bảng ghi permission mặc định
-- Phòng ban
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(100, 'DEPARTMENT_MGMT', 'Phòng ban', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(101, 'DEPARTMENT_GET_LIST', 'Xem danh sách phòng ban', 100, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(102, 'DEPARTMENT_CREATE', 'Tạo phòng ban', 100, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(103, 'DEPARTMENT_GET_DETAIL', 'Xem chi tiết phòng ban', 100, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(104, 'DEPARTMENT_UPDATE', 'Chỉnh sửa phòng ban', 100, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(105, 'DEPARTMENT_DELETE', 'Xoá phòng ban', 100, 'WORKPLACE');

-- Nhân viên
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(200, 'EMPLOYEE_MGMT', 'Nhân viên', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(201, 'EMPLOYEE_GET_LIST', 'Xem danh sách nhân viên', 200, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(202, 'EMPLOYEE_GET_DETAIL', 'Xem chi tiết nhân viên', 200, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(203, 'EMPLOYEE_CREATE', 'Tạo nhân viên', 200, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(204, 'EMPLOYEE_UPDATE', 'Chỉnh sửa nhân viên', 200, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(205, 'EMPLOYEE_DELETE', 'Xoá nhân viên', 200, 'WORKPLACE');

-- Vai trò
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(300, 'ROLE_MGMT', 'Vai trò', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(301, 'ROLE_GET_LIST', 'Xem danh sách vai trò', 300, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(302, 'ROLE_CREATE', 'Tạo vai trò', 300, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(303, 'ROLE_GET_DETAIL', 'Xem chi tiết vai trò', 300, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(304, 'ROLE_UPDATE', 'Chỉnh sửa vai trò', 300, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(305, 'ROLE_DELETE', 'Xóa vai trò', 300, 'WORKPLACE');

-- Quản lý Branding
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(400, 'BRANDING_MGMT', 'Quản lý thương hiệu', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(401, 'BRANDING_GET_DETAIL', 'Xem thông tin thương hiệu', 400, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(402, 'BRANDING_UPDATE', 'Chỉnh sửa thông tin thương hiệu', 400, 'WORKPLACE');

-- Navigation
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(500, 'NAVIGATION_MGMT', 'Navigation', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(501, 'NAVIGATION_UPDATE', 'Chỉnh sửa hiển thị Navigation', 500, 'WORKPLACE');

-- Profile doanh nghiệp
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(700, 'PROFILE_MGMT', 'Hồ sơ doanh nghiệp', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(701, 'PROFILE_GET_ENTERPRISE_DETAIL', 'Xem thông tin doanh nghiệp', 700, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(702, 'PROFILE_UPDATE_ENTERPRISE', 'Chỉnh sửa thông tin doanh nghiệp', 700, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(703, 'PROFILE_GET_ENTERPRISE_ADDRESS_DETAIL', 'Xem sổ địa chỉ doanh nghiệp', 700, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(704, 'PROFILE_UPDATE_ENTERPRISE_ADDRESS', 'Chỉnh sửa sổ địa chỉ doanh nghiệp', 700, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(705, 'PROFILE_DELETE_ENTERPRISE_ADDRESS', 'Xoá sổ địa chỉ doanh nghiệp', 700, 'WORKPLACE');

-- Thiết lập Workplace
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(800, 'WORKPLACE_SETTING', 'Thiết lập Workplace', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(801, 'NOTIFICATION_SETTING', 'Thiết lập thông báo', 800, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(802, 'ADMIN_SETTING', 'Thiết lập quyền quản trị', 800, 'WORKPLACE');

-- Trang chủ
insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(900, 'HOME_PAGE_MGMT', 'Trang chủ', -1, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(901, 'WORKPLACE_HOME_PAGE', 'Xem thông tin trang chủ', 900, 'WORKPLACE');

insert into vnpt_dev.wp_permission(code, name, display_name,  parent_code, api_key)
values(902, 'ENTERPRISE_HOME_PAGE_UPDATE', 'Tùy chỉnh thông tin trang chủ', 900, 'WORKPLACE');

-- 2. Role
-- Tạo bảng quản lý các role
drop table if exists vnpt_dev.wp_role;
create table vnpt_dev.wp_role (
    id bigserial primary key not null,
    code varchar(30) not null,
    name varchar(500) not null,
    user_id int8 not null,
    lst_permission_id _int8,
    lst_employee_id _int8,
    status int2 not null default 0,
    deleted_flag int2,
    created_by int8,
    created_at timestamp,
    modified_by int8,
    modified_at timestamp,
    constraint unique_wprole_code unique (code)
);

comment on column vnpt_dev.wp_role.code is 'Mã vai trò';
comment on column vnpt_dev.wp_role.name is 'Tên vai trò';
comment on column vnpt_dev.wp_role.status is 'Trạng thái của vai trò (0: tắt,1: bật)';
comment on column vnpt_dev.wp_role.user_id is 'Với quyền mặc định của hệ thống(DX Workplace_Admin user_id = -1, DX Workplace_Employee user_id = -2). Còn với các quyền khác thì user_id là id user trên ONESME của chủ doanh nghiệp';
comment on column vnpt_dev.wp_role.lst_permission_id is 'Danh sách ID các permission thuộc vai trò';
comment on column vnpt_dev.wp_role.lst_employee_id is 'Danh sách ID các employee thuộc vai trò';
comment on column vnpt_dev.wp_role.deleted_flag is 'Cờ đánh dấu đã xoá bản ghi (0: Đã xoá, 1: Chưa xoá)';
comment on column vnpt_dev.wp_role.created_at is 'Thời gian tạo vai trò';
comment on column vnpt_dev.wp_role.created_by is 'Id người tạo vai trò';
comment on column vnpt_dev.wp_role.modified_at is 'Thời gian cập nhật cuối cùng';
comment on column vnpt_dev.wp_role.modified_by is 'Id người cập nhật cuối cùng';
comment on constraint unique_wprole_code on vnpt_dev.wp_role is 'Mã vai trò trong workplace là duy nhất';
comment on table vnpt_dev.wp_role is 'Bảng quản lý các vai trò của workplace';

-- Thêm các bảng ghi role mặc định
-- 1. DX Workplace_Admin
insert into vnpt_dev.wp_role(
    code, 
    name, 
    user_id, 
    status,
    lst_permission_id, 
    lst_employee_id,
    deleted_flag,
    created_at
)
values( 
    'DX_WORKPLACE_ADMIN',
    'DX Workplace_Admin',
    -1,
    1,
    array[
        -- Phòng ban
        (select id from vnpt_dev.wp_permission where name = 'DEPARTMENT_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'DEPARTMENT_GET_LIST'),
        (select id from vnpt_dev.wp_permission where name = 'DEPARTMENT_GET_DETAIL'),
        (select id from vnpt_dev.wp_permission where name = 'DEPARTMENT_CREATE'),
        (select id from vnpt_dev.wp_permission where name = 'DEPARTMENT_UPDATE'),
        (select id from vnpt_dev.wp_permission where name = 'DEPARTMENT_DELETE'),
        -- Nhân viên
        (select id from vnpt_dev.wp_permission where name = 'EMPLOYEE_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'EMPLOYEE_GET_LIST'),
        (select id from vnpt_dev.wp_permission where name = 'EMPLOYEE_GET_DETAIL'),
        (select id from vnpt_dev.wp_permission where name = 'EMPLOYEE_CREATE'),
        (select id from vnpt_dev.wp_permission where name = 'EMPLOYEE_UPDATE'),
        (select id from vnpt_dev.wp_permission where name = 'EMPLOYEE_DELETE'),
        -- Vai trò
        (select id from vnpt_dev.wp_permission where name = 'ROLE_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'ROLE_GET_LIST'),
        (select id from vnpt_dev.wp_permission where name = 'ROLE_CREATE'),
        (select id from vnpt_dev.wp_permission where name = 'ROLE_GET_DETAIL'),
        (select id from vnpt_dev.wp_permission where name = 'ROLE_UPDATE'),
        (select id from vnpt_dev.wp_permission where name = 'ROLE_DELETE'),
        -- Quản lý Branding
        (select id from vnpt_dev.wp_permission where name = 'BRANDING_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'BRANDING_GET_DETAIL'),
        (select id from vnpt_dev.wp_permission where name = 'BRANDING_UPDATE'),
        -- Navigation
        (select id from vnpt_dev.wp_permission where name = 'NAVIGATION_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'NAVIGATION_UPDATE'),
        -- Profile doanh nghiệp
        (select id from vnpt_dev.wp_permission where name = 'PROFILE_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'PROFILE_GET_ENTERPRISE_DETAIL'),
        (select id from vnpt_dev.wp_permission where name = 'PROFILE_UPDATE_ENTERPRISE' ),
        (select id from vnpt_dev.wp_permission where name = 'PROFILE_GET_ENTERPRISE_ADDRESS_DETAIL' ),
        (select id from vnpt_dev.wp_permission where name = 'PROFILE_UPDATE_ENTERPRISE_ADDRESS' ),
        (select id from vnpt_dev.wp_permission where name = 'PROFILE_DELETE_ENTERPRISE_ADDRESS' ),
        -- Thiết lập workplace
        (select id from vnpt_dev.wp_permission where name = 'WORKPLACE_SETTING'),
        (select id from vnpt_dev.wp_permission where name = 'NOTIFICATION_SETTING'),
        (select id from vnpt_dev.wp_permission where name = 'ADMIN_SETTING'),
        -- Dashboard doanh nghiệp
        (select id from vnpt_dev.wp_permission where name = 'HOME_PAGE_MGMT'),
        (select id from vnpt_dev.wp_permission where name = 'WORKPLACE_HOME_PAGE'),
        (select id from vnpt_dev.wp_permission where name = 'ENTERPRISE_HOME_PAGE_UPDATE')
    ],
    array[]::_int8,
    1,
    now()
);

-- 2. DX Workplace_Employee
insert into vnpt_dev.wp_role(
    code, 
    name, 
    user_id, 
    status,
    lst_permission_id, 
    lst_employee_id,
    deleted_flag,
    created_at
)
values(
    'DX_WORKPLACE_EMPLOYEE',
    'DX Workplace_Employee',
    -2,
    1,
    array[]::_int8,
    array[]::_int8,
    1,
    now()
);


-- 3. Setting
-- Tạo bảng quản lý các setting
drop table if exists vnpt_dev.wp_setting;
create table vnpt_dev.wp_setting (
    id bigserial primary key not null,
    user_id int8 not null,
    config text,
    modified_at timestamp,
    modified_by int8,
    config_type varchar(100),
    constraint unique_wpsetting_userId unique (user_id, config_type)
);

comment on column vnpt_dev.wp_setting.user_id is 'ID user của chủ doanh nghiệp';
comment on column vnpt_dev.wp_setting.config is 'Cấu hình workplace của doanh nghiệp';
comment on column vnpt_dev.wp_setting.modified_at is 'Thời gian cập nhật cuối cùng';
comment on column vnpt_dev.wp_setting.modified_by is 'ID user cập nhật cuối cùng';
comment on column vnpt_dev.wp_setting.config_type is 'Loại cấu hình (BRANDING, NAVIGATION)';
comment on constraint unique_wpsetting_userId on vnpt_dev.wp_setting is 'Mỗi doanh nghiệp chỉ có một bản ghi cấu hình duy nhất với từng config_type';
comment on table vnpt_dev.wp_setting is 'Bảng lưu thông tin cấu hình workingplace của từng doanh nghiệp';

-- Bổ sung bảng users_departments
drop table if exists vnpt_dev.users_departments;
create table vnpt_dev.users_departments (
    id bigserial primary key not null,
    user_id int8 not null,
    department_id int8 not null,
    is_manager int2 default 0
);
comment on column vnpt_dev.users_departments.user_id is 'id của nhân viên';
comment on column vnpt_dev.users_departments.department_id is 'id của phòng ban';
comment on column vnpt_dev.users_departments.is_manager is 'Cờ kiểm tra quản lý phòng ban 0: Không là quản lý phòng ban, 1: Là quản lý phòng ban';

insert into vnpt_dev.users_departments(user_id,department_id)
select users.id, departments.id
from vnpt_dev.users
         left join vnpt_dev.departments on users.department_id = departments.id
where
    users.department_id is not null
order by users.id asc;

-- Bổ sung thông tin định danh workplace
INSERT INTO "vnpt_dev"."oauth_client_details"
    ("client_id", "resource_ids", "client_secret", "scope", "authorized_grant_types",
    "web_server_redirect_uri", "authorities", "access_token_validity", "refresh_token_validity",
    "additional_information", "autoapprove") VALUES
    ('vnpt_workplaceid', NULL, '$2a$10$F2dXfNuFjqezxIZp0ad5OeegW43cRdSiPgEtcetHspiNrUCi3iI6O', '', 'password,authorization_code,refresh_token,client_credentials',
    NULL, NULL, 36000, 2592000, NULL, 'true') on conflict do nothing;

-- Bổ sung thông tin verify app token
INSERT INTO "vnpt_dev"."oauth_client_details"
    ("client_id", "resource_ids", "client_secret", "scope", "authorized_grant_types",
     "web_server_redirect_uri", "authorities", "access_token_validity", "refresh_token_validity",
     "additional_information", "autoapprove") VALUES
     ('vnpt_taskmnmtid', NULL, '$2a$10$F2dXfNuFjqezxIZp0ad5OeegW43cRdSiPgEtcetHspiNrUCi3iI6O', '', 'password,authorization_code,refresh_token,client_credentials',
      NULL, NULL, 36000, 2592000, NULL, 'true') on conflict do nothing;

alter table vnpt_dev.users
    add column if not exists sme_uuid uuid;
update vnpt_dev.users set
    sme_uuid = uuid_generate_v4()
where
    id in (
    select
        distinct users.id
    from vnpt_dev.users
        left join vnpt_dev.users_roles on users_roles.user_id = users.id
        left join vnpt_dev.role on role.id = users_roles.role_id
    where
        role.name = 'ROLE_SME'
    order by id desc
    ) and sme_uuid is null;

-- Thêm func remove two array
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_remove_array"("arrayone" _int8, "arraytwo" _int8)
RETURNS "pg_catalog"."_int8" AS $BODY$
    DECLARE
        mQuery text;
        sQuery text;
        bigint_list BIGINT[];
    BEGIN
    mQuery = ' SELECT array(SELECT unnest(array[';
    sQuery = replace(replace(concat(mQuery, arrayOne,']::int8[]) EXCEPT SELECT unnest(array[', arrayTwo ,']::int8[] )) AS array_union '), '}',''),'{','');
    RAISE NOTICE 'mQuery: %', sQuery;
    execute sQuery into bigint_list;
    return bigint_list;
    END $BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100;
-- Thêm thông tin nhân viên workplace
ALTER TABLE "vnpt_dev"."users" DROP COLUMN IF EXISTS "employee_metadata";
ALTER TABLE "vnpt_dev"."users" ADD COLUMN "employee_metadata" text;

COMMENT ON COLUMN "vnpt_dev"."users"."employee_metadata" IS 'Thông tin nhân viên doanh nghiệp:
+ Chức vụ (title)
+ Thời gian bắt đầu làm việc (startWorkDate)
+ Ghi chú (note)';


-- Thêm mô tả cột object_type của bảng file_attach
comment on column vnpt_dev.file_attach.object_type is 'loai file (avatar, ảnh chụp màn hình, video hướng dẫn....), 6: pdf
7: doc
8: docx
9: xls
10: xlsx
11: ppt
12: pptx
13: banner của service
14: ảnh của combo
15: page_builder
16: file import_migration
17: file error import_migration
43: file branding logo của workplace';

-- Tạo mới bảng wp_role_permission
DROP TABLE IF EXISTS "vnpt_dev"."wp_role_permission";
CREATE TABLE "vnpt_dev"."wp_role_permission" (
     "id" bigserial NOT NULL,
     "role_code" varchar(200) NOT NULL,
     "permission_code" int4 NOT NULL,
     PRIMARY KEY ("id")
);
COMMENT ON COLUMN "vnpt_dev"."wp_role_permission"."role_code" IS 'Mã vai trò';
COMMENT ON COLUMN "vnpt_dev"."wp_role_permission"."permission_code" IS 'Mã quyền được chứa trong vai trò';
COMMENT ON TABLE "vnpt_dev"."wp_role_permission" IS 'Bảng lưu mapping role-permission';
INSERT INTO "vnpt_dev"."wp_role_permission" (role_code, permission_code)
VALUES
     ('DX_WORKPLACE_ADMIN', 100),
     ('DX_WORKPLACE_ADMIN', 101),
     ('DX_WORKPLACE_ADMIN', 102),
     ('DX_WORKPLACE_ADMIN', 103),
     ('DX_WORKPLACE_ADMIN', 104),
     ('DX_WORKPLACE_ADMIN', 105),
     ('DX_WORKPLACE_ADMIN', 200),
     ('DX_WORKPLACE_ADMIN', 201),
     ('DX_WORKPLACE_ADMIN', 202),
     ('DX_WORKPLACE_ADMIN', 203),
     ('DX_WORKPLACE_ADMIN', 204),
     ('DX_WORKPLACE_ADMIN', 205),
     ('DX_WORKPLACE_ADMIN', 300),
     ('DX_WORKPLACE_ADMIN', 301),
     ('DX_WORKPLACE_ADMIN', 302),
     ('DX_WORKPLACE_ADMIN', 303),
     ('DX_WORKPLACE_ADMIN', 304),
     ('DX_WORKPLACE_ADMIN', 305),
     ('DX_WORKPLACE_ADMIN', 400),
     ('DX_WORKPLACE_ADMIN', 401),
     ('DX_WORKPLACE_ADMIN', 402),
     ('DX_WORKPLACE_ADMIN', 500),
     ('DX_WORKPLACE_ADMIN', 501),
     ('DX_WORKPLACE_ADMIN', 700),
     ('DX_WORKPLACE_ADMIN', 701),
     ('DX_WORKPLACE_ADMIN', 702),
     ('DX_WORKPLACE_ADMIN', 703),
     ('DX_WORKPLACE_ADMIN', 704),
     ('DX_WORKPLACE_ADMIN', 705),
     ('DX_WORKPLACE_ADMIN', 800),
     ('DX_WORKPLACE_ADMIN', 801),
     ('DX_WORKPLACE_ADMIN', 802),
     ('DX_WORKPLACE_ADMIN', 900),
     ('DX_WORKPLACE_ADMIN', 901),
     ('DX_WORKPLACE_ADMIN', 902),
     ('DX_TASK_ADMIN', 100),
     ('DX_TASK_ADMIN', 101),
     ('DX_TASK_ADMIN', 102),
     ('DX_TASK_ADMIN', 103),
     ('DX_TASK_ADMIN', 104),
     ('DX_TASK_ADMIN', 105),
     ('DX_TASK_ADMIN', 200),
     ('DX_TASK_ADMIN', 201),
     ('DX_TASK_ADMIN', 202),
     ('DX_TASK_ADMIN', 203),
     ('DX_TASK_ADMIN', 204),
     ('DX_TASK_ADMIN', 205),
     ('DX_TASK_ADMIN', 300),
     ('DX_TASK_ADMIN', 301),
     ('DX_TASK_ADMIN', 302),
     ('DX_TASK_ADMIN', 303),
     ('DX_TASK_ADMIN', 304),
     ('DX_TASK_ADMIN', 305),
     ('DX_TASK_ADMIN', 400),
     ('DX_TASK_ADMIN', 401),
     ('DX_TASK_ADMIN', 402),
     ('DX_TASK_ADMIN', 500),
     ('DX_TASK_ADMIN', 501),
     ('DX_TASK_ADMIN', 700),
     ('DX_TASK_ADMIN', 701),
     ('DX_TASK_ADMIN', 702),
     ('DX_TASK_ADMIN', 703),
     ('DX_TASK_ADMIN', 704),
     ('DX_TASK_ADMIN', 705),
     ('DX_TASK_ADMIN', 800),
     ('DX_TASK_ADMIN', 801),
     ('DX_TASK_ADMIN', 802),
     ('DX_TASK_ADMIN', 900),
     ('DX_TASK_ADMIN', 901),
     ('DX_TASK_ADMIN', 902);

-- Tạo mới bảng wp_role_employee
DROP TABLE IF EXISTS "vnpt_dev"."wp_role_employee";
CREATE TABLE "vnpt_dev"."wp_role_employee" (
   "id" bigserial primary key not null,
   "role_code" varchar(200) NOT NULL,
   "user_id" int8 NOT NULL
);
COMMENT ON COLUMN "vnpt_dev"."wp_role_employee"."role_code" IS 'Mã vai trò';
COMMENT ON COLUMN "vnpt_dev"."wp_role_employee"."user_id" IS 'ID của employee tương ứng có vai trò tương ứng';
COMMENT ON TABLE "vnpt_dev"."wp_role_employee" IS 'Bảng lưu mapping UUID của employee với vai trò tương ứng';

-- Cập nhật cấu trúc bảng wp_role
drop table if exists vnpt_dev.wp_role cascade;
create table vnpt_dev.wp_role (
      id bigserial primary key not null,
      api_key varchar(200),
      code varchar(200) not null,
      name varchar(500) not null,
      user_id int8 not null,
      status int2 not null default 0,
      deleted_flag int2 default 1,
      created_by int8,
      created_at timestamp,
      modified_by int8,
      modified_at timestamp,
      constraint unique_wprole_code unique (code)
);

comment on column vnpt_dev.wp_role.api_key is 'API Key định danh ứng dụng chứa vai trò';
comment on column vnpt_dev.wp_role.code is 'Mã vai trò';
comment on column vnpt_dev.wp_role.name is 'Tên vai trò';
comment on column vnpt_dev.wp_role.status is 'Trạng thái của vai trò (0: tắt,1: bật)';
comment on column vnpt_dev.wp_role.user_id is 'ID của SME (-1: Quyền áp dụng trên tất cả các manager, -2: Quyền áp dụng cho tất cả employee, còn lại mang giá trị ID của doanh nghiệp)';
comment on column vnpt_dev.wp_role.deleted_flag is 'Cờ đánh dấu đã xoá bản ghi (0: Đã xoá, 1: Chưa xoá)';
comment on column vnpt_dev.wp_role.created_at is 'Thời gian tạo vai trò';
comment on column vnpt_dev.wp_role.created_by is 'Id người tạo vai trò';
comment on column vnpt_dev.wp_role.modified_at is 'Thời gian cập nhật cuối cùng';
comment on column vnpt_dev.wp_role.modified_by is 'Id người cập nhật cuối cùng';
comment on constraint unique_wprole_code on vnpt_dev.wp_role is 'Mã vai trò trong workplace là duy nhất';
comment on table vnpt_dev.wp_role is 'Bảng quản lý các vai trò của workplace';
insert into vnpt_dev.wp_role(api_key, code, name, user_id, status, created_by, created_at) values
    ('WORKPLACE', 'DX_WORKPLACE_ADMIN', 'Quyền quản trị doanh nghiệp trên Workplace', -1, 1, 1, now()),
('WORKPLACE', 'DX_WORKPLACE_EMPLOYEE', 'Quyền nhân viên doanh nghiệp trên Workplace', -2, 1, 1, now()),
('3a287bc7-3978-4b0f-8fce-bbb0cd0359c3', 'DX_TASK_ADMIN', 'Quyền quản trị doanh nghiệp trên Task', -1, 1, 1, now()),
('3a287bc7-3978-4b0f-8fce-bbb0cd0359c3', 'DX_TASK_EMPLOYEE', 'Quyền nhân viên doanh nghiệp trên Task', -2, 1, 1, now());