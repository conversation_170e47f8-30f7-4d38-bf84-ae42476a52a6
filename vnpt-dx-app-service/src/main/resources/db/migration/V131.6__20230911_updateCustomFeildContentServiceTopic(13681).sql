ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

UPDATE "vnpt_dev"."custom_layout"
SET layout_content = '{
	"ROOT": {
		"type": {
			"resolvedName": "AppContainer"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"14",
				"14",
				"14",
				"14"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": {
				"a": 1,
				"b": 255,
				"g": 255,
				"r": 255
			},
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "App",
		"custom": {
			"displayName": "App"
		},
		"hidden": false,
		"nodes": [
			"qwSN5MG24a",
			"n-t23K5gN9",
			"uNmsh4FUrJ",
			"dLJAk24zrH",
			"jgpnV6LLx4",
			"idxJzZxr1L",
			"tc1m3RRqej",
			"bnppyb2ijq",
			"7hL9b2_00l",
			"cUjgLlXbui",
			"BMHiMeeTiM"
		],
		"linkedNodes": {}
	},
	"0rSOWcOiAl": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "hVI_YX_h6F",
		"hidden": false,
		"nodes": [
			"dMdRNlEOO5",
			"cDNXBiM0qb",
			"a0gfnffvTh",
			"8826013e-85b4-4deb-bcc4-9585cd631c48",
			"XSMq15xLUv"
		],
		"linkedNodes": {}
	},
	"19ugjDhODU": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "B1N4wJnZl0",
		"hidden": false,
		"nodes": [
			"RQFbpcu1qX"
		],
		"linkedNodes": {}
	},
	"22t4godxHx": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "lj0bhaiR4S",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"2CBgqhITQF": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Tính năng",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "tobllfW2zv"
		}
	},
	"3GNxOypoiJ": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "B1N4wJnZl0",
		"hidden": false,
		"nodes": [
			"PmD1eOKm86"
		],
		"linkedNodes": {}
	},
	"3hf4xpy48p": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "B1N4wJnZl0",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"3qsIgkrEwS": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "_D75WFSgrN",
		"hidden": false,
		"nodes": [
			"w1VehWZtEG",
			"kBTfPO9NWL"
		],
		"linkedNodes": {}
	},
	"5ObxGrfHoY": {
		"type": {
			"resolvedName": "MultiLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4058,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Mô tả",
			"formControlName": "tech.description.0",
			"placeholder": "Nhập mô tả",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 140,
			"configs": [
				"required"
			],
			"touch": false
		},
		"displayName": "Multi Line Text",
		"custom": {},
		"parent": "mTVobbmv0q",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"AGcS-mSk6Y": {
		"type": {
			"resolvedName": "Button"
		},
		"isCanvas": true,
		"props": {
			"label": "Thêm công nghệ +",
			"alignItems": "center",
			"justifyContent": "end",
			"action": "add-section",
			"popup": "choose-service",
			"maxSection": 4,
			"formControlName": "Button_EowUsxr",
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"showStandardSettingIcon": false
		},
		"displayName": "Button",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"B1N4wJnZl0": {
		"type": {
			"resolvedName": "Column"
		},
		"isCanvas": true,
		"props": {
			"numberOfCols": 2,
			"isValid": true
		},
		"displayName": "Column",
		"custom": {},
		"parent": "RJnpmrgqLb",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"0": "3GNxOypoiJ",
			"1": "19ugjDhODU",
			"2": "YuQbWd2fIT",
			"3": "3hf4xpy48p"
		}
	},
	"BMHiMeeTiM": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": true,
			"showLabel": false,
			"label": "New Section",
			"collapsable": false,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "JKU_6IvHg_"
		}
	},
	"BbONAKI9Jm": {
		"type": {
			"resolvedName": "DropdownList"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 4049,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": true,
			"label": "Vị trí hiển thị",
			"formControlName": "techLayout",
			"placeholder": "Vị trí mặc định",
			"configs": [],
			"valueSelectItems": [
				{
					"id": "Vị trí mặc định",
					"label": "Vị trí mặc định",
					"default": false
				},
				{
					"id": "Trên cùng",
					"label": "Trên cùng",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueSelected": "Vị trí mặc định",
			"useDataApi": false,
			"dataApiType": "categories",
			"relatedFieldConfig": [
				{
					"relatedField": null,
					"relatedValue": null
				}
			],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Dropdown List",
		"custom": {},
		"parent": "olSGJozU4q",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"CuyIRODeAo": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4052,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Tên bố cục",
			"formControlName": "layout.title.0",
			"displayContent": "",
			"placeholder": "Tổng quan sản phẩm",
			"format": [
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]",
				"[0-9]"
			],
			"validateType": "OR",
			"maxLength": 50,
			"configs": [
				"noDuplicate",
				"required"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "gdSVMh7h4T",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"EZ8WOI-4sb": {
		"type": {
			"resolvedName": "MultiLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4053,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Mô tả",
			"formControlName": "layout.description.0",
			"format": [
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[0-9]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 1000,
			"configs": [
				"required"
			],
			"touch": false
		},
		"displayName": "Multi Line Text",
		"custom": {},
		"parent": "gdSVMh7h4T",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"FfxvOoJZUh": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "n-t23K5gN9",
		"hidden": false,
		"nodes": [
			"xY473J3WH5"
		],
		"linkedNodes": {}
	},
	"GCitC377M8": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "jgpnV6LLx4",
		"hidden": false,
		"nodes": [
			"hvHSo2h2tm",
			"lj0bhaiR4S",
			"qgsaN3c-um",
			"ObBw3x0dOj",
			"ccc54e20-6b3e-468d-9d8d-40852ced3550",
			"09969685-03ff-4e6a-b168-073980b11985",
			"AGcS-mSk6Y"
		],
		"linkedNodes": {}
	},
	"HUZZSxWtXW": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "lj0bhaiR4S",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"JKU_6IvHg_": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "BMHiMeeTiM",
		"hidden": false,
		"nodes": [
			"NLMxFZCw60"
		],
		"linkedNodes": {}
	},
	"MCdTi5d6F5": {
		"type": {
			"resolvedName": "NoteList"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 7616,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": false,
			"label": "List note",
			"formControlName": "NoteList_4CeAzky",
			"configs": [],
			"typeDisplay": "horizontal",
			"listNote": [
				{
					"id": "Định dạng: JPEG, WEBP",
					"label": "Định dạng: JPEG, WEBP",
					"default": false
				},
				{
					"id": "Kích thước: Ảnh 674x380",
					"label": "Kích thước: Ảnh 674x380",
					"default": false
				},
				{
					"id": "Dung lượng: Ảnh <1MB ( Chất lượng ảnh rõ nét )",
					"label": "Dung lượng: Ảnh <1MB ( Chất lượng ảnh rõ nét )",
					"default": false
				},
				{
					"id": "Bố cục: Tối thiểu 1 tính năng",
					"label": "Bố cục: Tối thiểu 1 tính năng",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueNote": [],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Note list",
		"custom": {},
		"parent": "tobllfW2zv",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"NLMxFZCw60": {
		"type": {
			"resolvedName": "CheckboxComponet"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4046,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": false,
			"label": "Tôi đồng ý với chính sách của sàn chuyển đổi số",
			"formControlName": "agreePolicy",
			"configs": [],
			"typeDisplay": "horizontal",
			"listCheckbox": [
				{
					"id": "Tôi đồng ý với chính sách của sàn chuyển đổi số",
					"label": "Tôi đồng ý với chính sách của sàn chuyển đổi số",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueCheckbox": [],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Check Box",
		"custom": {},
		"parent": "JKU_6IvHg_",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"ObBw3x0dOj": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Công nghệ 1",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "mTVobbmv0q"
		}
	},
	"Ox6bLDJF05": {
		"type": {
			"resolvedName": "Button"
		},
		"isCanvas": true,
		"props": {
			"label": "Thêm tính năng +",
			"alignItems": "center",
			"justifyContent": "end",
			"action": "add-section",
			"popup": "choose-service",
			"maxSection": 999,
			"formControlName": "Button_3bopJvJ",
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"touched": true,
			"showStandardSettingIcon": false
		},
		"displayName": "Button",
		"custom": {},
		"parent": "tobllfW2zv",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"PmD1eOKm86": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4075,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": "Độ dài không vượt quá 50 ký tự"
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Tên dịch vụ",
			"formControlName": "serviceName",
			"displayContent": "",
			"placeholder": "Nhập tên dịch vụ",
			"format": [
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]",
				"[0-9]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]"
			],
			"validateType": "OR",
			"maxLength": 50,
			"configs": [
				"noDuplicate",
				"required",
				"noteBox"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "3GNxOypoiJ",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"RJnpmrgqLb": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "bdK10d2Hv9",
		"hidden": false,
		"nodes": [
			"B1N4wJnZl0",
			"gEkvJ7Gwmq",
			"cCukm69Zqe",
			"98e1fc20-0bb5-427e-af3f-4f2af878ccc4",
			"uNmsh4FUrJ"
		],
		"linkedNodes": {}
	},
	"RQFbpcu1qX": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4074,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": true,
			"label": "Mã dịch vụ",
			"formControlName": "serviceCode",
			"displayContent": "",
			"placeholder": "Nhập mã dịch vụ",
			"format": [
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[0-9]"
			],
			"validateType": "OR",
			"maxLength": 99,
			"configs": [
				"noDuplicate"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "19ugjDhODU",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"UmenQWskqa": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "kBTfPO9NWL",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"Vq-hkxt8Lz": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "kBTfPO9NWL",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"Vrfc-EulJj": {
		"type": {
			"resolvedName": "CheckboxComponet"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4045,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": true,
			"label": "Đối tượng khách hàng",
			"formControlName": "customerTypeCode",
			"configs": [],
			"typeDisplay": "horizontal",
			"listCheckbox": [
				{
					"id": "Doanh nghiệp",
					"label": "Doanh nghiệp",
					"default": true
				},
				{
					"id": "Hộ kinh doanh",
					"label": "Hộ kinh doanh",
					"default": false
				},
				{
					"id": "Cá nhân",
					"label": "Cá nhân",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueCheckbox": [
				"Doanh nghiệp"
			],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Check Box",
		"custom": {},
		"parent": "ug-W-4ttlb",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"WtNwEnDU-S": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "kBTfPO9NWL",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"XSMq15xLUv": {
		"type": {
			"resolvedName": "MultiLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4060,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Mô tả",
			"formControlName": "feature.description.0",
			"placeholder": "Nhập mô tả tính năng",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 100,
			"configs": [
				"required"
			]
		},
		"displayName": "Multi Line Text",
		"custom": {},
		"parent": "0rSOWcOiAl",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"YuQbWd2fIT": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "B1N4wJnZl0",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"ZFkA52Mscj": {
		"type": {
			"resolvedName": "SwitchComponent"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 4078,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": true,
			"label": "Trạng thái hiển thị",
			"formControlName": "feature.featureVisible.0",
			"configs": [],
			"typeDisplay": "horizontal",
			"valueInputInser": "",
			"valueSwitch": true,
			"touch": true
		},
		"displayName": "Switch",
		"custom": {},
		"parent": "tobllfW2zv",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"_D75WFSgrN": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Bố cục 1",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "vDf-j5XvR-",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "3qsIgkrEwS"
		}
	},
	"a0gfnffvTh": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4073,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Tên tính năng",
			"formControlName": "feature.name.0",
			"displayContent": "",
			"placeholder": "Nhập tên tính năng",
			"format": [
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]",
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"
			],
			"validateType": "OR",
			"maxLength": 99,
			"configs": [
				"noDuplicate",
				"required"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "0rSOWcOiAl",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"bSTg-nP2kI": {
		"type": {
			"resolvedName": "ImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4082,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [
					"note"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": false,
			"label": "Tải ảnh",
			"formControlName": "tech.image.0",
			"urlTypes": "all",
			"formats": [
				".webp",
				".jpeg, .jpg"
			],
			"maxSize": 1,
			"maxFile": 1,
			"configs": [
				"required",
				"noteBox"
			],
			"listNote": [],
			"touch": false
		},
		"displayName": "Tải ảnh",
		"custom": {},
		"parent": "mTVobbmv0q",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"bdK10d2Hv9": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Thông tin chung",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "RJnpmrgqLb"
		}
	},
	"bnppyb2ijq": {
		"type": {
			"resolvedName": "SimilarProducts"
		},
		"isCanvas": true,
		"props": {
			"displayPortal": [
				"dev"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": true,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Sản phẩm tương tự",
			"formControlName": "SimilarProducts_WNMlqgB",
			"touch": false
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"cCukm69Zqe": {
		"type": {
			"resolvedName": "ImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4085,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Ảnh đại diện dịch vụ",
			"formControlName": "serviceImage",
			"urlTypes": "all",
			"formats": [
				".webp",
				".jpeg, .jpg"
			],
			"maxSize": 1,
			"maxFile": 1,
			"configs": [
				"required"
			],
			"listNote": [
				{
					"id": "13b1331b-971b-4911-a540-c13c486276cd",
					"label": "Định dạng JPEG, WEBP"
				},
				{
					"id": "6652ca96-7fe9-47ad-8280-924447adbb95",
					"label": "Dung lượng ảnh <1MB"
				},
				{
					"id": "8defc691-eca0-4a6c-841f-d98c28c6e193",
					"label": "Chất lượng ảnh rõ nét"
				},
				{
					"id": "5af4b74e-0983-4b59-b897-456d2614678c",
					"label": "Kích thước ảnh 64x64"
				}
			],
			"touch": false
		},
		"displayName": "Tải ảnh",
		"custom": {},
		"parent": "RJnpmrgqLb",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"cDNXBiM0qb": {
		"type": {
			"resolvedName": "ImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4086,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": false,
			"label": "File tải lên",
			"formControlName": "feature.upload.0",
			"urlTypes": "all",
			"formats": [
				".webp",
				".jpeg, .jpg"
			],
			"maxSize": 1,
			"maxFile": 1,
			"configs": [
				"required"
			],
			"listNote": [],
			"touch": true
		},
		"displayName": "Tải ảnh",
		"custom": {},
		"parent": "0rSOWcOiAl",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"cUjgLlXbui": {
		"type": {
			"resolvedName": "UpdateServiceReason"
		},
		"isCanvas": true,
		"props": {
			"displayPortal": [
				"dev"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"formControlName": "UpdateServiceReason_UhE7hOkbticYuXwn"
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"cnDNgSVmSQ": {
		"type": {
			"resolvedName": "Button"
		},
		"isCanvas": true,
		"props": {
			"label": "Thêm bố cục +",
			"alignItems": "center",
			"justifyContent": "end",
			"action": "add-section",
			"popup": "choose-service",
			"maxSection": 5,
			"formControlName": "Button_XnwxyAL",
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"showStandardSettingIcon": false
		},
		"displayName": "Button",
		"custom": {},
		"parent": "vDf-j5XvR-",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"dLJAk24zrH": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Bố cục",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "vDf-j5XvR-"
		}
	},
	"dMdRNlEOO5": {
		"type": {
			"resolvedName": "CheckboxComponet"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 4047,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": false,
			"label": "Hiển thị trên trang tổng quan",
			"formControlName": "feature.displayFeatureInSumPage.0",
			"configs": [],
			"typeDisplay": "horizontal",
			"listCheckbox": [
				{
					"id": "Hiển thị trên trang tổng quan",
					"label": "Hiển thị trên trang tổng quan",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueCheckbox": [],
			"touch": true,
			"dataInputAdd": "",
			"relatedFieldConfig": [
				{
					"relatedField": [
						"feature.upload.0",
						"feature.description.0"
					],
					"relatedValue": "Hiển thị trên trang tổng quan"
				}
			]
		},
		"displayName": "Check Box",
		"custom": {},
		"parent": "0rSOWcOiAl",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"gEkvJ7Gwmq": {
		"type": {
			"resolvedName": "DropdownList"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4048,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [
					"note"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Danh mục",
			"formControlName": "categoriesId",
			"placeholder": "Chọn danh mục",
			"configs": [
				"required",
				"noteBox"
			],
			"valueSelectItems": [
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				},
				{
					"id": null,
					"label": null,
					"default": false
				}
			],
			"valueInputInser": "",
			"useDataApi": true,
			"dataApiType": "categories",
			"relatedFieldConfig": [
				{
					"relatedField": null,
					"relatedValue": null
				}
			],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Dropdown List",
		"custom": {},
		"parent": "RJnpmrgqLb",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"gdSVMh7h4T": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "kBTfPO9NWL",
		"hidden": false,
		"nodes": [
			"CuyIRODeAo",
			"EZ8WOI-4sb"
		],
		"linkedNodes": {}
	},
	"hVI_YX_h6F": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Tính năng 1",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "tobllfW2zv",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "0rSOWcOiAl"
		}
	},
	"hvHSo2h2tm": {
		"type": {
			"resolvedName": "SwitchComponent"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 4079,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": true,
			"label": "Trạng thái hiển thị",
			"formControlName": "techVisible",
			"configs": [],
			"typeDisplay": "horizontal",
			"valueInputInser": "",
			"valueSwitch": true,
			"touch": false
		},
		"displayName": "Switch",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"idxJzZxr1L": {
		"type": {
			"resolvedName": "TopicListComponent"
		},
		"isCanvas": true,
		"props": {
			"displayPortal": [
				"dev"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Topic List",
			"formControlName": "TopicListComponent_nJUL6OhfJ8WKCAQn"
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"jgpnV6LLx4": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Công nghệ nổi bật",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "GCitC377M8"
		}
	},
	"kBTfPO9NWL": {
		"type": {
			"resolvedName": "Column"
		},
		"isCanvas": true,
		"props": {
			"numberOfCols": 2,
			"isValid": true
		},
		"displayName": "Column",
		"custom": {},
		"parent": "3qsIgkrEwS",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"0": "gdSVMh7h4T",
			"1": "UmenQWskqa",
			"2": "WtNwEnDU-S",
			"3": "Vq-hkxt8Lz"
		}
	},
	"lj0bhaiR4S": {
		"type": {
			"resolvedName": "Column"
		},
		"isCanvas": true,
		"props": {
			"numberOfCols": 2,
			"isValid": true
		},
		"displayName": "Column",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"0": "olSGJozU4q",
			"1": "HUZZSxWtXW",
			"2": "22t4godxHx",
			"3": "sqhoOUFGKw"
		}
	},
	"mTVobbmv0q": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ObBw3x0dOj",
		"hidden": false,
		"nodes": [
			"bSTg-nP2kI",
			"xHXkIyqiaO",
			"5ObxGrfHoY"
		],
		"linkedNodes": {}
	},
	"n-t23K5gN9": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Cấu hình multi-subcriptions",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "FfxvOoJZUh"
		}
	},
	"olSGJozU4q": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "lj0bhaiR4S",
		"hidden": false,
		"nodes": [
			"BbONAKI9Jm"
		],
		"linkedNodes": {}
	},
	"qgsaN3c-um": {
		"type": {
			"resolvedName": "NoteList"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 7617,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": false,
			"label": "List note",
			"formControlName": "NoteList_gS1FEK4",
			"configs": [],
			"typeDisplay": "horizontal",
			"listNote": [
				{
					"id": "Định dạng ảnh: JPEG, WEBP",
					"label": "Định dạng ảnh: JPEG, WEBP",
					"default": false
				},
				{
					"id": "Kích thước: Ảnh 120x120",
					"label": "Kích thước: Ảnh 120x120",
					"default": false
				},
				{
					"id": "Dung lượng: Ảnh <1MB ( Chất lượng ảnh rõ nét )",
					"label": "Dung lượng: Ảnh <1MB ( Chất lượng ảnh rõ nét )",
					"default": false
				},
				{
					"id": "Bố cục: Tối thiểu 3 công nghệ, tối đa 4 công nghệ",
					"label": "Bố cục: Tối thiểu 3 công nghệ, tối đa 4 công nghệ",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueNote": [],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Note list",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"qwSN5MG24a": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Đối tượng khách hàng",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true
		},
		"displayName": "Section",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "ug-W-4ttlb"
		}
	},
	"rTkJSo-myb": {
		"type": {
			"resolvedName": "NoteList"
		},
		"isCanvas": true,
		"props": {
			"0": "dev",
			"1": "admin",
			"fieldId": 7618,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": true,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": false,
			"label": "List note",
			"formControlName": "NoteList_sRoxeXc",
			"configs": [],
			"typeDisplay": "horizontal",
			"listNote": [
				{
					"id": "Định dạng: JPEG, WEBP",
					"label": "Định dạng: JPEG, WEBP",
					"default": false
				},
				{
					"id": "Định dạng video: MP4, WMV",
					"label": "Định dạng video: MP4, WMV",
					"default": false
				},
				{
					"id": "Kích thước: Ảnh/Video 674x380 (16:9)",
					"label": "Kích thước: Ảnh/Video 674x380 (16:9)",
					"default": false
				},
				{
					"id": "Dung lượng: Ảnh <1MB - Video <100MB ( Chất lượng ảnh rõ nét )",
					"label": "Dung lượng: Ảnh <1MB - Video <100MB ( Chất lượng ảnh rõ nét )",
					"default": false
				},
				{
					"id": "Bố cục: Tối thiểu 1 bố cục, tối đa 5 bố cục",
					"label": "Bố cục: Tối thiểu 1 bố cục, tối đa 5 bố cục",
					"default": false
				}
			],
			"valueInputInser": "",
			"valueNote": [],
			"touch": false,
			"dataInputAdd": ""
		},
		"displayName": "Note list",
		"custom": {},
		"parent": "vDf-j5XvR-",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"sqhoOUFGKw": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"columnNumber": 2
		},
		"displayName": "Container",
		"custom": {},
		"parent": "lj0bhaiR4S",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"tc1m3RRqej": {
		"type": {
			"resolvedName": "SEOConfig"
		},
		"isCanvas": true,
		"props": {
			"displayPortal": [
				"dev"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": true,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Thông tin cơ bản của dịch vụ",
			"formControlName": "SEOConfig_LbIqGLO",
			"touch": false
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"tobllfW2zv": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "2CBgqhITQF",
		"hidden": false,
		"nodes": [
			"ZFkA52Mscj",
			"MCdTi5d6F5",
			"hVI_YX_h6F",
			"Ox6bLDJF05"
		],
		"linkedNodes": {}
	},
	"uNmsh4FUrJ": {
		"type": {
			"resolvedName": "CustomInfoBasicService"
		},
		"isCanvas": true,
		"props": {
			"displayPortal": [
				"dev"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": true,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Thông tin cơ bản của dịch vụ",
			"formControlName": "CustomInfoBasicService_nuek0Ph",
			"touch": false
		},
		"displayName": "Container",
		"custom": {},
		"parent": "RJnpmrgqLb",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"ug-W-4ttlb": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "qwSN5MG24a",
		"hidden": false,
		"nodes": [
			"Vrfc-EulJj"
		],
		"linkedNodes": {}
	},
	"vDf-j5XvR-": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true
		},
		"displayName": "Container",
		"custom": {},
		"parent": "dLJAk24zrH",
		"hidden": false,
		"nodes": [
			"rTkJSo-myb",
			"_D75WFSgrN",
			"cnDNgSVmSQ"
		],
		"linkedNodes": {}
	},
	"w1VehWZtEG": {
		"type": {
			"resolvedName": "VideoImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4081,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"label": "File tải lên",
			"formControlName": "layout.image.0",
			"touch": false,
			"configs": [],
			"showLabel": false
		},
		"displayName": "Tải ảnh hoặc video",
		"custom": {},
		"parent": "3qsIgkrEwS",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"xHXkIyqiaO": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4054,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Tên công nghệ",
			"formControlName": "tech.title.0",
			"displayContent": "",
			"placeholder": "Nhập tên công nghệ",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 99,
			"configs": [
				"noDuplicate",
				"required"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "mTVobbmv0q",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"xY473J3WH5": {
		"type": {
			"resolvedName": "SwitchComponent"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4077,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "none",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "custom"
			},
			"showLabel": true,
			"label": "Bật tắt cấu hình",
			"formControlName": "allowMultiSub",
			"configs": [],
			"typeDisplay": "horizontal",
			"valueInputInser": "",
			"valueSwitch": true,
			"touch": false
		},
		"displayName": "Switch",
		"custom": {},
		"parent": "FfxvOoJZUh",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"09969685-03ff-4e6a-b168-073980b11985": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Công nghệ 3",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true,
			"showSettingDialogWhenOpen": false
		},
		"displayName": "Section",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "d17e39cd-5a96-4f2d-9cd3-06360e24b53d"
		}
	},
	"1697b6c3-ae92-4e02-8f44-3a7e59de8178": {
		"type": {
			"resolvedName": "ImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4084,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [
					"note"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": false,
			"label": "Tải ảnh",
			"formControlName": "tech.image.2",
			"urlTypes": "all",
			"formats": [
				".webp",
				".jpeg, .jpg"
			],
			"maxSize": 1,
			"maxFile": 1,
			"configs": [
				"required",
				"noteBox"
			],
			"listNote": [],
			"touch": false
		},
		"displayName": "Tải ảnh",
		"custom": {},
		"parent": "d17e39cd-5a96-4f2d-9cd3-06360e24b53d",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"44960798-832e-4891-829e-f00d5401facf": {
		"type": {
			"resolvedName": "ImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4083,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [
					"note"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": false,
			"label": "Tải ảnh",
			"formControlName": "tech.image.1",
			"urlTypes": "all",
			"formats": [
				".webp",
				".jpeg, .jpg"
			],
			"maxSize": 1,
			"maxFile": 1,
			"configs": [
				"required",
				"noteBox"
			],
			"listNote": [],
			"touch": false
		},
		"displayName": "Tải ảnh",
		"custom": {},
		"parent": "b469b44e-55b9-4bad-88ca-8e1baeae4b6b",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"46dcde14-563d-467b-bfe1-da5f9eaddfa6": {
		"type": {
			"resolvedName": "MultiLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4059,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Mô tả",
			"formControlName": "tech.description.1",
			"placeholder": "Nhập mô tả",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 140,
			"configs": [
				"required"
			],
			"touch": false
		},
		"displayName": "Multi Line Text",
		"custom": {},
		"parent": "b469b44e-55b9-4bad-88ca-8e1baeae4b6b",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"5a937e97-5e7a-411c-a35a-d8a74147aa62": {
		"type": {
			"resolvedName": "MultiLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4055,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Mô tả",
			"formControlName": "tech.description.2",
			"placeholder": "Nhập mô tả",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 140,
			"configs": [
				"required"
			],
			"touch": false
		},
		"displayName": "Multi Line Text",
		"custom": {},
		"parent": "d17e39cd-5a96-4f2d-9cd3-06360e24b53d",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"8354faf8-1ef6-4c98-89cb-f76b9f0d85f4": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4057,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Tên công nghệ",
			"formControlName": "tech.title.1",
			"displayContent": "",
			"placeholder": "Nhập tên công nghệ",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 99,
			"configs": [
				"noDuplicate",
				"required"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "b469b44e-55b9-4bad-88ca-8e1baeae4b6b",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"8826013e-85b4-4deb-bcc4-9585cd631c48": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4072,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Mã tính năng",
			"formControlName": "feature.code.0",
			"displayContent": "",
			"placeholder": "Nhập mã tính năng",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]"
			],
			"validateType": "OR",
			"maxLength": 99,
			"configs": [
				"required"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "0rSOWcOiAl",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"98e1fc20-0bb5-427e-af3f-4f2af878ccc4": {
		"type": {
			"resolvedName": "ImageUpload"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4087,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Banner sản phẩm",
			"formControlName": "serviceBanner",
			"urlTypes": "all",
			"formats": [
				".webp",
				".jpeg, .jpg"
			],
			"maxSize": 1,
			"maxFile": 1,
			"configs": [
				"required"
			],
			"listNote": [
				{
					"id": "079a3574-e578-42cd-b536-0a72624dd430",
					"label": "Định dạng JPEG, WEBP"
				},
				{
					"id": "c0b2749b-1afd-4bb2-96e4-1003ee61ab2b",
					"label": "Dung lượng ảnh <1MB"
				},
				{
					"id": "63427cd2-417f-4b8c-96b0-1827d5b85294",
					"label": "Chất lượng ảnh rõ nét"
				},
				{
					"id": "227c722e-d390-46f8-85aa-1d96681f75e1",
					"label": "Kích thước ảnh 300x300"
				}
			],
			"touch": false
		},
		"displayName": "Tải ảnh",
		"custom": {},
		"parent": "RJnpmrgqLb",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"b469b44e-55b9-4bad-88ca-8e1baeae4b6b": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"showSettingDialogWhenOpen": false
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ccc54e20-6b3e-468d-9d8d-40852ced3550",
		"hidden": false,
		"nodes": [
			"44960798-832e-4891-829e-f00d5401facf",
			"8354faf8-1ef6-4c98-89cb-f76b9f0d85f4",
			"46dcde14-563d-467b-bfe1-da5f9eaddfa6"
		],
		"linkedNodes": {}
	},
	"ccc54e20-6b3e-468d-9d8d-40852ced3550": {
		"type": {
			"resolvedName": "Section"
		},
		"isCanvas": true,
		"props": {
			"canAction": false,
			"showLabel": true,
			"label": "Công nghệ 2",
			"collapsable": true,
			"configs": [],
			"noteConfig": {
				"noteType": [
					"tooltip"
				],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"isValid": true,
			"showSettingDialogWhenOpen": false
		},
		"displayName": "Section",
		"custom": {},
		"parent": "GCitC377M8",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {
			"section": "b469b44e-55b9-4bad-88ca-8e1baeae4b6b"
		}
	},
	"d17e39cd-5a96-4f2d-9cd3-06360e24b53d": {
		"type": {
			"resolvedName": "Container"
		},
		"isCanvas": true,
		"props": {
			"flexDirection": "column",
			"alignItems": "flex-start",
			"justifyContent": "flex-start",
			"fillSpace": "no",
			"padding": [
				"0",
				"0",
				"0",
				"0"
			],
			"margin": [
				"0",
				"0",
				"0",
				"0"
			],
			"background": "#fff",
			"color": "#000",
			"shadow": 0,
			"radius": 0,
			"width": "100%",
			"height": "auto",
			"canResize": false,
			"disableDrag": false,
			"isValid": true,
			"showSettingDialogWhenOpen": false
		},
		"displayName": "Container",
		"custom": {},
		"parent": "09969685-03ff-4e6a-b168-073980b11985",
		"hidden": false,
		"nodes": [
			"1697b6c3-ae92-4e02-8f44-3a7e59de8178",
			"e7ae145b-c59e-46af-9ab0-9b71a6f4d7d3",
			"5a937e97-5e7a-411c-a35a-d8a74147aa62"
		],
		"linkedNodes": {}
	},
	"e7ae145b-c59e-46af-9ab0-9b71a6f4d7d3": {
		"type": {
			"resolvedName": "SingleLineText"
		},
		"isCanvas": true,
		"props": {
			"fieldId": 4056,
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"noteMessage": "",
				"tooltipMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": true,
			"isValid": true,
			"canAction": false,
			"permission": [],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"serviceIds": [],
				"requiredType": "always",
				"requiredServices": [],
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all"
			},
			"showLabel": true,
			"label": "Tên công nghệ",
			"formControlName": "tech.title.2",
			"displayContent": "",
			"placeholder": "Nhập tên công nghệ",
			"format": [
				"[0-9]",
				"[A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ ]",
				"[a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹ ]",
				"[^a-zàáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵýỷỹA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸ0-9]"
			],
			"validateType": "OR",
			"maxLength": 99,
			"configs": [
				"noDuplicate",
				"required"
			],
			"touch": false
		},
		"displayName": "Single Line Text",
		"custom": {},
		"parent": "d17e39cd-5a96-4f2d-9cd3-06360e24b53d",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	},
	"7hL9b2_00l": {
		"type": {
			"resolvedName": "PaymentMethod"
		},
		"isCanvas": true,
		"props": {
			"displayPortal": [
				"dev",
				"admin"
			],
			"noteConfig": {
				"noteType": [],
				"tooltipMessage": "",
				"noteMessage": ""
			},
			"showSettingDialogWhenOpen": false,
			"isStandardField": false,
			"isValid": true,
			"canAction": false,
			"permission": [
				{
					"roleId": 20,
					"permission": 2,
					"roleName": "ADMIN_DT"
				},
				{
					"roleId": 329,
					"permission": 2,
					"roleName": "Test"
				},
				{
					"roleId": 216,
					"permission": 2,
					"roleName": "test pht"
				},
				{
					"roleId": 180,
					"permission": 2,
					"roleName": "Xem chi tiết nhân viên kinh doanh"
				},
				{
					"roleId": 202,
					"permission": 2,
					"roleName": "vtro"
				},
				{
					"roleId": 231,
					"permission": 2,
					"roleName": "Phân quyền 10:34"
				},
				{
					"roleId": 335,
					"permission": 1,
					"roleName": "BU SME"
				},
				{
					"roleId": 169,
					"permission": 2,
					"roleName": "vai trò kookie"
				},
				{
					"roleId": 58,
					"permission": 2,
					"roleName": "test dashboard cdqc"
				},
				{
					"roleId": 129,
					"permission": 2,
					"roleName": "Vai trò A"
				},
				{
					"roleId": 183,
					"permission": 2,
					"roleName": "Xem danh sách nhân viên kinh doanh"
				},
				{
					"roleId": 130,
					"permission": 2,
					"roleName": "AM_MANAGER"
				},
				{
					"roleId": 171,
					"permission": 2,
					"roleName": "Quyền 11:12"
				},
				{
					"roleId": 209,
					"permission": 2,
					"roleName": "Bật/tắt tài khoản nhân viên kinh doanh"
				},
				{
					"roleId": 190,
					"permission": 2,
					"roleName": "hh"
				},
				{
					"roleId": 38,
					"permission": 2,
					"roleName": "ADMIN_TINH1"
				},
				{
					"roleId": 185,
					"permission": 2,
					"roleName": "Xem danh sách quy tắc"
				},
				{
					"roleId": 30,
					"permission": 2,
					"roleName": "Bật tắt báo cáo tự động"
				},
				{
					"roleId": 131,
					"permission": 2,
					"roleName": "AM_STAFF"
				},
				{
					"roleId": 128,
					"permission": 2,
					"roleName": "upgrade KH"
				},
				{
					"roleId": 31,
					"permission": 2,
					"roleName": "ADMIN_MARKETING3"
				},
				{
					"roleId": 260,
					"permission": 2,
					"roleName": "CF-bnhi test"
				},
				{
					"roleId": 295,
					"permission": 2,
					"roleName": "1 vai trò bí ẩn trên staging"
				},
				{
					"roleId": 57,
					"permission": 2,
					"roleName": "test qlgd"
				},
				{
					"roleId": 336,
					"permission": 2,
					"roleName": "bật/tắt trạng thái dv/gói dv"
				},
				{
					"roleId": 170,
					"permission": 2,
					"roleName": "Phân quyền mới nhất"
				},
				{
					"roleId": 59,
					"permission": 2,
					"roleName": "Giao diện"
				},
				{
					"roleId": 236,
					"permission": 2,
					"roleName": "nhi test OS mail"
				},
				{
					"roleId": 35,
					"permission": 2,
					"roleName": "AM_ONLINE"
				},
				{
					"roleId": 294,
					"permission": 2,
					"roleName": "DTMT TEST"
				},
				{
					"roleId": 203,
					"permission": 2,
					"roleName": "Xem danh sách phân vùng dữ liệu"
				},
				{
					"roleId": 215,
					"permission": 2,
					"roleName": "tets thuê bao"
				},
				{
					"roleId": 13,
					"permission": 2,
					"roleName": "ADMIN_SPDV"
				},
				{
					"roleId": 330,
					"permission": 2,
					"roleName": "tạo dtmt"
				},
				{
					"roleId": 206,
					"permission": 2,
					"roleName": "Nhi test vtro qtv"
				},
				{
					"roleId": 188,
					"permission": 2,
					"roleName": "Xem chi tiết quy tắc phân giao"
				},
				{
					"roleId": 127,
					"permission": 2,
					"roleName": "test xóa KH"
				},
				{
					"roleId": 62,
					"permission": 2,
					"roleName": "migration"
				},
				{
					"roleId": 168,
					"permission": 2,
					"roleName": "New role"
				},
				{
					"roleId": 261,
					"permission": 2,
					"roleName": "~!@#$%^&*()_+`-=[]\\{}|;:\",./<>?"
				},
				{
					"roleId": 14,
					"permission": 2,
					"roleName": "ADMIN_HTKH"
				},
				{
					"roleId": 328,
					"permission": 2,
					"roleName": "test"
				},
				{
					"roleId": 297,
					"permission": 2,
					"roleName": "BNgg_thêm,sửa,xóa qtv"
				},
				{
					"roleId": 165,
					"permission": 2,
					"roleName": "topic"
				},
				{
					"roleId": 12,
					"permission": 2,
					"roleName": "ADMIN_TINH"
				},
				{
					"roleId": 61,
					"permission": 2,
					"roleName": "phiếu hỗ trợ"
				},
				{
					"roleId": 33,
					"permission": 2,
					"roleName": "Vai trò admin tỉnh thành Hải Phòng"
				},
				{
					"roleId": 234,
					"permission": 2,
					"roleName": "1 quyền khác"
				},
				{
					"roleId": 235,
					"permission": 2,
					"roleName": "K hiển thị"
				},
				{
					"roleId": 187,
					"permission": 2,
					"roleName": "Chỉnh sửa quy tắc phân giao"
				},
				{
					"roleId": 32,
					"permission": 2,
					"roleName": "AM Test New"
				},
				{
					"roleId": 291,
					"permission": 2,
					"roleName": "Xem danh sách quản trị viên"
				},
				{
					"roleId": 179,
					"permission": 2,
					"roleName": "Xóa phân vùng Huyền"
				},
				{
					"roleId": 230,
					"permission": 2,
					"roleName": "Only custom field"
				},
				{
					"roleId": 181,
					"permission": 2,
					"roleName": "Tạo nhân viên kinh doanh"
				},
				{
					"roleId": 178,
					"permission": 2,
					"roleName": "Chỉnh sửa phân vùng Huyền"
				},
				{
					"roleId": 124,
					"permission": 2,
					"roleName": "test ABC"
				},
				{
					"roleId": 123,
					"permission": 2,
					"roleName": "Vai trò Nhung test"
				},
				{
					"roleId": 182,
					"permission": 2,
					"roleName": "Sửa nhân viên kinh doanh"
				},
				{
					"roleId": 232,
					"permission": 2,
					"roleName": "Vai trò thêm nhóa"
				},
				{
					"roleId": 259,
					"permission": 2,
					"roleName": "test 123"
				},
				{
					"roleId": 186,
					"permission": 2,
					"roleName": "Tạo quy tắc phân giao"
				},
				{
					"roleId": 122,
					"permission": 2,
					"roleName": "Quản lý transaction logs"
				},
				{
					"roleId": 229,
					"permission": 2,
					"roleName": "99"
				},
				{
					"roleId": 166,
					"permission": 2,
					"roleName": "Vai trò mới nhất"
				},
				{
					"roleId": 312,
					"permission": 2,
					"roleName": "Affiliate thành viên"
				},
				{
					"roleId": 327,
					"permission": 2,
					"roleName": "mai boi"
				},
				{
					"roleId": 228,
					"permission": 2,
					"roleName": "dd"
				},
				{
					"roleId": 296,
					"permission": 2,
					"roleName": "xxxx"
				},
				{
					"roleId": 184,
					"permission": 2,
					"roleName": "Xóa nhân viên kinh doanh"
				},
				{
					"roleId": 34,
					"permission": 2,
					"roleName": "Test0704"
				},
				{
					"roleId": 189,
					"permission": 2,
					"roleName": "Xóa quy tắc phân giao"
				},
				{
					"roleId": 60,
					"permission": 2,
					"roleName": "import"
				},
				{
					"roleId": 177,
					"permission": 2,
					"roleName": "Tạo phân vùng Huyền"
				},
				{
					"roleId": 204,
					"permission": 2,
					"roleName": "vai trò 3:50"
				},
				{
					"roleId": 270,
					"permission": 2,
					"roleName": "tesst nha"
				},
				{
					"roleId": 331,
					"permission": 2,
					"roleName": "bn123"
				},
				{
					"roleId": 16,
					"permission": 2,
					"roleName": "ADMIN_CAP1"
				},
				{
					"roleId": 313,
					"permission": 2,
					"roleName": "Affiliate đại lý"
				},
				{
					"roleId": 238,
					"permission": 2,
					"roleName": "Khách hàng 1"
				},
				{
					"roleId": 37,
					"permission": 2,
					"roleName": "ITKV4-Admin"
				},
				{
					"roleId": 211,
					"permission": 2,
					"roleName": "Tạo phân vùng Hậu test"
				},
				{
					"roleId": 240,
					"permission": 2,
					"roleName": "kkkkk"
				},
				{
					"roleId": 210,
					"permission": 2,
					"roleName": "vai tro liên hệ"
				},
				{
					"roleId": 176,
					"permission": 2,
					"roleName": "vt123"
				},
				{
					"roleId": 1000000000000000000,
					"permission": 2,
					"roleName": "AM"
				},
				{
					"roleId": 314,
					"permission": 2,
					"roleName": "ntbn_ql hóa đơn"
				},
				{
					"roleId": 233,
					"permission": 2,
					"roleName": "vai tro test"
				},
				{
					"roleId": 214,
					"permission": 2,
					"roleName": "testphanquyen"
				},
				{
					"roleId": 205,
					"permission": 2,
					"roleName": "vaitrochophieuhotro"
				},
				{
					"roleId": 239,
					"permission": 2,
					"roleName": "AQUANTRI"
				},
				{
					"roleId": 164,
					"permission": 2,
					"roleName": "Cấu hình custom field"
				}
			],
			"oldPermission": [],
			"showStandardSettingIcon": false,
			"touched": false,
			"showingFor": "categoryType",
			"showingConfig": {
				"serviceType": [
					"ON",
					"OS"
				],
				"categoryService": [
					"TatCaDanhMuc"
				],
				"serviceSelected": []
			},
			"isShowLabelDetail": true,
			"requiredConfig": {
				"requiredType": "always",
				"requiredCondition": "DKSPDV",
				"requiredServiceType": "all",
				"requiredServices": [],
				"serviceIds": []
			},
			"showLabel": true,
			"label": "Phương thức thanh toán",
			"formControlName": "PaymentMethod_Ndi6xb1puAPxIbVa"
		},
		"displayName": "Container",
		"custom": {},
		"parent": "ROOT",
		"hidden": false,
		"nodes": [],
		"linkedNodes": {}
	}
}
'
WHERE category = 'SERVICE' and (name in ('Tạo dịch vụ (Mặc định)') or (name in ('Tạo dịch vụ') and template_id is null));

ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";