-- Update API permission --

-- <PERSON> <PERSON><PERSON> nhân sự phụ trách cho khách hàng --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), '<PERSON><PERSON><PERSON> nhật nhân sự hỗ trợ khách hàng','CAP_NHAT_NHAN_SU_HO_TRO_KHACH_HANG', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHAN_SU_HO_TRO_KHACH_HANG' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHAN_SU_HO_TRO_KHACH_HANG' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/assign-assignee',
        'CAP_NHAT_NHAN_SU_HO_TRO_KHACH_HANG', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='CAP_NHAT_NHAN_SU_HO_TRO_KHACH_HANG' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'CAP_NHAT_NHAN_SU_HO_TRO_KHACH_HANG' LIMIT 1) LIMIT 1), 1, 1);

-------------------------------------

-- API Cập nhật phân vùng cho khách hàng  --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Cập nhật phân vùng khách hàng','CAP_NHAT_PHAN_VUNG_KHACH_HANG', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_PHAN_VUNG_KHACH_HANG' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_PHAN_VUNG_KHACH_HANG' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/update-partition',
        'CAP_NHAT_PHAN_VUNG_KHACH_HANG', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='CAP_NHAT_PHAN_VUNG_KHACH_HANG' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'CAP_NHAT_PHAN_VUNG_KHACH_HANG' LIMIT 1) LIMIT 1), 1, 1);
--------------------------------------------------------

-- API thêm khách hàng vào CDQC --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Thêm khách hàng vào chiến dịch quảng cáo','THEM_KHACH_HANG_VAO_CDQC', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_KHACH_HANG_VAO_CDQC' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_KHACH_HANG_VAO_CDQC' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/add-to-mc',
        'THEM_KHACH_HANG_VAO_CDQC', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='THEM_KHACH_HANG_VAO_CDQC' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'THEM_KHACH_HANG_VAO_CDQC' LIMIT 1) LIMIT 1), 1, 1);

------------------------------------------------------

-- API Gán nhân sự phụ trách cho tài khoản khách hàng --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Cập nhật nhân sự phụ trách tài khoản','CAP_NHAT_NHAN_SU_PHU_TRACH_TAI_KHOAN', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHAN_SU_PHU_TRACH_TAI_KHOAN' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHAN_SU_PHU_TRACH_TAI_KHOAN' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/portal/user/assign-assignee',
        'CAP_NHAT_NHAN_SU_PHU_TRACH_TAI_KHOAN', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='CAP_NHAT_NHAN_SU_PHU_TRACH_TAI_KHOAN' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'CAP_NHAT_NHAN_SU_PHU_TRACH_TAI_KHOAN' LIMIT 1) LIMIT 1), 1, 1);
------------------------------------------------

-- API Cập nhật phân vùng tài khoản khách hàng --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Cập nhật phân vùng tài khoản','CAP_NHAT_PHAN_VUNG_TAI_KHOAN', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_PHAN_VUNG_TAI_KHOAN' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_PHAN_VUNG_TAI_KHOAN' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/portal/user/update-partition',
        'CAP_NHAT_PHAN_VUNG_TAI_KHOAN', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='CAP_NHAT_PHAN_VUNG_TAI_KHOAN' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'CAP_NHAT_PHAN_VUNG_TAI_KHOAN' LIMIT 1) LIMIT 1), 1, 1);
---------------------------------------------

-- API Thêm tài khoản khách hàng vào chiến dịch quảng cáo --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Thêm tài khoảng khách hàng vào chiến dịch quảng cáo','THEM_TAI_KHOAN_VAO_CDQC', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_TAI_KHOAN_VAO_CDQC' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_TAI_KHOAN_VAO_CDQC' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/portal/user/add-to-mc',
        'THEM_TAI_KHOAN_VAO_CDQC', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='THEM_TAI_KHOAN_VAO_CDQC' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'THEM_TAI_KHOAN_VAO_CDQC' LIMIT 1) LIMIT 1), 1, 1);
--------------------------------------------------


-- API Cập nhật nhân sự phụ trách liên hệ --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Cập nhật nhân sự phu trách liên hệ','CAP_NHAT_NHAN_SU_PHU_TRACH_LIEN_HE', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHAN_SU_PHU_TRACH_LIEN_HE' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_NHAN_SU_PHU_TRACH_LIEN_HE' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/customer-contact/assign-assignee',
        'CAP_NHAT_NHAN_SU_PHU_TRACH_LIEN_HE', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='CAP_NHAT_NHAN_SU_PHU_TRACH_LIEN_HE' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'CAP_NHAT_NHAN_SU_PHU_TRACH_LIEN_HE' LIMIT 1) LIMIT 1), 1, 1);

--------------------------------------------------

-- API Cập nhật phân vùng liên hệ  --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Cập nhật phân vùng liên hệ','CAP_NHAT_PHAN_VUNG_LIEN_HE', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_PHAN_VUNG_LIEN_HE' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_PHAN_VUNG_LIEN_HE' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/customer-contact/add-contact-to-partition',
        'CAP_NHAT_PHAN_VUNG_LIEN_HE', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='CAP_NHAT_PHAN_VUNG_LIEN_HE' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'CAP_NHAT_PHAN_VUNG_LIEN_HE' LIMIT 1) LIMIT 1), 1, 1);
----------------------------------------------------

-- API Thêm liên hệ vào chiến dịch quảng cáo --
INSERT INTO "vnpt_dev"."permission" ("id", "name", "code", "parent_id", "priority")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission), 'Thêm liên hệ vào chiến dịch quảng cáo','THEM_LIEN_HE_VAO_CDQC', -1, (SELECT max(priority) + 1 FROM vnpt_dev.permission));

INSERT INTO "vnpt_dev"."permission_portal" ("id", "permission_id", "portal_id")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_LIEN_HE_VAO_CDQC' LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1));

INSERT INTO "vnpt_dev"."roles_permissions" ("id", "role_id", "permission_id", "allow_edit")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_LIEN_HE_VAO_CDQC' LIMIT 1), 1);

INSERT INTO "vnpt_dev"."apis" ("id", "api_path", "api_code", "method")
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/customer-contact/add-to-mc',
        'THEM_LIEN_HE_VAO_CDQC', 'POST');

INSERT INTO "vnpt_dev"."api_permission" ("id", "api_id", "permission_portal_id", "map_permission_portal", "delete_flag")
VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.api_permission), (SELECT id FROM vnpt_dev.apis WHERE api_code='THEM_LIEN_HE_VAO_CDQC' AND method = 'POST' LIMIT 1),
        (SELECT id FROM vnpt_dev.permission_portal WHERE portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' LIMIT 1) AND permission_id = (SELECT id FROM vnpt_dev.permission WHERE "code" = 'THEM_LIEN_HE_VAO_CDQC' LIMIT 1) LIMIT 1), 1, 1);

-------------------------------------------------------------
-- REFRESH  --
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;


-- Tạo function quét danh sách object quy tắc phân giao theo objectType và Condition query
DROP FUNCTION IF EXISTS "vnpt_dev"."func_list_object_by_condition_query"(text, int4);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_list_object_by_condition_query"("conditionquery" text, "objecttype" int4)
    RETURNS TABLE("id" int8) AS $BODY$
DECLARE
    mQuery text;
    sQuery text;
BEGIN
    CASE objectType
        WHEN 0 THEN
            mQuery = 'SELECT users.id as id
                    FROM vnpt_dev.users WHERE name is not null and assignee_id is null and ';
        WHEN 1 THEN
            mQuery = 'SELECT customer_ticket.id as id FROM vnpt_dev.customer_ticket WHERE name is not null and assignee_id is null and ';
        WHEN 2 THEN
            mQuery = 'SELECT enterprise.id as id FROM vnpt_dev.enterprise WHERE name is not null and assignee_id is null and ';
        ELSE
            mQuery = 'SELECT customer_contact.id as id FROM vnpt_dev.customer_contact WHERE name is not null and assignee_id is null and ';
        END CASE;
    sQuery = conditionQuery;
    mQuery = CONCAT(mQuery, sQuery);
    RAISE NOTICE 'mQuery: %', mQuery;
    RETURN QUERY EXECUTE mQuery;
END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;
DELETE FROM vnpt_dev.schedules WHERE bean_name = 'custom-field' AND method_name = 'cleanField';
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('custom-field', 'cleanField', NULL, '0 */30 * ? * *', 'cleanField', 1, 'batch', NULL, 'batch', NULL);
