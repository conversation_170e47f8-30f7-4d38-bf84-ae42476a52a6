--=================== SUBSCRIPTION - Thuê bao =========================
--=============== Thêm fields vào bảng custom_field ===============
DELETE FROM vnpt_dev.custom_field WHERE "code" IN ('taxCode', 'lastName', 'firstName', 'email', 'phoneNumber','isActivated', 'notificationType');
INSERT INTO vnpt_dev.custom_field ("name", code, is_standard, "type", category, config, lst_permission)
VALUES
	('Mã số thuế', 'taxCode', TRUE, 'NUMBER', 'SUBSCRIPTION',
	'{
  "label": "Mã số thuế",
  "labelEnabled": true,
  "hintText": "Nhập mã số thuế",
  "isUnique": null,
  "mandatory": "ALWAYS",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": null,
  "defaultValue": null,
  "pattern": "^[0-9]*$",
  "lstPatternToken": [
    3
  ],
  "patternCombination": 1,
  "maxLength": 13,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]'),

('Họ', 'lastName', TRUE, 'SINGLE_LINE_TEXT', 'SUBSCRIPTION',
	'{
  "label": "Họ",
  "labelEnabled": true,
  "hintText": "Nhập họ",
  "isUnique": null,
  "mandatory": "ALWAYS",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": null,
  "defaultValue": null,
  "pattern": "^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$",
  "lstPatternToken": [
    1,
    2,
    3,
    4
  ],
  "patternCombination": 1,
  "maxLength": 20,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]'),

('Tên', 'firstName', TRUE, 'SINGLE_LINE_TEXT', 'SUBSCRIPTION',
	'{
  "label": "Tên",
  "labelEnabled": true,
  "hintText": "Nhập tên",
  "isUnique": null,
  "mandatory": "ALWAYS",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": null,
  "defaultValue": null,
  "pattern": "^[aàảãáạăằẳẵắặâầẩẫấậbcdđeèẻẽéẹêềểễếệfghiìỉĩíịjklmnoòỏõóọôồổỗốộơờởỡớợpqrstuùủũúụưừửữứựvwxyỳỷỹýỵzAÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬBCDĐEÈẺẼÉẸÊỀỂỄẾỆFGHIÌỈĨÍỊJKLMNOÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢPQRSTUÙỦŨÚỤƯỪỬỮỨỰVWXYỲỶỸÝỴZ\\d\\W]*$",
  "lstPatternToken": [
    1,
    2,
    3,
    4
  ],
  "patternCombination": 1,
  "maxLength": 20,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]'),

('Email đăng nhập', 'email', TRUE, 'EMAIL', 'SUBSCRIPTION',
	'{
  "label": "Email đăng nhập",
  "labelEnabled": true,
  "hintText": "Email đăng nhập",
  "isUnique": null,
  "mandatory": "ALWAYS",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": null,
  "defaultValue": null,
  "pattern": null,
  "lstPatternToken": null,
  "patternCombination": null,
  "maxLength": 99,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]'),

('Số điện thoại liên hệ', 'phoneNumber', TRUE, 'NUMBER', 'SUBSCRIPTION',
	'{
  "label": "Số điện thoại liên hệ́",
  "labelEnabled": true,
  "hintText": "Nhập số điện thoại liên hệ",
  "isUnique": null,
  "mandatory": "ALWAYS",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": null,
  "defaultValue": null,
  "pattern": "^[0-9]*$",
  "lstPatternToken": [
    3
  ],
  "patternCombination": 1,
  "maxLength": 12,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]'),

('Kích hoạt ngay', 'isActivated', TRUE, 'CHECKBOX', 'SUBSCRIPTION',
	'{
  "label": "Kích hoạt ngay",
  "labelEnabled": true,
  "hintText": "",
  "isUnique": null,
  "mandatory": "NONE",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": ["Kích hoạt ngay"],
  "defaultValue": "",
  "pattern": null,
  "lstPatternToken": null,
  "patternCombination": null,
  "maxLength": null,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]'),

('Gửi thông tin tài khoản qua', 'notificationType', TRUE, 'CHECKBOX', 'SUBSCRIPTION',
	'{
  "label": "Gửi thông tin tài khoản qua",
  "labelEnabled": true,
  "hintText": "",
  "isUnique": null,
  "mandatory": "ALWAYS",
  "mandatoryCondition": {
    "action": "CREATE_SUBSCRIPTION",
    "applyFor": [],
    "listServices": [],
    "requiredServiceType": null
  },
  "tooltipsEnabled": false,
  "tooltipsContent": "",
  "noteEnabled": false,
  "noteContent": "",
  "smeEnabled": false,
  "devEnabled": false,
  "adminEnabled": true,
  "displayOnDetailPage": null,
  "canEdit": false,
  "lstValue": ["Email", "SMS"],
  "defaultValue": "",
  "pattern": null,
  "lstPatternToken": null,
  "patternCombination": null,
  "maxLength": null,
  "displayFormat": null,
  "defaultDisplay": null,
  "uploadType": null,
  "uploadExtension": null,
  "uploadMaxSize": null,
  "uploadMaxFile": null,
  "getUploadMaxFile": null,
  "urlMaxNum": null,
  "urlPattern": null
}',
'[]');


--=============== Thêm layout mặc định subscription - thuê bao ======================
DELETE FROM "vnpt_dev"."custom_layout" WHERE "name" IN ('Tạo thuê bao (Khách hàng Doanh nghiệp) - Admin', 'Tạo thuê bao (Khách hàng hộ kinh doanh) - Admin', 'Tạo thuê bao (Khách hàng cá nhân) - Admin', 'Tạo thuê bao - SME doanh nghiệp', 'Tạo thuê bao - SME hộ kinh doanh', 'Tạo thuê bao - SME cá nhân');

INSERT INTO "vnpt_dev"."custom_layout"("name", "category", "is_template", "template_id", "is_default", "lst_standard_field", "lst_custom_field", "sme_enabled", "dev_enabled", "admin_enabled", "layout_content", "created_at", "created_by", "modified_at", "modified_by", "status", "deleted_flag", "data_dependency", "lst_permission", "customer_type", "portal_type") VALUES
('Tạo thuê bao (Khách hàng Doanh nghiệp) - Admin', 'SUBSCRIPTION', 'f', (SELECT id FROM custom_layout WHERE name = 'Tạo thuê bao'), 't', '[]', '[]', null, 't', 't', '{}', '2023-02-07 00:00:00', 1, '2023-02-07 00:00:00', 1, 1, 1, '[]', '[]', 'KHDN', '1');

INSERT INTO "vnpt_dev"."custom_layout"("name", "category", "is_template", "template_id", "is_default", "lst_standard_field", "lst_custom_field", "sme_enabled", "dev_enabled", "admin_enabled", "layout_content", "created_at", "created_by", "modified_at", "modified_by", "status", "deleted_flag", "data_dependency", "lst_permission", "customer_type", "portal_type") VALUES
('Tạo thuê bao (Khách hàng hộ kinh doanh) - Admin', 'SUBSCRIPTION', 'f', (SELECT id FROM custom_layout WHERE name = 'Tạo thuê bao'), 't', '[]', '[]', null, 't', 't', '{}', '2023-02-07 00:00:00', 1, '2023-02-07 00:00:00', 1, 1, 1, '[]', '[]', 'HKD', '1');

INSERT INTO "vnpt_dev"."custom_layout"("name", "category", "is_template", "template_id", "is_default", "lst_standard_field", "lst_custom_field", "sme_enabled", "dev_enabled", "admin_enabled", "layout_content", "created_at", "created_by", "modified_at", "modified_by", "status", "deleted_flag", "data_dependency", "lst_permission", "customer_type", "portal_type") VALUES
('Tạo thuê bao (Khách hàng cá nhân) - Admin', 'SUBSCRIPTION', 'f', (SELECT id FROM custom_layout WHERE name = 'Tạo thuê bao'), 't', '[]', '[]', null, 't', 't', '{}', '2023-02-07 00:00:00', 1, '2023-02-07 00:00:00', 1, 1, 1, '[]', '[]', 'CN', '1');

INSERT INTO "vnpt_dev"."custom_layout"("name", "category", "is_template", "template_id", "is_default", "lst_standard_field", "lst_custom_field", "sme_enabled", "dev_enabled", "admin_enabled", "layout_content", "created_at", "created_by", "modified_at", "modified_by", "status", "deleted_flag", "data_dependency", "lst_permission", "customer_type", "portal_type") VALUES
('Tạo thuê bao - SME doanh nghiệp', 'SUBSCRIPTION', 'f', (SELECT id FROM custom_layout WHERE name = 'Tạo thuê bao'), 't', '[]', '[]', 't', null, null, '{}', '2023-02-07 00:00:00', 1, '2023-02-07 00:00:00', 1, 1, 1, '[]', '[]', 'KHDN', '3');

INSERT INTO "vnpt_dev"."custom_layout"("name", "category", "is_template", "template_id", "is_default", "lst_standard_field", "lst_custom_field", "sme_enabled", "dev_enabled", "admin_enabled", "layout_content", "created_at", "created_by", "modified_at", "modified_by", "status", "deleted_flag", "data_dependency", "lst_permission", "customer_type", "portal_type") VALUES
('Tạo thuê bao - SME hộ kinh doanh', 'SUBSCRIPTION', 'f', (SELECT id FROM custom_layout WHERE name = 'Tạo thuê bao'), 't', '[]', '[]', 't', null, null, '{}', '2023-02-07 00:00:00', 1, '2023-02-07 00:00:00', 1, 1, 1, '[]', '[]', 'HKD', '3');

INSERT INTO "vnpt_dev"."custom_layout"("name", "category", "is_template", "template_id", "is_default", "lst_standard_field", "lst_custom_field", "sme_enabled", "dev_enabled", "admin_enabled", "layout_content", "created_at", "created_by", "modified_at", "modified_by", "status", "deleted_flag", "data_dependency", "lst_permission", "customer_type", "portal_type") VALUES
('Tạo thuê bao - SME cá nhân', 'SUBSCRIPTION', 'f', (SELECT id FROM custom_layout WHERE name = 'Tạo thuê bao'), 't', '[]', '[]', 't', null, null, '{}', '2023-02-07 00:00:00', 1, '2023-02-07 00:00:00', 1, 1, 1, '[]', '[]', 'CN', '3');

--============== Update custom_layout subscription - tạo thuê bao  =================
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

UPDATE "vnpt_dev"."custom_layout"
    SET lst_standard_field = raw.lst_standard_field,
    layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["4F5rTYtOJi", "i1YtAjku78"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "4F5rTYtOJi": {"type": {"resolvedName": "CustomerInfor"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "CustomerInfor_cu8S4QLVBjiinpaK", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "i1YtAjku78": {"type": {"resolvedName": "SubscriptionDetailAdminDev"}, "nodes": [], "props": {"label": "Thông tin đơn hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "SubscriptionDetailAdminDev_yEBOhUGB62Em9kSA", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}',
    detail_layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": [], "props": {"id": "detailViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}}'
	FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and is_standard = true) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng cá nhân) - Admin');

UPDATE "vnpt_dev"."custom_layout"
    SET lst_standard_field = raw.lst_standard_field,
    layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["4F5rTYtOJi", "NU3JALMBkh"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "4F5rTYtOJi": {"type": {"resolvedName": "CustomerInfor"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "CustomerInfor_cu8S4QLVBjiinpaK", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "NU3JALMBkh": {"type": {"resolvedName": "SubscriptionDetailAdminDev"}, "nodes": [], "props": {"label": "Thông tin đơn hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "SubscriptionDetailAdminDev_lnvrEHPlXEK2B7bJ", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}',
    detail_layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": [], "props": {"id": "detailViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}}'
	FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and is_standard = true) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng hộ kinh doanh) - Admin');

UPDATE "vnpt_dev"."custom_layout"
    SET lst_standard_field = raw.lst_standard_field,
    layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["4F5rTYtOJi", "s5sF9lvTqP"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "4F5rTYtOJi": {"type": {"resolvedName": "CustomerInfor"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "CustomerInfor_cu8S4QLVBjiinpaK", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "s5sF9lvTqP": {"type": {"resolvedName": "SubscriptionDetailAdminDev"}, "nodes": [], "props": {"label": "Thông tin đơn hàng", "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "SubscriptionDetailAdminDev_ausYDQfd3q89Bv6I", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}',
    detail_layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": [], "props": {"id": "detailViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": false, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}}'
	FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and is_standard = true) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng Doanh nghiệp) - Admin');

UPDATE "vnpt_dev"."custom_layout"
    SET layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["pj3bZcZoph"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": true, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "ClQro0gQqX": {"type": {"resolvedName": "Container"}, "nodes": ["a6YmCBgr1P"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "IsQSVEU9Wz", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "IsQSVEU9Wz": {"type": {"resolvedName": "Container"}, "nodes": ["ClQro0gQqX"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "pj3bZcZoph", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "Mtb2LHvGtv": {"type": {"resolvedName": "Container"}, "nodes": ["j8SqHyZP_A"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "uLSH_qE3KD", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "YZkTWxiQSx": {"type": {"resolvedName": "PaymentProgress1"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "tKaYeuu3Ix", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "a6YmCBgr1P": {"type": {"resolvedName": "LayoutPaymentProgress"}, "nodes": [], "props": {"step": 1, "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ClQro0gQqX", "isCanvas": false, "displayName": "Container", "linkedNodes": {"section-1": "uTSfmXCdoW", "section-2": "uLSH_qE3KD"}}, "j8SqHyZP_A": {"type": {"resolvedName": "OrderSummaryStep1"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Mtb2LHvGtv", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "pj3bZcZoph": {"type": {"resolvedName": "PaymentSme"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "PaymentSme_hEf2oeYBWS3qHDvU", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {"section-1": "IsQSVEU9Wz"}}, "tKaYeuu3Ix": {"type": {"resolvedName": "Container"}, "nodes": ["YZkTWxiQSx"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "uTSfmXCdoW", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "uLSH_qE3KD": {"type": {"resolvedName": "Container"}, "nodes": ["Mtb2LHvGtv"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "a6YmCBgr1P", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "uTSfmXCdoW": {"type": {"resolvedName": "Container"}, "nodes": ["tKaYeuu3Ix"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "a6YmCBgr1P", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}',
    detail_layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["f17Bbs1yal"], "props": {"id": "detailViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": true, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "f17Bbs1yal": {"type": {"resolvedName": "SubscriptionDetailSME"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categorySetting", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "SubscriptionDetailSME_zuXcyqEH2kb9FEGj", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}'
	FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and is_standard = true ) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao - SME doanh nghiệp', 'Tạo thuê bao - SME hộ kinh doanh');

UPDATE "vnpt_dev"."custom_layout"
    SET layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["pj3bZcZoph"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": true, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "ClQro0gQqX": {"type": {"resolvedName": "Container"}, "nodes": ["a6YmCBgr1P"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "IsQSVEU9Wz", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "IsQSVEU9Wz": {"type": {"resolvedName": "Container"}, "nodes": ["ClQro0gQqX"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "pj3bZcZoph", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "Mtb2LHvGtv": {"type": {"resolvedName": "Container"}, "nodes": ["j8SqHyZP_A"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "uLSH_qE3KD", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "YZkTWxiQSx": {"type": {"resolvedName": "PaymentProgress1"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "tKaYeuu3Ix", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "a6YmCBgr1P": {"type": {"resolvedName": "LayoutPaymentProgress"}, "nodes": [], "props": {"step": 1, "isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ClQro0gQqX", "isCanvas": false, "displayName": "Container", "linkedNodes": {"section-1": "uTSfmXCdoW", "section-2": "uLSH_qE3KD"}}, "j8SqHyZP_A": {"type": {"resolvedName": "OrderSummaryStep1"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "Mtb2LHvGtv", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "pj3bZcZoph": {"type": {"resolvedName": "PaymentSme"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": true, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "", "displayPortal": ["dev"], "oldPermission": [], "showingConfig": {"serviceType": [], "categoryService": "", "serviceSelected": []}, "formControlName": "PaymentSme_hEf2oeYBWS3qHDvU", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {"section-1": "IsQSVEU9Wz"}}, "tKaYeuu3Ix": {"type": {"resolvedName": "Container"}, "nodes": ["YZkTWxiQSx"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "uTSfmXCdoW", "isCanvas": false, "displayName": "Container", "linkedNodes": {}}, "uLSH_qE3KD": {"type": {"resolvedName": "Container"}, "nodes": ["Mtb2LHvGtv"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "a6YmCBgr1P", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "uTSfmXCdoW": {"type": {"resolvedName": "Container"}, "nodes": ["tKaYeuu3Ix"], "props": {"color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["0", "0", "0", "0"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": "#fff", "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {}, "hidden": false, "parent": "a6YmCBgr1P", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}',
    detail_layout_content = '{"ROOT": {"type": {"resolvedName": "AppContainer"}, "nodes": ["BSPaHVfbmo"], "props": {"id": "detailViewport", "color": "#000", "width": "100%", "height": "auto", "margin": ["0", "0", "0", "0"], "radius": 0, "shadow": 0, "padding": ["14", "14", "14", "14"], "canResize": false, "fillSpace": "no", "alignItems": "flex-start", "background": {"a": 1, "b": 255, "g": 255, "r": 255}, "disableDrag": true, "flexDirection": "column", "justifyContent": "flex-start"}, "custom": {"displayName": "App"}, "hidden": false, "isCanvas": true, "displayName": "App", "linkedNodes": {}}, "BSPaHVfbmo": {"type": {"resolvedName": "SubscriptionDetailSME"}, "nodes": [], "props": {"isValid": true, "touched": false, "canAction": false, "noteConfig": {"noteType": [], "noteMessage": "", "tooltipMessage": ""}, "permission": [], "showingFor": "categorySetting", "displayPortal": ["dev", "admin", "sme"], "oldPermission": [], "showingConfig": {"serviceType": ["ON", "OS"], "categoryService": ["TatCaDanhMuc"], "serviceSelected": []}, "formControlName": "SubscriptionDetailSME_tC51bLOk0nie2S4W", "isStandardField": false, "showStandardSettingIcon": false, "showSettingDialogWhenOpen": false}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": true, "displayName": "Container", "linkedNodes": {}}}'
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao - SME cá nhân');

ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";


--==================== update field config =====================
UPDATE vnpt_dev.custom_field
SET config = jsonb_set(config::jsonb,'{adminEnabled}','true')
WHERE custom_field.category in ('PRICING','SERVICE') and custom_field.is_standard = true;



