DROP TABLE IF EXISTS vnpt_dev.address_categories;
CREATE TABLE vnpt_dev.address_categories (
                                             id bigserial NOT NULL PRIMARY KEY,  -- ID danh mục địa chỉ
                                             name varchar(100) NOT NULL,  -- Tên danh mục địa chỉ
                                             description varchar(100) NOT NULL,  -- <PERSON><PERSON> tả danh mục địa chỉ
                                             status int2 DEFAULT 0,  -- Tr<PERSON>ng thái (1: bật, 0: tắt), mặc định là tắt
                                             created_at timestamp,  -- Thời gian tạo danh mục địa chỉ
                                             created_by int8,  -- ID người tạo danh mục
                                             modified_at timestamp,  -- Thời gian cập nhật danh mục địa chỉ
                                             modified_by int8,  -- ID người cập nhật danh mục
                                             deleted_flag int2 DEFAULT 1  -- Trạng thái xóa (0: Đ<PERSON> xóa, 1: <PERSON>ưa xóa)
);

-- Thêm comment cho từng cột
COMMENT ON TABLE vnpt_dev.address_categories IS 'Lưu và quản lý danh mục sổ địa chỉ';

COMMENT ON COLUMN vnpt_dev.address_categories.id IS 'ID danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.name IS 'Tên danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.description IS 'Mô tả danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.status IS 'Trạng thái (1: bật, 0: tắt)';
COMMENT ON COLUMN vnpt_dev.address_categories.created_at IS 'Thời gian tạo danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.created_by IS 'ID người tạo danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.modified_at IS 'Thời gian cập nhật danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.modified_by IS 'ID người cập nhật danh mục địa chỉ';
COMMENT ON COLUMN vnpt_dev.address_categories.deleted_flag IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';

-- Thêm comment cho cột type của bảng address
COMMENT ON COLUMN vnpt_dev.address.type IS 'ID tương ứng của bảng address_categories';

-- Thêm dữ liệu mặc định: địa chỉ xuất hóa đơn (id = 0), địa chỉ lắp đặt (id = 1) và địa chỉ giao hàng (id = 2)
DELETE FROM vnpt_dev.address_categories WHERE id IN (0,1,2);
INSERT INTO vnpt_dev.address_categories (id, name, description, status, created_at, created_by, modified_at , deleted_flag)
VALUES
    (0, 'Địa chỉ xuất hóa đơn', 'Dùng để lưu địa chỉ xuất hóa đơn của khách hàng', 1, now(), 0, now(), 1),
    (1, 'Địa chỉ lắp đặt', 'Dùng để lưu địa chỉ lắp đặt thiết bị hoặc dịch vụ', 1, now(), 0, now(), 1),
    (2, 'Địa chỉ giao hàng', 'Dùng để lưu địa chỉ giao hàng thiết bị hoặc dịch vụ', 1, now(), 0, now(), 1);

-- Đặt lại sequence để đảm bảo id tự tăng bắt đầu từ 3 trở đi
SELECT setval(pg_get_serial_sequence('vnpt_dev.address_categories', 'id'), 3, false);
