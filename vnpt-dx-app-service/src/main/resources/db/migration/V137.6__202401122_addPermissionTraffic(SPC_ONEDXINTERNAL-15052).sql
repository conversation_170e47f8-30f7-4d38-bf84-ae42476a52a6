----------------------------------------------QUAN_LY_LUU_LUONG----------------------------------------------
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_LUU_LUONG');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_LUU_LUONG');
DELETE FROM vnpt_dev.permission WHERE code = 'QUAN_LY_LUU_LUONG';
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG');
DELETE FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Quản lý ví lưu lượng', 'QUAN_LY_VI_LUU_LUONG', -1, (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

----permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_VI_LUU_LUONG'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_VI_LUU_LUONG'), 1);


---------------------------Xem danh sách sách ví lưu lượng admin
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_VI_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TRAFFIC_LIST';
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN';

----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem danh sách ví lưu lượng',
        'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/wallet/info', 'ROLE_ADMIN_VIEW_TRAFFIC_LIST', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TRAFFIC_LIST' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_VI_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xem danh sách sách ví lưu lượng sme
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_VI_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_TRAFFIC_LIST';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem danh sách sách ví lưu lượng',
        'XEM_DANH_SACH_VI_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/wallet/info', 'ROLE_SME_VIEW_TRAFFIC_LIST', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_TRAFFIC_LIST' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_VI_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Xác thực ví lưu lượng ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XAC_THUC_VI_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_ACCURACY_WALLET_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xác thực ví lưu lượng',
        'XAC_THUC_VI_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XAC_THUC_VI_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/wallet/accuracy', 'ROLE_ADMIN_ACCURACY_WALLET_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_ACCURACY_WALLET_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XAC_THUC_VI_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xác thực ví lưu lượng SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XAC_THUC_VI_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_ACCURACY_WALLET_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xác thực ví lưu lượng',
        'XAC_THUC_VI_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XAC_THUC_VI_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XAC_THUC_VI_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XAC_THUC_VI_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/wallet/accuracy', 'ROLE_SME_ACCURACY_WALLET_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_ACCURACY_WALLET_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XAC_THUC_VI_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Xem chi tiết ví lưu lượng ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_VI_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_WALLET_TRAFFIC_DETAIL';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem chi tiết ví lưu lượng',
        'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/wallet/detail/bss', 'ROLE_ADMIN_VIEW_WALLET_TRAFFIC_DETAIL', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_WALLET_TRAFFIC_DETAIL' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_VI_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xem chi tiết ví lưu lượng SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_VI_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_WALLET_TRAFFIC_DETAIL';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem chi tiết ví lưu lượng',
        'XEM_CHI_TIET_VI_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/wallet/detail/bss', 'ROLE_SME_VIEW_WALLET_TRAFFIC_DETAIL', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_WALLET_TRAFFIC_DETAIL' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_VI_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Chia sẻ lưu lượng ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CHIA_SE_VI_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_SHARE_WALLET_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chia sẻ lưu lượng',
        'CHIA_SE_VI_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CHIA_SE_VI_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/create', 'ROLE_ADMIN_SHARE_WALLET_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_SHARE_WALLET_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CHIA_SE_VI_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Chia sẻ lưu lượng SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CHIA_SE_VI_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_WALLET_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chia sẻ lưu lượng',
        'CHIA_SE_VI_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_VI_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CHIA_SE_VI_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CHIA_SE_VI_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/create', 'ROLE_SME_WALLET_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_WALLET_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CHIA_SE_VI_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Xem lịch sử hoạt động ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_HISTORY_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem lịch sử hoạt động',
        'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/history/list', 'ROLE_ADMIN_VIEW_HISTORY_TRAFFIC', 'GET');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_HISTORY_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xem lịch sử hoạt động SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_HISTORY_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem lịch sử hoạt động',
        'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/history/list', 'ROLE_SME_VIEW_HISTORY_TRAFFIC', 'GET');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_HISTORY_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_LICH_SU_HOAT_DONG_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Xóa lịch sử hoạt động ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_DELETE_HISTORY_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xóa lịch sử hoạt động',
        'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/history/delete', 'ROLE_ADMIN_DELETE_HISTORY_TRAFFIC', 'DELETE');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_DELETE_HISTORY_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xóa lịch sử hoạt động SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_DELETE_HISTORY_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xóa lịch sử hoạt động',
        'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/history/delete', 'ROLE_SME_DELETE_HISTORY_TRAFFIC', 'DELETE');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_DELETE_HISTORY_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XOA_LICH_SU_HOAT_DONG_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Cấu hình quản lý lưu lượng ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CONFIG_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Cấu hình quản lý lưu lượng',
        'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_VI_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/config', 'ROLE_ADMIN_CONFIG_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_CONFIG_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CAU_HINH_QUAN_LY_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

----config_delete
DELETE FROM vnpt_dev.system_params WHERE param_type = 'TRANSACTION_LOG_TRAFFIC_WALLET_DELETE_CONFIG';

INSERT INTO vnpt_dev.system_params (param_name, param_type, time_value, time_type, status)
VALUES ('Cài đặt xóa lịch sử giao dịch chia sẻ lưu lượng', 'TRANSACTION_LOG_TRAFFIC_WALLET_DELETE_CONFIG', 3, 2, -1);

DELETE FROM vnpt_dev.schedules WHERE method_name = 'deleteTransactionLogTrafficByBatch' AND bean_name = 'traffic-history';

INSERT INTO vnpt_dev.schedules (bean_name, method_name, cron_expression, remark, job_status, created_by, modified_by)
VALUES ('traffic-history', 'deleteTransactionLogTrafficByBatch', '0 0 0 * * ?', 'traffic history', 1, 'batch', 'batch');









----------------------------------------------QUAN_LY_CHIA_SE_LUU_LUONG----------------------------------------------
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG');
DELETE FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Quản lý chia sẻ lưu lượng', 'QUAN_LY_CHIA_SE_LUU_LUONG', -1, (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

----permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG'), 1);

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG'), 1);

---------------------------Xem danh sách chia sẻ ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem danh sách chia sẻ',
        'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/list', 'ROLE_ADMIN_VIEW_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xem danh sách chia sẻ SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem danh sách chia sẻ',
        'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/list', 'ROLE_SME_VIEW_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_CHIA_SE_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Xem chi tiết người được chia sẻ ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_SHARE_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem chi tiết người được chia sẻ',
        'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/detail', 'ROLE_ADMIN_VIEW_SHARE_DETAIL_TRAFFIC', 'GET');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_SHARE_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xem chi tiết người được chia sẻ SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_SHARE_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xem chi tiết người được chia sẻ',
        'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/detail', 'ROLE_SME_VIEW_SHARE_DETAIL_TRAFFIC', 'GET');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_VIEW_SHARE_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_NGUOI_DUOC_CHIA_SE_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);
---------------------------Chia sẻ lưu lượng ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CHIA_SE_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chia sẻ lưu lượng',
        'CHIA_SE_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CHIA_SE_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/create', 'ROLE_ADMIN_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CHIA_SE_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Chia sẻ lưu lượng SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CHIA_SE_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chia sẻ lưu lượng',
        'CHIA_SE_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CHIA_SE_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CHIA_SE_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CHIA_SE_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/create', 'ROLE_SME_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CHIA_SE_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);






























----------------------------------------------QUAN_LY_GIAO_DICH_LUU_LUONG----------------------------------------------
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG');
DELETE FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission), 'Quản lý transaction logs lưu lượng', 'QUAN_LY_GIAO_DICH_LUU_LUONG', -1, (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

----permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG'), 1);

---------------------------Danh sách giao dịch
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TRANSACTION_LOGS_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Danh sách giao dịch',
        'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/transaction/list', 'ROLE_ADMIN_VIEW_TRANSACTION_LOGS_TRAFFIC', 'GET');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TRANSACTION_LOGS_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_DANH_SACH_GIAO_DICH_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Cập nhật thông tin doanh nghiệp
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_CUSTOMER_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Cập nhật thông tin doanh nghiệp',
        'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/business', 'ROLE_ADMIN_UPDATE_CUSTOMER_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_CUSTOMER_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CAP_NHAT_THONG_TIN_DOANH_NGHIEP_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Chi tiết giao dịch
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TRANSACTION_LOGS_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chi tiết giao dịch',
        'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/transaction/detail/{id}', 'ROLE_ADMIN_VIEW_TRANSACTION_LOGS_DETAIL_TRAFFIC', 'GET');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TRANSACTION_LOGS_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XEM_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Đánh dấu đã xử lý sự cố ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TICK_TRANSACTION_LOGS_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Đánh dấu đã xử lý sự cố',
        'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/transaction/tick/{id}', 'ROLE_ADMIN_VIEW_TICK_TRANSACTION_LOGS_DETAIL_TRAFFIC', 'PUT');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_TICK_TRANSACTION_LOGS_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'DANH_DAU_DA_XU_LY_SU_CO_CHI_TIET_GIAO_DICH_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Gửi lại API giao dịch lưu lượn ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_RESEND_API_ACTIVITY_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Gửi lại API',
        'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_GIAO_DICH_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/transaction/resend/{id}', 'ROLE_ADMIN_VIEW_RESEND_API_ACTIVITY_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_VIEW_RESEND_API_ACTIVITY_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'GUI_LAI_API_GIAO_DICH_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);


REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;