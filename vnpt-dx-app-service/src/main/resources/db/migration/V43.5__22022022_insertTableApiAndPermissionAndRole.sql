INSERT INTO vnpt_dev.apis
(id, api_path, api_code, method)
VALUES
((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/error-intergration', 'ROLE_ADMIN_VIEW_LIST_ERROR_1', 'GET'),
((SELECT max(id) + 2 FROM vnpt_dev.apis), '/api/admin-portal/error-intergration/{id}', 'ROLE_ADMIN_VIEW_DETAIL_ERROR_1', 'GET'),
((SELECT max(id) + 3 FROM vnpt_dev.apis), '/api/admin-portal/error-intergration/retry/{id}', 'ROLE_ADMIN_PROCESS_ERROR_1', 'PUT');


INSERT INTO vnpt_dev."permission"
(id, "name", code, parent_id, priority)
VALUES
((SELECT max(id) + 1 FROM vnpt_dev."permission"), '<PERSON><PERSON><PERSON><PERSON> lý sự cố', 'QUAN_LY_SU_CO_1', -1, (SELECT max(priority) + 1 FROM vnpt_dev."permission"));

INSERT INTO vnpt_dev."permission"
(id, "name", code, parent_id, priority)
VALUES
((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xem danh sách sự cố', 'XEM_DANH_SACH_SU_CO_1', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_SU_CO_1'), (SELECT max(priority) + 1 FROM vnpt_dev."permission")),
((SELECT max(id) + 2 FROM vnpt_dev."permission"), 'Xem chi tiết sự cố', 'XEM_CHI_TIET_SU_CO_1', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_SU_CO_1'), (SELECT max(priority) + 2 FROM vnpt_dev."permission")),
((SELECT max(id) + 3 FROM vnpt_dev."permission"), 'Xử lý sự cố', 'XU_LY_SU_CO_1', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_SU_CO_1'), (SELECT max(priority) + 3 FROM vnpt_dev."permission"));


INSERT INTO vnpt_dev.api_permission
(api_id, permission_id)
VALUES
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/error-intergration'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SU_CO_1')),
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/error-intergration/{id}'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_SU_CO_1')),
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/error-intergration/retry/{id}'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XU_LY_SU_CO_1'));


INSERT INTO vnpt_dev.roles_permissions
(role_id, permission_id, allow_edit)
VALUES
(9, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SU_CO_1'), 1),
(1000000000000000000, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SU_CO_1'), 1),
(9, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_SU_CO_1'), 1),
(1000000000000000000, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_SU_CO_1'), 1),
(9, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XU_LY_SU_CO_1'), 1),
(1000000000000000000, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XU_LY_SU_CO_1'), 1),
(9, (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_SU_CO_1'), 1),
(1000000000000000000, (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_SU_CO_1'), 1);


INSERT INTO vnpt_dev.permission_portal
(permission_id, portal_id)
VALUES
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_DANH_SACH_SU_CO_1'), 1),
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XEM_CHI_TIET_SU_CO_1'), 1),
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XU_LY_SU_CO_1'), 1),
((SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_SU_CO_1'), 1);


REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;