ALTER TABLE "vnpt_dev"."transaction_3rd_party" 
  ADD COLUMN "transaction_type" text,
  ADD COLUMN "transaction_id" text,
  ADD COLUMN "corp_name" text,
  ADD COLUMN "tax_code" text,
  ADD COLUMN "phone_no" text,
  ADD COLUMN "email" text,
  ADD COLUMN "request_date" timestamp;

CREATE INDEX "index_transaction3rdParty_transactionId" ON "vnpt_dev"."transaction_3rd_party" USING hash ("transaction_id");
CREATE INDEX "index_transaction3rdParty_transactionType" ON "vnpt_dev"."transaction_3rd_party" USING btree ("transaction_type");
CREATE INDEX "index_transaction3rdParty_corpName" ON "vnpt_dev"."transaction_3rd_party" USING btree ("corp_name");
CREATE INDEX "index_transaction3rdParty_taxCode" ON "vnpt_dev"."transaction_3rd_party" USING btree ("tax_code");
CREATE INDEX "index_transaction3rdParty_phoneNo" ON "vnpt_dev"."transaction_3rd_party" USING btree ("phone_no");
CREATE INDEX "index_transaction3rdParty_email" ON "vnpt_dev"."transaction_3rd_party" USING btree ("email");
CREATE INDEX "index_transaction3rdParty_requestDate" ON "vnpt_dev"."transaction_3rd_party" USING btree ("request_date");

COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."created_at" IS 'Thời gian nhận được transaction';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."user_id" IS 'ID của user trong transaction';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."provider_id" IS 'ID nhà cung cấp';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."service_code" IS 'Mã dịch vụ trong transaction';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."event" IS 'Thông tin chi tiết transaction';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."transaction_type" IS 'Loại giao dịch';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."transaction_id" IS 'Số hiệu giao dịch';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."corp_name" IS 'Tên doanh nghiệp';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."tax_code" IS 'Mã số thuế';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."phone_no" IS 'Số điện thoại';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."email" IS 'Email';
COMMENT ON COLUMN "vnpt_dev"."transaction_3rd_party"."request_date" IS 'Ngày tạo giao dịch';

UPDATE "vnpt_dev"."transaction_3rd_party"
    SET
        transaction_id = event->> 'transactionID',
        transaction_type = event->> 'transactionType',
        corp_name = event->> 'corpName',
        tax_code = event->> 'taxCode',
        phone_no = event->> 'phoneNo',
        email = event->> 'email',
        request_date = CAST(event->> 'requestDate' AS TIMESTAMP);

UPDATE "vnpt_dev"."transaction_3rd_party" mTransaction
    SET
        corp_name = coalesce(corp_name, mUser.name),
        tax_code = coalesce(tax_code, mUser.tin),
        phone_no = coalesce(phone_no, mUser.phone_number),
        email = coalesce(mTransaction.email, mUser.email)
FROM "vnpt_dev"."users" mUser
WHERE
    user_id = mUser.id;