--thêm cột ruleId và activityId
ALTER TABLE vnpt_dev.mc_effect_item
    ADD COLUMN IF NOT EXISTS rule_id int8,
    ADD COLUMN IF NOT EXISTS activity_id int8;

COMMENT ON COLUMN vnpt_dev.mc_effect_item.rule_id IS 'Id của bảng mc_activity_rule';
COMMENT ON COLUMN vnpt_dev.mc_effect_item.activity_id IS 'Id của bảng mc_activity';

--thêm thông tin ruleId dựa trên ruleId của effect_item_set
WITH effect_item_rule AS (
    SELECT mc_effect_item.id AS mcEffectItemId, mc_effect_item_set.mc_activity_rule_id AS ruleId
    FROM vnpt_dev.mc_effect_item
        JOIN vnpt_dev.mc_effect_item_set ON mc_effect_item.effect_item_set_id = mc_effect_item_set.id
)
UPDATE vnpt_dev.mc_effect_item
SET rule_id = effect_item_rule.ruleId
FROM effect_item_rule
WHERE mc_effect_item.id = effect_item_rule.mcEffectItemId;

--thêm thông tin activityId dựa trên activityId của activity_rule_id
WITH effect_item_activity AS (
    SELECT mc_effect_item.id AS mcEffectItemId, mc_activity_rule.mc_activity_id AS activityId
    FROM vnpt_dev.mc_effect_item
        JOIN vnpt_dev.mc_effect_item_set ON mc_effect_item.effect_item_set_id = mc_effect_item_set.id
        JOIN vnpt_dev.mc_activity_rule ON mc_effect_item_set.mc_activity_rule_id = mc_activity_rule.id
)
UPDATE vnpt_dev.mc_effect_item
SET activity_id = effect_item_activity.activityId
FROM effect_item_activity
WHERE mc_effect_item.id = effect_item_activity.mcEffectItemId;