drop view vnpt_dev.feature_view_shopping_cart_get_spdv_info_new;
create view vnpt_dev.feature_view_shopping_cart_get_spdv_info_new as
(
    SELECT
        0 AS calculate_type,
        services.pre_order_url,
        (concat (services.id, '0000')) :: BIGINT AS service_unique_id,
        services.id AS services_id,
        pricing.id AS pricing_id,
        (concat (pricing.id, '0000')) :: BIGINT AS pricing_unique_id,
        pricing_multi_plan.id AS pricing_multi_plan_id,
        services.service_name,
        services.product_type,
        services.services_draft_id AS draft_id,
        services.pricing_default AS pricing_default,
        pricing.pricing_draft_id AS pricing_draft_id,
        ARRAY [ services.categories_id ] AS lst_category,
        (COALESCE ((services.service_owner) :: INTEGER, 3) = ANY (ARRAY [ 0, 1 ])) AS is_on,
        services.user_id AS provider_id,
        pricing.pricing_name,
        COALESCE ((pricing.number_of_trial) :: INTEGER, 0) AS number_of_trial,
        COALESCE ((pricing.trial_type) :: INTEGER, 0) AS trial_type,
        CASE
            WHEN (COALESCE ((pricing.number_of_trial) :: INTEGER, 0) > 0) THEN
                1 ELSE 0
            END AS is_trial,
        services.allow_multi_sub,
        string_to_array(TRANSLATE((services.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT) AS lst_customer_type,
        CASE
            WHEN pricing.id is not null THEN string_to_array(TRANSLATE((pricing.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT)
            ELSE '{HKD,KHDN,CN}'
            END AS pricing_lst_customer_type,
        CASE
            WHEN pricing_multi_plan.id is not null THEN string_to_array(TRANSLATE((pricing_multi_plan.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT)
            ELSE '{HKD,KHDN,CN}'
            END AS pricing_multi_plan_lst_customer_type,
        CASE
            WHEN (pricing_multi_plan.circle_type = 0) THEN concat (pricing_multi_plan.payment_cycle, ' ngày')
            WHEN (pricing_multi_plan.circle_type = 1) THEN concat (pricing_multi_plan.payment_cycle, ' tuần')
            WHEN (pricing_multi_plan.circle_type = 2) THEN concat (pricing_multi_plan.payment_cycle, ' tháng')
            WHEN (pricing_multi_plan.circle_type = 3) THEN concat (pricing_multi_plan.payment_cycle, ' năm')
            WHEN (pricing.cycle_type = 0) THEN concat (pricing.payment_cycle, ' ngày')
            WHEN (pricing.cycle_type = 1) THEN concat (pricing.payment_cycle, ' tuần')
            WHEN (pricing.cycle_type = 2) THEN concat (pricing.payment_cycle, ' tháng')
            WHEN (pricing.cycle_type = 3) THEN concat (pricing.payment_cycle, ' năm') ELSE NULL :: TEXT
            END AS payment_cycle,
        COALESCE ((pricing.is_one_time) :: INTEGER, 1) AS is_one_time
    FROM vnpt_dev.services
             JOIN vnpt_dev.users ON users.id = services.user_id
             LEFT JOIN (
        select count(id), service_id
        from (
                 SELECT max(pricing.id) as id, pricing.service_id
                 FROM vnpt_dev.pricing
                 WHERE status = 1 and approve = 1 and deleted_flag = 1
                 GROUP BY pricing.pricing_draft_id, pricing.service_id
             ) cPricing
        GROUP BY cPricing.service_id
    ) pCount on pCount.service_id = services.id
             LEFT JOIN (
        SELECT p.*
        FROM  vnpt_dev.pricing p
        WHERE p.status = 1
          AND p.deleted_flag = 1
          AND p.approve = 1
          AND id IN (
            SELECT MAX(p.id)
            FROM vnpt_dev.pricing_draft pd
                     JOIN vnpt_dev.pricing p ON pd.id = p.pricing_draft_id AND pd.deleted_flag = 1
            GROUP BY pd.id
        )
    ) as pricing ON pCount.service_id = pricing.service_id and pricing.id in (
        SELECT max(pricing.id) as id
        FROM vnpt_dev.pricing
        WHERE status = 1 and approve = 1 and deleted_flag = 1
        GROUP BY pricing.pricing_draft_id
    ) and pricing.pricing_draft_id in (SELECT id from vnpt_dev.pricing_draft where deleted_flag = 1)
             LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.pricing_id = pricing.id AND pricing_multi_plan.deleted_flag = 1 AND pricing_multi_plan.display_status = 1
    WHERE services.deleted_flag = 1 AND services.status = 1 AND services.approve = 1 AND (pricing.id is not null or services.product_type = 1)
      AND (services.product_type = 1 or ((services.product_type <> 1 or services.product_type is null) and pCount.count <> 0))
    ORDER BY services.id, pricing.id, pricing_multi_plan.id
)
UNION ALL
(
    SELECT
        1 AS calculate_type,
        string_agg ((services.pre_order_url) :: TEXT, ', ' :: TEXT) AS pre_order_url,
        (concat (combo.id, '0001')) :: BIGINT AS service_unique_id,
            combo.id AS services_id,
        cp.id AS pricing_id,
        (concat (cp.id, '0001')) :: BIGINT AS pricing_unique_id,
        NULL :: BIGINT AS pricing_multi_plan_id,
        combo.combo_name AS service_name,
        NULL :: SMALLINT AS product_type,
        combo.combo_draft_id AS draft_id,
        null AS pricing_default,
        null AS pricing_draft_id,
        (string_to_array((combo.categories_id) :: TEXT, ',' :: TEXT)) :: BIGINT [] AS lst_category,
        (COALESCE ((combo.combo_owner) :: INTEGER, 3) = ANY (ARRAY [ 0, 1 ])) AS is_on,
        combo.user_id AS provider_id,
        cp.combo_name AS pricing_name,
        COALESCE ((cp.number_of_trial) :: INTEGER, 0) AS number_of_trial,
        COALESCE ((cp.trial_type) :: INTEGER, 0) AS trial_type,
        CASE
            WHEN (COALESCE ((cp.number_of_trial) :: INTEGER, 0) > 0) THEN
                1 ELSE 0
            END AS is_trial,
        combo.allow_multi_sub,
        string_to_array(TRANSLATE((combo.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT) AS lst_customer_type,
        string_to_array(TRANSLATE((cp.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT) AS pricing_lst_customer_type,
        '{HKD,KHDN,CN}' AS pricing_multi_plan_lst_customer_type,
        CASE
            WHEN (cp.cycle_type = 0) THEN concat (cp.payment_cycle, ' ngày')
            WHEN (cp.cycle_type = 1) THEN concat (cp.payment_cycle, ' tuần')
            WHEN (cp.cycle_type = 2) THEN concat (cp.payment_cycle, ' tháng')
            WHEN (cp.cycle_type = 3) THEN concat (cp.payment_cycle, ' năm') ELSE NULL :: TEXT
            END AS payment_cycle,
        1 AS is_one_time
    FROM vnpt_dev.combo
             JOIN vnpt_dev.users ON users.id = combo.user_id
             LEFT JOIN (
        select count(id), combo_id
        from (
                 SELECT max(combo_plan.id) as id, combo_plan.combo_id
                 FROM vnpt_dev.combo_plan
                 WHERE deleted_flag = 1 AND status = 1 and approve = 1
                 GROUP BY combo_plan.combo_plan_draft_id, combo_plan.combo_id
             ) cPricing
        GROUP BY cPricing.combo_id
    ) cpCount on cpCount.combo_id = combo.id
             LEFT JOIN vnpt_dev.combo_plan cp on cpCount.combo_id = cp.combo_id and cp.id in (
                SELECT max(combo_plan.id)
                FROM vnpt_dev.combo_plan
                WHERE deleted_flag = 1 AND status = 1 and approve = 1
                GROUP BY combo_plan.combo_plan_draft_id
             )
              LEFT JOIN vnpt_dev.combo_pricing ON combo_pricing.id_combo_plan = cp.id
             LEFT JOIN vnpt_dev.pricing ON pricing.id = combo_pricing.pricing_id
             LEFT JOIN vnpt_dev.services ON services.id = pricing.service_id
    WHERE combo.deleted_flag = 1 AND combo.status = 1 AND combo.approve = 1 and cpCount.count <> 0
    GROUP BY combo.id, cp.id, cp.combo_name, cp.number_of_trial, cp.trial_type, cp.customer_type_code, cp.cycle_type, cp.payment_cycle
    ORDER BY combo.id, cp.id
);







DROP VIEW IF EXISTS vnpt_dev.combobox_feature_view_shopping_cart_get_spdv_info_new;
create or replace view vnpt_dev.combobox_feature_view_shopping_cart_get_spdv_info_new as
(
SELECT
	services.service_name,
	0 AS type,
	services.id,
    (concat (services.id, '0000')) :: BIGINT AS service_unique_id,
	services.user_id as provider,
	pricing.id as pricing_id,
    services.categories_id AS categories_id,
	pricing_multi_plan.id as pricing_multi_plan_id ,
	string_to_array(TRANSLATE((services.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT) AS lst_customer_type,
    CASE
        WHEN pricing.id is not null THEN string_to_array(TRANSLATE((pricing.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT)
        ELSE '{HKD,KHDN,CN}'
    END AS pricing_lst_customer_type,
    CASE
        WHEN pricing_multi_plan.id is not null THEN string_to_array(TRANSLATE((pricing_multi_plan.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT)
        ELSE '{HKD,KHDN,CN}'
    END AS pricing_multi_plan_lst_customer_type

FROM vnpt_dev.services
	JOIN vnpt_dev.users ON users.id = services.user_id
	LEFT JOIN (
		select count(id), service_id
		from (
			SELECT max(pricing.id) as id, pricing.service_id
			FROM vnpt_dev.pricing
			WHERE status = 1 and approve = 1 and deleted_flag = 1
			GROUP BY pricing.pricing_draft_id, pricing.service_id
		) cPricing
		GROUP BY cPricing.service_id
	) pCount on pCount.service_id = services.id
	LEFT JOIN (
		SELECT p.*
		FROM  vnpt_dev.pricing p
		WHERE p.status = 1
			AND p.deleted_flag = 1
			AND p.approve = 1
			AND id IN (
				SELECT MAX(p.id)
				FROM vnpt_dev.pricing_draft pd
				JOIN vnpt_dev.pricing p ON pd.id = p.pricing_draft_id AND pd.deleted_flag = 1
				GROUP BY pd.id
			)
	) as pricing ON pCount.service_id = pricing.service_id and pricing.id in (
			SELECT max(pricing.id) as id
			FROM vnpt_dev.pricing
			WHERE status = 1 and approve = 1 and deleted_flag = 1
			GROUP BY pricing.pricing_draft_id
		) and pricing.pricing_draft_id in (SELECT id from vnpt_dev.pricing_draft where deleted_flag = 1)
	 LEFT JOIN vnpt_dev.pricing_multi_plan ON pricing_multi_plan.pricing_id = pricing.id AND pricing_multi_plan.deleted_flag = 1 AND pricing_multi_plan.display_status = 1
WHERE services.deleted_flag = 1 AND services.status = 1 AND services.approve = 1 AND (pricing.id is not null or services.product_type = 1)
	AND (services.product_type = 1 or ((services.product_type <> 1 or services.product_type is null) and pCount.count <> 0))
ORDER BY services.id, pricing.id, pricing_multi_plan.id
)
UNION ALL
(
SELECT
	combo.combo_name AS service_name,
    1 AS type,
	combo.id AS services_id,
    (concat (combo.id, '0001')) :: BIGINT AS service_unique_id,
    combo.user_id as provider,
	cp.id as pricing_id,
    CAST ( UNNEST ( string_to_array( combo.categories_id, ',' ) ) AS INTEGER ) AS categories_id,
	null as pricing_multi_plan_id ,
	string_to_array(TRANSLATE((combo.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT) AS lst_customer_type,
	string_to_array(TRANSLATE((cp.customer_type_code) :: TEXT, '[]"' :: TEXT, '' :: TEXT), ',' :: TEXT) AS pricing_lst_customer_type,
	'{HKD,KHDN,CN}' AS pricing_multi_plan_lst_customer_type

FROM vnpt_dev.combo
	JOIN vnpt_dev.users ON users.id = combo.user_id
	LEFT JOIN (
		select count(id), combo_id
		from (
			SELECT max(combo_plan.id) as id, combo_plan.combo_id
			FROM vnpt_dev.combo_plan
			WHERE deleted_flag = 1 AND status = 1 and approve = 1
			GROUP BY combo_plan.combo_plan_draft_id, combo_plan.combo_id
		) cPricing
		GROUP BY cPricing.combo_id
	) cpCount on cpCount.combo_id = combo.id
	LEFT JOIN vnpt_dev.combo_plan cp on cpCount.combo_id = cp.combo_id and cp.id in (
			SELECT max(combo_plan.id)
			FROM vnpt_dev.combo_plan
			WHERE deleted_flag = 1 AND status = 1 and approve = 1
			GROUP BY combo_plan.combo_plan_draft_id
		)
	LEFT JOIN vnpt_dev.combo_pricing ON combo_pricing.id_combo_plan = cp.id
	LEFT JOIN vnpt_dev.pricing ON pricing.id = combo_pricing.pricing_id
	LEFT JOIN vnpt_dev.services ON services.id = pricing.service_id
WHERE combo.deleted_flag = 1 AND combo.status = 1 AND combo.approve = 1 and cpCount.count <> 0
GROUP BY combo.id, cp.id, cp.combo_name, cp.number_of_trial, cp.trial_type, cp.customer_type_code, cp.cycle_type, cp.payment_cycle
ORDER BY combo.id, cp.id
)

