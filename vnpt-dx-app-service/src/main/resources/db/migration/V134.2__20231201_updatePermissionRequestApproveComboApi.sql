UPDATE vnpt_dev.permission SET name = '<PERSON><PERSON><PERSON> cầu phê duyệt gói combo dịch vụ' WHERE code = 'YEU_CAU_PHE_DUYET_GOI_COMBO_DICH_VU_1';
UPDATE vnpt_dev.permission SET name = 'Yêu cầu phê duyệt combo dịch vụ' WHERE code = 'YEU_CAU_PHE_DUYET_1';

INSERT INTO vnpt_dev.apis(id, api_path, api_code, method)
SELECT 
    (SELECT (MAX(id) + 1) FROM vnpt_dev.apis),
    '/api/admin-portal/combos/{id}/request-approve',
    'API_ADMIN_REQUEST_APPROVE_COMBO',
    'PUT'
WHERE NOT EXISTS(SELECT 1 FROM vnpt_dev.apis WHERE api_code = 'API_ADMIN_REQUEST_APPROVE_COMBO');

SELECT setval('api_permission_id_seq', (select max(id) from vnpt_dev.api_permission), true);

insert into vnpt_dev.api_permission(api_id, permission_portal_id, map_permission_portal, delete_flag)
select
    (select id from vnpt_dev.apis where api_code = 'API_ADMIN_REQUEST_APPROVE_COMBO') as api_id,
    permission_portal.id as permission_portal_id,
    1 as map_permission_portal,
    1 as delete_flag
from vnpt_dev.permission_portal
    join vnpt_dev.permission on permission.id = permission_portal.permission_id
where
    permission.code = 'YEU_CAU_PHE_DUYET_1' and
    not exists (
        select 1 from vnpt_dev.api_permission join vnpt_dev.apis on apis.id = api_permission.api_id and apis.api_code = 'API_ADMIN_REQUEST_APPROVE_COMBO'
    );

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;