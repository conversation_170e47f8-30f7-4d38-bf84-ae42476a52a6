DROP VIEW IF EXISTS "vnpt_dev"."view_subscription_statistic";
CREATE VIEW "vnpt_dev"."view_subscription_statistic" AS
SELECT to_char(subs.created_at, 'YYYY'::text) AS create_year,
    to_char(subs.created_at, 'YYYY/MM'::text) AS create_month,
    to_char(subs.created_at, 'YYYY/MM/dd'::text) AS create_date,
    subs.id AS subscription_id,
    usr.id AS user_id,
    usr.province_id,
    usr.province_name,
        CASE
            WHEN ((co.combo_owner = 0) OR (co.combo_owner = 1)) THEN 1
            WHEN ((svs.service_owner = 0) OR (svs.service_owner = 1)) THEN 1
            ELSE 2
        END AS service_type,
        CASE
            WHEN (co.id IS NOT NULL) THEN (concat(co.id, '0001'))::bigint
            WHEN (svs.id IS NOT NULL) THEN (concat(svs.id, '0000'))::bigint
            ELSE NULL::bigint
        END AS unique_id,
    subs.created_at,
    subs.modified_at,
    subs.pricing_id
   FROM (((((vnpt_dev.subscriptions subs
     LEFT JOIN vnpt_dev.report_view_user_full_info usr ON ((subs.user_id = usr.id)))
     LEFT JOIN vnpt_dev.pricing prc ON ((subs.pricing_id = prc.id)))
     LEFT JOIN vnpt_dev.services svs ON ((prc.service_id = svs.id)))
     LEFT JOIN vnpt_dev.combo_plan cp ON ((subs.combo_plan_id = cp.id)))
     LEFT JOIN vnpt_dev.combo co ON ((cp.combo_id = co.id)));


DROP VIEW IF EXISTS "vnpt_dev"."report_view_top_subscription_from_product";
CREATE
OR REPLACE VIEW "vnpt_dev"."report_view_top_subscription_from_product" AS
SELECT to_char(subs.created_at, 'YYYY'::text) AS create_year,
       to_char(subs.created_at, 'YYYY/MM'::text) AS create_month,
       to_char(subs.created_at, 'YYYY/MM/dd'::text) AS create_date,
       subs.id AS subscription_id,
       usr.id AS user_id,
       usr.province_id,
       usr.province_name,
       CASE
           WHEN ((co.combo_owner = 0) OR (co.combo_owner = 1)) THEN 1
           WHEN ((svs.service_owner = 0) OR (svs.service_owner = 1)) THEN 1
           ELSE 2
           END AS product_type,
       CASE
           WHEN (co.id IS NOT NULL) THEN (concat(co.id, '0001'))::bigint
           WHEN (svs.id IS NOT NULL) THEN (concat(svs.id, '0000'))::bigint
           ELSE NULL::bigint
           END AS unique_id,
       subs.created_at,
       subs.modified_at,
       subs.pricing_id,
       subs.combo_plan_id,
       subs.reg_type,
       subs.confirm_status,
       COALESCE(co.combo_name, svs.service_name) AS product_name
FROM (((((vnpt_dev.subscriptions subs
    LEFT JOIN vnpt_dev.report_view_user_full_info usr ON ((subs.user_id = usr.id)))
    LEFT JOIN vnpt_dev.pricing prc ON ((subs.pricing_id = prc.id)))
    LEFT JOIN vnpt_dev.services svs ON ((prc.service_id = svs.id)))
    LEFT JOIN vnpt_dev.combo_plan cp ON ((subs.combo_plan_id = cp.id)))
         LEFT JOIN vnpt_dev.combo co ON ((cp.combo_id = co.id)));
