DELETE FROM vnpt_dev.apis WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id FROM vnpt_dev.api_permission JOIN vnpt_dev.permission ON permission.id = api_permission.permission_id
    WHERE vnpt_dev.permission.code IN ('QUAN_LY_KHACH_HANG', 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA', 'QUAN_LY_KHACH_HANG_THEM_MOI', 'QUAN_LY_KHACH_HANG_CAP_NHAT', 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET')
);

DELETE FROM vnpt_dev.api_permission WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_<PERSON>HACH_HANG', 'QUAN_LY_<PERSON>HA<PERSON>_HANG_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA', 'QUAN_LY_KHACH_HANG_THEM_MOI', 'QUAN_LY_KHACH_HANG_CAP_NHAT', 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET'))
);

DELETE FROM vnpt_dev.roles_permissions WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG', 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA', 'QUAN_LY_KHACH_HANG_THEM_MOI', 'QUAN_LY_KHACH_HANG_CAP_NHAT', 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET'))
);

DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG', 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA', 'QUAN_LY_KHACH_HANG_THEM_MOI', 'QUAN_LY_KHACH_HANG_CAP_NHAT', 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET'))
);

DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG', 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA', 'QUAN_LY_KHACH_HANG_THEM_MOI', 'QUAN_LY_KHACH_HANG_CAP_NHAT', 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET');


-- INSERT vao bang permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Quản lý Khách hàng',
        'QUAN_LY_KHACH_HANG',
        -1,
        13000000
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem danh sách',
        'QUAN_LY_KHACH_HANG_XEM_DANH_SACH',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        13000001
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Lưu trữ/Bỏ lưu trữ/Xóa khách hàng',
        'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        13000002
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Thêm mới',
        'QUAN_LY_KHACH_HANG_THEM_MOI',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        13000003
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Cập nhật',
        'QUAN_LY_KHACH_HANG_CAP_NHAT',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        13000004
    );


INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết',
        'QUAN_LY_KHACH_HANG_XEM_CHI_TIET',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        13000005
    );

-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
                                                                          (
                                                                              (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
                                                                              (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
                                                                          (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
                                                                          (
                                                                              (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
                                                                              (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
                                                                          (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
                                                                          (
                                                                              (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
                                                                              (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA' ORDER BY id DESC LIMIT 1),
                                                                          (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
                                                                          (
                                                                              (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
                                                                              (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_THEM_MOI' ORDER BY id DESC LIMIT 1),
                                                                          (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
                                                                          (
                                                                              (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
                                                                              (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' ORDER BY id DESC LIMIT 1),
                                                                          (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
                                                                          (
                                                                              (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
                                                                              (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
                                                                          (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
                                                                                    (
                                                                                        (SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
                                                                                        (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
                                                                                    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1),
    1
    ),
(
    (SELECT MAX(id) + 2 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 3 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 4 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_THEM_MOI' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 5 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 6 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
    1
);
-- Add vao bang apis, api_permission
    --add api lay danh sach khach hang tiem nang
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-list-potential-enterprise',
        'ROLE_QLKH_LAY_DANH_SACH_KHACH_HANG_TIEM_NANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DANH_SACH_KHACH_HANG_TIEM_NANG' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu combobox ten mien dia ly
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        'api/admin-portal/crm/enterprise-mgmt/common/get-list-region',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_MIEN_DIA_LY',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_MIEN_DIA_LY' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu combobox ten tinh
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-province',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_TINH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_TINH' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu combobox ten huyen
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-district',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_HUYEN',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_HUYEN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu combobox ten xa
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-ward',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_XA',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_XA' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu business area
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-business-area',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_NGANH_NGHE_KINH_DOANH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_NGANH_NGHE_KINH_DOANH' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu business type
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-business-type',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_LOAI_HINH_KINH_DOANH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_LOAI_HINH_KINH_DOANH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api lay du lieu business size
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-business-size',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_QUY_MO_DOANH_NGHIEP',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_QUY_MO_DOANH_NGHIEP' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu email
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-email',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_EMAIL',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_EMAIL' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu phone number
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-phone',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_PHONE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_PHONE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu service
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-service',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_SERVICE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_SERVICE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu bill code
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-bill-code',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_BILL_CODE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_BILL_CODE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu customer by status
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-all-name-by-customer-status',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_STATUS',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_STATUS' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu customer by classification
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-all-name-by-classification',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_CLASSIFICATION',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_CLASSIFICATION' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu tax-code
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-tax-code',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_TAX_CODE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_TAX_CODE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu representative
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-representative',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_REPRESENTATIVE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_REPRESENTATIVE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay du lieu combobox cac truong thong tin
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-preference',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_PREFERENCE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_PREFERENCE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api update du lieu combobox cac truong thong tin
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-preference',
        'ROLE_QLKH_UPDATE_DU_LIEU_COMBOBOX_PREFERENCE',
        'PUT'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_DU_LIEU_COMBOBOX_PREFERENCE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay danh sach khach hang hien huu
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-lst-current-enterprise',
        'ROLE_QLKH_UPDATE_DU_LIEU_CURRENT_ENTERPRISE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_DU_LIEU_CURRENT_ENTERPRISE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay danh sach khach hang tiem nang
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-list-potential-enterprise',
        'ROLE_QLKH_UPDATE_DU_LIEU_POTENTIAL_ENTERPRISE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_DU_LIEU_POTENTIAL_ENTERPRISE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lay danh sahc khach hang roi bo
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-lst-left-enterprise',
        'ROLE_QLKH_UPDATE_DU_LIEU_LEFT_ENTERPRISE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_DU_LIEU_LEFT_ENTERPRISE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api cap nhat trang thai khach hang
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-status',
        'ROLE_QLKH_UPDATE_ENTERPRISE_STATUS',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_ENTERPRISE_STATUS' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT_TRANG_THAI_VA_XOA' ORDER BY id DESC LIMIT 1)
    );

    --api xoa khach hang doanh nghiep
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/enterprise/delete-by-id',
        'ROLE_QLKH_DELETE_ENTERPRISE',
        'DELETE'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_DELETE_ENTERPRISE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    -- ngonc 2021-12-10

    --api import khach hang doanh nghiep
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/enterprise/import-enterprise',
        'ROLE_QLKH_IMPORT_ENTERPRISE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_IMPORT_ENTERPRISE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_THEM_MOI' ORDER BY id DESC LIMIT 1)
    );

    --api nhap khach hang doanh nghiep
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/create/create-potential-enterprise',
        'ROLE_QLKH_CREATE_POTENTIAL_ENTERPRISE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_CREATE_POTENTIAL_ENTERPRISE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_THEM_MOI' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet thong tin doanh nghiep
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-business-detail',
        'ROLE_QLKH_GET_ENTERPRISE_BUSINESS_DETAILS',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_BUSINESS_DETAILS' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet thong tin lien he
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-contact-detail',
        'ROLE_QLKH_GET_ENTERPRISE_CONTACT_DETAILS',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_CONTACT_DETAILS' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet thong tin ca nhan
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-sme-detail',
        'ROLE_QLKH_GET_ENTERPRISE_SME_DETAILS',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_SME_DETAILS' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet thong tin nguoi dai dien
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/get-representative-detail',
        'ROLE_QLKH_GET_ENTERPRISE_REPRESENTATIVE_DETAILS',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_REPRESENTATIVE_DETAILS' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet lich su CSKH
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/customer-care',
        'ROLE_QLKH_GET_ENTERPRISE_CUSTOMER_CARE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_CUSTOMER_CARE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet lich su thue bao
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/subs-history',
        'ROLE_QLKH_GET_ENTERPRISE_SUBS_HISTORY',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_SUBS_HISTORY' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet lich su hoa don
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/bill-history',
        'ROLE_QLKH_GET_ENTERPRISE_BILL_HISTORY',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_BILL_HISTORY' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet lich su phieu ho tro
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/customer-ticket',
        'ROLE_QLKH_GET_ENTERPRISE_CUSTOMER_TICKET',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_CUSTOMER_TICKET' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api xem chi tiet nhat ky hoat dong
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/details/action-history',
        'ROLE_QLKH_GET_ENTERPRISE_ACTION_HISTORY',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_ENTERPRISE_ACTION_HISTORY' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --api cap nhat thong tin doanh nghiep
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/update/business-detail',
        'ROLE_QLKH_UPDATE_ENTERPRISE_BUSINESS_DETAIL',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_ENTERPRISE_BUSINESS_DETAIL' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' ORDER BY id DESC LIMIT 1)
    );

    --api cap nhat thong tin lien he
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/update/contact-detail',
        'ROLE_QLKH_UPDATE_ENTERPRISE_CONTACT_DETAIL',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_ENTERPRISE_CONTACT_DETAIL' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' ORDER BY id DESC LIMIT 1)
    );

    --api cap nhat thong tin ca nhan
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/update/sme-detail',
        'ROLE_QLKH_UPDATE_ENTERPRISE_SME_DETAIL',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_ENTERPRISE_SME_DETAIL' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' ORDER BY id DESC LIMIT 1)
    );

    --api cap nhat thong tin nguoi dai dien
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/update/representative-detail',
        'ROLE_QLKH_UPDATE_ENTERPRISE_REPRESENTATIVE_DETAIL',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_UPDATE_ENTERPRISE_REPRESENTATIVE_DETAIL' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT' ORDER BY id DESC LIMIT 1)
    );

--add api lấy danh sách đường phố
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-street',
        'ROLE_QLKHLH_GET_DANH_SACH_DUONG_PHO',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_GET_DANH_SACH_DUONG_PHO' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--add api Cập nhật danh sách template cac trường thông tin
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/update-preference',
        'ROLE_QLKHLH_CAP_NHAT_DS_TEMPLATE',
        'PUT'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_CAP_NHAT_DS_TEMPLATE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--add api lấy danh sách dân tộc
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-folk',
        'ROLE_QLKH_GET_DAN_TOC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_DAN_TOC' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--add api lấy dan sách admin và sme
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-employee',
        'ROLE_QLKH_GET_DANH_SACH_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_GET_DANH_SACH_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

-- REFRESH MATERIALIZED VIEW
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;