drop function if exists "vnpt_dev"."func_get_affiliate_child_by_role_user_id";
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_affiliate_child_by_role_user_id"("i_role_name" varchar, "i_current_user_id" int8)
  RETURNS TABLE("user_id" int8) AS $BODY$
begin
	--Role full_admin
	if
i_role_name like 'FULL_ADMIN' then
begin
			--Lấy ra tất cả affiliate_link
return query(select affiliate_users.user_id from vnpt_dev.affiliate_users);
end;
	--Đại lý affiliate
	elseif
i_role_name like 'ROLE_AFFILIATE_DAILY' then
begin
return query(
    --<PERSON><PERSON> quy để lấy ra đại lý và tất cả thành viên con
    with recursive affiliate_users_all_level_cte as(
					select affiliate_users.user_id, affiliate_users.affiliate_code from vnpt_dev.affiliate_users affiliate_users where affiliate_users.user_id = i_current_user_id
					union all
					select aff_users_child.user_id, aff_users_child.affiliate_code
					from vnpt_dev.affiliate_users aff_users_child 
					join affiliate_users_all_level_cte aff_users_parent on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
		)
		select
			distinct data.user_id
		from
		(
			select
				*
			from affiliate_users_all_level_cte aff_all_level
		) as data
    );
end;
	--Cá nhân affiliate
else
begin
			--Lấy ra các affiliate link được gán cho cá nhân
return query(select user_id from vnpt_dev.affiliate_users where user_id = i_current_user_id);
end;
end if;
end $BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;