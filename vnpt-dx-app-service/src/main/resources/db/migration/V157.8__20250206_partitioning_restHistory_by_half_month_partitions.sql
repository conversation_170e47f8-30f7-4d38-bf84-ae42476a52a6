-- Tạo function cho phép tạo partition theo nửa tháng cho một bảng bất kì
CREATE OR REPLACE FUNCTION dx20_common.func_create_partition_by_half_month(schema_name varchar, tbl_name varchar, timeout int4) RETURNS int2 AS $$
DECLARE
    partition_0_name TEXT;
    partition_1_name TEXT;
    deleting_partition TEXT;
    sql_query TEXT;
    start_date DATE;
    end_0_date DATE;
    end_1_date DATE;
BEGIN
    -- Tạo phân vùng tháng hiện tại
    EXECUTE 'SELECT cast(date_trunc(''month'', now()) as date)' INTO start_date;
    end_0_date := cast(start_date + INTERVAL '15 day' as date);
    end_1_date := cast(start_date + INTERVAL '1 month' as date);
    partition_0_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM') || '_0';
    partition_1_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM') || '_1';
    
    IF NOT EXISTS (
            SELECT 1
            FROM pg_class
                JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace
            WHERE pg_class.relname = partition_0_name AND pg_namespace.nspname = schema_name
        ) THEN
            -- Tạo partition từ ngày 01-15
            sql_query := format(
                'CREATE TABLE %I.%I PARTITION OF %I.%I FOR VALUES FROM (%L) TO (%L)',
                schema_name,
                partition_0_name,
                schema_name,
                tbl_name,
                start_date,
                end_0_date
            );
            RAISE NOTICE 'SQL query % ', sql_query;
            EXECUTE sql_query;
            RAISE NOTICE 'Partition % created.', partition_0_name;
        END IF;

    IF NOT EXISTS (
            SELECT 1
            FROM pg_class
                JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace
            WHERE pg_class.relname = partition_1_name AND pg_namespace.nspname = schema_name
        ) THEN         
            -- Tạo partition từ ngày 16-cuối tháng
            sql_query := format(
                'CREATE TABLE %I.%I PARTITION OF %I.%I FOR VALUES FROM (%L) TO (%L)',
                schema_name,
                partition_1_name,
                schema_name,
                tbl_name,
                end_0_date,
                end_1_date
            );
            RAISE NOTICE 'SQL query % ', sql_query;
            EXECUTE sql_query;
            RAISE NOTICE 'Partition % created.', partition_1_name;
        END IF;

    -- Tạo phân vùng tháng sau
    EXECUTE 'SELECT cast(date_trunc(''month'', now() + interval ''1 month'') as date)' INTO start_date;
    end_0_date := cast(start_date + INTERVAL '15 day' as date);
    end_1_date := cast(start_date + INTERVAL '1 month' as date);
    partition_0_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM') || '_0';
    partition_1_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM') || '_1';
    
    IF NOT EXISTS (
            SELECT 1
            FROM pg_class
                JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace
            WHERE pg_class.relname = partition_0_name AND pg_namespace.nspname = schema_name
        ) THEN
            -- Tạo partition từ ngày 01-15
            sql_query := format(
                'CREATE TABLE %I.%I PARTITION OF %I.%I FOR VALUES FROM (%L) TO (%L)',
                schema_name,
                partition_0_name,
                schema_name,
                tbl_name,
                start_date,
                end_0_date
            );
            RAISE NOTICE 'SQL query % ', sql_query;
            EXECUTE sql_query;
            RAISE NOTICE 'Partition % created.', partition_0_name;
        END IF;

    IF NOT EXISTS (
            SELECT 1
            FROM pg_class
                JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace
            WHERE pg_class.relname = partition_1_name AND pg_namespace.nspname = schema_name
        ) THEN         
            -- Tạo partition từ ngày 16-cuối tháng
            sql_query := format(
                'CREATE TABLE %I.%I PARTITION OF %I.%I FOR VALUES FROM (%L) TO (%L)',
                schema_name,
                partition_1_name,
                schema_name,
                tbl_name,
                end_0_date,
                end_1_date
            );
            RAISE NOTICE 'SQL query % ', sql_query;
            EXECUTE sql_query;
            RAISE NOTICE 'Partition % created.', partition_1_name;
        END IF;

    -- Xóa các partition cũ (cùng lúc tạo partition mới)
    IF timeout != -1 THEN
        EXECUTE format('SELECT CONCAT(''%I_'', TO_CHAR(now() - INTERVAL ''%I'' MONTH, ''YYYY_MM''))', tbl_name, timeout) INTO deleting_partition;
        EXECUTE format('DROP TABLE IF EXISTS %I.%I', schema_name, deleting_partition);
        EXECUTE format('DROP TABLE IF EXISTS %I.%I_0', schema_name, deleting_partition);
        EXECUTE format('DROP TABLE IF EXISTS %I.%I_1', schema_name, deleting_partition);
    ELSE
        RAISE NOTICE 'Skip removing old partition of %.%', schema_name, tbl_name;
    END IF;
    RETURN 0;
END;
$$ LANGUAGE plpgsql;

DROP TABLE IF EXISTS vnpt_dev.rest_history_2025_02;
DROP TABLE IF EXISTS vnpt_dev.rest_history_2025_03;
DROP TABLE IF EXISTS vnpt_dev.rest_history_2025_04;
DROP TABLE IF EXISTS dx20_common.rest_history_2025_02;
DROP TABLE IF EXISTS dx20_common.rest_history_2025_03;
DROP TABLE IF EXISTS dx20_common.rest_history_2025_04;
SELECT dx20_common.func_create_partition_by_half_month('vnpt_dev', 'rest_history', 3);
SELECT dx20_common.func_create_partition_by_half_month('dx20_common', 'rest_history', 3);