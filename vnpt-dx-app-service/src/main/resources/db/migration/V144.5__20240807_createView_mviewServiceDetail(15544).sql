DROP MATERIALIZED VIEW IF EXISTS vnpt_dev.mview_service_detail CASCADE;
CREATE MATERIALIZED VIEW vnpt_dev.mview_service_detail AS
WITH service_categories AS (
    SELECT mapping_services_categories.service_id,
        CAST(JSONB_AGG(json_build_object('id', categories.id, 'name', categories.name)) AS TEXT) as categories
    FROM vnpt_dev.mapping_services_categories
        LEFT JOIN vnpt_dev.categories ON categories.id = mapping_services_categories.categories_id
    WHERE mapping_services_categories.service_id IS NOT NULL
    GROUP BY mapping_services_categories.service_id
),
service_features AS (
    SELECT service_id,
        CAST(JSONB_AGG(json_build_object('id', id, 'name', name, 'description', description, 'icon', icon)) AS TEXT) as features
    FROM vnpt_dev.features
    WHERE service_id IS NOT NULL
    GROUP BY service_id
)
SELECT
    services.id AS service_id,
    service_categories.categories AS categories,
    service_features.features AS features
FROM
    vnpt_dev.services
    LEFT JOIN service_categories ON service_categories.service_id = services.id
    LEFT JOIN service_features ON service_features.service_id = services.id;