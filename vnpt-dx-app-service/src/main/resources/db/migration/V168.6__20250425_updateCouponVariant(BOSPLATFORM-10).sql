drop table if exists "vnpt_dev"."coupon_variant";
create table if not exists "vnpt_dev"."coupon_variant" (
   "id" bigserial not null unique,
   "coupon_id" int8 not null,
   "variant_id" int8 not null,
   "variant_draft_id" int8,
   "pricing_id" int8,
   "pricing_draft_id" int8,
   "pricing_multi_plan_id" int8,
   "pricing_multi_plan_original_id" int8
);
comment on table "vnpt_dev"."coupon_variant" is 'Bảng lưu mapping CTKM - biến thể với loại khuyến mại là MIỄN PHÍ THEO SẢN PHẨM (promotion_type = 1)';
comment on column "vnpt_dev"."coupon_variant"."variant_id" is 'Id biến thể';
comment on column "vnpt_dev"."coupon_variant"."variant_draft_id" is 'Id bản ghi draft của variant';
comment on column "vnpt_dev"."coupon_variant"."pricing_id" is 'Id gói cướ<PERSON> (phiên bản tại thời điểm lựa chọn) đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant"."pricing_draft_id" is 'Id draft gói cước đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant"."pricing_multi_plan_id" is 'Id CLDG của gói cước (phiên bản tại thời điểm lựa chọn) đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant"."pricing_multi_plan_original_id" is 'Id original CLDG của gói cước (phiên bản tại thời điểm lựa chọn) đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant"."coupon_id" is 'Id CTKM';
create index "couponVariantCouponIdIdx" on "vnpt_dev"."coupon_variant" using btree("coupon_id");
create unique index "couponVariantCouponIdVariantIdIdx" on "vnpt_dev"."coupon_variant" using btree("coupon_id", "variant_id");

create table if not exists "vnpt_dev"."coupon_variant_apply" (
     "id" bigserial not null unique,
     "coupon_id" int8 not null,
     "variant_id" int8 not null,
     "variant_draft_id" int8,
     "pricing_id" int8,
     "pricing_draft_id" int8,
     "pricing_multi_plan_id" int8,
     "pricing_multi_plan_original_id" int8
);
comment on table "vnpt_dev"."coupon_variant_apply" is 'Bảng lưu mapping CTKM - biến thể với loại khuyến mại là CHIẾT KHẤU (promotion_type = 0)';
comment on column "vnpt_dev"."coupon_variant_apply"."variant_id" is 'Id biến thể';
comment on column "vnpt_dev"."coupon_variant_apply"."variant_draft_id" is 'Id bản ghi draft của variant';
comment on column "vnpt_dev"."coupon_variant_apply"."pricing_id" is 'Id gói cước (phiên bản tại thời điểm lựa chọn) đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant_apply"."pricing_draft_id" is 'Id draft gói cước đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant_apply"."pricing_multi_plan_id" is 'Id CLDG của gói cước (phiên bản tại thời điểm lựa chọn) đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant_apply"."pricing_multi_plan_original_id" is 'Id original CLDG của gói cước (phiên bản tại thời điểm lựa chọn) đi kèm với biến thể';
comment on column "vnpt_dev"."coupon_variant_apply"."coupon_id" is 'Id CTKM';
create index "couponVariantApplyCouponIdIdx" on "vnpt_dev"."coupon_variant_apply" using btree("coupon_id");
create unique index "couponVariantApplyCouponIdVariantIdIdx" on "vnpt_dev"."coupon_variant_apply" using btree("coupon_id", "variant_id");

-- thêm cột plan_original_id vào bảng coupon_pricing và coupon_pricing_plan --
-- pricing
alter table "vnpt_dev"."coupon_pricing" add column if not exists "pricing_draft_id" int8; -- tặng sản phẩm
comment on column "vnpt_dev"."coupon_pricing"."pricing_draft_id" is 'Id draft của pricing -> áp dụng CTKM cho tất cả các phiên bản của draft_id được chọn';
alter table "vnpt_dev"."coupon_pricing_apply" add column if not exists "pricing_draft_id" int8; -- chiết khấu
comment on column "vnpt_dev"."coupon_pricing_apply"."pricing_draft_id" is 'Id draft của pricing -> áp dụng CTKM cho tất cả các phiên bản của draft_id được chọn';
-- pricing_multi_plan
alter table "vnpt_dev"."coupon_pricing_plan" add column if not exists "plan_original_id" int8; -- chiết khấu
comment on column "vnpt_dev"."coupon_pricing_plan"."plan_original_id" is 'Id original của pricing_multi_plan -> áp dụng CTKM cho tất cả các phiên bản của original_id được chọn';
alter table "vnpt_dev"."coupon_pricing_plan_apply" add column if not exists "plan_original_id" int8; -- tặng sản phẩm
comment on column "vnpt_dev"."coupon_pricing_plan_apply"."plan_original_id" is 'Id original của pricing_multi_plan -> áp dụng CTKM cho tất cả các phiên bản của original_id được chọn';
-- combo-plan
alter table "vnpt_dev"."coupon_combo_plan" add column if not exists "combo_plan_draft_id" int8; -- tặng sản phẩm combo plan
comment on column "vnpt_dev"."coupon_combo_plan"."combo_plan_draft_id" is 'Id draft của combo_plan -> áp dụng CTKM cho tất cả các phiên bản của draft_id được chọn';
alter table "vnpt_dev"."coupon_combo_plan_apply" add column if not exists "combo_plan_draft_id" int8; -- chiết khấu cho combo plan
comment on column "vnpt_dev"."coupon_combo_plan_apply"."combo_plan_draft_id" is 'Id draft của combo_plan -> áp dụng CTKM cho tất cả các phiên bản của draft_id được chọn';
-- addon
alter table "vnpt_dev"."coupon_addons" add column if not exists "addon_draft_id" int8;
comment on column "vnpt_dev"."coupon_addons"."addon_draft_id" is 'Id draft của addon -> áp dụng CTKM cho tất cả các phiên bản của draft_id được chọn';


-- set lại thông tin id original (draft_id, original_id) --
-- bảng coupon_pricing --
with idRootCTE as (
    select id, pricing_draft_id as root_id from vnpt_dev.pricing
) update vnpt_dev.coupon_pricing set pricing_draft_id = idRootCTE.root_id from idRootCTE where coupon_pricing.pricing_id = idRootCTE.id;
-- bảng coupon_pricing_apply --
with idRootCTE as (
    select id, pricing_draft_id as root_id from vnpt_dev.pricing
) update vnpt_dev.coupon_pricing_apply set pricing_draft_id = idRootCTE.root_id from idRootCTE where coupon_pricing_apply.pricing_id = idRootCTE.id;
-- bảng coupon_pricing_plan với 2 trường hợp type = 0 => pricing và type = 1 => addon --
with idRootCTE as (
    select id, original_id as root_id from vnpt_dev.pricing_multi_plan
) update vnpt_dev.coupon_pricing_plan set plan_original_id = idRootCTE.root_id from idRootCTE where coupon_pricing_plan.pricing_multi_plan_id = idRootCTE.id and coupon_pricing_plan.type = 0;
-- bảng coupon_pricing_plan_apply --
with idRootCTE as (
    select id, original_id as root_id from vnpt_dev.pricing_multi_plan
) update vnpt_dev.coupon_pricing_plan_apply set plan_original_id = idRootCTE.root_id from idRootCTE where coupon_pricing_plan_apply.pricing_multi_plan_id = idRootCTE.id;
-- bảng coupon_combo_plan --
with idRootCTE as (
    select id, combo_plan_draft_id as root_id from vnpt_dev.combo_plan
) update vnpt_dev.coupon_combo_plan set combo_plan_draft_id = idRootCTE.root_id from idRootCTE where coupon_combo_plan.combo_plan_id = idRootCTE.id;
-- bảng coupon_combo_plan_apply --
with idRootCTE as (
    select id, combo_plan_draft_id as root_id from vnpt_dev.combo_plan
) update vnpt_dev.coupon_combo_plan_apply set combo_plan_draft_id = idRootCTE.root_id from idRootCTE where coupon_combo_plan_apply.combo_plan_id = idRootCTE.id;
-- bảng coupon_variant--
with idRootCTE as (
    select id, variant_draft_id as root_id from vnpt_dev.variant
) update vnpt_dev.coupon_variant set variant_draft_id = idRootCTE.root_id from idRootCTE where coupon_variant.variant_id = idRootCTE.id;
-- bảng coupon_variant_apply--
with idRootCTE as (
    select id, variant_draft_id as root_id from vnpt_dev.variant
) update vnpt_dev.coupon_variant_apply set variant_draft_id = idRootCTE.root_id from idRootCTE where coupon_variant_apply.variant_id = idRootCTE.id;
-- bảng coupon_addons --
with idRootCTE as (
    select id, addon_draft_id as root_id from vnpt_dev.addons
) update vnpt_dev.coupon_addons set addon_draft_id = idRootCTE.root_id from idRootCTE where coupon_addons.addons_id = idRootCTE.id;

