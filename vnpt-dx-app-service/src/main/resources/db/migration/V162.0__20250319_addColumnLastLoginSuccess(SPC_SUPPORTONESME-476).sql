-- Th<PERSON><PERSON> cột lưu thông tin thời gian đăng nhập thành công lần cuối, mặc định trước đó là last_login --
alter table "vnpt_dev"."users"
    add column if not exists "last_successful_login" timestamp;
comment on column "vnpt_dev"."users"."last_successful_login" is 'Thời đăng nhập thành công gần nhất';
update "vnpt_dev"."users" set "last_successful_login" = "last_login" where "last_login" is not null;

-- Update uuid cho cái user chưa gen --
update "vnpt_dev"."users" set "uuid" = uuid_generate_v4() where "uuid" is null;
alter table "vnpt_dev"."users" alter column "uuid" set default uuid_generate_v4();

-- Thêm trạng thái RESET: mật khẩu đã bị reset bởi người dùng kh<PERSON>c (admin/admin-sme/admin-dev)
comment on column "vnpt_dev"."users"."password_updated_status" is 'Trạng thái cập nhật mật khẩu của tài kho<PERSON>n (null: Các tài khoản cũ chưa có thông tin, -1: Không cho phép đổi mật khẩu (các nguồn tạo tài khoản khác OneSME) 0: Chưa đổi bất kỳ lần nào, 1: Đã thay đổi ít nhất một lần (do người dùng tự đổi), 2: Mật khẩu bị reset bởi người dùng khác))';