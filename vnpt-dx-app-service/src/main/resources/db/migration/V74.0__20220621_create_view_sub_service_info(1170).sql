CREATE OR REPLACE VIEW "vnpt_dev"."view_sub_service_info" AS
 SELECT sub.id,
        CASE
            WHEN sub.pricing_id IS NOT NULL THEN concat(s.id, '0000')::bigint
            ELSE concat(c.id, '0001')::bigint
        END AS service_combo_id,
        CASE
            WHEN sub.pricing_id IS NOT NULL THEN concat(p.id, '0000')::bigint
            ELSE concat(cp.id, '0001')::bigint
        END AS pricing_combo_plan_id,
        CASE
            WHEN sub.pricing_id IS NOT NULL THEN s.service_name
            ELSE c.combo_name
        END AS service_combo_name,
        CASE
            WHEN sub.pricing_id IS NOT NULL THEN p.pricing_name
            ELSE cp.combo_name
        END AS pricing_combo_plan_name,
        CASE
            WHEN sub.pricing_multi_plan_id IS NOT NULL THEN pmp.payment_cycle
            WHEN sub.pricing_multi_plan_id IS NULL AND sub.pricing_id IS NOT NULL THEN p.payment_cycle::bigint
            ELSE cp.payment_cycle::bigint
        END AS payment_cycle,
        CASE
            WHEN sub.pricing_multi_plan_id IS NOT NULL THEN pmp.circle_type
            WHEN sub.pricing_multi_plan_id IS NULL AND sub.pricing_id IS NOT NULL THEN p.cycle_type
            ELSE cp.cycle_type
        END AS cycle_type
   FROM vnpt_dev.subscriptions sub
     LEFT JOIN vnpt_dev.pricing p ON p.id = sub.pricing_id AND sub.pricing_id IS NOT NULL
     LEFT JOIN vnpt_dev.pricing_multi_plan pmp ON pmp.id = sub.pricing_multi_plan_id AND sub.pricing_multi_plan_id IS NOT NULL
     LEFT JOIN vnpt_dev.combo_plan cp ON cp.id = sub.combo_plan_id AND sub.combo_plan_id IS NOT NULL
     LEFT JOIN vnpt_dev.services s ON s.id = p.service_id AND sub.pricing_id IS NOT NULL
     LEFT JOIN vnpt_dev.combo c ON c.id = cp.combo_id AND sub.combo_plan_id IS NOT NULL