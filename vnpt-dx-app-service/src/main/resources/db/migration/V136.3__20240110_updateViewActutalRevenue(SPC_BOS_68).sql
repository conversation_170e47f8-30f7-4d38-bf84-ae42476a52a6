drop view if exists vnpt_dev.feature_view_get_actual_revenue_subscriptions;
create or replace view vnpt_dev.feature_view_get_actual_revenue_subscriptions as (
 WITH targettype AS (
         SELECT COALESCE(system_params.param_text_value, '0,1,2'::text) AS param_text_value
           FROM vnpt_dev.system_params
          WHERE system_params.param_name::text = '<PERSON><PERSON>u hình loại thuê bao hiển thị doanh thu mục tiêu'::text
        )
 SELECT
        CASE
            WHEN target.object_type = 0 THEN targetvalue.partition_id
            WHEN target.object_type = 1 THEN targetvalue.service_id
            WHEN target.object_type = 2 THEN targetvalue.admin_id
            ELSE NULL::bigint
        END AS object_id,
        CASE
            WHEN target.object_type = 0 THEN crm_data_partition.name
            WHEN target.object_type = 1 THEN services.service_name
            WHEN target.object_type = 2 THEN concat(users.last_name::text, ' ', users.first_name)::character varying
            ELSE NULL::character varying
        END AS object_name,
        CASE
            WHEN target.object_type = 0 THEN crm_data_partition.code
            WHEN target.object_type = 1 THEN services.service_code
            WHEN target.object_type = 2 THEN users.admin_code
            ELSE NULL::character varying
        END AS object_code,
        CASE
            WHEN target.object_type = 0 THEN NULL::character varying
            WHEN target.object_type = 1 THEN NULL::character varying
            WHEN target.object_type = 2 THEN users.email
            ELSE NULL::character varying
        END AS email,
    target.id AS target_id,
    target.object_type,
    target.code AS target_code,
    target.name AS target_name,
    target.interval_type,
    target.start_date,
    target.end_date,
    targetvalue.admin_id AS assignee_id,
        CASE
            WHEN targetvalue.id IS NULL THEN 0::bigint
            ELSE targetvalue.target_value
        END AS target_value,
    target.description,
    target.range_type,
    targetvalue.id AS target_value_id,
    COALESCE(target.modified_at, target.created_at) AS modified_at,
    target.target_type,
		target.created_by
   FROM vnpt_dev.crm_revenue_target target
     LEFT JOIN vnpt_dev.crm_revenue_target_value targetvalue ON targetvalue.target_id = target.id
     LEFT JOIN vnpt_dev.services ON services.id = targetvalue.service_id AND target.object_type = 1
     LEFT JOIN vnpt_dev.crm_data_partition ON crm_data_partition.id = targetvalue.partition_id AND target.object_type = 0
     LEFT JOIN vnpt_dev.users ON users.id = targetvalue.admin_id AND target.object_type = 2
)