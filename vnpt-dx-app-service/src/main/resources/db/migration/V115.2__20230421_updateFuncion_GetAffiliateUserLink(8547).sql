drop function if exists func_get_affiliate_user_link(varchar, bigint);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_affiliate_user_link"("rolename" varchar, "currentuserid" int8)
  RETURNS TABLE("userid" int8, "username" varchar, "useremail" varchar, "usercode" varchar, "userlevel" int2) AS $BODY$
begin
	--Admin tổng
	if roleName like 'FULL_ADMIN' then
begin
			--L<PERSON>y ra tất cả affiliate_user
return query (
    select users.id, users."name", users.email,
                     affiliate_users.affiliate_code, affiliate_users.affiliate_level
                         from vnpt_dev.affiliate_users join vnpt_dev.users
					on affiliate_users.user_id = users.id
				where users.deleted_flag = 1 and affiliate_users.affiliate_status = 2
				union
				-- lấy ra tất cả full_admin tạo link affiliate
				select
				    users.id,
                     'FULL ADMIN' as name, users.email,
                     null as affiliate_code,
                     null as affiliate_level
                 from vnpt_dev.users
                 join vnpt_dev.users_roles on users.id = users_roles.user_id
                 where
                     users_roles.role_id = (
                                             select id
                                             from vnpt_dev.role
                                             where name = 'FULL_ADMIN')
                     and users.id in (select created_by from vnpt_dev.affiliate_link)
    );
end;
	--Đại lý affiliate
	elseif roleName like 'ROLE_AFFILIATE_DAILY' then
begin
return query (
    --Đệ quy để lấy ra đại lý và tất cả thành viên con
    with recursive affiliate_users_all_level_cte as (
					select user_id, affiliate_code, affiliate_level from vnpt_dev.affiliate_users
					where user_id = currentuserid
						and affiliate_users.affiliate_status = 2
					union all
					select aff_users_child.user_id, aff_users_child.affiliate_code, aff_users_child.affiliate_level
					from vnpt_dev.affiliate_users aff_users_child join affiliate_users_all_level_cte aff_users_parent
						on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
					where aff_users_child.affiliate_status = 2
				)
				select users.id, users."name", users.email,
                                 aff_multi_level.affiliate_code, aff_multi_level.affiliate_level
                                         from affiliate_users_all_level_cte aff_multi_level join vnpt_dev.users
					on aff_multi_level.user_id = users.id
				where users.deleted_flag = 1
			                    -- lấy ra tất cả full_admin tạo link affiliate
				union
				select users.id, 'FULL ADMIN' as name, users.email,
                                 null as affiliate_code, null as affiliate_level
				from vnpt_dev.users join vnpt_dev.users_roles
                    on users.id = users_roles.user_id
                where users_roles.role_id = (select id from vnpt_dev.role where name = 'FULL_ADMIN')
                and users.id in
                    (select created_by from vnpt_dev.affiliate_link
                    where assigned_to in (select user_id from affiliate_users_all_level_cte))
    );
end;
	--Cá nhân affiliate
else
begin
			--Lấy ra thông tin user hiện tại
return query (
    select
					distinct on (id)
					*
				from
				(
						select users.id, users."name", users.email,
						 affiliate_users.affiliate_code, affiliate_users.affiliate_level
							 from vnpt_dev.affiliate_users join vnpt_dev.users
						on affiliate_users.user_id = users.id
					where users.id = currentuserid and users.deleted_flag = 1 and affiliate_users.affiliate_status = 2
					-- lấy ra tất cả full_admin tạo link affiliate
					union
					select users.id, 'FULL ADMIN' as name, users.email,
						 null as affiliate_code, null as affiliate_level
					from vnpt_dev.users join vnpt_dev.users_roles
											on users.id = users_roles.user_id
									where users_roles.role_id = (select id from vnpt_dev.role where name = 'FULL_ADMIN')
								and users.id in (select created_by from vnpt_dev.affiliate_link where assigned_to = currentuserid)
					--lấy ra những thằng affiliate cấp trên tạo link cho thằng cấp 3
					union
					select users.id, users.name as name, users.email,
						 null as affiliate_code, null as affiliate_level
					from vnpt_dev.users
					where users.id in (select created_by from vnpt_dev.affiliate_link where assigned_to = currentuserid)
				) as a
	);
end;
end if;
end $BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000