-- Tạo mapping dữ liệu mới
drop materialized view if exists vnpt_dev.mview_enterprise_address;
create materialized view vnpt_dev.mview_enterprise_address AS 
select
    enterprise.id as enterprise_id,
    province.id_new as province_id,
    wardMapping.ward_id_new as ward_id,
    streetMapping.street_id_new as street_id
from
    vnpt_dev.enterprise
    left join vnpt_dev.province on province.id = enterprise.province_id_old
    left join vnpt_dev.mview_mapping_ward wardMapping on wardMapping.province_id_old = enterprise.province_id_old and 
        wardMapping.district_id_old = enterprise.district_id_old and 
        wardMapping.ward_id_old = enterprise.ward_id_old
    left join vnpt_dev.geography_2025 streetMapping on streetMapping.province_id_old = enterprise.province_id_old and 
        streetMapping.district_id_old = enterprise.district_id_old and 
        streetMapping.ward_id_old = enterprise.ward_id_old and 
        streetMapping.street_id_old = enterprise.street_id_old;
-- Tạo index tăng tốc việc truy xuất
create index index_mViewEnterpriseAddress_enterpriseId ON vnpt_dev.mview_enterprise_address(enterprise_id);

-- <PERSON><PERSON><PERSON> nhật dữ liệu vào bảng enterprise qua batch
DO $$
DECLARE
    v_batch_size INTEGER := 100000; -- số bản ghi mỗi batch
    v_min_id BIGINT := 0;
    v_max_id BIGINT;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
BEGIN
    -- Tìm ID lớn nhất để làm mốc
    SELECT MAX(id) INTO v_max_id FROM vnpt_dev.enterprise;

    WHILE v_min_id <= v_max_id LOOP
        v_start_time := clock_timestamp();
        
        UPDATE vnpt_dev.enterprise mEnterprise
        SET province_id = mView.province_id,
            ward_id = mView.ward_id,
            street_id = mView.street_id
        FROM vnpt_dev.mview_enterprise_address mView
        WHERE mEnterprise.id = mView.enterprise_id
          AND (
                mEnterprise.province_id  IS DISTINCT FROM mView.province_id
                OR mEnterprise.ward_id   IS DISTINCT FROM mView.ward_id
                OR mEnterprise.street_id IS DISTINCT FROM mView.street_id
              )
          AND mEnterprise.id >= v_min_id
          AND mEnterprise.id < v_min_id + v_batch_size;

        v_min_id := v_min_id + v_batch_size;

        v_end_time := clock_timestamp();
        RAISE NOTICE 'Updated enterprise by batch from ID % to %, duration: % seconds',
                     v_min_id, v_min_id + v_batch_size - 1,
                     EXTRACT(EPOCH FROM (v_end_time - v_start_time));
    END LOOP;
END$$;