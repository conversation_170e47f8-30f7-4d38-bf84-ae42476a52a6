DELETE FROM "vnpt_dev"."action_notification" WHERE name IN ('Quy tắc phân giao');
INSERT INTO "vnpt_dev"."action_notification"("action_code", "name", "is_send_email", "is_send_sms", "is_notification", "parent_id", "created_by", "created_at", "modified_by", "modified_at", "receiver", "allow_change_email", "allow_change_sms", "allow_change_notification", "priority_order", "is_send_telegram", "allow_change_telegram") VALUES
    ('PG', 'Quy tắc phân giao', 1, 0, 1, -1, 'system', '2024-11-21 00:00:00', NULL, NULL, null, 'B', 'B', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 1, 'B');

update vnpt_dev.action_notification
set parent_id = (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quy tắc phân giao')
where action_code in ('PGD-01', 'PGD-02', 'PGD-03', 'PGD-04', 'PGD-05', 'PGD-06', 'PGD-07', 'PGD-08', 'PGD-09', 'PGD-10', 'PGD-11');