ALTER TABLE "vnpt_dev"."subscriptions_extradata" DROP COLUMN IF EXISTS "khcn_phone";
ALTER TABLE "vnpt_dev"."subscriptions_extradata" ADD COLUMN "khcn_phone" varchar(15);

COMMENT ON COLUMN "vnpt_dev"."subscriptions_extradata"."khcn_phone" IS 'Số điện thoại khách hàng đăng ký mua SPDV ban KHCN';

-- Fill dữ liệu
WITH raw AS (
    SELECT subscription_id as sub_id,
        coalesce(khcn_metadata ->> 'so_dt', khcn_metadata ->> 'phone_number') as phone_number
    FROM vnpt_dev.subscriptions_extradata
)

UPDATE vnpt_dev.subscriptions_extradata
    SET khcn_phone = raw.phone_number
FROM raw
WHERE
    subscription_id = raw.sub_id AND
    khcn_phone IS DISTINCT FROM raw.phone_number;