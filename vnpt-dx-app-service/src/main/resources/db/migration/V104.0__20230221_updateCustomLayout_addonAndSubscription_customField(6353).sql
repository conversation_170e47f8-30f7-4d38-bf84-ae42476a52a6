--======================== add creation_layout_id ==============================
ALTER TABLE "vnpt_dev"."addons" ADD COLUMN IF NOT EXISTS "creation_layout_id" int8;
ALTER TABLE "vnpt_dev"."addon_draft" ADD COLUMN IF NOT EXISTS "creation_layout_id" int8;

COMMENT ON COLUMN "vnpt_dev"."addons"."creation_layout_id" IS 'ID custom layout sử dụng để tạo/sửa dịch vụ. <PERSON><PERSON> giá trị null nếu sử dụng custom layout mặc định';
COMMENT ON COLUMN "vnpt_dev"."addon_draft"."creation_layout_id" IS 'ID custom layout sử dụng để tạo/sửa dịch vụ. Có giá trị null nếu sử dụng custom layout mặc định';

ALTER TABLE "vnpt_dev"."custom_layout"
DROP COLUMN "portal_type";
-- update customField
-- Thêm trường bảng custom_layout
ALTER TABLE "vnpt_dev"."custom_layout" ADD COLUMN IF NOT EXISTS "customer_type" varchar;
ALTER TABLE "vnpt_dev"."custom_layout" ADD COLUMN IF NOT EXISTS "portal_type" int2;
COMMENT ON COLUMN "vnpt_dev"."custom_layout"."customer_type" IS 'Loại khách hàng được áp dụng custom layout';
COMMENT ON COLUMN "vnpt_dev"."custom_layout"."portal_type" IS 'Portal sử dụng layout';
-- Thêm trường bảng custom_field
ALTER TABLE "vnpt_dev"."custom_field" ADD COLUMN IF NOT EXISTS "customer_type" varchar;
ALTER TABLE "vnpt_dev"."custom_field" ADD COLUMN IF NOT EXISTS "portal_type" int2;
COMMENT ON COLUMN "vnpt_dev"."custom_field"."customer_type" IS 'Loại khách hàng được áp dụng custom layout';
COMMENT ON COLUMN "vnpt_dev"."custom_field"."portal_type" IS 'Portal sử dụng layout';

update vnpt_dev.custom_layout set portal_type = 1
where name in ('Tạo thuê bao (Khách hàng cá nhân) - Admin', 'Tạo thuê bao (Khách hàng hộ kinh doanh) - Admin','Tạo thuê bao (Khách hàng Doanh nghiệp) - Admin');
update vnpt_dev.custom_layout set portal_type = 3
where name in ('Tạo thuê bao - SME doanh nghiệp', 'Tạo thuê bao - SME hộ kinh doanh', 'Tạo thuê bao - SME cá nhân');

--======================== bo sung component thue bao =======================
DELETE FROM vnpt_dev.custom_field WHERE "code" IN ('enterprise.customer.info.1', 'enterprise.sub.info.1', 'enterprise.account.info.2', 'enterprise.customer.info.2', 'enterprise.sub.info.2', 'houseHold.customer.info.1', 'houseHold.sub.info.1', 'houseHold.account.info.2', 'houseHold.customer.info.2', 'houseHold.sub.info.2', 'personal.customer.info.1', 'personal.sub.info.1', 'personal.account.info.2', 'personal.customer.info.2', 'personal.sub.info.2');
INSERT INTO vnpt_dev.custom_field ("name", code, is_standard, "type", category, config, lst_permission)
VALUES
	('Thông tin khách hàng', 'enterprise.customer.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin đơn hàng', 'enterprise.sub.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin tài khoản', 'enterprise.account.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin tài khoản","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'enterprise.customer.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin đơn hàng', 'enterprise.sub.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'houseHold.customer.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin đơn hàng', 'houseHold.sub.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin tài khoản', 'houseHold.account.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin tài khoản","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'houseHold.customer.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin đơn hàng', 'houseHold.sub.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'personal.customer.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin đơn hàng', 'personal.sub.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin tài khoản', 'personal.account.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin tài khoản","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'personal.customer.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin đơn hàng', 'personal.sub.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]');

DELETE FROM vnpt_dev.custom_field WHERE "code" IN ('sme.enterprise.sub.info.1', 'sme.enterprise.subSummary.1', 'sme.enterprise.billAddress.info.2', 'sme.enterprise.customer.info.2', 'sme.enterprise.contact.info.2', 'sme.enterprise.paymentMethod.2', 'sme.enterprise.subSummary.2','sme.houseHold.sub.info.1', 'sme.houseHold.subSummary.1', 'sme.houseHold.billAddress.info.2', 'sme.houseHold.customer.info.2', 'sme.houseHold.contact.info.2', 'sme.houseHold.paymentMethod.2', 'sme.houseHold.subSummary.2', 'sme.personal.sub.info.1', 'sme.personal.subSummary.1', 'sme.personal.billAddress.info.2', 'sme.personal.customer.info.2', 'sme.personal.contact.info.2', 'sme.personal.paymentMethod.2', 'sme.personal.subSummary.2');
INSERT INTO vnpt_dev.custom_field ("name", code, is_standard, "type", category, config, lst_permission)
VALUES
	('Thông tin đơn hàng ', 'sme.enterprise.sub.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng ","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Tóm tắt đơn hàng', 'sme.enterprise.subSummary.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Tóm tắt đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin xuất hóa đơn', 'sme.enterprise.billAddress.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin xuất hóa đơn","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'sme.enterprise.customer.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin người liên hệ', 'sme.enterprise.contact.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin người liên hệ","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Phương thức thanh toán', 'sme.enterprise.paymentMethod.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Phương thức thanh toán","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Tóm tắt đơn hàng', 'sme.enterprise.subSummary.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Tóm tắt đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),

	('Thông tin đơn hàng ', 'sme.houseHold.sub.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng ","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Tóm tắt đơn hàng', 'sme.houseHold.subSummary.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Tóm tắt đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin xuất hóa đơn', 'sme.houseHold.billAddress.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin xuất hóa đơn","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'sme.houseHold.customer.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin người liên hệ', 'sme.houseHold.contact.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin người liên hệ","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Phương thức thanh toán', 'sme.houseHold.paymentMethod.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Phương thức thanh toán","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Tóm tắt đơn hàng', 'sme.houseHold.subSummary.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Tóm tắt đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),

	('Thông tin đơn hàng ', 'sme.personal.sub.info.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin đơn hàng ","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Tóm tắt đơn hàng', 'sme.personal.subSummary.1', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Tóm tắt đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin xuất hóa đơn', 'sme.personal.billAddress.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin xuất hóa đơn","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin khách hàng', 'sme.personal.customer.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin khách hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Thông tin người liên hệ', 'sme.personal.contact.info.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Thông tin người liên hệ","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Phương thức thanh toán', 'sme.personal.paymentMethod.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Phương thức thanh toán","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]'),
	('Tóm tắt đơn hàng', 'sme.personal.subSummary.2', TRUE, 'SECTION', 'SUBSCRIPTION','{"label":"Tóm tắt đơn hàng","labelEnabled":null,"hintText":"","isUnique":null,"mandatory":null,"mandatoryCondition":{},"tooltipsEnabled":null,"tooltipsContent":"","noteEnabled":null,"noteContent":"","smeEnabled":null,"devEnabled":null,"adminEnabled":null,"displayOnDetailPage":null,"canEdit":null,"lstValue":[],"defaultValue":"","pattern":null,"lstPatternToken":null,"patternCombination":null,"maxLength":null,"displayFormat":null,"defaultDisplay":null,"uploadType":null,"uploadExtension":null,"uploadMaxSize":null,"uploadMaxFile":null,"getUploadMaxFile":null,"urlMaxNum":null,"urlPattern":null}','[]');


--============== Update lst_standard_field thuê bao  =================
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('taxCode', 'lastName', 'firstName', 'email', 'phoneNumber','isActivated', 'notificationType', 'enterprise.customer.info.1', 'enterprise.sub.info.1', 'enterprise.account.info.2', 'enterprise.customer.info.2', 'enterprise.sub.info.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng Doanh nghiệp) - Admin');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('taxCode', 'lastName', 'firstName', 'email', 'phoneNumber','isActivated', 'notificationType', 'houseHold.customer.info.1', 'houseHold.sub.info.1', 'houseHold.account.info.2', 'houseHold.customer.info.2', 'houseHold.sub.info.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng hộ kinh doanh) - Admin');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('taxCode', 'lastName', 'firstName', 'email', 'phoneNumber','isActivated', 'notificationType', 'personal.customer.info.1', 'personal.sub.info.1', 'personal.account.info.2', 'personal.customer.info.2', 'personal.sub.info.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng cá nhân) - Admin');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('sme.enterprise.sub.info.1', 'sme.enterprise.subSummary.1', 'sme.enterprise.billAddress.info.2', 'sme.enterprise.customer.info.2', 'sme.enterprise.contact.info.2', 'sme.enterprise.paymentMethod.2', 'sme.enterprise.subSummary.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao - SME doanh nghiệp');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('sme.houseHold.sub.info.1', 'sme.houseHold.subSummary.1','sme.houseHold.billAddress.info.2', 'sme.houseHold.customer.info.2','sme.houseHold.contact.info.2','sme.houseHold.paymentMethod.2', 'sme.houseHold.subSummary.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao - SME hộ kinh doanh');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('sme.personal.sub.info.1', 'sme.personal.subSummary.1', 'sme.personal.billAddress.info.2', 'sme.personal.customer.info.2', 'sme.personal.contact.info.2', 'sme.personal.paymentMethod.2', 'sme.personal.subSummary.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao - SME cá nhân');

ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";