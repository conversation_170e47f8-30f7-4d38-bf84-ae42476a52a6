COMMENT ON COLUMN vnpt_dev.services.provider_type IS 'Đơn vị phát triển. 0:VNPT, 1:PARTNER, 2:THIRD_PARTY, 3:<PERSON><PERSON><PERSON> hợp <PERSON>, 4:VNPT_TECHNOLOGY (T<PERSON><PERSON> ra từ cột service_owner và service_owner_partner)';
COMMENT ON COLUMN vnpt_dev.services_draft.provider_type IS 'Đơn vị phát triển. 0:VNPT, 1:PARTNER, 2:THIRD_PARTY, 3:<PERSON><PERSON><PERSON> hợ<PERSON>, 4:VNPT_TECHNOLOGY (Tách ra từ cột service_owner và service_owner_partner)';
COMMENT ON COLUMN vnpt_dev.combo.provider_type IS 'Đơn vị phát triển. 0:VNPT, 2:THIRD_PARTY, 4:VNPT_TECHNOLOGY (Tách ra từ cột combo_owner)';
COMMENT ON COLUMN vnpt_dev.combo_draft.provider_type IS 'Đơn vị phát triển. 0:VNPT, 2:THIRD_PARTY, 4:VNPT_TECHNOLOGY (Tách ra từ cột combo_owner)';

--========= bảng services =========
with new_data as (
    select id,
    case -- 0: ON, 1: OS
        when service_type_application is not null then 0 -- ứng dụng là ON
        when service_owner_partner = 0 then 0
        when service_owner_partner = 1 then 1
        when service_owner in (0, 1, 4) then 0
        when service_owner in (2, 3, 5) then 1
        else 1
    end as on_os,
    case -- 0: VNPT, 1: PARTNER, 2: THIRD_PARTY, 3: BAN_KHCN, 4: VNPT_TECHNOLOGY
        when created_source_migration = 2 then 3
        when service_owner_partner is not null then 1
        when service_owner in (0, 2) then 2
        when service_owner in (1, 3) then 0
        when service_owner in (4, 5) then 4
        else 0
    end as provider
    from vnpt_dev.services
)
update vnpt_dev.services
set on_os_type = new_data.on_os,
    provider_type = new_data.provider
from new_data
where
    services.id = new_data.id and
    (on_os_type is distinct from new_data.on_os
    or provider_type is distinct from new_data.provider);

--======== bảng service_draft =========
with new_data as (
    select id,
    case -- 0: ON, 1: OS
        when service_type_application is not null then 0 -- ứng dụng là ON
        when service_owner_partner = 0 then 0
        when service_owner_partner = 1 then 1
        when service_owner in (0, 1, 4) then 0
        when service_owner in (2, 3, 5) then 1
        else 1
    end as on_os,
    case -- 0: VNPT, 1: PARTNER, 2: THIRD_PARTY, 3: BAN_KHCN, 4: VNPT_TECHNOLOGY
        when created_source_migration = 2 then 3
        when service_owner_partner is not null then 1
        when service_owner in (0, 2) then 2
        when service_owner in (1, 3) then 0
        when service_owner in (4, 5) then 4
        else 0
    end as provider
    from vnpt_dev.services_draft
)
update vnpt_dev.services_draft
set on_os_type = new_data.on_os,
    provider_type = new_data.provider
from new_data
where
    services_draft.id = new_data.id and
    (on_os_type is distinct from new_data.on_os
    or provider_type is distinct from new_data.provider);

--======== bảng combo =============
with new_data as (
        select id,
        case -- 0: ON, 1: OS
            when combo_owner in (0, 1, 4, 6, 7) then 0
            else 1
        end as on_os,
        case -- 0:VNPT, 2:THIRD_PARTY, 4:VNPT_TECHNOLOGY (Tách ra từ cột combo_owner)
            when combo_owner in (1, 3, 4) then 0
            when combo_owner in (6, 7, 8) then 4
            else 2
        end as provider
        from vnpt_dev.combo
    )
    update vnpt_dev.combo
    set on_os_type = new_data.on_os,
        provider_type = new_data.provider
    from new_data
    where
        combo.id = new_data.id and
        (on_os_type is distinct from new_data.on_os
        or provider_type is distinct from new_data.provider);

--======== bảng combo_draft =============
with new_data as (
    select id,
    case -- 0: ON, 1: OS
        when combo_owner in (0, 1, 4, 6, 7) then 0
        else 1
    end as on_os,
    case -- 0:VNPT, 2:THIRD_PARTY, 4:VNPT_TECHNOLOGY (Tách ra từ cột combo_owner)
        when combo_owner in (1, 3, 4) then 0
        when combo_owner in (6, 7, 8) then 4
        else 2
    end as provider
    from vnpt_dev.combo_draft
)
update vnpt_dev.combo_draft
set on_os_type = new_data.on_os,
    provider_type = new_data.provider
from new_data
where
    combo_draft.id = new_data.id and
    (on_os_type is distinct from new_data.on_os
    or provider_type is distinct from new_data.provider);