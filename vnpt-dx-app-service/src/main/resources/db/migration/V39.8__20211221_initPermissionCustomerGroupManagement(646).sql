DELETE FROM vnpt_dev.apis WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id FROM vnpt_dev.api_permission JOIN vnpt_dev.permission ON permission.id = api_permission.permission_id
    WHERE vnpt_dev.permission.code IN ('QUAN_LY_NHOM_KHACH_HANG', 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET', 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG', 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU', 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM')
);

DELETE FROM vnpt_dev.api_permission WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_NHOM_KHACH_HANG', 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET', 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG', 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU', 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM'))
);

DELETE FROM vnpt_dev.roles_permissions WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_NHOM_KHACH_HANG', 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET', 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG', 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU', 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM'))
);

DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_NHOM_KHACH_HANG', 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET', 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG', 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU', 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM'))
);

DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_NHOM_KHACH_HANG', 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH', 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET', 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM', 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG', 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU', 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM');


-- INSERT vao bang permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Quản lý nhóm khách hàng',
        'QUAN_LY_NHOM_KHACH_HANG',
        -1,
        15000000
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem danh sách nhóm khách hàng',
        'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000001
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Thêm mới nhóm khách hàng',
        'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000002
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Sửa nhóm khách hàng',
        'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000003
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết nhóm khách hàng',
        'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000004
    );


INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết nhóm khách hàng / Thêm Khách hàng vào nhóm',
        'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000005
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết nhóm khách hàng /Xóa khách hàng khỏi nhóm',
        'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000006
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Bật/tắt hoạt động',
        'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000007
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Lưu trữ/Bỏ lưu trữ',
        'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000008
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xóa nhóm',
        'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        15000009
    );

-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
(
    (SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 2 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 3 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 4 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 5 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 6 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 7 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 8 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_BAT_TAT_HOAT_DONG' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 9 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_LUU_TRU' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 10 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM' ORDER BY id DESC LIMIT 1),
    1
);

-- Add vao bang apis, api_permission

--add api UC4. Lấy danh sách tên khách hàng trong nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/get-all-customer-name',
        'ROLE_QLNKH_LAY_DANH_SACH_TEN_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_DANH_SACH_TEN_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC4. Lấy danh sách mã số thuế trong nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/get-all-customer-tax-code',
        'ROLE_QLNKH_LAY_DANH_SACH_MA_SO_THUE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_DANH_SACH_MA_SO_THUE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC4. Lấy danh sách người đại diện trong nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/get-all-representative',
        'ROLE_QLNKH_LAY_DANH_SACH_NGUOI_DAI_DIEN',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_DANH_SACH_NGUOI_DAI_DIEN' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC4. Lấy chi tiết nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-detail',
        'ROLE_QLNKH_LAY_CHI_TIET_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_CHI_TIET_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC4. Xóa nhiều khách hàng trong nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/remove-lst-customer',
        'ROLE_QLNKH_XOA_NHIEU_KHACH_HANG_KHOI_NHOM',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_XOA_NHIEU_KHACH_HANG_KHOI_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM' ORDER BY id DESC LIMIT 1)
    );

--add api UC4. Thêm doanh nghiệp/ liên hệ vào nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/add-lst-customer',
        'ROLE_QLNKH_THEM_KHACH_HANG_VAO_NHOM',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_THEM_KHACH_HANG_VAO_NHOM' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_KHACH_HANG_VAO_NHOM' ORDER BY id DESC LIMIT 1)
    );

--add api UC456. Lấy danh sách doanh nghiệp
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/popup/get-lst-enterprise',
        'ROLE_QLNKH_POPUP_LAY_DANH_SACH_DOANH_NGHIEP',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_POPUP_LAY_DANH_SACH_DOANH_NGHIEP' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC456. Lấy danh sách doanh nghiệp được chọn
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/popup/get-lst-selected-enterprise',
        'ROLE_QLNKH_POPUP_LAY_DANH_SACH_DOANH_NGHIEP_DUOC_CHON',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_POPUP_LAY_DANH_SACH_DOANH_NGHIEP_DUOC_CHON' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC456. Lấy danh sách liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/popup/get-all-contact',
        'ROLE_QLNKH_POPUP_LAY_DANH_SACH_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_POPUP_LAY_DANH_SACH_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC456. Lấy danh sách liên hệ được chọn
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/popup/get-all-selected-contact',
        'ROLE_QLNKH_POPUP_LAY_DANH_SACH_LIEN_HE_DUOC_CHON',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_POPUP_LAY_DANH_SACH_LIEN_HE_DUOC_CHON' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC1. Lấy danh sách nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/get-all-group-name',
        'ROLE_QLNKH_COMBOBOX_LAY_DANH_SACH_TEN_CAC_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_COMBOBOX_LAY_DANH_SACH_TEN_CAC_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --add api UC1. Lấy danh sách người tạo nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/get-all-creator',
        'ROLE_QLNKH_COMBOBOX_LAY_DANH_SACH_NGUOI_TAO_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_COMBOBOX_LAY_DANH_SACH_NGUOI_TAO_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

        --add api UC1. Lấy danh sách nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-all',
        'ROLE_QLNKH_LAY_DANH_SACH_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_DANH_SACH_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

            --add api UC1. Xóa 1 nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/delete-by-id',
        'ROLE_QLNKH_XOA_NHOM_KHACH_HANG',
        'DELETE'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_XOA_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_NHOM' ORDER BY id DESC LIMIT 1)
    );

                --add api UC1. Update trạng thái hoạt động của nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/update-active-status',
        'ROLE_QLNKH_UPDATE_HOAT_DONG_NHOM_KHACH_HANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_UPDATE_HOAT_DONG_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

                   --add api UC1. Update trạng thái lưu trữ của nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/update-archive-status',
        'ROLE_QLNKH_UPDATE_LUU_TRU_NHOM_KHACH_HANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_UPDATE_LUU_TRU_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

                       --add api UC3. Lấy thông tin nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-group-info',
        'ROLE_QLNKH_LAY_THONG_TIN_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_THONG_TIN_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

--add api UC3. Lấy thông tin nhóm trong TH sửa nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-group-info',
        'ROLE_QLNKH_LAY_THONG_TIN_NHOM_KHACH_HANG_KHI_SUA',
        'GET'
    );

INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_THONG_TIN_NHOM_KHACH_HANG_KHI_SUA' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM' ORDER BY id DESC LIMIT 1)
    );

                         --add api UC3. Lấy thông tin doanh nghiệp của nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-lst-enterprise',
        'ROLE_QLNKH_LAY_THONG_TIN_DOANH_NGHIEP_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_THONG_TIN_DOANH_NGHIEP_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

     --add api UC3. Lấy thông tin danh sách liên hệ của nhóm
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-lst-contact',
        'ROLE_QLNKH_LAY_DANH_SACH_LIEN_HE_NHOM_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_DANH_SACH_LIEN_HE_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

     --add api UC3. Lấy thông tin danh sách khách hàng đã tải lên
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/get-lst-uploaded-customer',
        'ROLE_QLNKH_LAY_DANH_SACH_KHACH_HANG_DA_TAI_LEN',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_LAY_DANH_SACH_KHACH_HANG_DA_TAI_LEN' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

    --add api UC3. Cập nhật nhóm khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/update-group',
        'ROLE_QLNKH_CAP_NHAT_DANH_SACH_KHACH_HANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_CAP_NHAT_DANH_SACH_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_SUA_NHOM' ORDER BY id DESC LIMIT 1)
    );

--add api Tạo nhóm khách hàng

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group',
        'ROLE_QLNKH_TAO_NHOM_KHACH_HANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_TAO_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM' ORDER BY id DESC LIMIT 1)
    );

--add api import nhóm khách hàng

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/import-customer',
        'ROLE_QLNKH_IMPORT_NHOM_KHACH_HANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_IMPORT_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_THEM_NHOM' ORDER BY id DESC LIMIT 1)
    );

--add api UC4. Xóa ds khách hàng vừa import
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/remove-customer-uploaded',
        'ROLE_QLNKH_XOA_DS_KHACH_HANG_VUA_IMPORT',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_XOA_DS_KHACH_HANG_VUA_IMPORT' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG_XOA_KHACH_HANG_KHOI_NHOM' ORDER BY id DESC LIMIT 1)
    );

--add api Lấy thông tin danh sách liên hệ để hiển thị trên popup chọn khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-group/common/popup/get-lst-contact',
        'ROLE_QLNKH_DS_KHACH_HANG_LIEN_HE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLNKH_DS_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--api lay du lieu tax-code
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-tax-code',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_TAX_CODE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_CUSTOMER_BY_TAX_CODE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--api lay du lieu representative
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-representative',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_REPRESENTATIVE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_REPRESENTATIVE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--api lay du lieu combobox cac truong thong tin
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-preference',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_PREFERENCE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_PREFERENCE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--api lấy danh sách họ tên các liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact-name',
        'ROLE_QLKHLH_LAY_DANH_SACH_HO_TEN_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_HO_TEN_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--api lấy danh sách email liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact-email',
        'ROLE_QLKHLH_LAY_DANH_SACH_EMAIL_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_EMAIL_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );

--api lấy danh sách số điện thoại liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact-phone',
        'ROLE_QLKHLH_LAY_DANH_SACH_PHONE_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_PHONE_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_NHOM_KHACH_HANG' ORDER BY id DESC LIMIT 1)
    );


-- REFRESH MATERIALIZED VIEW
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;