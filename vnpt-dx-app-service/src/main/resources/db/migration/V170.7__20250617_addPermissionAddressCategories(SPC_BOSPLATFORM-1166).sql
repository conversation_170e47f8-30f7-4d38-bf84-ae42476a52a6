DELETE FROM vnpt_dev.permission WHERE code IN ('XEM_DANH_MUC_DIA_CHI', 'CAP_NHAT_DANH_MUC_DIA_CHI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
      (
          (SELECT max(id) + 1 from vnpt_dev.permission),
          'Xem danh mục sổ địa chỉ',
          'XEM_DANH_MUC_DIA_CHI',
          (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_1'),
          (SELECT max(priority) + 1 from vnpt_dev.permission)
      ),
      (
          (SELECT max(id) + 2 from vnpt_dev.permission),
          'Cập nhật danh mục sổ địa chỉ',
          'CAP_NHAT_DANH_MUC_DIA_CHI',
          (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_1'),
          (SELECT max(priority) + 4 from vnpt_dev.permission)
      );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
      (
          (SELECT max(id) + 1 from vnpt_dev.permission_portal),
          (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_MUC_DIA_CHI'),
          (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
      ),
      (
          (SELECT max(id) + 2 from vnpt_dev.permission_portal),
          (SELECT id from vnpt_dev.permission WHERE code = 'CAP_NHAT_DANH_MUC_DIA_CHI'),
          (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
      );


-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_MUC_DIA_CHI'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'CAP_NHAT_DANH_MUC_DIA_CHI'),
        0
    );

-- Cập nhật view phân quyền
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;