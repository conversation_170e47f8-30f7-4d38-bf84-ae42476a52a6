WITH pricing_contain_in_pricing_multi_plan AS (
   SELECT DISTINCT
       ( pricing.ID ) AS ID
   FROM
       vnpt_dev.pricing
           JOIN vnpt_dev.pricing_multi_plan ON pricing.ID = pricing_multi_plan.pricing_id
   ORDER BY
       pricing.ID ASC
),
reamining_pricing_infor AS (
   SELECT
       *
   FROM
       vnpt_dev.pricing
   WHERE
       pricing.ID :: BIGINT NOT IN ( SELECT UNNEST ( ARRAY_AGG ( ID ) :: int8 [] ) FROM pricing_contain_in_pricing_multi_plan )
    ORDER BY id ASC
)

INSERT INTO vnpt_dev.pricing_multi_plan (
    pricing_id,
    plan_name,
    pricing_plan,
    payment_cycle,
    number_of_cycles,
    circle_type,
    unit_id,
    currency_id,
    price,
    trial_type,
    number_of_trial,
    free_quantity,
    estimate_quantity,
    created_at,
    created_by,
    modified_at,
    modified_by,
    deleted_flag,
    pricing_draft_id,
    addon_id,
    default_circle,
    reference_id,
    addon_draft_id,
    cycle_code,
    customer_type_code,
    display_status,
    priority,
    converted_ids,
    minimum_quantity,
    maximum_quantity,
    migration_id,
    summary,
    description,
    original_id
)
SELECT
    id,
    null, --tên c<PERSON><PERSON> kế hoạch định giá
    pricing_plan,
    payment_cycle,
    number_of_cycles,
    cycle_type,
    unit_id,
    currency_id,
    price,
    trial_type,
    number_of_trial,
    free_quantity,
    estimate_quantity,
    created_at,
    created_by,
    modified_at,
    modified_by,
    deleted_flag,
    pricing_draft_id,
    null, --kế hoạch định giá của addon
    1,
    null, --id của kế hoạch định giá mà kế hoạch định giá này tham chiếu đến
    null, --id của kế hoạch định giá của addon draft
    null, --mã chu kỳ
    customer_type_code,
    1,
    1,
    null, --Các gói được chuyển đổi
    null, --số lượng tối thiểu
    null, --số lượng tối đa
    null, --id đồng bộ KHCN
    null, --mô tả ngắn
    null, --mô tả chi tiết
    null
FROM
    reamining_pricing_infor;

UPDATE vnpt_dev.pricing_multi_plan Set original_id = null;

WITH RECURSIVE cte (id, level, reference_id, root_id) AS (
    SELECT p1.id, 1 as level, p1.reference_id, p1.id as root_id
    from vnpt_dev.pricing_multi_plan p1
    WHERE p1.reference_id is null
    UNION
    SELECT p2.id, (cte.level + 1) as level, p2.reference_id, cte.root_id
    from vnpt_dev.pricing_multi_plan p2
             join cte on p2.reference_id = cte.id
)
update vnpt_dev.pricing_multi_plan p Set original_id = (select cte.root_id from cte where cte.id = p.id);