DROP FUNCTION IF EXISTS "vnpt_dev"."get_paid_info_of_customer";

CREATE OR REPLACE FUNCTION "vnpt_dev"."get_paid_info_of_customer"("i_customer_id" int8, "i_starttime" varchar, "i_endtime" varchar)
  RETURNS TABLE("username" varchar, "taxcode" varchar, "phonenumber" varchar, "email" varchar, "billaddress" varchar, "setupaddress" varchar, "billcode" varchar, "subscode" varchar, "servicename" varchar, "pricingname" varchar, "provider" varchar, "note" varchar, "amount" float8, "amountdiscounted" float8, "amountpretax" float8, "amounttax" float8, "amountrefund" float8, "amountaftertax" float8, "nextpaymentcycle" text, "paymentmethod" text, "paymentstatus" text, "paymentdate" text) AS $BODY$
	
	DECLARE
	
	BEGIN
	-- Routine body goes here...

	RETURN QUERY
		SELECT 
			us.name AS userName,
			us.tin AS taxCode,
			us.phone_number AS SDT,
			us.email AS EMAIL,
			us.address AS DiaChiXuatHoaDon,
			sub.address AS DiaChiLapDat,
			bill.billing_code AS MaHoaDon,
			sub.sub_code AS MaThueBao,
			CASE 
				WHEN sub.pricing_id IS NOT NULL THEN pricing_info.ServiceName
				WHEN sub.combo_plan_id IS NOT NULL THEN cp_info.ComboName
				ELSE ''
			END AS TenDichVu,
			CASE 
				WHEN sub.pricing_id IS NOT NULL THEN pricing_info.PricingName
				WHEN sub.combo_plan_id IS NOT NULL THEN cp_info.ComboPlanName
				ELSE ''
			END AS TenGoiDichVu,
			CASE 
				WHEN sub.pricing_id IS NOT NULL THEN pricing_info.ProviderName
				WHEN sub.combo_plan_id IS NOT NULL THEN cp_info.ProviderName
				ELSE ''
			END AS NhaCungCap,
			''::VARCHAR AS GhiChu,
			billing_item_total.amount AS SoTienTruocKhuyenMai, -- "Số tiền trước khuyến mại"
			(billing_item_total.amount - billing_item_total.amount_pre_tax) AS SoTienKhuyenMai , --"Số tiền khuyến mại"
			billing_item_total.amount_pre_tax AS SoTienChuaTinhThue,-- "Số tiền chưa tính thuế"
			(billing_item_total.amount_after_tax - billing_item_total.amount_pre_tax  ) AS SoTienNopThue, -- "Số tiền nộp thuế"
			0::float8 AS SoTienHoanTra,
			billing_item_total.amount_after_tax AS SoTienDaTinhThue, -- "Số tiền đã tính thuế"
			TO_CHAR(sub.next_payment_time, 'YYYY-MM-DD') AS ChuKyThanhToanTiepTheo,
			CASE
				WHEN sub.payment_method = 0 then 'Chuyển khoản (P2P)'
				WHEN sub.payment_method = 1 then 'VNPT Pay'
				WHEN sub.payment_method = 2 then 'Tiền mặt'
				ELSE 'Chuyển khoản (P2P)'
			END AS PhuongThucThanhToan,
			CASE
				WHEN COALESCE(bill.status, 0) = 0 then 'Khởi tạo'
				WHEN COALESCE(bill.status, 0) = 1 then 'Chờ thanh toán'
				WHEN COALESCE(bill.status, 0) = 2 then 'Đã thanh toán'
				WHEN COALESCE(bill.status, 0) = 3 then 'Thanh toán thất bại'
				WHEN COALESCE(bill.status, 0) = 4 then 'Quá hạn thanh toán'
			END AS TrangThaiThanhToan,
			TO_CHAR(bill.payment_date, 'YYYY-MM-DD') AS NgayThanhToan
			
		FROM vnpt_dev.billings AS bill
		LEFT JOIN vnpt_dev.subscriptions AS sub ON sub.id = bill.subscriptions_id
		LEFT JOIN vnpt_dev.users AS us ON us.id = sub.user_id
		LEFT JOIN (
			SELECT 
				p.id AS PricingId,
				p.pricing_name AS PricingName,
				s.service_name AS ServiceName,
				us.name AS ProviderName
			FROM vnpt_dev.pricing AS p
			LEFT JOIN vnpt_dev.services AS s ON s.id = p.service_id
			LEFT JOIN vnpt_dev.users AS us ON us.id = s.user_id
		) pricing_info ON (pricing_info.PricingId = sub.pricing_id AND sub.pricing_id IS NOT NULL)
		LEFT JOIN (
			SELECT 
				cp.id AS ComboPlanId,
				cp.combo_name AS ComboPlanName,
				co.combo_name AS ComboName,
				CASE
					WHEN 1 = ANY (user_role.Roles) OR 2 = ANY (user_role.Roles) THEN co.publisher -- Role_id cua Admin: 1, 2
					ELSE user_role.Name 
				END AS ProviderName
			FROM vnpt_dev.combo_plan AS cp
			LEFT JOIN vnpt_dev.combo AS co ON co.id = cp.combo_id
			LEFT JOIN (
				SELECT u.id AS UserId, u.name AS Name , ARRAY_AGG(ur.role_id) AS Roles
				FROM vnpt_dev.users AS u
				LEFT JOIN vnpt_dev.users_roles AS ur ON ur.user_id = u.id
				GROUP BY u.id
			) AS user_role ON user_role.UserId = co.user_id
		) cp_info ON (cp_info.ComboPlanId = sub.combo_plan_id AND sub.combo_plan_id IS NOT NULL)

		LEFT JOIN (
			SELECT 
				B_Items.billing_id, 
				sum(B_Items.amount) as amount, 
				sum(B_Items.amount_after) as amount_after, 
				sum(B_Items.amount_pre_tax) as amount_pre_tax, 
				sum(B_Items.amount_after_tax) as amount_after_tax 
			FROM vnpt_dev.bill_item as B_Items 
			GROUP BY B_Items.billing_id 
		) AS billing_item_total ON billing_item_total.billing_id = bill.id
		
		WHERE bill.status = 2 AND 
					us.id = i_customer_id AND
					(bill.payment_date::date >= i_starttime::date AND bill.payment_date::date <= i_endtime::date);
		
END$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;