-- New function get_paid_info_of_customer_curr_term--
DROP FUNCTION IF EXISTS "vnpt_dev"."get_paid_info_of_customer_curr_term_new";

CREATE OR REPLACE FUNCTION "vnpt_dev"."get_paid_info_of_customer_curr_term_new"("i_customer_id" int8, "i_starttime" varchar, "i_endtime" varchar)
    RETURNS TABLE
            (
                "username"         varchar,
                "taxcode"          varchar,
                "phonenumber"      varchar,
                "email"            varchar,
                "billaddress"      varchar,
                "setupaddress"     varchar,
                "billcode"         varchar,
                "subscode"         varchar,
                "servicename"      varchar,
                "pricingname"      varchar,
                "provider"         varchar,
                "note"             varchar,
                "amount"           float8,
                "amountdiscounted" float8,
                "amountpretax"     float8,
                "amounttax"        float8,
                "amountrefund"     float8,
                "amountaftertax"   float8,
                "nextpaymentcycle" text,
                "paymentmethod"    text,
                "paymentstatus"    text,
                "paymentdate"      text,
                "planpaymentdate"  text
            )
AS
$BODY$

DECLARE

BEGIN
    RETURN QUERY
        SELECT us.name                                                                   AS userName,
               us.tin                                                                    AS taxCode,
               us.phone_number                                                           AS SDT,
               us.email                                                                  AS EMAIL,
               us.address                                                                AS DiaChiXuatHoaDon,
               sub.address                                                               AS DiaChiLapDat,
               bill.billing_code                                                         AS MaHoaDon,
               sub.sub_code                                                              AS MaThueBao,
               CASE
                   WHEN sub.pricing_id IS NOT NULL THEN services.service_name
                   WHEN sub.combo_plan_id IS NOT NULL THEN combo.combo_name
                   ELSE ''
                   END                                                                   AS TenDichVu,
               CASE
                   WHEN sub.pricing_id IS NOT NULL THEN pricing.pricing_name
                   WHEN sub.combo_plan_id IS NOT NULL THEN cbPlan.combo_name
                   ELSE ''
                   END                                                                   AS TenGoiDichVu,
               CASE
                   WHEN sub.pricing_id IS NOT NULL THEN pProvider.name
                   WHEN sub.combo_plan_id IS NOT NULL THEN (
                       CASE
                           WHEN vCbProviderAdminRole.user_id IS NOT NULL THEN combo.publisher
                           ELSE cbProvider.name
                           END
                       )
                   ELSE ''
                   END                                                                   AS NhaCungCap,
               ''::VARCHAR                                                               AS GhiChu,
               billing_item_total.amount                                                 AS SoTienTruocKhuyenMai, -- "Số tiền trước khuyến mại"
               (billing_item_total.amount - billing_item_total.amount_pre_tax)           AS SoTienKhuyenMai,      --"Số tiền khuyến mại"
               billing_item_total.amount_pre_tax                                         AS SoTienChuaTinhThue,-- "Số tiền chưa tính thuế"
               (billing_item_total.amount_after_tax - billing_item_total.amount_pre_tax) AS SoTienNopThue,        -- "Số tiền nộp thuế"
               0::float8                                                                 AS SoTienHoanTra,
               billing_item_total.amount_after_tax                                       AS SoTienDaTinhThue,     -- "Số tiền đã tính thuế"
               TO_CHAR(sub.next_payment_time, 'YYYY-MM-DD')                              AS ChuKyThanhToanTiepTheo,
               CASE
                   WHEN sub.payment_method = 0 then 'Chuyển khoản (P2P)'
                   WHEN sub.payment_method = 1 then 'VNPT Pay'
                   WHEN sub.payment_method = 2 then 'Tiền mặt'
                   ELSE 'Chuyển khoản (P2P)'
                   END                                                                   AS PhuongThucThanhToan,
               CASE
                   WHEN COALESCE(bill.status, 0) = 0 then 'Khởi tạo'
                   WHEN COALESCE(bill.status, 0) = 1 then 'Chờ thanh toán'
                   WHEN COALESCE(bill.status, 0) = 2 then 'Đã thanh toán'
                   WHEN COALESCE(bill.status, 0) = 3 then 'Thanh toán thất bại'
                   WHEN COALESCE(bill.status, 0) = 4 then 'Quá hạn thanh toán'
                   END                                                                   AS TrangThaiThanhToan,
               TO_CHAR(bill.payment_date, 'YYYY-MM-DD')                                  AS NgayThanhToan,
               TO_CHAR(sub.current_payment_date, 'YYYY-MM-DD')                           AS NgayCanThanhToan

        FROM vnpt_dev.billings AS bill
                 LEFT JOIN vnpt_dev.subscriptions AS sub ON sub.id = bill.subscriptions_id
                 LEFT JOIN vnpt_dev.users AS us ON us.id = sub.user_id
                 LEFT JOIN vnpt_dev.pricing ON pricing.id = sub.pricing_id
                 LEFT JOIN vnpt_dev.services ON pricing.service_id = services.id
                 LEFT JOIN vnpt_dev.users AS pProvider ON pProvider.id = services.user_id
                 LEFT JOIN vnpt_dev.combo_plan AS cbPlan ON cbPlan.id = sub.combo_plan_id
                 LEFT JOIN vnpt_dev.combo AS combo ON combo.id = cbPlan.combo_id
                 LEFT JOIN vnpt_dev.users AS cbProvider ON cbProvider.id = combo.user_id
                 LEFT JOIN vnpt_dev.view_role_admin AS vCbProviderAdminRole ON cbProvider.id = vCbProviderAdminRole.user_id
                 LEFT JOIN (
                            SELECT  B_Items.billing_id,
                                    sum(B_Items.amount)           as amount,
                                    sum(B_Items.amount_after)     as amount_after,
                                    sum(B_Items.amount_pre_tax)   as amount_pre_tax,
                                    sum(B_Items.amount_after_tax) as amount_after_tax
                                FROM vnpt_dev.bill_item as B_Items
                                GROUP BY B_Items.billing_id
                            ) AS billing_item_total ON billing_item_total.billing_id = bill.id

        WHERE bill.status = 2
          AND us.id = i_customer_id
          AND (bill.payment_date::date >= i_starttime::date AND bill.payment_date::date <= i_endtime::date);

END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;


--- New fuction get_payment_info_of_customer_curr_term --
DROP FUNCTION IF EXISTS "vnpt_dev"."get_payment_info_of_customer_curr_term_new";

CREATE OR REPLACE FUNCTION "vnpt_dev"."get_payment_info_of_customer_curr_term_new"("i_customer_id" int8, "i_starttime" varchar, "i_endtime" varchar)
    RETURNS TABLE
            (
                "username"         varchar,
                "taxcode"          varchar,
                "phonenumber"      varchar,
                "email"            varchar,
                "billaddress"      varchar,
                "setupaddress"     varchar,
                "billcode"         varchar,
                "subscode"         varchar,
                "servicename"      varchar,
                "pricingname"      varchar,
                "provider"         varchar,
                "note"             varchar,
                "amount"           float8,
                "amountdiscounted" float8,
                "amountpretax"     float8,
                "amounttax"        float8,
                "amountrefund"     float8,
                "amountaftertax"   float8,
                "nextpaymentcycle" text,
                "paymentmethod"    text,
                "paymentstatus"    text,
                "paymentdate"      text,
                "planpaymentdate"  text
            )
AS
$BODY$

DECLARE

BEGIN
    RETURN QUERY
        SELECT us.name                                                                   AS TenKhachHang,
               us.tin                                                                    AS MaSoThue,
               us.phone_number                                                           AS SDT,
               us.email                                                                  AS EMAIL,
               us.address                                                                AS DiaChiXuatHoaDon,
               sub.address                                                               AS DiaChiLapDat,
               latest_bill.billing_code                                                  AS MaHoaDon,
               sub.sub_code                                                              AS MaThueBao,
               CASE
                   WHEN sub.pricing_id IS NOT NULL THEN services.service_name
                   WHEN sub.combo_plan_id IS NOT NULL THEN combo.combo_name
                   ELSE ''
                   END                                                                   AS TenDichVu,
               CASE
                   WHEN sub.pricing_id IS NOT NULL THEN pricing.pricing_name
                   WHEN sub.combo_plan_id IS NOT NULL THEN cbPlan.combo_name
                   ELSE ''
                   END                                                                   AS TenGoiDichVu,
               CASE
                   WHEN sub.pricing_id IS NOT NULL THEN pProvider.name
                   WHEN sub.combo_plan_id IS NOT NULL THEN (
                       CASE
                           WHEN vCbProviderAdminRole.user_id IS NOT NULL THEN combo.publisher
                           ELSE cbProvider.name
                           END
                       )
                   ELSE ''
                   END                                                                   AS NhaCungCap,
               ''::VARCHAR                                                               AS GhiChu,
               billing_item_total.amount                                                 AS SoTienTruocKhuyenMai, -- "Số tiền trước khuyến mại"
               (billing_item_total.amount - billing_item_total.amount_pre_tax)           AS SoTienKhuyenMai,      --"Số tiền khuyến mại"
               billing_item_total.amount_pre_tax                                         AS SoTienChuaTinhThue,-- "Số tiền chưa tính thuế"
               (billing_item_total.amount_after_tax - billing_item_total.amount_pre_tax) AS SoTienNopThue,        -- "Số tiền nộp thuế"
               0::float8                                                                 AS SoTienHoanTra,
               billing_item_total.amount_after_tax                                       AS SoTienDaTinhThue,     -- "Số tiền đã tính thuế"
               TO_CHAR(sub.next_payment_time, 'YYYY-MM-DD')                              AS ChuKyThanhToanTiepTheo,
               CASE
                   WHEN sub.payment_method = 0 then 'Chuyển khoản (P2P)'
                   WHEN sub.payment_method = 1 then 'VNPT Pay'
                   WHEN sub.payment_method = 2 then 'Tiền mặt'
                   ELSE 'Chuyển khoản (P2P)'
                   END                                                                   AS PhuongThucThanhToan,
               CASE
                   WHEN COALESCE(latest_bill.status, 0) = 0 then 'Khởi tạo'
                   WHEN COALESCE(latest_bill.status, 0) = 1 then 'Chờ thanh toán'
                   WHEN COALESCE(latest_bill.status, 0) = 2 then 'Đã thanh toán'
                   WHEN COALESCE(latest_bill.status, 0) = 3 then 'Thanh toán thất bại'
                   WHEN COALESCE(latest_bill.status, 0) = 4 then 'Quá hạn thanh toán'
                   END                                                                   AS TrangThaiThanhToan,
               ''                                                                        AS NgayThanhToan,
               TO_CHAR(sub.current_payment_date, 'YYYY-MM-DD')                           AS NgayCanThanhToan

        FROM vnpt_dev.subscriptions AS sub
                 LEFT JOIN (
                                SELECT DISTINCT ON (bill.subscriptions_id) bill.id,
                                                                       bill.status,
                                                                       bill.billing_code,
                                                                       bill.subscriptions_id
                                    FROM vnpt_dev.billings AS bill
                                    ORDER BY bill.subscriptions_id, bill.created_at DESC
                            ) AS latest_bill ON latest_bill.subscriptions_id = sub.id
                 LEFT JOIN vnpt_dev.users AS us ON us.id = sub.user_id
                 LEFT JOIN vnpt_dev.pricing ON pricing.id = sub.pricing_id
                 LEFT JOIN vnpt_dev.services ON pricing.service_id = services.id
                 LEFT JOIN vnpt_dev.users AS pProvider ON pProvider.id = services.user_id
                 LEFT JOIN vnpt_dev.combo_plan AS cbPlan ON cbPlan.id = sub.combo_plan_id
                 LEFT JOIN vnpt_dev.combo AS combo ON combo.id = cbPlan.combo_id
                 LEFT JOIN vnpt_dev.users AS cbProvider ON cbProvider.id = combo.user_id
                 LEFT JOIN vnpt_dev.view_role_admin AS vCbProviderAdminRole ON cbProvider.id = vCbProviderAdminRole.user_id
                 LEFT JOIN (
                            SELECT  B_Items.billing_id,
                                    sum(B_Items.amount)           as amount,
                                    sum(B_Items.amount_after)     as amount_after,
                                    sum(B_Items.amount_pre_tax)   as amount_pre_tax,
                                    sum(B_Items.amount_after_tax) as amount_after_tax
                                FROM vnpt_dev.bill_item as B_Items
                                GROUP BY B_Items.billing_id
                            ) AS billing_item_total ON billing_item_total.billing_id = latest_bill.id

        WHERE sub.confirm_status = 1
          AND us.id = i_customer_id
          AND ((latest_bill.status = 1 AND sub.current_payment_date::date >= i_starttime::date AND
                sub.current_payment_date::date <= i_endtime::date) OR
               (latest_bill.status = 2 AND sub.next_payment_time::date >= i_starttime::date AND sub.next_payment_time::date <= i_endtime::date));

END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000;

