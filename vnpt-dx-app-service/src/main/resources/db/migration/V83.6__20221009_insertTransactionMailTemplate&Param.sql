INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA', 'Quản lý giao dịch', null, null, null, null, null, null, null, null, 2900, null);


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-01', '<PERSON><PERSON><PERSON> thông báo khi có sự cố đăng ký mới', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đăng ký thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đăng ký thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29001, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$USER', '[Người dùng]', 'SLA-01', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-01', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-01', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-01', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-01', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$LINK_INCIDENT', '[Link trang chi tiết giao dịch xảy ra sự cố]', 'SLA-01', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-01', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-01' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-01', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-02', 'Gửi thông báo khi có sự cố đăng ký mới', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đăng ký thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đăng ký thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29002, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$USER', '[Người dùng]', 'SLA-02', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-02', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-02', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-02', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-02', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-02', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-02' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-02', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-03', 'Gửi thông báo khi có sự cố gia hạn', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> gia hạn thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> gia hạn thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29003, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$USER', '[Người dùng]', 'SLA-03', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-03', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-03', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-03', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-03', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$LINK_INCIDENT', '[Link trang chi tiết giao dịch xảy ra sự cố]', 'SLA-03', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-03', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-03' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-03', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-04', 'Gửi thông báo khi có sự cố gia hạn', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> gia hạn thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> gia hạn thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29004, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$USER', '[Người dùng]', 'SLA-04', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-04', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-04', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-04', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-04', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-04', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-04' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-04', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-05', 'Gửi thông báo khi có sự cố cập nhật thuê bao', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> cập nhật thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> cập nhật thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29005, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$USER', '[Người dùng]', 'SLA-05', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-05', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-05', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-05', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-05', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$LINK_INCIDENT', '[Link trang chi tiết giao dịch xảy ra sự cố]', 'SLA-05', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-05', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-05' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-05', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-06', 'Gửi thông báo khi có sự cố cập nhật thuê bao', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> cập nhật thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> cập nhật thuê bao <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29006, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$USER', '[Người dùng]', 'SLA-06', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-06', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-06', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-06', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-06', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-06', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-06' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-06', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-07', 'Gửi thông báo khi có sự cố đổi gói dịch vụ', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đổi gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> sang <span>$NAME_SERVICE1</span> - <span>$NAME_PRICING1</span> - <span>$NAME_PERIOD1</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đổi gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> sang <span>$NAME_SERVICE1</span> - <span>$NAME_PRICING1</span> - <span>$NAME_PERIOD1</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29007, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$USER', '[Người dùng]', 'SLA-07', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-07', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ ban đầu]', 'SLA-07', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ ban đầu]', 'SLA-07', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán ban đầu]', 'SLA-07', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$LINK_INCIDENT', '[Link trang chi tiết giao dịch xảy ra sự cố]', 'SLA-07', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-07', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-07', '0942827446');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$NAME_SERVICE1', '[Tên dịch vụ đổi sang]', 'SLA-07', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$NAME_PRICING1', '[Tên gói dịch vụ đổi sang]', 'SLA-07', 'Gói cơ bản 1 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-07' LIMIT 1), '$NAME_PERIOD1', '[Chu kì thanh toán đổi sang]', 'SLA-07', '1 tháng');



INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-08', 'Gửi thông báo khi có sự cố đổi gói dịch vụ', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đổi gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> sang <span>$NAME_SERVICE1</span> - <span>$NAME_PRICING1</span> - <span>$NAME_PERIOD1</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> đổi gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> sang <span>$NAME_SERVICE1</span> - <span>$NAME_PRICING1</span> - <span>$NAME_PERIOD1</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29008, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$USER', '[Người dùng]', 'SLA-08', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-08', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ ban đầu]', 'SLA-08', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ ban đầu]', 'SLA-08', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán ban đầu]', 'SLA-08', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-08', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-08', '0942827446');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$NAME_SERVICE1', '[Tên dịch vụ đổi sang]', 'SLA-08', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$NAME_PRICING1', '[Tên gói dịch vụ đổi sang]', 'SLA-08', 'Gói cơ bản 1 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-08' LIMIT 1), '$NAME_PERIOD1', '[Chu kì thanh toán đổi sang]', 'SLA-08', '1 tháng');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-09', 'Gửi thông báo khi có sự cố hủy gói', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> hủy gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> hủy gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Truy cập vào link sau để đi đến trang chi tiết sự cố.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_INCIDENT" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Chi tiết sự cố</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29009, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$USER', '[Người dùng]', 'SLA-09', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-09', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-09', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-09', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-09', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$LINK_INCIDENT', '[Link trang chi tiết giao dịch xảy ra sự cố]', 'SLA-09', 'yyDez01G5ucWXALyCnhO');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-09', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-09' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-09', '0942827446');


INSERT INTO vnpt_dev.mail_template
(code, "name", status, content_html, content_html_default, content_text, content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES('SLA-10', 'Gửi thông báo khi có sự cố hủy gói', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> hủy gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đã có sự cố xảy ra</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
				<p style="margin: 0;">Đã có sự cố xảy ra.</p>
                <p style="margin: 0;">Khách hàng <span>$CUSTOMER_COMPANY_NAME</span> hủy gói dịch vụ <span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span> - <span>$NAME_PERIOD</span> đã gặp sự cố</p>
                <p style="margin: 0;">Chúng tôi đang điều tra nguyên nhân và tìm cách khắc phục sớm nhất.</p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>', '', '', 'SLA', 'Đã có sự cố xảy ra', 'Đã có sự cố xảy ra', 29010, null);

INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$USER', '[Người dùng]', 'SLA-10', 'Nguyễn Ngọc Viện');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$CUSTOMER_COMPANY_NAME', '[Tên khách hàng]', 'SLA-10', 'Tập đoàn Bưu chính Viễn thông Việt Nam (VNPT)');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$NAME_SERVICE', '[Tên dịch vụ]', 'SLA-10', 'VNPT Pharmacy');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$NAME_PRICING', '[Tên gói dịch vụ]', 'SLA-10', 'Gói cơ bản 3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$NAME_PERIOD', '[Chu kì thanh toán]', 'SLA-10', '3 tháng');
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$HOTLINE_TINH', '[Hotline tỉnh]', 'SLA-10', null);
INSERT INTO vnpt_dev.param_email
(id, id_mail_template, param_name, remark, mail_template_code, param_default_value)
VALUES((SELECT MAX(id)+1 FROM vnpt_dev.param_email), (SELECT id FROM vnpt_dev.mail_template WHERE code = 'SLA-10' LIMIT 1), '$HOTLINE_TOANQUOC', '[Hotline oneSME]', 'SLA-10', '0942827446');