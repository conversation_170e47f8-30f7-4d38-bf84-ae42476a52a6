drop view if exists vnpt_dev.view_spc_supportonesme_429;
DROP VIEW IF EXISTS vnpt_dev.view_report_sub_bills;

ALTER TABLE "vnpt_dev"."billings"
ALTER COLUMN "setup_contract_complete_date" TYPE timestamp(6) USING "setup_contract_complete_date"::timestamp(6);

CREATE OR REPLACE VIEW vnpt_dev.view_report_sub_bills AS
(
SELECT bill.id,
       bill.action_type,
       bill.status,
       bill.payment_date,
       bill.created_at,
       bill.billing_code,
       bill.subscriptions_id,
       bill.total_amount,
       bill.billing_date,
       bill.total_amount_after_adjustment,
       max(invoice.created_at)        AS created_export_invoice,
       string_agg(invoice.code, '; ') AS code,
       bill.portal_type               AS portal_type,
       bill.created_by                AS created_by,
       bill.end_date,
       bill.end_date_new_renewal,
       CASE
           -- bill_action_type: 0: t<PERSON>o mới, 1: sử<PERSON>, 2: đ<PERSON><PERSON>, 3, 4: kích hoạ<PERSON> lạ<PERSON>,  5: g<PERSON> hạn,
           WHEN bill.action_type = -1 OR (bill.action_type IS NULL AND bill.created_by <> 'batch') THEN 0 -- thuê bao tạo mới
           WHEN bill.action_type = 1 THEN 1 -- thuê bao sửa
           WHEN bill.action_type = 2 THEN 2 -- thuê bao đổi gói
           WHEN bill.action_type IN (3, 4) THEN 3 -- thuê bao kích hoạt lại
           WHEN bill.action_type = 5 OR (bill.action_type IS NULL AND bill.created_by = 'batch') THEN 5 -- thuê bao gia hạn
           ELSE bill.action_type
           END                                  AS bill_action_type,
       affAgency.aff_user_id         AS aff_agency_user_id,
       affAgency.affiliate_code      AS aff_agency_code,
       bill.setup_contract_complete_date
FROM vnpt_dev.billings AS bill
         LEFT JOIN vnpt_dev.e_invoice AS invoice ON bill.id = invoice.billing_id
         LEFT JOIN vnpt_dev.affiliate_bill_commission_note AS affAgency ON bill.id = affAgency.bill_id AND affAgency.affiliate_level = 1
WHERE bill.status in (0, 1, 2, 3, 4)
GROUP BY bill.id, affAgency.aff_user_id, affAgency.affiliate_code
    );

create view vnpt_dev.view_spc_supportonesme_429 as
select
    'Tỉnh thành' as provinceName,
    'Loại thuê bao' as subType,
    'Tên khách hàng' as customerName,
    'Loại khách hàng' as customerType,
    'Mã số thuế' as taxNo,
    'Số chứng từ' as identityNo,
    'Địa chỉ' as address,
    'SĐT' as phoneNo,
    'Email' as email,
    'Mã nhân viên giới thiệu' as employeeCode,
    'Dịch vụ' as serviceName,
    'Nhà cung cấp' as providerName,
    'Trạng thái dịch vụ' as subscriptionStatus,
    'Thời gian hoàn thành đơn hàng' as subscriptionCompletionTime,
    'Gói dịch vụ' as pricingName,
    'ON/OS' as onOs,
    'Ngày đăng ký/ Ngày gia hạn' as registrationDate,
    'Ngày bắt đầu sử dụng' as startUsingDate,
    'Ngày kết thúc (oneSME)' as endDate,
    'Ngày đến hạn gia hạn (DHSXKD)' as renewalDate,
    'Trạng thái gia hạn' renewalStatus,
    'Ngày thanh toán' as paymentDate,
    'Ngày hủy' as cancelledDate,
    'Số chu kỳ' as numCycle,
    'Chu kỳ thanh toán' as paymentCycle,
    'Giá cước dịch vụ/gói cước' as price,
    'Số tiền khuyến mại' as promotionAmount,
    'Số tiền đã thanh toán (chưa thuế)' as preTaxAmount,
    'Số tiền nộp thuế' as taxAmount,
    'Số tiền thanh toán (đã có thuế)' as afterTaxAmount,
    'Mã giao dịch với PAY' as PAYTransactionCode,
    'Mã giao dịch với DHSX' as DHSXTransactionCode,
    'Ngày xuất hóa đơn điện tử' as eInvoiceCreationDate,
    'Số hóa đơn điện tử' as eInvoiceNo,
    'Đơn hàng tạo bởi' as createdBy,
    'Mã đơn hàng' as subscriptionCode,
    'Mã lắp đặt' as setupCode,
    'Mã hóa đơn' as billingCode,
    'Trạng thái thanh toán' as paymentStatus,
    'Nguồn tạo' as creationSource,
    'Tên đại lý affiliate' as affiliateAgentName,
    'Mã đại lý cấp 1' as affiliate1stAgentName,
    'Mã thành viên affiliate giới thiệu' as affiliateMemberCode,
    'Mã đồng bộ' as migrationCode,
    'Thời gian đồng bộ' as migrationTime
union all
select
    provinceName as provinceName, -- Tỉnh thành
    state as subType, -- Loại thuê bao
    smeName as customerName, -- Tên khách hàng
    customerType as customerType, -- Loại khách hàng
    taxtNo as taxNo, -- Mã số thuế
    identityNo as identityNo, -- Số chứng từ
    address as address, -- Địa chỉ
    phoneNo as phoneNo, -- SĐT
    email ,  -- Email
    employeeCode as email, -- Mã nhân viên giới thiệu
    serviceName as employeeCode, -- Dịch vụ
    provider as serviceName, -- Nhà cung cấp
    status as providerName, -- Trạng thái dịch vụ
    to_char(setupContractCompleteDate , 'dd/MM/yyyy HH24:MI:SS') as subscriptionStatus, -- Thời gian hoàn thành đơn hàng
    pricingName as subscriptionCompletionTime, -- Gói dịch vụ
    serviceOwnerType as pricingName, -- ON/OS
    to_char(registrationDate , 'dd/MM/yyyy HH24:MI:SS') as onOs, -- Ngày đăng ký/ Ngày gia hạn
    to_char(startAt , 'dd/MM/yyyy HH24:MI:SS') as registrationDate, -- Ngày bắt đầu sử dụng
    to_char(endCurrentCycle , 'dd/MM/yyyy HH24:MI:SS') as startUsingDate, -- Ngày kết thúc (oneSME)
    null as endDate, -- Ngày đến hạn gia hạn (DHSXKD)
    null as renewalDate, -- Trạng thái gia hạn
    to_char(paymentDate , 'dd/MM/yyyy HH24:MI:SS') as paymentDate, -- Ngày thanh toán
    to_char(cancelledTime , 'dd/MM/yyyy HH24:MI:SS') as cancelledDate, -- Ngày hủy
    case
        when numberOfCycle = -1 then 'Không giới hạn'
        else cast(numberOfCycle as text)
        end as numCycle, -- Số chu kỳ
    case
        when planCycleType = 'DAILY' then concat(planPaymentCycle , ' ngày')
        when planCycleType = 'WEEKLY' then concat(planPaymentCycle , ' tuần')
        when planCycleType = 'MONTHLY' then concat(planPaymentCycle , ' tháng')
        when planCycleType = 'YEARLY' then concat(planPaymentCycle , ' năm')
        when multiCycleType = 'DAILY' then concat(multiPaymentCycle , ' ngày')
        when multiCycleType = 'WEEKLY' then concat(multiPaymentCycle , ' tuần')
        when multiCycleType = 'MONTHLY' then concat(multiPaymentCycle , ' tháng')
        when multiCycleType = 'YEARLY' then concat(multiPaymentCycle , ' năm')
        end as paymentCycle, -- Chu kỳ thanh toán
    cast(unitAmount as text) as price, -- Giá cước dịch vụ/gói cước
    cast(promotionAmount as text) as promotionAmount, -- Số tiền khuyến mại
    cast(preAmountTax as text) as preTaxAmount, -- Số tiền đã thanh toán (chưa thuế)
    cast(amountTax as text) as taxAmount, -- Số tiền nộp thuế
    cast(afterAmountTax as text) as afterTaxAmount, -- Số tiền thanh toán (đã có thuế)
    payTransactionCode as PAYTransactionCode, -- Mã giao dịch với PAY
    dhsxkdCode as DHSXTransactionCode, -- Mã giao dịch với DHSX
    to_char(createdExportInvoice , 'dd/MM/yyyy HH24:MI:SS') as eInvoiceCreationDate, -- Ngày xuất hóa đơn điện tử
    codeInvoice as eInvoiceNo, -- Số hóa đơn điện tử
    creator as createdBy, -- Đơn hàng tạo bởi
    subCode as subscriptionCode, -- Mã đơn hàng
    setupCode as setupCode, -- Mã lắp đặt
    billCode as billingCode, -- Mã hóa đơn ,
    billStatus as paymentStatus, -- Trạng thái thanh toán
    createdSource as creationSource, -- Nguồn tạo
    affagencyname as affiliateAgentName, -- Tên đại lý affiliate
    affagencycode as affiliate1stAgentName, -- Mã đại lý cấp 1
    cast(affagencyuserid as text) as affiliateMemberCode, -- Mã thành viên affiliate giới thiệu
    migrateCode as migrationCode, -- Mã đồng bộ
    to_char(migrateTime , 'dd/MM/yyyy HH24:MI:SS') as migrationTime -- Thời gian đồng bộ
from vnpt_dev.get_report_subscriptions_setup_complete(to_char(date_trunc('day', CURRENT_TIMESTAMP) - interval '1' day, 'yyyy-MM-dd HH24:MI:ss'),
                                                      to_char(date_trunc('day', CURRENT_TIMESTAMP) - interval '1' second, 'yyyy-MM-dd HH24:MI:ss'));