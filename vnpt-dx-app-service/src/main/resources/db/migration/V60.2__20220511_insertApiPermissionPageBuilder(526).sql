INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id)
                    + 1
         FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_COUNT_NOTIFICATION' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_PAGE_BUILDER_VIEW' AND pp.portal_id = 1 LIMIT 1)
        ,1,1
    );

INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id)
                    + 1
         FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_GET_NOTIFICATION' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id FROM vnpt_dev."permission" p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'QUAN_LY_PAGE_BUILDER_VIEW' AND pp.portal_id = 1 LIMIT 1)
        ,1,1
    );