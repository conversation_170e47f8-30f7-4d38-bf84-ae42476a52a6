DROP TABLE IF EXISTS vnpt_dev.subscription_order_status_history;
CREATE TABLE vnpt_dev.subscription_order_status_history (
    id BIGSERIAL PRIMARY KEY,
    subscription_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT now(),
    previous_status int8 NOT NULL,
    next_status int8 NOT NULL,
    trigger_type VARCHAR(20) NOT NULL CHECK (trigger_type IN ('MANUAL', 'AUTO')),
    user_id BIGINT,
    user_display_name VARCHAR(255),
    trigger_id BIGINT,
    trigger_description TEXT,
    note TEXT
);
-- Thêm comment chi tiết cho bảng và từng cột
COMMENT ON TABLE subscription_order_status_history IS 'Lịch sử thay đổi trạng thái của subscription, bao gồm trigger thủ công và tự động';
COMMENT ON COLUMN subscription_order_status_history.id IS 'ID tự tăng duy nhất của bản ghi lịch sử';
COMMENT ON COLUMN subscription_order_status_history.subscription_id IS 'ID của Subscription liên quan';
COMMENT ON COLUMN subscription_order_status_history.created_at IS 'Thời điểm ghi nhận sự kiện thay đổi trạng thái';
COMMENT ON COLUMN subscription_order_status_history.previous_status IS 'Trạng thái trước khi thay đổi';
COMMENT ON COLUMN subscription_order_status_history.next_status IS 'Trạng thái sau khi thay đổi';
COMMENT ON COLUMN subscription_order_status_history.trigger_type IS 'Kiểu trigger: MANUAL hoặc AUTO';
COMMENT ON COLUMN subscription_order_status_history.user_id IS 'ID người dùng thực hiện thay đổi nếu trigger_type=MANUAL';
COMMENT ON COLUMN subscription_order_status_history.user_display_name IS 'Tên người dùng snapshot tại thời điểm thay đổi';
COMMENT ON COLUMN subscription_order_status_history.trigger_id IS 'ID trigger tự động kích hoạt nếu trigger_type=AUTO';
COMMENT ON COLUMN subscription_order_status_history.trigger_description IS 'Mô tả trigger snapshot tại thời điểm kích hoạt';
COMMENT ON COLUMN subscription_order_status_history.note IS 'Thông tin bổ sung hoặc lý do thay đổi trạng thái';

CREATE INDEX IF NOT EXISTS idx_subs_order_status_history_sub_id_created_at_desc
  ON subscription_order_status_history (subscription_id, created_at DESC);

DROP TABLE IF EXISTS vnpt_dev.payment_history;
CREATE TABLE IF NOT EXISTS vnpt_dev.payment_history (
    id BIGSERIAL PRIMARY KEY,
    source_type VARCHAR(30) NOT NULL CHECK (source_type IN ('PRODUCT_ORDER', 'SHOPPING_CART', 'SUBSCRIPTION')),
    source_reference VARCHAR(100) NOT NULL,
    -- Mã định danh nguồn (ví dụ: order id, cart code, subscription code)
    transaction_date TIMESTAMP NOT NULL DEFAULT now(),
    -- Ngày giờ giao dịch
    transaction_amount float8 NOT NULL CHECK (transaction_amount >= 0),
    -- Số tiền giao dịch
    transaction_status VARCHAR(30) NOT NULL,
    -- Trạng thái giao dịch (SUCCESS, FAILED, PENDING,...)
    transaction_code VARCHAR(100) NOT NULL UNIQUE,
    -- Mã giao dịch duy nhất
    payment_method int2 NOT NULL,
    -- Phương thức thanh toán
    note TEXT
    -- Ghi chú thêm
);

-- Comment cho bảng
COMMENT ON TABLE payment_history IS 'Lịch sử giao dịch thanh toán cho các loại nguồn khác nhau (đơn hàng, giỏ hàng, subscription)';
-- Comment cho từng cột
COMMENT ON COLUMN payment_history.id IS 'ID tự tăng duy nhất của bản ghi thanh toán';
COMMENT ON COLUMN payment_history.source_type IS 'Loại nguồn phát sinh giao dịch: PRODUCT_ORDER, SHOPPING_CART, SUBSCRIPTION';
COMMENT ON COLUMN payment_history.source_reference IS 'Mã định danh nguồn (ví dụ: subscription_code, order_id, shopping_cart_code)';
COMMENT ON COLUMN payment_history.transaction_date IS 'Thời điểm thực hiện giao dịch';
COMMENT ON COLUMN payment_history.transaction_amount IS 'Số tiền giao dịch (đơn vị VNĐ)';
COMMENT ON COLUMN payment_history.transaction_status IS 'Trạng thái giao dịch: SUCCESS, FAILED, PENDING...';
COMMENT ON COLUMN payment_history.transaction_code IS 'Mã giao dịch duy nhất trong hệ thống thanh toán';
COMMENT ON COLUMN payment_history.payment_method IS 'Phương thức thanh toán (VNPTPAY, CASH, ...)';
COMMENT ON COLUMN payment_history.note IS 'Thông tin bổ sung hoặc lý do giao dịch';
CREATE INDEX IF NOT EXISTS idx_payment_history_source_ref_date_desc
  ON payment_history (source_type, source_reference, transaction_date DESC);
CREATE UNIQUE INDEX IF NOT EXISTS idx_payment_history_transaction_code
  ON payment_history (transaction_code);

ALTER TABLE  vnpt_dev.payment_history DROP CONSTRAINT payment_history_source_type_check;
UPDATE vnpt_dev.payment_history
SET source_type = 'SAAS_SUBSCRIPTION'
WHERE source_type = 'SUBSCRIPTION';

UPDATE vnpt_dev.payment_history
SET source_type = 'SAAS_SHOPPING_CART'
WHERE source_type = 'SHOPPING_CART';

ALTER TABLE  vnpt_dev.payment_history ADD CONSTRAINT payment_history_source_type_check CHECK (source_type IN ('PRODUCT_ORDER', 'SAAS_SHOPPING_CART', 'SAAS_SUBSCRIPTION'));
COMMENT ON COLUMN payment_history.source_type IS 'Loại nguồn phát sinh giao dịch: PRODUCT_ORDER - don hang order bao gom hang hoa vat ly va giai phap hoac bundling , SAAS_SHOPPING_CART, SAAS_SUBSCRIPTION';
COMMENT ON COLUMN payment_history.source_reference IS 'Mã định danh nguồn (order_id, shopping_cart_code, subscription_id)';
COMMENT ON COLUMN payment_history.payment_method IS 'Phương thức thanh toán (VNPTPAY, VNPTPAY_QR)';
