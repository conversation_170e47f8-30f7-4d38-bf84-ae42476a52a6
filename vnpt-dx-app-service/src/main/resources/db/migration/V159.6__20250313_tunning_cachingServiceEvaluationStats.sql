-- Tạo materialzied view chứa thông tin rating metadata ứng với từng service
DROP MATERIALIZED VIEW IF EXISTS vnpt_dev.mview_stats_services_rating CASCADE;
CREATE MATERIALIZED VIEW vnpt_dev.mview_stats_services_rating AS
    SELECT 
        service_evaluation.service_id,
        ROUND(AVG(criteria_service_evaluation.rate), 1) as avg_rating,
        COUNT(distinct service_evaluation.id) as num_rating
    FROM 
        vnpt_dev.service_evaluation
        LEFT JOIN vnpt_dev.criteria_service_evaluation on criteria_service_evaluation.evaluation_id = service_evaluation.id 
    GROUP BY service_id;
COMMENT ON MATERIALIZED VIEW "vnpt_dev"."mview_stats_services_rating" IS 'Materialized view chứa thông tin extradata liên quan phần đánh giá service';

-- Tạo function phục vụ việc refresh một materialzied view bất kì
CREATE OR REPLACE FUNCTION vnpt_dev.func_refresh_materialized_view(mview_name varchar) 
    RETURNS int2 AS
$BODY$
DECLARE
    sql_query TEXT;
BEGIN
    sql_query := format('REFRESH MATERIALIZED VIEW vnpt_dev.%I', mview_name);
    EXECUTE sql_query;
    return 0;
END 
$BODY$
LANGUAGE plpgsql VOLATILE
COST 100;

-- Refresh materialzied view 'mved_service_rating'
SELECT func_refresh_materialized_view('mview_stats_services_rating');

-- Cập nhật mview_service_detail
DROP MATERIALIZED VIEW "vnpt_dev"."mview_service_detail";
CREATE MATERIALIZED VIEW "vnpt_dev"."mview_service_detail"
AS
WITH service_categories AS (
         SELECT mapping_services_categories.service_id,
            (jsonb_agg(json_build_object('id', categories.id, 'name', categories.name)))::text AS categories,
            array_agg(categories.id) as category_ids,
            array_agg(categories.name) as category_names
           FROM (vnpt_dev.mapping_services_categories
             LEFT JOIN vnpt_dev.categories ON ((categories.id = mapping_services_categories.categories_id)))
          WHERE (mapping_services_categories.service_id IS NOT NULL)
          GROUP BY mapping_services_categories.service_id
        ), service_features AS (
         SELECT features.service_id,
            (jsonb_agg(json_build_object('id', features.id, 'name', features.name, 'description', features.description, 'icon', features.icon)))::text AS features
           FROM vnpt_dev.features
          WHERE (features.service_id IS NOT NULL)
          GROUP BY features.service_id
        )
 SELECT services.id AS service_id,
    service_categories.categories,
    service_categories.category_ids,
    service_categories.category_names,
    service_features.features
   FROM ((vnpt_dev.services
     LEFT JOIN service_categories ON ((service_categories.service_id = services.id)))
     LEFT JOIN service_features ON ((service_features.service_id = services.id)));
