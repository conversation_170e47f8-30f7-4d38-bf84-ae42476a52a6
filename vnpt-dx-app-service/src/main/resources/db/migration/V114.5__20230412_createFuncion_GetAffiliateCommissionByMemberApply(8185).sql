drop function if exists vnpt_dev.func_get_affiliate_commission_by_member_apply;
CREATE OR REPLACE FUNCTION vnpt_dev.func_get_affiliate_commission_by_member_apply(
	i_memberId bigint,
	i_memberlevel smallint, 
	i_memberparentids bigint[]
)
 RETURNS TABLE(affiliatecommissionid bigint)
 LANGUAGE plpgsql
AS $function$
begin
	return query (
		select aff_commission.id as affiliatecommissionid
		from vnpt_dev.affiliate_commission aff_commission 
			join vnpt_dev.affiliate_commission_apply_member aff_commission_member 
				on aff_commission.id = aff_commission_member.affiliate_commission_id 
		where aff_commission_member.is_apply_member_all = false
			and aff_commission_member.apply_member_ids != '{}'
			and aff_commission_member."level" = i_memberlevel 
			and i_memberId = any(aff_commission_member.apply_member_ids)
		union all
		select aff_commission_result.affiliate_commission_id as affiliatecommissionid
		from (
			select affiliate_commission.id as affiliate_commission_id, affiliate_commission.created_by, affiliate_commission.priority,
				array_agg(affiliate_commission_member_cte.member_id) as member_ids
			from vnpt_dev.affiliate_commission join (
				select affiliate_commission_id, "level",
					unnest(apply_member_ids) as member_id
				from vnpt_dev.affiliate_commission_apply_member
				where (
					(is_apply_member_all = true) or
					(apply_member_ids != '{}')
				) and level <= i_memberlevel
			) as affiliate_commission_member_cte 
	  			on affiliate_commission.id = affiliate_commission_member_cte.affiliate_commission_id  
			where affiliate_commission.id in (
				select aff_commission.id 
				from vnpt_dev.affiliate_commission aff_commission 
					join vnpt_dev.affiliate_commission_apply_member aff_commisson_member
						on aff_commission.id = aff_commisson_member.affiliate_commission_id
				where aff_commisson_member.is_apply_member_all = true 
				and aff_commisson_member."level" = i_memberlevel
			)
			group by affiliate_commission.id
			) aff_commission_result
		where 
		(
			(aff_commission_result.member_ids = '{}' and aff_commission_result.priority = 0) or 
			(aff_commission_result.member_ids = '{}' and aff_commission_result.priority != 0 and 
				aff_commission_result.created_by = any(i_memberparentids)
			)
		) or 
		(
			aff_commission_result.member_ids != '{}' and 
			aff_commission_result.member_ids && i_memberparentids
		)
	);
end $function$
;


