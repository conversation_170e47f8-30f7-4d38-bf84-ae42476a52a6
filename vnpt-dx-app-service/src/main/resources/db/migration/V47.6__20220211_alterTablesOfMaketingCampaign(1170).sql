-- Alter table marketing_campaign
ALTER TABLE "vnpt_dev"."marketing_campaign"
  ADD COLUMN "province_id" int8,
  ALTER COLUMN "name" TYPE varchar(300) COLLATE "pg_catalog"."default",
  ALTER COLUMN "status" SET NOT NULL,
  ALTER COLUMN "start_time" TYPE timestamp(6) USING "start_time"::timestamp(6),
  ALTER COLUMN "end_time" TYPE timestamp(6) USING "end_time"::timestamp(6),
  ALTER COLUMN "web_image_path" TYPE varchar(300) USING "web_image_path"::varchar(300),
  ALTER COLUMN "mobile_image_path" TYPE varchar(300) USING "mobile_image_path"::varchar(300),
  ALTER COLUMN "modified_by" TYPE int8 USING "modified_by"::int8;

COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."id" IS 'ID chiến dịch quảng cáo';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."name" IS 'Tên CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."status" IS 'Trạng thái CDQC (0: Mới, 1: Không hoạt động, 2: Hoạt động, 3: Kết thúc, 4: Lưu trữ)';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."start_time" IS 'Thời gian bắt đầu CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."end_time" IS 'Thời gian kết thúc CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."description" IS 'Mô tả về CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."created_by" IS 'ID của người tạo CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."web_image_path" IS 'Url ảnh đại diện CDQC trên web';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."mobile_image_path" IS 'Url ảnh đại diện CDQC trên mobile';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."json_object" IS 'Json object mô tả toàn bộ CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."created_at" IS 'Thời gian tạo CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."modified_at" IS 'Thời gian cập nhật CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."modified_by" IS 'ID người cập nhật CDQC';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."deleted_flag" IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';
COMMENT ON COLUMN "vnpt_dev"."marketing_campaign"."province_id" IS 'ID tỉnh của admin tạo CDQC';
ALTER TABLE "vnpt_dev"."marketing_campaign" RENAME COLUMN "web_image_path" TO "web_banner_path";
ALTER TABLE "vnpt_dev"."marketing_campaign" RENAME COLUMN "mobile_image_path" TO "mobile_banner_path";
ALTER TABLE "vnpt_dev"."marketing_campaign"
  ADD CONSTRAINT "uniquesCampaignName" UNIQUE ("name");

-- Alter table mc_activity
ALTER TABLE "vnpt_dev"."mc_activity" DROP CONSTRAINT "fk_mc_activity_mc_action_email_auto_sms_1";
ALTER TABLE "vnpt_dev"."mc_activity" RENAME COLUMN "action_id" TO "action_type";
ALTER TABLE "vnpt_dev"."mc_activity"
ALTER COLUMN "action_type" TYPE int2 USING "action_type"::int2;
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."action_type" IS '0: Email; 1: tự động: 2: SMS';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."mc_id" IS 'id của marketing_campaign';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."target" IS '0 Khách hàng; 1 Thuê bao; 2 sản phẩm';

ALTER TABLE "vnpt_dev"."mc_activity"
  ALTER COLUMN "name" TYPE varchar(300) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "vnpt_dev"."mc_activity"."name" IS 'Tên hoạt động CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."mc_id" IS 'ID của CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."ads_position_id" IS 'ID cài đặt quảng cáo';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."type" IS 'Cách tạo hoạt động (0: CTKM, 1: Rule)';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."target" IS 'Đối tượng mục tiêu (0: Khách hàng, 1: Thuê bao, 2: Sản phẩm)';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."notif_ind" IS 'Trạng thái gửi thông báo';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."index" IS 'Thứ tự của hoạt động trong CDQC';

ALTER TABLE "vnpt_dev"."mc_activity" RENAME COLUMN "action_type" TO "notification_type";
ALTER TABLE "vnpt_dev"."mc_activity" RENAME COLUMN "type" TO "activity_type";
ALTER TABLE "vnpt_dev"."mc_activity" RENAME COLUMN "notif_ind" TO "has_notified";

ALTER TABLE "vnpt_dev"."mc_activity"
  ADD COLUMN "discount_info" varchar(50),
  ADD COLUMN "gift_info" varchar(50);
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."discount_info" IS 'Thông tin chiết khấu hiển thị trên banner SPDV';
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."gift_info" IS 'Thông tin quà tặng hiển thị trên banner SPDV';

ALTER TABLE "vnpt_dev"."mc_activity"
  ADD CONSTRAINT "mcIdIndex" UNIQUE ("mc_id", "index");

ALTER TABLE "vnpt_dev"."mc_activity"
  ADD COLUMN "display_effect_text" varchar(255);
COMMENT ON COLUMN "vnpt_dev"."mc_activity"."display_effect_text" IS 'Thông tin hiển thị Hiệu lực trong preview Dashboard';

-- Alter table mc_activity_rule
COMMENT ON COLUMN "vnpt_dev"."mc_activity_rule"."mc_activity_id" IS 'ID của hoạt động CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_rule"."mc_id" IS 'ID của CDQC';

-- Alter table mc_ads_position
ALTER TABLE "vnpt_dev"."mc_ads_position"
  ADD COLUMN "activity_id" int8,
  ALTER COLUMN "mc_id" SET NOT NULL;

COMMENT ON COLUMN "vnpt_dev"."mc_ads_position"."activity_id" IS 'ID activity của CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_ads_position"."page_type" IS '0: Home Page; 1: Login Page; 2: Register Page; 3: All Service; 4: All combo; 5: Detail service';
COMMENT ON COLUMN "vnpt_dev"."mc_ads_position"."display_popup_event" IS '0: Enter page , 1: Leave page , 2: Scroll down, 3: Stay page, 4: Click on , 5: Hover';

-- Alter table mc_action_email_auto_sms
ALTER TABLE "vnpt_dev"."mc_action_email_auto_sms"
  ALTER COLUMN "subject" TYPE varchar(300) COLLATE "pg_catalog"."default",
  ALTER COLUMN "sender" TYPE varchar(100) COLLATE "pg_catalog"."default",
  ALTER COLUMN "sender" DROP NOT NULL,
  ADD COLUMN "activity_idx" int8,
  ADD COLUMN "lst_enterprise_id" text,
  ADD COLUMN "lst_contact_id" text,
  ADD COLUMN "lst_group_id" text,
  DROP COLUMN "receiver",
  DROP COLUMN "receiver_cus_contact",
  DROP COLUMN "receiver_cus_group",
  ADD COLUMN "send_status" int2;

ALTER TABLE "vnpt_dev"."mc_action_email_auto_sms"
  ADD CONSTRAINT "mcIdActivityIdxIndexUniques" UNIQUE ("mc_id", "activity_idx");

COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."lst_enterprise_id" IS 'Danh sách ID các khách hàng nhận thông báo';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."lst_contact_id" IS 'Danh sách ID các liên hệ nhận thông báo';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."lst_group_id" IS 'Danh sách ID các nhóm nhận thông báo';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."action_type" IS '0: Email; 1: Tự động: 2: SMS';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."subject" IS 'Tiêu đề email/notification, SMS bỏ qua trường này';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."sender" IS 'Email gửi thông báo CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."template_id" IS 'ID mẫu tin nhắn/email được sử dụng';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."schedule_type" IS 'Lập lịch ngày gửi (0: Mặc định sau khi CDQC hoạt động; 1: Chọn ngày)';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."schedule_time" IS 'Ngày gửi trong trường hợp schedule_type = 1';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."mc_id" IS 'ID của bảng marketing_campagin';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."activity_idx" IS 'Index của activity trong CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."send_status" IS 'Trạng thái gửi (1: Đã gửi, Others: Chưa gửi)';

-- Create table mc_event_status_changed
DROP TABLE IF EXISTS "vnpt_dev"."mc_event_status_changed";
CREATE TABLE "vnpt_dev"."mc_event_status_changed" (
  "id" bigserial NOT NULL,
  "created_at" timestamp(6) NOT NULL,
  "created_by" int8 NOT NULL,
  "campaign_id" int8 NOT NULL,
  "pre_status" int2,
  "status" int2 NOT NULL,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."mc_event_status_changed"."created_at" IS 'Thời gian phát sinh event';
COMMENT ON COLUMN "vnpt_dev"."mc_event_status_changed"."created_by" IS 'ID của admin kích hoạt event. Giá trị bằng -1 nếu event là do hệ thống tự động trigger';
COMMENT ON COLUMN "vnpt_dev"."mc_event_status_changed"."campaign_id" IS 'ID CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_status_changed"."pre_status" IS 'Trạng thái CDQC trước thay đổi';
COMMENT ON COLUMN "vnpt_dev"."mc_event_status_changed"."status" IS 'Trạng thái CDQC sau thay đổi';

-- Create table mc_event_ads_info
DROP TABLE IF EXISTS "vnpt_dev"."mc_event_ads_info";
CREATE TABLE "vnpt_dev"."mc_event_ads_info" (
  "id" bigserial NOT NULL,
  "created_at" timestamp(6) NOT NULL,
  "campaign_id" int8 NOT NULL,
  "activity_idx" int8,
  "type" int2,
  "action" int2,
  "sender_info" varchar(200),
  "send_status" int2,
  "session_id" varchar(255),
  "object_type" int2,
  "object_id" int8,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."created_at" IS 'Thời gian phát sinh event';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."campaign_id" IS 'ID CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."activity_idx" IS 'Index của activity trong CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."type" IS 'Loại quảng cáo (0: email,1: sms,2: banner,3: popup,4: notification)';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."action" IS 'Hành động (0: view, 1: click)';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."sender_info" IS 'Email / SĐT gửi email/sms';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."send_status" IS 'Trạng thái gửi sms/email (1: Thành công, 0: Thất bại)';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."session_id" IS 'ID của session hiển thị';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."object_type" IS 'Loại đối tượng xem/click quảng cáo (0: Doanh nghiệp, 1: Liên hệ)';
COMMENT ON COLUMN "vnpt_dev"."mc_event_ads_info"."object_id" IS 'ID của đối tượng xem/click quảng cáo';

-- Create table mc_event_applied_info
DROP TABLE IF EXISTS "vnpt_dev"."mc_event_applied_info";
CREATE TABLE "vnpt_dev"."mc_event_applied_info" (
  "id" bigserial NOT NULL,
  "created_at" timestamp(6) NOT NULL,
  "campaign_id" int8 NOT NULL,
  "activity_idx" int8,
  "rule_id" int8,
  "effect_set_id" int8,
  "effect_item_id" int8,
  "coupon_id" int8,
  "coupon_set_id" int8,
  "coupon_code" varchar(200),
  "subscription_id" int8,
  "discounted_amount" float8,
  "refer_code" varchar(200),
  "object_type" int2,
  "object_id" int8,
  "status" int2,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."created_at" IS 'Thời gian phát sinh event';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."campaign_id" IS 'ID CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."activity_idx" IS 'Index của activity trong CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."rule_id" IS 'ID rule của activity CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."effect_set_id" IS 'ID effect-set của activity CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."effect_item_id" IS 'ID effect của effect-set CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."coupon_id" IS 'ID CTKM của activity CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."coupon_set_id" IS 'ID bộ mã CTKM của activity CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."coupon_code" IS 'Mã khuyến mại';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."subscription_id" IS 'ID của subscription áp dụng CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."discounted_amount" IS 'Số tiền khuyến mại';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."refer_code" IS 'Mã giới thiệu';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."object_type" IS 'Đối tượng áp dụng (0: Pricing, 1: Combo Plan, 2: Addon, 3: Tổng hóa đơn)';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."object_id" IS 'ID của đối tượng áp dụng';
COMMENT ON COLUMN "vnpt_dev"."mc_event_applied_info"."status" IS 'Trạng thái của việc áp dụng (0: Thành công, 1: Thất bại)';

-- Alter table mc_activity_statistic
ALTER TABLE "vnpt_dev"."mc_activity_statistic"
    ADD COLUMN "action_id" int8,
    DROP CONSTRAINT "fk_activity_statistic_mc_action_email_auto_sms_1",
    DROP COLUMN "activity_id",
    ADD COLUMN "activity_idx" int8,
    ADD COLUMN "total_sms" int4,
    ADD COLUMN "success_sms" int4,
    ADD COLUMN "fail_email" int4,
    ADD COLUMN "send_time" timestamp(6),
    ADD COLUMN "fail_sms" int4;

ALTER TABLE "vnpt_dev"."mc_activity_statistic" RENAME COLUMN "action_id" TO "action_idx";
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."action_idx" IS 'index của bảng mc_action_email_auto_sms';


ALTER TABLE "vnpt_dev"."mc_activity_statistic"
  ADD CONSTRAINT "mcIdActivityIdxActionIdx" UNIQUE ("mc_id", "action_idx", "activity_idx");

COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."activity_idx" IS 'Activity index';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."status" IS '0: Nháp(chưa gửi thông báo); 1: Đã gửi(đã gửi thông báo)';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."total_sms" IS 'tổng số sms gửi';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."success_sms" IS 'tổng số sms gửi thành công';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."fail_email" IS 'tổng số email gửi lỗi';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."fail_sms" IS 'tổng số sms gửi lỗi';
COMMENT ON COLUMN "vnpt_dev"."mc_activity_statistic"."send_time" IS 'thời gian gửi email';

-- Insert to table action_notification
INSERT INTO "vnpt_dev"."action_notification"("id", "name", "is_send_email", "is_send_sms", "is_notification", "parent_id", "created_by", "created_at", "modified_by", "modified_at", "receiver", "action_code", "allow_change_email", "allow_change_sms", "allow_change_notification", "priority_order", "is_send_telegram", "allow_change_telegram") VALUES (200, 'Thông báo CDQC', 1, 1, 1, -1, 'system', '2022-01-01 00:00:00', '<EMAIL>', NULL, NULL, 'MC-01', NULL, NULL, NULL, 15000, 0, 'D');

-- Alter table mc_statistic
COMMENT ON COLUMN "vnpt_dev"."mc_statistic"."used_amount" IS 'Số lượng tiền mà CDQC đã sử dụng';
COMMENT ON COLUMN "vnpt_dev"."mc_statistic"."used_subscription" IS 'Số lượng thuê bao đã áp dụng CDQC';
COMMENT ON COLUMN "vnpt_dev"."mc_statistic"."used_refer_code" IS 'Số lượng mã giới thiệu đã được sử dụng';

-- Add column table mc_action_email_auto_sms
ALTER TABLE "vnpt_dev"."mc_action_email_auto_sms"
    ADD COLUMN "sms_content" varchar(300),
    ADD COLUMN "index" int8,
    ALTER COLUMN "send_status" SET NOT NULL;

COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."sms_content" IS 'nội dung sms';
COMMENT ON COLUMN "vnpt_dev"."mc_action_email_auto_sms"."index" IS 'Thứ tự của ds thông báo';

COMMENT ON COLUMN "vnpt_dev"."mail_send_main"."type" IS '0 email, 1 telegram, 2 sms';
COMMENT ON COLUMN "vnpt_dev"."mail_send_history"."type" IS '0 : mail , 1 : telegram, 2: sms';

-- Alter table subscription
ALTER TABLE "vnpt_dev"."subscriptions"
  ADD COLUMN "give_away_main_sub_id" int8;

COMMENT ON COLUMN "vnpt_dev"."subscriptions"."give_away_main_sub_id" IS 'Id của thuê bao chính của thuê bao được tặng';