CREATE OR REPLACE VIEW report_view_subscription_coupon_total AS
SELECT sum(a.coupon_money) AS coupon_money,
array_to_string(array_agg(DISTINCT a.coupons), ','::text, ''::text) AS coupons,
    a.subscriptions_id
   FROM ( SELECT sum(COALESCE(cp.amount_by_cash, cp.amount_by_percent)) AS coupon_money,
	 array_to_string(array_agg(DISTINCT c.name), ','::text, ''::text) AS coupons,
            b.subscriptions_id
           FROM ((vnpt_dev.bill_coupon_total cp
             INNER JOIN vnpt_dev.bill_item bt ON ((bt.id = cp.billing_item_id)))
             INNER JOIN vnpt_dev.billings b ON ((b.id = bt.billing_id)))
						 JOIN vnpt_dev.coupons c ON ((c.id = cp.coupon_id))
          GROUP BY b.subscriptions_id
        UNION ALL
         SELECT sum(COALESCE(cp.amount_by_cash, cp.amount_by_percent)) AS coupon_money,
				 array_to_string(array_agg(DISTINCT c.name), ','::text, ''::text) AS coupons,
            b.subscriptions_id
           FROM ((vnpt_dev.bill_coupon_private cp
             LEFT JOIN vnpt_dev.bill_item bt ON ((bt.id = cp.billing_item_id)))
             LEFT JOIN vnpt_dev.billings b ON ((b.id = bt.billing_id)))
						 JOIN vnpt_dev.coupons c ON ((c.id = cp.coupon_id))
          GROUP BY b.subscriptions_id) a
  GROUP BY a.subscriptions_id;