drop table if exists "vnpt_dev"."mail_send_setting_history" cascade;
create table "vnpt_dev"."mail_send_setting_history"
(
    "id"                          bigserial NOT NULL,
    "type"                        int2,      -- <PERSON><PERSON><PERSON> mail: 1-<PERSON><PERSON> đ<PERSON>, 2-T<PERSON><PERSON> công
    "mail_uuid_id"                int8,      -- <PERSON><PERSON><PERSON> t<PERSON> dụng: 1-<PERSON><PERSON><PERSON>, 2-<PERSON><PERSON>, 3-<PERSON><PERSON><PERSON>, 4-<PERSON><PERSON><PERSON> <PERSON>(D1)
    "condition_json"              text,      -- <PERSON><PERSON><PERSON><PERSON> kiện lọc KH
    "lst_condition_enterprise_id" text,      -- mail_uuid_id = 4 => Danh sách contact_id, ngược lại => Danh sách enterprise_id
    "periodic_type"               int2,      -- <PERSON><PERSON><PERSON> đ<PERSON><PERSON> kỳ quét dữ liệu: 1-<PERSON><PERSON><PERSON> ngày, 2-<PERSON><PERSON><PERSON> tuần, 3-<PERSON><PERSON><PERSON> tháng, 4-Ch<PERSON><PERSON> ngày
    "days"                        text,      -- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> kỳ quét dữ liệu => 4 => list ngày
    "time_type"                   int2,      -- <PERSON><PERSON><PERSON> thời gian <PERSON>: 1-<PERSON><PERSON> <PERSON><PERSON><PERSON>, 2-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>ian <PERSON>, 3-<PERSON><PERSON><PERSON><PERSON> thờ<PERSON> gian
    "frequency_from"              timestamp, -- timeType = 2 => thời gian gửi, timeType = 3 => Tần suất từ ngày
    "frequency_to"                timestamp, -- Tần suất đến ngày
    "frequency"                   varchar,   -- '1/1' - 1 ngày 1 lần
    "coupon_id"                   int8,      -- Chương trình khuyến mãi
    "status"                      int2,
    "deleted_flag"                int2,
    "description"                 text,
    "created_at"                  timestamp,
    "created_by"                  int8,
    "modified_at"                 timestamp,
    "modified_by"                 int8,
    PRIMARY KEY (id)
);
comment
on table "vnpt_dev"."mail_send_setting_history"                                 is 'Thiết lập gửi mail';
comment
on column "vnpt_dev"."mail_send_setting_history"."id"                           is 'id';
comment
on column "vnpt_dev"."mail_send_setting_history"."type"                         is 'Loại mail: 1-Tự động, 2-Thủ công';
comment
on column "vnpt_dev"."mail_send_setting_history"."mail_uuid_id"                 is 'Đối tượng KH áp dụng: 1-Mới, 2-Có tài khoản, 3-Hiển hữu, 4-Liên hệ(D1)';
comment
on column "vnpt_dev"."mail_send_setting_history"."condition_json"               is 'Điều kiện lọc KH';
comment
on column "vnpt_dev"."mail_send_setting_history"."lst_condition_enterprise_id"  is 'mail_uuid_id = 4 => Danh sách contact_id, ngược lại => Danh sách enterprise_id';
comment
on column "vnpt_dev"."mail_send_setting_history"."periodic_type"                is 'Loại định kỳ quét dữ liệu: 1-Hàng ngày, 2-Hàng tuần, 3-Hàng tháng, 4-Chọn ngày';
comment
on column "vnpt_dev"."mail_send_setting_history"."days"                         is 'Loại định kỳ quét dữ liệu => 4 => list ngày';
comment
on column "vnpt_dev"."mail_send_setting_history"."time_type"                    is 'Loại thời gian gửi: 1-Ngay lập tức, 2-Chọn thời gian gửi, 3-khoảng thời gian';
comment
on column "vnpt_dev"."mail_send_setting_history"."frequency_from"               is 'timeType = 2 => thời gian gửi, timeType = 3 => Tần suất từ ngày';
comment
on column "vnpt_dev"."mail_send_setting_history"."frequency_to"                 is 'Tần suất đến ngày';
comment
on column "vnpt_dev"."mail_send_setting_history"."frequency"                    is '1/1 - 1 ngày 1 lần';
comment
on column "vnpt_dev"."mail_send_setting_history"."coupon_id"                    is 'Chương trình khuyến mãi';
comment
on column "vnpt_dev"."mail_send_setting_history"."status"                       is 'Trạng thái';
comment
on column "vnpt_dev"."mail_send_setting_history"."deleted_flag"                 is 'Trạng thái xóa : 0 - Đã xóa, 1 - Chưa xóa';
comment
on column "vnpt_dev"."mail_send_setting_history"."description"                  is 'Mô tả';
comment
on column "vnpt_dev"."mail_send_setting_history"."created_by"                   is 'Người tạo';
comment
on column "vnpt_dev"."mail_send_setting_history"."created_at"                   is 'Thời gian tạo';
comment
on column "vnpt_dev"."mail_send_setting_history"."modified_by"                  is 'Người sửa';
comment
on column "vnpt_dev"."mail_send_setting_history"."modified_at"                  is 'Thời gian sửa';