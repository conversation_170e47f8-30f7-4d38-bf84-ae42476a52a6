-- Tạo job scan coupon expired
INSERT INTO vnpt_dev.schedules(bean_name, method_name, cron_expression, remark, job_status) VALUES
    ('batch-event', 'scanExpiredCouponsDaily', '* * 5 * * *', 'Quet lay coupon het han', 1);


--Tạo job quét events
INSERT INTO vnpt_dev.schedules(bean_name, method_name, cron_expression, remark, job_status) VALUES
    ('batch-event', 'componentChangedEvent', '13 * * * * *', 'Quet lay events để xử lý', 1);

-- Tạo bảng events

DROP TABLE IF EXISTS events;
CREATE TABLE "vnpt_dev"."events" (
                                     "id" bigserial NOT NULL,
                                     "partition_id" date NOT NULL,
                                     "created_at" timestamp(6) NOT NULL,
                                     "created_by" int8 NOT NULL,
                                     "status" int2 NOT NULL,
                                     "type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                     "metadata" jsonb NOT NULL,
                                     PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "vnpt_dev"."events"."id" IS '<PERSON><PERSON><PERSON><PERSON> chính, ID duy nhất của sự kiện, tự động tăng.';

COMMENT ON COLUMN "vnpt_dev"."events"."partition_id" IS 'Ngày phát sinh sự kiện. Dùng để phân vùng bảng theo tuần.';

COMMENT ON COLUMN "vnpt_dev"."events"."created_at" IS 'Thời gian thực tế sự kiện được tạo.';

COMMENT ON COLUMN "vnpt_dev"."events"."created_by" IS 'ID của actor (người dùng/hệ thống) đã thao tác dẫn đến sự kiện này.';

COMMENT ON COLUMN "vnpt_dev"."events"."status" IS 'Trạng thái xử lý của sự kiện (ví dụ: 1: đã xử lý, 0: chưa xử lý).';

COMMENT ON COLUMN "vnpt_dev"."events"."type" IS 'Loại sự kiện (ví dụ: user_login, order_created, item_updated).';

COMMENT ON COLUMN "vnpt_dev"."events"."metadata" IS 'Thông tin chi tiết đi kèm dưới dạng JSON liên quan đến sự kiện.';

--update bảng packages and package_item_addons
ALTER TABLE "vnpt_dev"."packages"
    ADD COLUMN "purchaseable" bool DEFAULT TRUE;
COMMENT ON COLUMN "vnpt_dev"."packages"."purchaseable" IS 'Trạng thái có thể mua/ đăng ký thuê bao của package';


ALTER TABLE "vnpt_dev"."package_item_addons"
    ADD COLUMN "purchaseable" bool DEFAULT TRUE;
COMMENT ON COLUMN "vnpt_dev"."package_item_addons"."purchaseable" IS 'Trạng thái có thể mua, hoặc đăng ký thuê bao của addon trong package';
