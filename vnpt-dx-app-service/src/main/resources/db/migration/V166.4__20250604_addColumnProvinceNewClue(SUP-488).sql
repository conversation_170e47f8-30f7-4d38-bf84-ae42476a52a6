ALTER TABLE "vnpt_dev"."clue"
    ADD COLUMN IF NOT EXISTS "province_code_pay" varchar(20);

COMMENT ON COLUMN "vnpt_dev"."clue"."province_code_pay" IS 'Mã province mới cung cấp thêm khi gọi api init pay (task SPC_SUPPORTONESME-488)';

update vnpt_dev.clue set province_code_pay = province_code;

-- update mã mới do mã cũ ăn theo danh mục địa chỉ của hệ thống rồi
update vnpt_dev.clue set province_code_pay = 'BCN' where province_code = 'BKN';
update vnpt_dev.clue set province_code_pay = 'HAG' where province_code = 'HGI';
update vnpt_dev.clue set province_code_pay = 'DAN' where province_code = 'DNO';