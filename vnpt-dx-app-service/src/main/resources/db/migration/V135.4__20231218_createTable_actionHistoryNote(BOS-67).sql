-- <PERSON><PERSON><PERSON> bảng action_history_note
DROP TABLE IF EXISTS "vnpt_dev"."action_history_note";
CREATE TABLE "vnpt_dev"."action_history_note" (
  "id" bigserial NOT NULL,
  "object_type" int2 NOT NULL,
  "object_id" int8 NOT NULL,
  "title" varchar(100) NOT NULL,
  "content" varchar(300),
  "created_by" int8 NOT NULL,
  "created_at" timestamp NOT NULL,
  "modified_by" int8,
  "modified_at" timestamp,
  PRIMARY KEY ("id")
);
CREATE INDEX "index_actionHistoryNote_id" ON "vnpt_dev"."action_history_note" USING btree ("id" DESC NULLS LAST);
CREATE INDEX "index_actionHistoryNote_objectId" ON "vnpt_dev"."action_history_note" USING hash ("object_id");
CREATE INDEX "index_actionHistoryNote_createdAt" ON "vnpt_dev"."action_history_note" USING btree ("created_at" DESC NULLS LAST);
CREATE INDEX "index_actionHistoryNote_modifiedAt" ON "vnpt_dev"."action_history_note" USING btree ("modified_at" DESC NULLS LAST);
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."object_type" IS 'Loại đối tượng gán ghi chú (0: USER, 1: ENTERPRISE, 2: CONTACT)';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."object_id" IS 'ID đối tượng được gán ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."title" IS 'Tiêu đề ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."content" IS 'Nội dung ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."created_by" IS 'ID người tạo ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."created_at" IS 'Thời gian tạo ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."modified_by" IS 'ID người cập nhật ghi chú';
COMMENT ON COLUMN "vnpt_dev"."action_history_note"."modified_at" IS 'Thời gian cập nhật ghi chú';
COMMENT ON TABLE "vnpt_dev"."action_history_note" IS 'Bảng lưu thông tin ghi chú của nhật ký hoạt động';