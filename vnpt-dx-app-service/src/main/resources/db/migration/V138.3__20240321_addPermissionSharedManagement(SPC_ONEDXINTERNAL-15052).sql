----------------------------------------------QUAN_LY_CHIA_SE_LUU_LUONG----------------------------------------------
ALTER TABLE "vnpt_dev"."shared_bss" ADD COLUMN IF NOT EXISTS "deleted_flag" int2;

---------------------------Bảng sđt do người dùng thêm vào
drop TABLE if exists vnpt_dev.sharing_infor_bss;
CREATE TABLE "vnpt_dev"."sharing_infor_bss" (
                                         "id" bigserial NOT NULL,
                                         "phone_receipt" varchar(255),
                                         "name" varchar(255),
                                         "email" varchar(255),
                                         "deleted_flag" int2,
                                         "created_at" timestamp(6),
                                         "modified_at" timestamp(6),
                                         "created_by" int8,
                                         "modified_by" int8,
                                         "portal" int2,
                                         PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."sharing_infor_bss"."phone_receipt" IS 'Số điện thoại nhận';

COMMENT ON COLUMN "vnpt_dev"."sharing_infor_bss"."name" IS 'Tên người được chia sẻ';

COMMENT ON COLUMN "vnpt_dev"."sharing_infor_bss"."email" IS 'Email người được chia sẻ';

COMMENT ON COLUMN "vnpt_dev"."sharing_infor_bss"."portal" IS 'Nguồn tạo (1:ADMIN, 3:SME)';

---------------------------Thêm sđt vào danh sách chia sẻ ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_ADD_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Thêm mới người được chia sẻ',
        'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/create/phone_receipt', 'ROLE_ADMIN_ADD_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_ADD_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Thêm sđt vào danh sách chia sẻ SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_ADD_SHARE_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Thêm mới người được chia sẻ',
        'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/create/phone_receipt', 'ROLE_SME_ADD_SHARE_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_ADD_SHARE_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'THEM_SDT_DANH_SACH_CHIA_SE_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

---------------------------Update thông tin sđt chia sẻ ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_SHARE_INFO_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chỉnh sửa người được chia sẻ',
        'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/update/phone_receipt', 'ROLE_ADMIN_UPDATE_SHARE_INFO_DETAIL_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_SHARE_INFO_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Update thông tin sđt chia sẻ SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SD_TCHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_UPDATE_SHARE_INFO_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Chỉnh sửa người được chia sẻ',
        'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/update/phone_receipt', 'ROLE_SME_UPDATE_SHARE_INFO_DETAIL_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_UPDATE_SHARE_INFO_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'CAP_NHAT_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);
---------------------------Xóa thông tin chia sẻ ADMIN
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN');
DELETE FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_DELETE_SHARE_INFO_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xóa người được chia sẻ',
        'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/traffic/share/delete/phone_receipt', 'ROLE_ADMIN_DELETE_SHARE_INFO_DETAIL_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_DELETE_SHARE_INFO_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_ADMIN' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN') LIMIT 1),
    1, 1);

---------------------------Xóa thông tin chia sẻ SME
DELETE FROM vnpt_dev.api_permission WHERE permission_portal_id IN (
    SELECT id FROM vnpt_dev.permission_portal WHERE permission_id IN (
        SELECT id FROM vnpt_dev."permission" WHERE code IN ('XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME')));
DELETE FROM vnpt_dev.permission_portal WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id = (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME');
DELETE FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME';
DELETE FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_DELETE_SHARE_INFO_DETAIL_TRAFFIC';
----permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission),
        'Xóa người được chia sẻ',
        'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CHIA_SE_LUU_LUONG' ORDER BY id DESC LIMIT 1),
       (SELECT (MAX(priority) + 1) FROM vnpt_dev.permission));

---- permission_portal
INSERT INTO vnpt_dev.permission_portal(id, permission_id, portal_id)
VALUES ((SELECT (MAX(id) + 1) FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME'),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'SME'));

----roles_permissions
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_SME'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME'), 1);
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES ((SELECT id FROM vnpt_dev.ROLE WHERE name = 'FULL_ADMIN'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME'), 1);

----apis
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis), '/api/sme-portal/traffic/share/delete/phone_receipt', 'ROLE_SME_DELETE_SHARE_INFO_DETAIL_TRAFFIC', 'POST');

----api_permission
INSERT INTO vnpt_dev.api_permission (id,  api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_SME_DELETE_SHARE_INFO_DETAIL_TRAFFIC' ORDER BY id DESC LIMIT 1),
       (SELECT pp.id
           FROM vnpt_dev."permission" p
           JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id
           WHERE p.code = 'XOA_THONG_TIN_SDT_CHIA_SE_LUU_LUONG_SME' AND pp.portal_id = (SELECT id FROM vnpt_dev.portal WHERE name = 'SME') LIMIT 1),
    1, 1);

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;