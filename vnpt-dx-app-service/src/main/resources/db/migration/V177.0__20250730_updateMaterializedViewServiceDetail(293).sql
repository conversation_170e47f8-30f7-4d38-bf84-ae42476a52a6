-- <PERSON><PERSON><PERSON> nh<PERSON>t mview_service_detail
DROP MATERIALIZED VIEW "vnpt_dev"."mview_service_detail";
CREATE MATERIALIZED VIEW "vnpt_dev"."mview_service_detail"
AS
WITH service_categories AS (
         SELECT mapping_services_categories.service_id,
            (jsonb_agg(json_build_object('id', categories.id, 'name', categories.name)))::text AS categories,
            array_agg(categories.id) AS category_ids,
            to_json(array_agg(categories.name)) AS category_names
           FROM (vnpt_dev.mapping_services_categories
             LEFT JOIN vnpt_dev.categories ON ((categories.id = mapping_services_categories.categories_id)))
          WHERE (mapping_services_categories.service_id IS NOT NULL)
          GROUP BY mapping_services_categories.service_id
        ), service_features AS (
         SELECT features.service_id,
            (jsonb_agg(json_build_object('id', features.id, 'name', features.name, 'description', features.description, 'icon', features.icon)))::text AS features
           FROM vnpt_dev.features
          WHERE (features.service_id IS NOT NULL)
          GROUP BY features.service_id
        )
 SELECT services.id AS service_id,
    service_categories.categories,
    service_categories.category_ids,
    service_categories.category_names,
    service_features.features
   FROM ((vnpt_dev.services
     LEFT JOIN service_categories ON ((service_categories.service_id = services.id)))
     LEFT JOIN service_features ON ((service_features.service_id = services.id)));