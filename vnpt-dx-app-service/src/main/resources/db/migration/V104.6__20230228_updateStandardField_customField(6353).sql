ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" DISABLE RULE "rulepreventupdatetemplatelayout";

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('enterprise.customer.info.1', 'enterprise.sub.info.1', 'enterprise.account.info.2', 'enterprise.customer.info.2', 'enterprise.sub.info.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng Doanh nghiệp) - Admin');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('houseHold.customer.info.1', 'houseHold.sub.info.1', 'houseHold.account.info.2', 'houseHold.customer.info.2', 'houseHold.sub.info.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng hộ kinh doanh) - Admin');

UPDATE "vnpt_dev"."custom_layout"
   SET lst_standard_field = raw.lst_standard_field
   FROM (select '[' || string_agg('"' || code || '"', ',') || ']' as lst_standard_field from "vnpt_dev"."custom_field" where category = 'SUBSCRIPTION' and code IN ('personal.customer.info.1', 'personal.sub.info.1', 'personal.account.info.2', 'personal.customer.info.2', 'personal.sub.info.2')) AS raw
WHERE category = 'SUBSCRIPTION' and name in ('Tạo thuê bao (Khách hàng cá nhân) - Admin');

ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventdeletetemplatelayout";
ALTER TABLE "vnpt_dev"."custom_layout" ENABLE RULE "rulepreventupdatetemplatelayout";

--======== standard fields =================
UPDATE "vnpt_dev"."custom_field"
SET config = jsonb_set(config::jsonb,'{maxLength}','1000')
WHERE "code" = 'addonDescription';

UPDATE "vnpt_dev"."custom_field"
SET config = jsonb_set(config::jsonb,'{maxLength}','99')
WHERE "code" = 'addonName';

UPDATE "vnpt_dev"."custom_field"
SET config = jsonb_set(config::jsonb,'{lstPatternToken}','[1,2]')
WHERE "code" = 'serviceCode';