
drop table if exists vnpt_dev.combo_draft cascade;
CREATE TABLE vnpt_dev.combo_draft (
	id bigserial NOT NULL,
	user_id int8 NULL,
	status int2 NULL, -- Trạng thái duyệt: 0 - deactive, 1 active
	combo_name varchar(200) NULL,
	short_description text NULL,
	phone_number varchar(30) NULL,
	email varchar(50) NULL,
	address varchar(200) NULL,
	description text NULL,
	categories_id varchar(300) NULL, -- Id của categories cách nhau bởi dấu phẩy: 1,2,3,4
	created_at timestamp NULL,
	created_by int8 NULL,
	modified_at timestamp NULL,
	modified_by int8 NULL,
	approve_by int8 NULL,
	approve_at timestamp NULL,
	approve int2 NULL, -- Trạng thái duyệt: 0 - chưa duyệt, 1 - đ<PERSON> du<PERSON>, 2 - ch<PERSON> duy<PERSON>, 3 - từ chối
	deleted_flag int2 NULL,
	portal_type int2 NULL, -- portal khi tạo combo. 1 - admin, 2 - dev, 3 - sme
	reason varchar(500) NULL, -- <PERSON><PERSON> do cập nhật
	CONSTRAINT combo_draft_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE vnpt_dev.combo_draft IS 'Combo draft lưu thông tin các combo khi chưa được duyệt';

-- Column comments

COMMENT ON COLUMN vnpt_dev.combo_draft.status IS 'Trạng thái duyệt: 0 - deactive, 1 active';
COMMENT ON COLUMN vnpt_dev.combo_draft.categories_id IS 'Id của categories cách nhau bởi dấu phẩy: 1,2,3,4';
COMMENT ON COLUMN vnpt_dev.combo_draft.approve IS 'Trạng thái duyệt: 0 - chưa duyệt, 1 - đã duyệt, 2 - chờ duyệt, 3 - từ chối';
COMMENT ON COLUMN vnpt_dev.combo_draft.portal_type IS 'portal khi tạo combo. 1 - admin, 2 - dev, 3 - sme';
COMMENT ON COLUMN vnpt_dev.combo_draft.reason IS 'Lý do cập nhật';

alter table vnpt_dev.combo
add column combo_draft_id int8;
COMMENT ON COLUMN vnpt_dev.combo.combo_draft_id IS 'Id của combo_draft';


drop table if exists vnpt_dev.combo_plan_draft cascade;
CREATE TABLE vnpt_dev.combo_plan_draft (
	id bigserial NOT NULL,
	combo_code varchar NULL, -- mã gói
	combo_name varchar NULL, -- tên gói
	description varchar NULL, -- mô tả
	payment_cycle int2 NULL, -- chu kỳ thanh toán
	number_of_cycles int2 NULL, -- số chu kỳ
	cycle_type int2 NULL, -- 0 - ngày, 1 - tuần, 2 - tháng, 3 - năm
	combo_id int8 NULL, -- services.id
	unit_id int8 NULL, -- id đơn vị tính
	currency_id int8 NULL, -- id loại tiền tệ
	amount float8 NULL, -- số tiền thanh toán
	setup_fee float8 NULL, -- phí cài đặt
	free_quantity int4 NULL, -- số lượng miễn phí
	estimate_quantity int4 NULL, -- số lượng ước tính
	created_at timestamp NULL, -- thời gian tạo
	created_by int8 NULL, -- người tạo
	modified_at timestamp NULL, -- thời gian sửa
	modified_by int8 NULL, -- người sửa
	status int2 NULL, -- trạng thái: trạng thái: 0 - Inactive, 1 - active
	deleted_flag int2 NULL, -- Trạng thái xóa : 0 - Đã xóa, 1 - Chưa xóa
	price float8 NULL, -- đơn giá. set giá trị khi combo_plan_draftPlan = 0
	list_feature_id varchar NULL, -- danh sách tính năng features.id, các id phân cách bằng dấu ,
	combo_order int2 NULL,
	recommended_status int2 NULL,
	update_reason varchar NULL, -- Lý do cập nhật
	approve int2 NULL, -- trạng thái duyệt Trạng thái duyệt: 0 - chưa duyệt, 1 - đã duyệt, 2 - chờ duyệt, 3 - từ chối
	combo_draft_id int8 NULL,
	approve_time timestamp NULL,
	sme_combo_id int8 NULL,
	trial_type int2 NULL,
	number_of_trial int2 NULL, -- số dùng thử
	combo_plan_draft_type int2 NULL, -- 0 là trả trước / 1 trả sau
	has_change_price int2 NULL, -- Cho phép thay đổi giá 0: không, 1: có
	has_change_quantity int2 NULL, -- 0: Không cho phép 1: Cho phép tăng 2: Cho phép giảm 3: Cho phép tăng và giảm
	has_refund int2 NULL, -- Cho phép hoàn trả 1: có, 0: không
	cancel_date int2 NULL, -- Thời điểm hủy sau khi thao tác hủy 1: hết chu kỳ, 0: ngay lập tức
	active_date int4 NULL, -- Thời gian kích hoạt sau hủy  -1: không giới hạn, 1: thời gian cho phép
	duration_type int2 NULL, -- kiểu của thời gian kích hoạt 0: ngày, 1 tuần, 2 tháng, 3: năm, 4: quý
	CONSTRAINT combo_plan_draft_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE vnpt_dev.combo_plan_draft IS 'Bảng draft của gói combo';

-- Column comments

COMMENT ON COLUMN vnpt_dev.combo_plan_draft.combo_code IS 'mã gói';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.combo_name IS 'tên gói';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.description IS 'mô tả';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.payment_cycle IS 'chu kỳ thanh toán';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.number_of_cycles IS 'số chu kỳ';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.cycle_type IS '0 - ngày, 1 - tuần, 2 - tháng, 3 - năm';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.combo_id IS 'services.id';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.unit_id IS 'id đơn vị tính';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.currency_id IS 'id loại tiền tệ';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.amount IS 'số tiền thanh toán';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.setup_fee IS 'phí cài đặt';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.free_quantity IS 'số lượng miễn phí';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.estimate_quantity IS 'số lượng ước tính';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.created_at IS 'thời gian tạo';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.created_by IS 'người tạo';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.modified_at IS 'thời gian sửa';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.modified_by IS 'người sửa';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.status IS 'trạng thái: trạng thái: 0 - Inactive, 1 - active';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.deleted_flag IS 'Trạng thái xóa : 0 - Đã xóa, 1 - Chưa xóa';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.price IS 'đơn giá. set giá trị khi combo_plan_draftPlan = 0';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.list_feature_id IS 'danh sách tính năng features.id, các id phân cách bằng dấu ,';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.update_reason IS 'Lý do cập nhật';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.approve IS 'trạng thái duyệt Trạng thái duyệt: 0 - chưa duyệt, 1 - đã duyệt, 2 - chờ duyệt, 3 - từ chối';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.number_of_trial IS 'số dùng thử';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.combo_plan_draft_type IS '0 là trả trước / 1 trả sau';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.has_change_price IS 'Cho phép thay đổi giá 0: không, 1: có';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.has_change_quantity IS '0: Không cho phép 1: Cho phép tăng 2: Cho phép giảm 3: Cho phép tăng và giảm';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.has_refund IS 'Cho phép hoàn trả 1: có, 0: không';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.cancel_date IS 'Thời điểm hủy sau khi thao tác hủy 1: hết chu kỳ, 0: ngay lập tức';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.active_date IS 'Thời gian kích hoạt sau hủy  -1: không giới hạn, 1: thời gian cho phép';
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.duration_type IS 'kiểu của thời gian kích hoạt 0: ngày, 1 tuần, 2 tháng, 3: năm, 4: quý';


alter table vnpt_dev.combo_plan
add column combo_plan_draft_id int8;
COMMENT ON COLUMN vnpt_dev.combo_plan.combo_plan_draft_id IS 'Id của combo_plan_draft';