drop function if exists vnpt_dev.func_get_affiliate_commission_by_role;
CREATE OR REPLACE FUNCTION vnpt_dev.func_get_affiliate_commission_by_role(rolename character varying, userid bigint)
 RETURNS TABLE(
 	affiliate_commission_id bigint, 
 	affiliate_commission_name varchar(100),
 	affiliate_commission_code varchar(10)
 )
 LANGUAGE plpgsql
AS $function$
begin
	--Admin tổng
	if roleName like 'FULL_ADMIN' then
		begin
			--Lấy ra tất cả affiliate commission
			return query (select id, "name", code from vnpt_dev.affiliate_commission where deleted_flag = 1);
		end;
	--Đại lý affiliate
	elseif roleName like 'ROLE_AFFILIATE_DAILY' then
		begin
			return query (
				--<PERSON><PERSON> quy để lấy ra đại lý và tất cả thành viên con
				with recursive affiliate_users_all_level_cte as(
					select user_id, affiliate_code from vnpt_dev.affiliate_users where user_id = userId
					union all
					select aff_users_child.user_id, aff_users_child.affiliate_code 
					from vnpt_dev.affiliate_users aff_users_child join affiliate_users_all_level_cte aff_users_parent 
						on aff_users_child.parent_affiliate_code = aff_users_parent.affiliate_code
				)
				--Lấy ra các affiliate commission được tạo bởi đại lý và tất cả thành viên con
				select aff_commission.id, aff_commission."name", aff_commission.code  
				from vnpt_dev.affiliate_commission aff_commission join affiliate_users_all_level_cte aff_all_level 
					on aff_commission.created_by = aff_all_level.user_id 
					and aff_commission.deleted_flag = 1
			);
		end;
	end if;
end $function$
;
