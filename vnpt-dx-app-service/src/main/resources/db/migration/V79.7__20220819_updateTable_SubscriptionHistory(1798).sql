ALTER TABLE "vnpt_dev"."subscription_history" DROP COLUMN IF EXISTS "content_type";
ALTER TABLE "vnpt_dev"."subscription_history" ADD COLUMN "content_type" int4;

COMMENT ON COLUMN "vnpt_dev"."subscription_history"."content_type" IS 'Loại nội dung thay đổi:
    0: "Tạo thuê bao dùng thử";
    1: "Thay đổi thuê bao dùng thử";
    2: "Kết thúc dùng thử";
    3: "Hủy thuê bao dùng thử";
    4: "Đăng ký dịch vụ";
    5: "Đăng ký combo dịch vụ";
    6: "Sửa số lượng gói dịch vụ chính";
    7: "Người sử dụng hủy thuê bao";
    8: "Sửa giá gói combo dịch vụ";
    9: "Sửa số lượng gói DV bổ sung";
    10: "Thêm DV bổ sung %s";
    11: "Bỏ  DV bổ sung %s";
    12: "Thêm khuyến mại %s";
    13: "Thêm phí %s";
    14: "Thay đổi số chu kỳ từ %s thành %s";
    15: "Gia hạn thuê bao";
    16: "Đổi gói dịch vụ từ %s thành %s";
    17: "Quản trị viên hủy thuê bao";
    18: "Nhà cung cấp hủy thuê bao";
    19: "Hủy thuê bao do quá hạn thanh toán";
    20: "Quản trị viên kích hoạt lại thuê bao";
    21: "Nhà phát triển kích hoạt lại thuê bao";
    22: "Người sử dụng kích hoạt lại thuê bao";
    23: "Kích hoạt lại thuê bao do thanh toán thành công";
    24: "Khuyến mại %s hết số lần sử dụng trên %s";
    25: "Thuê bao hết hạn";';

UPDATE "vnpt_dev"."subscription_history" SET content_type = 0 WHERE "content" = 'Tạo thuê bao dùng thử';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 1 WHERE "content" = 'Thay đổi thuê bao dùng thử';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 2 WHERE "content" = 'Kết thúc dùng thử';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 3 WHERE "content" = 'Hủy thuê bao dùng thử';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 4 WHERE "content" = 'Đăng ký dịch vụ';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 5 WHERE "content" = 'Đăng ký combo dịch vụ';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 6 WHERE "content" = 'Sửa số lượng gói dịch vụ chính';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 7 WHERE "content" = 'Người sử dụng hủy thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 8 WHERE "content" = 'Sửa giá gói combo dịch vụ';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 9 WHERE "content" = 'Sửa số lượng gói DV bổ sung';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 10 WHERE "content" like 'Thêm DV bổ sung %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 11 WHERE "content" like 'Bỏ  DV bổ sung %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 12 WHERE "content" like 'Thêm khuyến mại %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 13 WHERE "content" like 'Thêm phí %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 14 WHERE "content" like 'Thay đổi số chu kỳ từ % thành %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 15 WHERE "content" = 'Gia hạn thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 16 WHERE "content" like 'Đổi gói dịch vụ từ % thành %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 17 WHERE "content" = 'Quản trị viên hủy thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 18 WHERE "content" = 'Nhà cung cấp hủy thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 19 WHERE "content" = 'Hủy thuê bao do quá hạn thanh toán';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 20 WHERE "content" = 'Quản trị viên kích hoạt lại thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 21 WHERE "content" = 'Nhà phát triển kích hoạt lại thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 22 WHERE "content" = 'Người sử dụng kích hoạt lại thuê bao';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 23 WHERE "content" = 'Kích hoạt lại thuê bao do thanh toán thành công';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 24 WHERE "content" like 'Khuyến mại % hết số lần sử dụng trên %';
UPDATE "vnpt_dev"."subscription_history" SET content_type = 25 WHERE "content" = 'Thuê bao hết hạn';