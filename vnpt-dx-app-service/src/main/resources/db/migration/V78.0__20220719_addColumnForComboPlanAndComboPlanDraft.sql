ALTER TABLE vnpt_dev.combo_plan
ADD COLUMN IF NOT EXISTS payment_method varchar;
COMMENT ON COLUMN vnpt_dev.combo_plan.payment_method IS '0: <PERSON><PERSON><PERSON><PERSON> (P2P), 1: vnpt pay, 2: <PERSON><PERSON><PERSON><PERSON> mặt (by_cash)';

ALTER TABLE vnpt_dev.combo_plan_draft
ADD COLUMN IF NOT EXISTS payment_method varchar;
COMMENT ON COLUMN vnpt_dev.combo_plan_draft.payment_method IS '0: <PERSON><PERSON><PERSON><PERSON> kho<PERSON>n (P2P), 1: vnpt pay, 2: Tiề<PERSON> mặt (by_cash)';

UPDATE vnpt_dev.combo_plan_draft
SET payment_method = '[2]' WHERE payment_method IS NULL;

UPDATE vnpt_dev.combo_plan
SET payment_method = '[2]' WHERE payment_method IS NULL;