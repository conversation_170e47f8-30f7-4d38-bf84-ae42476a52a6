-- Thêm bản ghi api ứng với api /api/admin-portal/billings/payment
delete from vnpt_dev.apis where api_code = 'ROLE_DEV_PAYMENT_BILL';
insert into vnpt_dev.apis(
    id,
    api_path,
    api_code,
    method
)
values (
               (select max(id) from vnpt_dev.apis) + 1,
               '/api/dev-portal/billings/payment',
               'ROLE_DEV_PAYMENT_BILL',
               'POST'
       );

-- Thểm bản ghi api_permission tương ứng với api /api/admin-portal/billings/payment và permission KICH_HOAT_SU_DUNG_THUE_BAO_1 trên admin portal
with mPermissionPortal as (
    select
        mPerPortal.id
    from vnpt_dev.permission_portal mPerPortal
             join vnpt_dev.portal mPortal on mPerPortal.portal_id = mPortal.id
             join vnpt_dev.permission mPer on mPerPortal.permission_id = mPer.id
    where
            mPortal.name = 'DEV' and
            mPer.code = 'KICH_HOAT_SU_DUNG_THUE_BAO_1'
),
     mApi as (
         select id
         from vnpt_dev.apis
         where api_code = 'ROLE_DEV_PAYMENT_BILL'
     )
insert into vnpt_dev.api_permission (
    id,
    api_id,
    permission_portal_id,
    map_permission_portal,
    delete_flag
)
select
        (select max(id) from vnpt_dev.api_permission) + 1,
        (select id from mApi),
        (select id from mPermissionPortal),
        1,
        1
    where not exists (
    select 1
    from vnpt_dev.api_permission mApiPer
        join mPermissionPortal on mApiPer.permission_portal_id = mPermissionPortal.id
        join mApi on mApiPer.api_id = mApi.id
);

-- Refresh mview
refresh materialized view vnpt_dev.role_permission_api;

