-- C<PERSON><PERSON> nh<PERSON><PERSON> các trạng thái chấp nhận chinh sách dữ liệu -> false (thực hiện x<PERSON>c nhận lại) --
update vnpt_dev.users set status_confirm_data_policy = false where vnpt_dx.vnpt_dev.users.status_confirm_data_policy = true;

-- thê<PERSON> c<PERSON>t confirmed_data_policies --
alter table "vnpt_dev"."users"
add column if not exists "confirmed_data_policies" text;
comment on column "vnpt_dev"."users"."confirmed_data_policies" is 'C<PERSON><PERSON> điều khoản dữ liệu đã đồng ý dạng mảng [1,2,3,4,...]';

-- cột old_policies và new_policies bảng "history_confirm_data_policy" -> nếu sau cần lấy log nội dung thay đổi --
alter table "vnpt_dev"."history_confirm_data_policy"
    add column if not exists "old_policies" text;
comment on column "vnpt_dev"."history_confirm_data_policy"."old_policies" is '<PERSON><PERSON><PERSON> điề<PERSON> kho<PERSON>n cũ';

alter table "vnpt_dev"."history_confirm_data_policy"
    add column if not exists "new_policies" text;
comment on column "vnpt_dev"."history_confirm_data_policy"."new_policies" is 'Các điều khoản mới cập nhật';

-- clear dữ liệu cũ --
delete from "vnpt_dev"."history_confirm_data_policy" where "new_policies" is null; -- các bản ghi cũ không có dữ liệu về các điều khoản cập nhật --

-- set default false cho status_confirm_data_policy ---
alter table vnpt_dev.users alter column status_confirm_data_policy set default false;
update vnpt_dev.users set status_confirm_data_policy = false where status_confirm_data_policy is null;