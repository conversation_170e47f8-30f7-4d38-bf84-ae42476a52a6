-- Bỏ qua các index để tối ưu thời gian cập nhật
DROP INDEX IF EXISTS "vnpt_dev"."index_enterprise_customerType";
DROP INDEX IF EXISTS "vnpt_dev"."index_enterprise_districtId";
DROP INDEX IF EXISTS "vnpt_dev"."index_enterprise_provinceCode";
DROP INDEX IF EXISTS "vnpt_dev"."index_enterprise_provinceId";

-- Rename các cột
ALTER TABLE vnpt_dev.enterprise RENAME COLUMN province_id TO province_id_old;
ALTER TABLE vnpt_dev.enterprise RENAME COLUMN district_id TO district_id_old;
ALTER TABLE vnpt_dev.enterprise RENAME COLUMN ward_id TO ward_id_old;
ALTER TABLE vnpt_dev.enterprise RENAME COLUMN street_id TO street_id_old;
ALTER TABLE vnpt_dev.enterprise RENAME COLUMN contact_province_id TO contact_province_id_old;


-- Drop các cột quận
ALTER TABLE vnpt_dev.enterprise
  DROP COLUMN IF EXISTS district_code,
  DROP COLUMN IF EXISTS district_name,
  DROP COLUMN IF EXISTS province_id_new,
  DROP COLUMN IF EXISTS ward_id_new,
  DROP COLUMN IF EXISTS street_id_new,
  DROP COLUMN IF EXISTS contact_province_id_new;

-- Thêm các cột mới
ALTER TABLE vnpt_dev.enterprise
  ADD COLUMN IF NOT EXISTS nation_name varchar(50) DEFAULT 'Việt Nam',
  ADD COLUMN IF NOT EXISTS province_id int8,
  ADD COLUMN IF NOT EXISTS ward_id int8,
  ADD COLUMN IF NOT EXISTS street_id int8,
  ADD COLUMN IF NOT EXISTS contact_province_id int8;

-- Tạo index để tăng tốc phần migration
CREATE INDEX IF NOT EXISTS "index_enterprise_oldAddress" ON "vnpt_dev"."enterprise" USING btree (province_id_old, district_id_old, ward_id_old, street_id_old);