-- Rename các cột
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN province_id TO province_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN district_id TO district_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN ward_id TO ward_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN street_id TO street_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN personal_province_id TO personal_province_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN personal_district_id TO personal_district_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN personal_ward_id TO personal_ward_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN personal_street_id TO personal_street_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN contact_province_id TO contact_province_id_old;
ALTER TABLE vnpt_dev.customer_contact RENAME COLUMN personal_nation TO personal_nation_name;

-- Drop các cột quận
ALTER TABLE vnpt_dev.customer_contact
  DROP COLUMN IF EXISTS district_code,
  DROP COLUMN IF EXISTS district_name,
  DROP COLUMN IF EXISTS personal_district_code,
  DROP COLUMN IF EXISTS personal_district_name,
  DROP COLUMN IF EXISTS contact_location_id,
  DROP COLUMN IF EXISTS personal_location_id;

-- Thêm các cột mới
ALTER TABLE vnpt_dev.customer_contact
  ADD COLUMN IF NOT EXISTS nation_name varchar(50) DEFAULT 'Việt Nam',
  ADD COLUMN IF NOT EXISTS province_id int8,
  ADD COLUMN IF NOT EXISTS ward_id int8,
  ADD COLUMN IF NOT EXISTS street_id int8,
  ADD COLUMN IF NOT EXISTS personal_province_id int8,
  ADD COLUMN IF NOT EXISTS personal_ward_id int8,
  ADD COLUMN IF NOT EXISTS personal_street_id int8,
  ADD COLUMN IF NOT EXISTS contact_province_id int8;

-- Tạo index để tăng tốc phần migration
CREATE INDEX IF NOT EXISTS "index_customerContact_oldAddress" ON "vnpt_dev"."customer_contact" USING btree (province_id_old, district_id_old, ward_id_old, street_id_old);
CREATE INDEX IF NOT EXISTS "index_customerContact_oldPersonalAddress" ON "vnpt_dev"."customer_contact" USING btree (personal_province_id_old, personal_district_id_old, personal_ward_id_old, personal_street_id_old);

-- Khách hàng doanh nghiệp/ Hộ kinh doanh
-- Tạo mapping dữ liệu mới
drop materialized view if exists vnpt_dev.mview_customer_contact_address;
create materialized view vnpt_dev.mview_customer_contact_address AS
select
    mContact.id as contact_id,
    provinces.nation_id as nation_id,
    provinces.nation_name as nation_name,
    provinces.region_id as region_id,
    provinces.region_name as region_name,
    provinces.id as province_id,
    provinces.code as province_code,
    provinces.name as province_name,
    wards.id as ward_id,
    wards.code as ward_code,
    wards.name as ward_name,
    streets.id as street_id,
    streets.name as street_name
from
    vnpt_dev.customer_contact mContact
    left join vnpt_dev.province on province.id = mContact.province_id_old
    left join vnpt_dev.mview_mapping_ward wardMapping on wardMapping.province_id_old = mContact.province_id_old and
        wardMapping.district_id_old = mContact.district_id_old and
        wardMapping.ward_id_old = mContact.ward_id_old
    left join vnpt_dev.geography_2025 streetMapping on streetMapping.province_id_old = mContact.province_id_old and
        streetMapping.district_id_old = mContact.district_id_old and
        streetMapping.ward_id_old = mContact.ward_id_old and
        streetMapping.street_id_old = mContact.street_id_old
    left join vnpt_dev.provinces on provinces.id = province.id_new
    left join vnpt_dev.wards on wards.id = wardMapping.ward_id_new and wards.province_id = provinces.id
    left join vnpt_dev.streets on streets.id = streetMapping.street_id_new and streets.ward_id = wards.id and streets.province_id = provinces.id;
-- Tạo index tăng tốc việc truy xuất
create index index_mViewCustomerContactAddress_contactId ON vnpt_dev.mview_customer_contact_address(contact_id);

-- Cập nhật dữ liệu vào bảng customer_contact qua batch
DO $$
DECLARE
    v_batch_size INTEGER := 100000; -- số bản ghi mỗi batch
    v_min_id BIGINT := 0;
    v_max_id BIGINT;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
BEGIN
    -- Tìm ID lớn nhất để làm mốc
    SELECT MAX(id) INTO v_max_id FROM vnpt_dev.customer_contact;

    WHILE v_min_id <= v_max_id LOOP
        v_start_time := clock_timestamp();

        UPDATE vnpt_dev.customer_contact mContact
        SET
            nation_id = mView.nation_id,
            nation_name = mView.nation_name,
            region_id = mView.region_id,
            region_name = mView.region_name,
            province_id = mView.province_id,
            province_code = mView.province_code,
            province_name = mView.province_name,
            ward_id = mView.ward_id,
            ward_code = mView.ward_code,
            ward_name = mView.ward_name,
            street_id = mView.street_id,
            street_name = mView.street_name
        FROM vnpt_dev.mview_customer_contact_address mView
        WHERE mContact.id = mView.contact_id
          AND (
                mContact.province_id  IS DISTINCT FROM mView.province_id
                OR mContact.ward_id   IS DISTINCT FROM mView.ward_id
                OR mContact.street_id IS DISTINCT FROM mView.street_id
              )
          AND mContact.id >= v_min_id
          AND mContact.id < v_min_id + v_batch_size;

        v_min_id := v_min_id + v_batch_size;

        v_end_time := clock_timestamp();
        RAISE NOTICE 'Updated customer_contact by batch from ID % to %, duration: % seconds',
                     v_min_id, v_min_id + v_batch_size - 1,
                     EXTRACT(EPOCH FROM (v_end_time - v_start_time));
    END LOOP;
END$$;






-- Dữ liệu khách hàng cá nhân
-- Tạo mapping dữ liệu mới
drop materialized view if exists vnpt_dev.mview_customer_contact_personal_address;
create materialized view vnpt_dev.mview_customer_contact_personal_address AS
select
    mContact.id as contact_id,
    provinces.nation_id as nation_id,
    provinces.nation_name as nation_name,
    provinces.region_id as region_id,
    provinces.region_name as region_name,
    provinces.id as province_id,
    provinces.code as province_code,
    provinces.name as province_name,
    wards.id as ward_id,
    wards.code as ward_code,
    wards.name as ward_name,
    streets.id as street_id,
    streets.name as street_name
from
    vnpt_dev.customer_contact mContact
    left join vnpt_dev.province on province.id = mContact.personal_province_id_old
    left join vnpt_dev.mview_mapping_ward wardMapping on
        wardMapping.province_id_old = mContact.personal_province_id_old and
        wardMapping.district_id_old = mContact.personal_district_id_old and
        wardMapping.ward_id_old = mContact.personal_ward_id_old
    left join vnpt_dev.geography_2025 streetMapping on
        streetMapping.province_id_old = mContact.personal_province_id_old and
        streetMapping.district_id_old = mContact.personal_district_id_old and
        streetMapping.ward_id_old = mContact.personal_ward_id_old and
        streetMapping.street_id_old = mContact.personal_street_id_old
    left join vnpt_dev.provinces on provinces.id = province.id_new
    left join vnpt_dev.wards on wards.id = wardMapping.ward_id_new and wards.province_id = provinces.id
    left join vnpt_dev.streets on streets.id = streetMapping.street_id_new and streets.ward_id = wards.id and streets.province_id = provinces.id;
-- Tạo index tăng tốc việc truy xuất
create index index_mViewCustomerContactPersonalAddress_contactId ON vnpt_dev.mview_customer_contact_personal_address(contact_id);

-- Cập nhật dữ liệu vào bảng customer_contact qua batch
DO $$
DECLARE
    v_batch_size INTEGER := 100000; -- số bản ghi mỗi batch
    v_min_id BIGINT := 0;
    v_max_id BIGINT;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
BEGIN
    -- Tìm ID lớn nhất để làm mốc
    SELECT MAX(id) INTO v_max_id FROM vnpt_dev.customer_contact;

    WHILE v_min_id <= v_max_id LOOP
        v_start_time := clock_timestamp();

        UPDATE vnpt_dev.customer_contact mContact
        SET
            personal_nation_id = mView.nation_id,
            personal_nation_name = mView.nation_name,
            personal_region_id = mView.region_id,
            personal_region_name = mView.region_name,
            personal_province_id = mView.province_id,
            personal_province_code = mView.province_code,
            personal_province_name = mView.province_name,
            personal_ward_id = mView.ward_id,
            personal_ward_code = mView.ward_code,
            personal_ward_name = mView.ward_name,
            personal_street_id = mView.street_id,
            personal_street_name = mView.street_name
        FROM vnpt_dev.mview_customer_contact_personal_address mView
        WHERE mContact.id = mView.contact_id
          AND (
                mContact.personal_province_id  IS DISTINCT FROM mView.province_id
                OR mContact.personal_ward_id   IS DISTINCT FROM mView.ward_id
                OR mContact.personal_street_id IS DISTINCT FROM mView.street_id
              )
          AND mContact.id >= v_min_id
          AND mContact.id < v_min_id + v_batch_size;

        v_min_id := v_min_id + v_batch_size;

        v_end_time := clock_timestamp();
        RAISE NOTICE 'Updated customer_contact by batch from ID % to %, duration: % seconds',
                     v_min_id, v_min_id + v_batch_size - 1,
                     EXTRACT(EPOCH FROM (v_end_time - v_start_time));
    END LOOP;
END$$;
