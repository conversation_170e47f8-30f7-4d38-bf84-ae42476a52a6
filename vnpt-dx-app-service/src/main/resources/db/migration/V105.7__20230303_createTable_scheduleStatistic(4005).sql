DROP TABLE IF EXISTS "vnpt_dev"."schedule_statistic";
CREATE TABLE "vnpt_dev"."schedule_statistic" (
  "id" bigserial NOT NULL,
  "bean_name" varchar(200) NOT NULL,
  "method_name" varchar(500) NOT NULL,
  "last_start_at" timestamp,
  "last_success_at" timestamp,
  "last_failure_at" timestamp,
  "last_error" text,
  "execution_time" int8,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."bean_name" IS 'Tên bean chứa cronjob';
COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."method_name" IS 'Tên method chạy cronjob';
COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."last_start_at" IS 'Thời gian bắt đầu chạy cronjob gần nhất';
COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."last_success_at" IS 'Thời gian chạy thành công cronjob gần nhất';
COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."last_failure_at" IS 'Thời gian chạy thất bại cronjob gần nhất';
COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."last_error" IS 'Lỗi xảy ra trong lần chạy cuối cùng';
COMMENT ON COLUMN "vnpt_dev"."schedule_statistic"."execution_time" IS 'Thời gian chạy cronjob (đơn vị millisec)';
COMMENT ON TABLE "vnpt_dev"."schedule_statistic" IS 'Bảng lưu trạng thái các cronjob trong hệ thống';