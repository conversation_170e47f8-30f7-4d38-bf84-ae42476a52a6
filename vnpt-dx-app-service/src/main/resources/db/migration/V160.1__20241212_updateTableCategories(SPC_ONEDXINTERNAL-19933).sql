ALTER TABLE "vnpt_dev"."categories" ADD COLUMN
    IF NOT EXISTS "priority" int8;

COMMENT ON COLUMN "vnpt_dev"."categories"."priority" IS 'Thông tin cấu hình độ ưu tiên danh mục';

WITH categories_data AS (
    SELECT ROW_NUMBER
               ( ) OVER ( ) AS INDEX,
            cate.ID
    FROM
        ( SELECT * FROM vnpt_dev.categories WHERE LEVEL = 1 ORDER BY ID ASC ) AS cate
) UPDATE vnpt_dev.categories
SET priority = ( SELECT DATA.INDEX FROM categories_data DATA WHERE DATA.ID = categories.ID )
WHERE
    LEVEL = 1;

UPDATE vnpt_dev.categories
SET priority = (
    SELECT DATA
               .INDEX
    FROM
        (
            SELECT ROW_NUMBER
                       ( ) OVER ( ) AS INDEX,
                    cate.ID,
                   cate.parent_id
            FROM
                ( SELECT C.* FROM vnpt_dev.categories C WHERE LEVEL <> 1 AND C.parent_id = categories.parent_id ORDER BY ID ASC ) AS cate
        ) DATA
    WHERE
            DATA.ID = categories.ID
)
WHERE
        LEVEL <> 1;