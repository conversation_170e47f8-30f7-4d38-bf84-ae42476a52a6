-- vnpt_dev.view_statistic_sub_new source

CREATE OR REPLACE VIEW vnpt_dev.view_statistic_sub_new
AS SELECT to_char(subscriptions.created_at, 'YYYY'::text) AS create_year,
    to_char(subscriptions.created_at, 'YYYY/MM'::text) AS create_month,
    to_char(subscriptions.created_at, 'YYYY/MM/dd'::text) AS create_date,
    subscriptions.id AS subscription_id,
    sub_user.id AS user_id,
    sub_user.province_id,
        CASE
            WHEN services.id IS NOT NULL AND (services.service_owner = ANY (ARRAY[0, 1])) THEN 1
            WHEN combo.id IS NOT NULL AND (combo.combo_owner = ANY (ARRAY[0, 1])) THEN 1
            ELSE 2
        END AS service_type,
        CASE
            WHEN combo_plan.id IS NOT NULL THEN concat(combo_plan.id, '0001')::bigint
            WHEN pricing.id IS NOT NULL THEN concat(pricing.id, '0000')::bigint
            ELSE NULL::bigint
        END AS pricing_unique_id,
        CASE
            WHEN subscriptions.pricing_id IS NOT NULL THEN pricing.pricing_name
            WHEN subscriptions.combo_plan_id IS NOT NULL THEN combo_plan.combo_name
            ELSE NULL::character varying
        END AS pricing_unique_name,
        CASE
            WHEN combo.id IS NOT NULL THEN concat(combo.id, '0001')::bigint
            WHEN services.id IS NOT NULL THEN concat(services.id, '0000')::bigint
            ELSE NULL::bigint
        END AS unique_id,
    subscriptions.created_at,
    subscriptions.modified_at,
    subscriptions.pricing_id,
    subscriptions.reg_type,
    subscriptions.confirm_status,
    subscriptions.status,
    subscriptions.deleted_flag,
    subscriptions.combo_plan_id,
    pricing.pricing_draft_id,
    services.user_id AS service_developer_id,
    combo.user_id AS combo_developer_id
   FROM subscriptions
     LEFT JOIN users sub_user ON subscriptions.user_id = sub_user.id
     LEFT JOIN pricing ON subscriptions.pricing_id = pricing.id
     LEFT JOIN services ON pricing.service_id = services.id
     LEFT JOIN combo_plan ON subscriptions.combo_plan_id = combo_plan.id
     LEFT JOIN combo ON combo_plan.combo_id = combo.id
     LEFT JOIN province ON sub_user.province_id = province.id;


-- vnpt_dev.report_view_top_subscription_from_product_new source

CREATE OR REPLACE VIEW vnpt_dev.report_view_top_subscription_from_product_new
AS SELECT
    to_char(subs.created_at, 'YYYY'::text) AS create_year,
    to_char(subs.created_at, 'YYYY/MM'::text) AS create_month,
    to_char(subs.created_at, 'YYYY/MM/dd'::text) AS create_date,
    subs.id AS subscription_id,
    user_info.id AS user_id,
    user_info.province_id,
    user_info.province_name,
    CASE
        WHEN combo.combo_owner = 0 OR combo.combo_owner = 1 THEN 1
        WHEN services.service_owner = 0 OR services.service_owner = 1 THEN 1
        ELSE 2
    END AS product_type,
    CASE
        WHEN combo.id IS NOT NULL THEN concat(combo.id, '0001')::bigint
        WHEN services.id IS NOT NULL THEN concat(services.id, '0000')::bigint
        ELSE NULL::bigint
    END AS unique_id,
    subs.created_at,
    subs.modified_at,
    subs.pricing_id,
    subs.combo_plan_id,
    subs.reg_type,
    subs.confirm_status,
    COALESCE(combo.combo_name, services.service_name) AS product_name,
    services.user_id as service_developer_id,
    combo.user_id as combo_developer_id
FROM subscriptions subs
    LEFT JOIN report_view_user_full_info as user_info ON subs.user_id = user_info.id
    LEFT JOIN pricing ON subs.pricing_id = pricing.id
    LEFT JOIN services ON pricing.service_id = services.id
    LEFT JOIN combo_plan ON subs.combo_plan_id = combo_plan.id
    LEFT JOIN combo ON combo_plan.combo_id = combo.id;


-- vnpt_dev.view_subscription_statistic_new
CREATE OR REPLACE VIEW vnpt_dev.view_subscription_statistic_new
AS SELECT
    to_char(subs.created_at, 'YYYY'::text) AS create_year,
    to_char(subs.created_at, 'YYYY/MM'::text) AS create_month,
    to_char(subs.created_at, 'YYYY/MM/dd'::text) AS create_date,
    subs.id AS subscription_id,
    users.id AS user_id,
    users.province_id as province_id,
    users.customer_type as customer_type,
    province.name as province_name,
        CASE
            WHEN combo.combo_owner = 0 OR combo.combo_owner = 1 THEN 1
            WHEN services.service_owner = 0 OR services.service_owner = 1 THEN 1
            ELSE 2
        END AS service_type,
        CASE
            WHEN combo.id IS NOT NULL THEN concat(combo.id, '0001')::bigint
            WHEN services.id IS NOT NULL THEN concat(services.id, '0000')::bigint
            ELSE NULL::bigint
        END AS unique_id,
    subs.created_at,
    subs.modified_at,
    subs.pricing_id,
    subs.confirm_status,
    subs.reg_type,
    subs.deleted_flag,
    services.user_id as service_developer_id,
    combo.user_id as combo_developer_id
FROM subscriptions subs
    LEFT JOIN users ON subs.user_id = users.id
    LEFT JOIN province ON users.province_id = province.id
    LEFT JOIN pricing ON subs.pricing_id = pricing.id
    LEFT JOIN services ON pricing.service_id = services.id
    LEFT JOIN combo_plan ON subs.combo_plan_id = combo_plan.id
    LEFT JOIN combo ON combo_plan.combo_id = combo.id;

CREATE INDEX "index_pricing_pricingDraftId" ON "vnpt_dev"."pricing" USING btree ("pricing_draft_id");
COMMENT ON INDEX "vnpt_dev"."index_pricing_pricingDraftId" IS 'Index cho pricing_draft_id, bảng pricing';

CREATE INDEX "index_comboPlan_comboDraftId" ON "vnpt_dev"."combo_plan" USING btree ("combo_draft_id");
COMMENT ON INDEX "vnpt_dev"."index_comboPlan_comboDraftId" IS 'Index cho cột combo_draft_id, bảng combo_plan';


 COMMENT ON COLUMN "vnpt_dev"."affiliate_users"."permanent_address" IS 'Địa chỉ thường trú';
 COMMENT ON COLUMN "vnpt_dev"."affiliate_users"."bank_beneficiary" IS 'Tên người thụ hưởng';
 COMMENT ON COLUMN "vnpt_dev"."affiliate_users"."user_id" IS 'ID user tương ứng của tài khoản affiliate';
 COMMENT ON COLUMN "vnpt_dev"."affiliate_users"."approval_reason" IS 'Lý do phê duyệt/từ chối phê duyệt';

 COMMENT ON COLUMN "vnpt_dev"."users"."created_at" IS 'Thời gian tạo';
 COMMENT ON COLUMN "vnpt_dev"."users"."created_by" IS 'ID người tạo';
 COMMENT ON COLUMN "vnpt_dev"."users"."deleted_flag" IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';
 COMMENT ON COLUMN "vnpt_dev"."users"."modified_at" IS 'Thời gian cập nhật';
 COMMENT ON COLUMN "vnpt_dev"."users"."modified_by" IS 'ID người cập nhật';
 COMMENT ON COLUMN "vnpt_dev"."users"."status" IS 'Trạng thái';
 COMMENT ON COLUMN "vnpt_dev"."users"."account_expired" IS 'Trạng thái expired của tài khoản';
 COMMENT ON COLUMN "vnpt_dev"."users"."account_locked" IS 'Trạng thái locked của tài khoản';
 COMMENT ON COLUMN "vnpt_dev"."users"."activation_key" IS 'Mã kích hoạt của tài khoản';
 COMMENT ON COLUMN "vnpt_dev"."users"."address" IS 'Địa chỉ';
 COMMENT ON COLUMN "vnpt_dev"."users"."avatar" IS 'Url ảnh đại diện doanh nghiệp';
 COMMENT ON COLUMN "vnpt_dev"."users"."birthday" IS 'Ngày sinh';
 COMMENT ON COLUMN "vnpt_dev"."users"."company" IS '<không còn sử dụng>';
 COMMENT ON COLUMN "vnpt_dev"."users"."tin" IS 'Mã số thuế doanh nghiệp';
 COMMENT ON COLUMN "vnpt_dev"."users"."cover_image" IS 'Url ảnh bìa doanh nghiệp';
 COMMENT ON COLUMN "vnpt_dev"."users"."credentials_expired" IS 'Trạng thái quá hạn đăng nhập';
 COMMENT ON COLUMN "vnpt_dev"."users"."email" IS 'Email tài khoản';
 COMMENT ON COLUMN "vnpt_dev"."users"."enabled" IS 'Trạng thái enable/disable';
 COMMENT ON COLUMN "vnpt_dev"."users"."first_name" IS 'Tên';
 COMMENT ON COLUMN "vnpt_dev"."users"."gender" IS 'Giới tính';
 COMMENT ON COLUMN "vnpt_dev"."users"."last_name" IS 'Họ';
 COMMENT ON COLUMN "vnpt_dev"."users"."password" IS 'Mật khẩu';
 COMMENT ON COLUMN "vnpt_dev"."users"."password_tmp" IS 'Mật khẩu tạm (khi yêu cầu đổi mật khẩu)';
 COMMENT ON COLUMN "vnpt_dev"."users"."phone_number" IS 'Số điện thoại';
 COMMENT ON COLUMN "vnpt_dev"."users"."user_name" IS 'User name của tài khoản SME';
 COMMENT ON COLUMN "vnpt_dev"."users"."website" IS 'Website doanh nghiệp';
 COMMENT ON COLUMN "vnpt_dev"."users"."district_id" IS 'ID quận huyện';
 COMMENT ON COLUMN "vnpt_dev"."users"."nation_id" IS 'ID quốc gia';
 COMMENT ON COLUMN "vnpt_dev"."users"."province_id" IS 'ID tỉnh';
 COMMENT ON COLUMN "vnpt_dev"."users"."business_area_id" IS 'ID ngành nghề kinh doanh';
 COMMENT ON COLUMN "vnpt_dev"."users"."business_size_id" IS 'ID quy mô doanh nghiệp';
 COMMENT ON COLUMN "vnpt_dev"."users"."business_email" IS 'Email nhà phát triển';
 COMMENT ON COLUMN "vnpt_dev"."users"."name" IS 'Tên doanh nghiệp';
 COMMENT ON COLUMN "vnpt_dev"."users"."description" IS 'Mô tả về tài khoản';
 COMMENT ON COLUMN "vnpt_dev"."users"."department_id" IS 'ID phòng ban (ID bảng department)';
 COMMENT ON COLUMN "vnpt_dev"."users"."sme_user_id" IS 'ID user khách hàng';
 COMMENT ON COLUMN "vnpt_dev"."users"."tech_id" IS 'ID tài khoản Vnpt ID';
 COMMENT ON COLUMN "vnpt_dev"."users"."province_code" IS 'Mã tỉnh thành';
 COMMENT ON COLUMN "vnpt_dev"."users"."social_insurance_number" IS 'Mã số BHXH';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_fullname" IS 'Họ tên người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_gender" IS 'Giới tính người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_title" IS 'Chức vụ người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_birthday" IS 'Ngày sinh người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_nation_id" IS 'ID quốc gia người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_folk_id" IS 'ID dân tộc người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_personal_cert_number" IS 'Mã số giấy tờ chứng thực cá nhân';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_personal_cert_date" IS 'Ngày cấp giấy tờ chứng thực cá nhân';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_personal_cert_place" IS 'Nơi cấp giấy tờ chứng thực cá nhân';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_registered_place" IS 'Địa chỉ thường trú người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_address" IS 'Nơi ở hiện tại người đại diện';
 COMMENT ON COLUMN "vnpt_dev"."users"."founding_date" IS 'Ngày thành lập công ty';
 COMMENT ON COLUMN "vnpt_dev"."users"."rep_personal_cert_expire_date" IS 'Ngày hết hạn của giấy tờ chứng thực cá nhân';
 COMMENT ON COLUMN "vnpt_dev"."users"."business_license_number" IS 'Số giấy phép kinh doanh, dành cho KHDN hoặc HKD';
 COMMENT ON COLUMN "vnpt_dev"."users"."business_license_issued_by" IS 'Đơn vị cấp giấy phép kinh doanh, dành cho KHDN hoặc HKD';
 COMMENT ON COLUMN "vnpt_dev"."users"."business_license_issued_date" IS 'Ngày cấp giấy phép kinh doanh, dành cho KHDN hoặc HKD';
 COMMENT ON COLUMN "vnpt_dev"."users"."assignee_id" IS 'ID nhân sự phụ trách';
 COMMENT ON COLUMN "vnpt_dev"."users"."lst_assignees_id" IS 'Danh sách ID nhân sự theo dõi';