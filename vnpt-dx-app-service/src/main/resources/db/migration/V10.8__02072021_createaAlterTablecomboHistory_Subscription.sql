DROP TABLE IF EXISTS action_log_combo CASCADE;
CREATE TABLE vnpt_dev.action_log_combo (
	id bigserial NOT NULL,
	object_id int8 NULL,
	"object" varchar(100) NULL, -- đ<PERSON>i tượng
	status int2 NULL, -- trạng thái
	"content" varchar(1000) NULL, -- nội dung
	created_by int8 NULL, -- người tạo
	created_at timestamp NULL, -- thời gian tạo
	action_type int2 NULL, -- loại
	portal int2 NULL, -- portal
	version int2 NULL, -- portal
	CONSTRAINT action_log_combo_pk PRIMARY KEY (id)
);
COMMENT ON TABLE vnpt_dev.action_log_combo IS 'Lịch sử tác động combo hoặc gói combo';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN vnpt_dev.action_log_combo.object_id IS 'id_combo hoặc id_combo_plan';
COMMENT ON COLUMN vnpt_dev.action_log_combo."object" IS 'đối tượng';
COMMENT ON COLUMN vnpt_dev.action_log_combo.status IS 'trạng thái';
COMMENT ON COLUMN vnpt_dev.action_log_combo."content" IS 'nội dung';
COMMENT ON COLUMN vnpt_dev.action_log_combo.created_by IS 'người tạo';
COMMENT ON COLUMN vnpt_dev.action_log_combo.created_at IS 'thời gian tạo';
COMMENT ON COLUMN vnpt_dev.action_log_combo.action_type IS '0: COMBO,  1: COMBO_PLAN,  -1: ALL';
COMMENT ON COLUMN vnpt_dev.action_log_combo.portal IS 'portal';
COMMENT ON COLUMN vnpt_dev.action_log_combo.version IS 'Phiêm bản khác nhau của combo hoặc gói combo';

ALTER TABLE vnpt_dev.subscriptions
ADD COLUMN called_trans int2;
COMMENT ON COLUMN vnpt_dev.subscriptions.called_trans IS 'Cho phép null, mặc định = 0 trong bảng subscription 0/null: chưa call api transaction/ đẩy bản tin kafka, 1: đã call api transaction/ đẩy bản tin kafka';
COMMENT ON COLUMN vnpt_dev.subscriptions.payment_method IS '0: Chuyển khoản (P2P), 1: vnpt pay, 2: Tiền mặt (by_cash)';

INSERT INTO vnpt_dev.schedules(bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by, modified_at)
VALUES ('subscription-history', 'endSubscriptionAuto', NULL, '0 0 0 * * ?', 'subscription history', 1, 'batch', NULL, 'batch', NULL);

