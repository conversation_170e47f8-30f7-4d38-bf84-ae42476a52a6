 CREATE OR REPLACE VIEW view_enterprise_user_classification_new AS

 WITH last_login AS (
        SELECT DISTINCT ON (history_login.user_id) history_login.user_id,
            history_login.created_at
        FROM vnpt_dev.history_login
        ORDER BY history_login.user_id, history_login.created_at DESC
    ),
    user_subs AS (
        SELECT subscriptions.user_id,
            count(subscriptions.id) FILTER (WHERE (subscriptions.status = 1)) AS num_trial,
            count(subscriptions.id) FILTER (WHERE (subscriptions.status = 2)) AS num_active,
            count(subscriptions.id) FILTER (WHERE ((subscriptions.status = 1) OR (subscriptions.status = 2))) AS num_active_or_trial,
            count(subscriptions.id) FILTER (WHERE ((subscriptions.status = 3) OR (subscriptions.status = 4))) AS num_cancel_or_finish
        FROM vnpt_dev.subscriptions
        WHERE ((subscriptions.deleted_flag = 1) AND (subscriptions.confirm_status = 1))
        GROUP BY subscriptions.user_id
    ),
    user_classification AS (
        SELECT users.id AS user_id,
            users.tin,
            users.email,
            users.phone_number,
            users.customer_type,
            users.status,
            users.created_at,
            user_subs.num_active_or_trial,
            user_subs.num_cancel_or_finish,
            users.provider_type,
            user_subs.num_trial,
            user_subs.num_active,
            CASE
                WHEN (user_subs.num_active_or_trial > 0)
                    THEN 0
                WHEN ((user_subs.num_active_or_trial = 0) AND
                    (user_subs.num_cancel_or_finish > 0) AND
                    ((last_login.created_at)::date IS NOT NULL) AND
                    ((last_login.created_at)::date < ((now() - '90 days'::interval))::date))
                    THEN 1
                ELSE 2
            END AS type,
            users.migrate_time,
            users.migrate_code
        FROM ((vnpt_dev.users
            LEFT JOIN user_subs ON ((users.id = user_subs.user_id)))
            LEFT JOIN last_login ON ((users.id = last_login.user_id)))
        WHERE users.deleted_flag = 1
    ),
    user_enterprise_mapping AS (
        SELECT DISTINCT ON (enterprise.id)
            enterprise.id AS enterprise_id,
            users.id AS user_id
        FROM (vnpt_dev.enterprise
            JOIN vnpt_dev.users ON (((users.activation_key IS NULL) AND (COALESCE(users.deleted_flag, 1) = 1) AND ((COALESCE(users.customer_type, 'KHDN'::character varying))::text = (COALESCE(enterprise.customer_type, 'KHDN'::character varying))::text) AND ((((COALESCE(users.customer_type, 'KHDN'::character varying))::text = ANY (ARRAY[('KHDN'::character varying)::text, ('HKD'::character varying)::text])) AND ((users.tin)::text = (enterprise.tin)::text)) OR (((users.customer_type)::text = 'CN'::text) AND (users.provider_type = ANY (ARRAY[0, 1, 2])) AND ((users.email)::text = (enterprise.email)::text)) OR (((users.customer_type)::text = 'CN'::text) AND (users.provider_type = 3) AND ((users.phone_number)::text = (enterprise.phone)::text))))))
        ORDER BY enterprise.id, users.id DESC
    ),
    user_enterprise_mapping_without_duplicating AS (
        SELECT DISTINCT ON (user_id)
            enterprise_id,
            user_id
        FROM user_enterprise_mapping
        ORDER BY user_id, enterprise_id DESC
    )
SELECT enterprise.id AS enterprise_id,
    user_classification.user_id,
    (COALESCE(user_classification.num_active_or_trial, (0)::bigint) + COALESCE(user_classification.num_cancel_or_finish, (0)::bigint)) AS num_subs,
    COALESCE(user_classification.num_trial, (0)::bigint) AS num_trial,
    COALESCE(user_classification.num_active, (0)::bigint) AS num_active,
    user_classification.type,
    user_classification.status,
    user_classification.created_at,
    user_classification.migrate_time,
    user_classification.migrate_code
FROM ((user_classification
    JOIN user_enterprise_mapping_without_duplicating ON ((user_enterprise_mapping_without_duplicating.user_id = user_classification.user_id)))
    JOIN vnpt_dev.enterprise ON ((user_enterprise_mapping_without_duplicating.enterprise_id = enterprise.id)))
WHERE ((COALESCE((enterprise.deleted_flag)::integer, 1) = 1) AND
    (COALESCE((enterprise.archive_status)::integer, 0) = 0))