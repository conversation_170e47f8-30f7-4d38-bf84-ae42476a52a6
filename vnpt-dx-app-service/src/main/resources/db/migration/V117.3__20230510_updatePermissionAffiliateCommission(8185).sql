--Thê<PERSON> các permission liên quan đến sửa hoa hồng
insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Sửa hoa hồng affiliate',
	'SUA_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'SUA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'SUA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'SUA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/update',
	'SUA_HOA_HONG_AFFILIATE',
	'PUT'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'SUA_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'SUA_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'SUA_HOA_HONG_AFFILIATE'
);

refresh materialized view concurrently vnpt_dev.role_permission_api;