ALTER TABLE credit_note
    ADD COLUMN code varchar(25);

COMMENT ON column credit_note.code is 'mã của credit note';

COMMENT ON column  credit_note.status
    IS '0: ch<PERSON><PERSON> hoàn trả, 1: ch<PERSON><PERSON> điề<PERSON> chỉnh, 2: đã hoàn trả, 3: đã điều chỉnh, 4: v<PERSON> hiệ<PERSON> , 5: đến hạn hoàn trả';


INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority)
values ((SELECT MAX(id) + 1 FROM vnpt_dev.permission ),'Xem danh sách mã credit note','XEM_DANH_SACH_MA_CREDIT_NOTE',102,9989030),
       ((SELECT MAX(id) + 2 FROM vnpt_dev.permission ),'Xem danh sách tên khách hàng','XEM_DANH_SACH_TEN_KHACH_HANG',102,9989040);


-- Thê<PERSON> vào quyền theo portal
INSERT INTO vnpt_dev.permission_portal(permission_id, portal_id)
values ((SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE'),1),
       ((SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE'),2),
       ((SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE'),3),

       ((SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG'),1),
       ((SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG'),2),
       ((SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG'),3);



-- Thêm quyền mặc định cho role mặc định FULL_ADMIN, FULL_DEV
-- FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES (9, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE'), 1),
       (9, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG'), 1);

-- FUll_DEV
INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit)
VALUES (10, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE'), 1),
       (10, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG'), 1);




INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
values (509,'/api/dev-portal/credit-note/search-name','ROLE_DEV_GET_NAME_CUSTOMER','GET'),
       (510,'/api/dev-portal/credit-note/search-code','ROLE_DEV_GET_CODE_CREDIT_NOTE','GET'),

       (511,'/api/admin-portal/credit-note/search-name','ROLE_ADMIN_GET_NAME_CUSTOMER','GET'),
       (512,'/api/admin-portal/credit-note/search-code','ROLE_ADMIN_GET_CODE_CREDIT_NOTE','GET'),
       (513,'/api/sme-portal/credit-note/search-name','ROLE_SME_GET_NAME_CUSTOMER','GET'),
       (514,'/api/sme-portal/credit-note/all','ROLE_SME_GET_CREDIT_NOTE','GET'),
       (515,'/api/sme-portal/credit-note/search-code','ROLE_SME_GET_CODE_CREDIT_NOTE','GET');


INSERT INTO vnpt_dev.api_permission (api_id, permission_id)
VALUES (509, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG')),
       (510, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE')),
       (511, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG')),
       (512, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE')),

       (513,(SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_TEN_KHACH_HANG')),
       (514,(SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_HOAN_TRA')),
       (515, (SELECT id FROM vnpt_dev.permission where code = 'XEM_DANH_SACH_MA_CREDIT_NOTE'));


REFRESH MATERIALIZED VIEW CONCURRENTLY role_permission_api;