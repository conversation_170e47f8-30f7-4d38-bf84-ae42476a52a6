-- Tạo partition theo tháng cho tháng hiện tại 
CREATE OR REPLACE FUNCTION vnpt_dev.func_create_partition_by_month_current(tbl_name varchar) RETURNS int2 AS $$
DECLARE
    partition_name TEXT;
    sql_query TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- L<PERSON>y thời gian là tháng hiện tại
    EXECUTE 'SELECT cast(date_trunc(''month'', now()) as date)' INTO start_date;
    end_date := cast(start_date + INTERVAL '1 month' as date);

    -- Tạo tên partition dựa trên tháng
    partition_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM');

    -- Kiểm tra nếu partition đã tồn tại
    IF NOT EXISTS (
        SELECT 1
        FROM pg_class
        WHERE relname = partition_name
    ) THEN
        -- Tạo partition mới nếu chưa tồn tại
        sql_query := format(
            'CREATE TABLE vnpt_dev.%I PARTITION OF vnpt_dev.%I FOR VALUES FROM (%L) TO (%L)',
            partition_name,
            tbl_name,
            start_date,
            end_date
        );
        RAISE NOTICE 'SQL query % ', sql_query;
        EXECUTE sql_query;
        RAISE NOTICE 'Partition % created.', partition_name;
        RETURN 1;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Tạo function cho phép tạo partition của tháng hiện tại và tháng sau cho một bảng bất kì
CREATE OR REPLACE FUNCTION vnpt_dev.func_create_partition_by_month(tbl_name varchar) RETURNS int2 AS $$
DECLARE
    partition_name TEXT;
    sql_query TEXT;
    start_date DATE;
    end_date DATE;
    create_current_partition int2;
BEGIN
    -- Tạo partition tháng hiện tại
    create_current_partition := func_create_partition_by_month_current(tbl_name);

    -- Lấy thời gian là tháng sau
    EXECUTE 'SELECT cast(date_trunc(''month'', now() + INTERVAL ''1 month'') as date)' INTO start_date;
    end_date := cast(start_date + INTERVAL '1 month' as date);

    -- Tạo tên partition dựa trên tháng
    partition_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM');

    -- Kiểm tra nếu partition đã tồn tại
    IF NOT EXISTS (
        SELECT 1
        FROM pg_class
        WHERE relname = partition_name
    ) THEN
        -- Tạo partition mới nếu chưa tồn tại
        sql_query := format(
            'CREATE TABLE vnpt_dev.%I PARTITION OF vnpt_dev.%I FOR VALUES FROM (%L) TO (%L)',
            partition_name,
            tbl_name,
            start_date,
            end_date
        );
        RAISE NOTICE 'SQL query % ', sql_query;
        EXECUTE sql_query;
        RAISE NOTICE 'Partition % created.', partition_name;
        RETURN 1;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;