-- Create table customer_classification_history
DROP TABLE IF EXISTS "vnpt_dev"."customer_classification_history";
CREATE TABLE "vnpt_dev"."customer_classification_history" (
  "id" bigserial NOT NULL,
  "user_id" int8 NOT NULL,
  "enterprise_id" int8 NOT NULL,
  "classification_type" int2 NOT NULL,
  "created_at" timestamp(6) NOT NULL,
  "user_created_at" timestamp(6),
  "enterprise_created_at" timestamp(6),
  "province_id" int8,
  "customer_type" varchar(16) COLLATE "pg_catalog"."default",
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."user_id" IS 'ID user';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."enterprise_id" IS 'ID enterprise';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."classification_type" IS 'Phân loại khách hàng (0: <PERSON><PERSON> hiện hữu, 1: <PERSON><PERSON> rời bỏ, 2: KH tiềm năng)';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."created_at" IS 'Thời gian thay đổi';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."user_created_at" IS 'Thời gian tạo user';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."enterprise_created_at" IS 'Thời gian tạo enterprise';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."province_id" IS 'ID tỉnh thành của user/enterprise';
COMMENT ON COLUMN "vnpt_dev"."customer_classification_history"."customer_type" IS 'Phân loại khách hàng (KHDN/HKD/CN)';
COMMENT ON TABLE "vnpt_dev"."customer_classification_history" IS 'Lịch sử chuyển đổi loại khách hàng (tiềm năng/hiện hữu/rời bỏ)';

COMMENT ON COLUMN "vnpt_dev"."subscriptions"."started_at" IS 'Ngày bắt đầu sử dụng';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."start_charge_at" IS 'Ngày bắt đầu tính tiền';

-- Init data from users
INSERT INTO "vnpt_dev"."customer_classification_history" (user_id, enterprise_id, classification_type, created_at, user_created_at, enterprise_created_at, province_id, customer_type)
SELECT
    mUser.id AS user_id,
    mEnterprise.id AS enterprise_id,
    2 AS classification_type,
    coalesce(mUser.created_at, now()) AS created_at,
    mUser.created_at AS user_created_at,
    mEnterprise.created_at AS enterprise_created_at,
    COALESCE ( mUser.province_id, mEnterprise.province_id ) AS province_id,
    mUser.customer_type AS customer_type
FROM
    "vnpt_dev"."users" AS mUser
    JOIN "vnpt_dev"."enterprise" AS mEnterprise ON
        ((mUser.customer_type = mEnterprise.customer_type OR mEnterprise.customer_type IS NULL) and
        (((mUser.provider_type = ANY(ARRAY[0, 1, 2]) AND mUser.email = mEnterprise.email) OR
            (mUser.provider_type = 3 AND mUser.phone_number = mEnterprise.phone) OR
            (mUser.tin = mEnterprise.tin))))
WHERE
    mUser.deleted_flag = 1 AND
    mEnterprise.deleted_flag = 1;

-- Init data from view_enterprise_user_classification_new
WITH current_classification AS (
        SELECT mClassification.user_id, mClassification.enterprise_id, mClassification.type
        FROM "vnpt_dev"."view_enterprise_user_classification_new" AS mClassification
    ),
    lastest_classification AS (
        SELECT DISTINCT ON ( user_id ) *
        FROM "vnpt_dev"."customer_classification_history"
        ORDER BY user_id, created_at DESC
    )

INSERT INTO "vnpt_dev"."customer_classification_history" (user_id, enterprise_id, classification_type, created_at, user_created_at, enterprise_created_at, province_id, customer_type)
SELECT
    mUser.id AS user_id,
    mEnterprise.id AS enterprise_id,
    mCurrent.type AS classification_type,
    now( ) AS created_at,
    mUser.created_at AS user_created_at,
    mEnterprise.created_at AS enterprise_created_at,
    COALESCE ( mUser.province_id, mEnterprise.province_id ) AS province_id,
    mUser.customer_type AS customer_type
FROM
    current_classification AS mCurrent
    LEFT JOIN lastest_classification AS mLastest ON mLastest.user_id = mCurrent.user_id
    LEFT JOIN "vnpt_dev"."users" AS mUser ON mUser.id = mCurrent.user_id
    LEFT JOIN "vnpt_dev"."enterprise" AS mEnterprise ON mEnterprise.id = mCurrent.enterprise_id
WHERE
    mLastest.id IS NULL OR mLastest.classification_type != mCurrent.type;