DELETE FROM vnpt_dev.apis WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id FROM vnpt_dev.api_permission JOIN vnpt_dev.permission ON permission.id = api_permission.permission_id
    WHERE vnpt_dev.permission.code IN ('QUAN_LY_KHACH_HANG_LIEN_HE', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA', 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET', 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG')
);

DELETE FROM vnpt_dev.api_permission WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG_LIEN_HE', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA', 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET', 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG'))
);

DELETE FROM vnpt_dev.roles_permissions WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG_LIEN_HE', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA', 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET', 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG'))
);

DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG_LIEN_HE', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA', 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET', 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG'))
);

DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_KHACH_HANG_LIEN_HE', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA', 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI', 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT', 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET', 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG');


-- INSERT vao bang permission
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Quản lý Khách hàng liên hệ',
        'QUAN_LY_KHACH_HANG_LIEN_HE',
        -1,
        14000000
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem danh sách',
        'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        14000001
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Lưu trữ/Bỏ lưu trữ/Xóa khách hàng',
        'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        14000002
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Thêm mới',
        'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        14000003
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Cập nhật',
        'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        14000004
    );


INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Xem chi tiết',
        'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        14000005
    );

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission),
        'Chuyển sang khách hàng tiềm năng',
        'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG',
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        14000006
    );

-- INSERT dữ liệu bảng permission_portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.permission_portal),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
    );

-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
(
    (SELECT MAX(id) + 1 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 2 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 3 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 4 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 5 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 6 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT MAX(id) + 7 FROM vnpt_dev.roles_permissions),
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG' ORDER BY id DESC LIMIT 1),
    1
);

-- Add vao bang apis, api_permission

--add api Xem danh sách khách hàng liên hệ
-- TODO

--add api Tạo mới khách hàng liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact',
        'ROLE_QLKHLH_TAO_MOI_KH_LIEN_HE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_TAO_MOI_KH_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI' ORDER BY id DESC LIMIT 1)
    );

--add api lấy ls template
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-preference',
        'ROLE_QLKHLH_LAY_DS_TEMPLATE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DS_TEMPLATE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI' ORDER BY id DESC LIMIT 1)
    );

--add api lấy ds province
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-province',
        'ROLE_QLKHLH_LAY_DS_TINH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DS_TINH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI' ORDER BY id DESC LIMIT 1)
    );

--api lấy danh sách họ tên các liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact-name',
        'ROLE_QLKHLH_LAY_DANH_SACH_HO_TEN_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_HO_TEN_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lấy danh sách email liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact-email',
        'ROLE_QLKHLH_LAY_DANH_SACH_EMAIL_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_EMAIL_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lấy danh sách số điện thoại liên hệ
 INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact-phone',
        'ROLE_QLKHLH_LAY_DANH_SACH_PHONE_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_PHONE_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

    --api lấy danh sách liên hệ
 INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-lst-contact',
        'ROLE_QLKHLH_LAY_DANH_SACH_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_DANH_SACH_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--api lấy chi tiết liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-contact-detail',
        'ROLE_QLKHLH_LAY_CHI_TIET_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_CHI_TIET_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

-- api lấy chi tiết doanh nghiệp trong chi tiết liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-business-detail',
        'ROLE_QLKHLH_LAY_CHI_TIET_DOANH_NGHIEP_TRONG_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_CHI_TIET_DOANH_NGHIEP_TRONG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

-- api lấy thông tin người đại diện trong chi tiết liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/get-representative-detail',
        'ROLE_QLKHLH_LAY_CHI_TIET_DAI_DIEN_TRONG_LIEN_HE',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LAY_CHI_TIET_DAI_DIEN_TRONG_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

-- api cập nhật liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
     (
         (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
         '/api/customer-contact/update-contact-detail',
         'ROLE_QLKHLH_CAP_NHAT_LIEN_HE',
         'POST'
     );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_CAP_NHAT_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' ORDER BY id DESC LIMIT 1)
    );

   --api lưu trữ/hủy lưu trữ liên hệ
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/archive-lst-contact',
        'ROLE_QLKHLH_LUU_TRU_LIEN_HE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_LUU_TRU_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA' ORDER BY id DESC LIMIT 1)
    );

    --api xóa liên hệ
 INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/delete-contact',
        'ROLE_QLKHLH_XOA_LIEN_HE',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_XOA_LIEN_HE' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CAP_NHAT_TRANG_THAI_LUU_TRU_VA_XOA' ORDER BY id DESC LIMIT 1)
    );


--add api chuyển liên hệ sang khách hàng tiềm năng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/convert-to-potential-enterprise',
        'ROLE_QLKHLH_CHUYEN_LIEN_HE_SANG_TIEM_NANG',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_CHUYEN_LIEN_HE_SANG_TIEM_NANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG' ORDER BY id DESC LIMIT 1)
    );

--add api chuyển liên hệ sang khách hàng tiềm năng (có thêm quyền tạo kh tiềm năng)
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/create/create-potential-enterprise',
        'ROLE_QLKH_CREATE_POTENTIAL_ENTERPRISE_CONVERT',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_CREATE_POTENTIAL_ENTERPRISE_CONVERT' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG' ORDER BY id DESC LIMIT 1)
    );

--add api chuyển liên hệ sang khách hàng tiềm năng (có thêm quyền xóa liên hệ)
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/delete-contact',
        'ROLE_QLKHLH_XOA_LIEN_HE_CONVERT',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_XOA_LIEN_HE_CONVERT' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_CHUYEN_SANG_KHACH_HANG_TIEM_NANG' ORDER BY id DESC LIMIT 1)
    );

--add api lấy danh sách dân tộc
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-folk',
        'ROLE_QLKHLH_GET_DAN_TOC',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_GET_DAN_TOC' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--add api lấy dan sách admin và sme
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-employee',
        'ROLE_QLKHLH_GET_DANH_SACH_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_GET_DANH_SACH_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--add api lấy danh sách đường phố
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-street',
        'ROLE_QLKHLH_GET_DANH_SACH_DUONG_PHO',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_GET_DANH_SACH_DUONG_PHO' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu combobox ten mien dia ly
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        'api/admin-portal/crm/enterprise-mgmt/common/get-list-region',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_MIEN_DIA_LY',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_MIEN_DIA_LY' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu combobox ten tinh
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-province',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_TINH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_TINH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu combobox ten huyen
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-district',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_HUYEN',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_HUYEN' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu combobox ten xa
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-ward',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_XA',
        'GET'
    );

INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_TEN_XA' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );
--add api lấy danh sách phiếu khách hàng
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-customer-ticket-title',
        'ROLE_QLKHLH_GET_DANH_SACH_PHEU_KHACH_HANG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_GET_DANH_SACH_PHEU_KHACH_HANG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--add api lấy danh sách nhật ký hoạt động
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-action-history-content',
        'ROLE_QLKHLH_GET_DANH_SACH_NHAT_KY_HOAT_DONG',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_GET_DANH_SACH_NHAT_KY_HOAT_DONG' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_DANH_SACH' ORDER BY id DESC LIMIT 1)
    );

--add api Import liên hệ từ file excel
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/customer-contact/import-contact',
        'ROLE_QLKHLH_IMPORT_CUSTOMER_CONTACT',
        'POST'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKHLH_IMPORT_CUSTOMER_CONTACT' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE_THEM_MOI' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu business area
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-business-area',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_NGANH_NGHE_KINH_DOANH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_NGANH_NGHE_KINH_DOANH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu business type
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-business-type',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_LOAI_HINH_KINH_DOANH',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_LOAI_HINH_KINH_DOANH' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );

--add api lay du lieu business size
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.apis),
        '/api/admin-portal/crm/enterprise-mgmt/common/get-list-business-size',
        'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_QUY_MO_DOANH_NGHIEP',
        'GET'
    );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_QLKH_LAY_DU_LIEU_COMBOBOX_QUY_MO_DOANH_NGHIEP' ORDER BY id DESC LIMIT 1),
        (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_KHACH_HANG_LIEN_HE' ORDER BY id DESC LIMIT 1)
    );

-- REFRESH MATERIALIZED VIEW
REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;