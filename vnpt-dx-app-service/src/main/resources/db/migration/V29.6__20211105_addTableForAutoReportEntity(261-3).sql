create table vnpt_dev.auto_send_report_config
(
    id bigserial
        constraint auto_send_report_config_pk
            primary key,
    status int2,
    deleted_flag int2,
    created_at timestamp(6),
    created_by int8,
    modified_at timestamp(6),
    modified_by int8,
    code varchar(50),
    report_type varchar(255),
    type int2,
    period int2,
    start_time varchar(4),
    end_time varchar(4),
    day varchar(255),
    week int,
    start_date date not null,
    mail_template_codes varchar(1000)
);

comment on column vnpt_dev.auto_send_report_config.id is 'id tự sinh khi tạo bản ghi trong CSDL';

comment on column vnpt_dev.auto_send_report_config.status is 'trạng thái kích hoạt hay deactive';

comment on column vnpt_dev.auto_send_report_config.deleted_flag is 'xóa mềm';

comment on column vnpt_dev.auto_send_report_config.created_at is 'thời gian tạo';

comment on column vnpt_dev.auto_send_report_config.created_by is 'id user cập nhật lần cuối';

comment on column vnpt_dev.auto_send_report_config.modified_at is 'thời gian cập nhật lần cuối';

comment on column vnpt_dev.auto_send_report_config.modified_by is 'id user cập nhật lần cuối';

comment on column vnpt_dev.auto_send_report_config.code is 'Mã code của entity tự tăng (tiền tố ML)';

comment on column vnpt_dev.auto_send_report_config.report_type is 'Chứa list các loại báo cáo cần gửi (Ngày, Tuần, Tháng, Quý 1, Quý 2, Quý 3, Quý 4, Năm, Tổng)
Example: [1,2 …]
tương ứng Ngày, Tuần …
';

comment on column vnpt_dev.auto_send_report_config.type is '1.Hằng ngày
2. ngày trong tuần
3. Ngày trong tháng
';

comment on column vnpt_dev.auto_send_report_config.period is '1.chạy một lần
2. 3 giờ 1 lần
';

comment on column vnpt_dev.auto_send_report_config.start_time is 'giờ bắt đầu chạy lịch
Example: 1705 (17h 05 phút)
';

comment on column vnpt_dev.auto_send_report_config.end_time is 'giờ bắt đầu chạy lịch
Example: 1705 (17h 05 phút)
';

comment on column vnpt_dev.auto_send_report_config.day is '3 trường hợp
nếu trường type = 1 thì trường này sẽ null
nếu type = 2 (ngày trong tuần) thì sẽ chứa list các ngày trong tuần được chọn. Example [1,2,4,7] (CN là 1)
nếu type = 3 (ngày trong tháng). List chứa giá trị từ 1 đến 31. (Ngoài ra nếu chứa giá trị 32 nghĩa là người dùng đang chọn “ngày cuối cùng của tháng”)
';

comment on column vnpt_dev.auto_send_report_config.week is 'các giá trị tuần thứ 1, tuần thứ 2, … tuần thứ 4 và tuần cuối cùng. Giá trị 5 là tuần cuối cùng.';

comment on column vnpt_dev.auto_send_report_config.start_date is 'Ngày bắt đầu áp dụng gửi báo cáo định kì';

comment on column vnpt_dev.auto_send_report_config.mail_template_codes is 'List code các template mà cấu hình tự động gửi này sử dụng.
Ex: [“AC1”, “BL-01”]
';

create table vnpt_dev.external_email_received_report
(
    id bigserial
        constraint external_email_received_report_pk
            primary key,
    status int2,
    deleted_flag int2,
    created_at timestamp(6),
    created_by int8,
    modified_at timestamp(6),
    modified_by int8,
    name varchar(255),
    email varchar(128)
);

comment on column vnpt_dev.external_email_received_report.id is 'id tự sinh khi tạo bản ghi trong CSDL';

comment on column vnpt_dev.external_email_received_report.status is 'trạng thái kích hoạt hay deactive';

comment on column vnpt_dev.external_email_received_report.deleted_flag is 'xóa mềm';

comment on column vnpt_dev.external_email_received_report.created_at is 'thời gian tạo';

comment on column vnpt_dev.external_email_received_report.created_by is 'id user tạo';

comment on column vnpt_dev.external_email_received_report.modified_at is 'thời gian cập nhật lần cuối';

comment on column vnpt_dev.external_email_received_report.modified_by is 'id user cập nhật lần cuối';

comment on column vnpt_dev.external_email_received_report.name is 'tên người nhận';


create table vnpt_dev.send_report_department
(
    id bigserial
        constraint send_report_department_pk
            primary key,
    send_report_id int8 not null,
    department_id int8
);

comment on column vnpt_dev.send_report_department.id is 'id tự sinh khi tạo bản ghi trong CSDL';

comment on column vnpt_dev.send_report_department.send_report_id is 'id của đối tượng trong bảng auto_send_report_config';

comment on column vnpt_dev.send_report_department.department_id is 'id của đối tượng trong bảng departments';

create table vnpt_dev.send_report_user
(
    id bigserial
        constraint send_report_user_pk
            primary key,
    send_report_id int8 not null,
    user_id int8
);

comment on column vnpt_dev.send_report_user.id is 'id tự sinh khi tạo bản ghi trong CSDL';

comment on column vnpt_dev.send_report_user.send_report_id is 'id của đối tượng trong bảng auto_send_report_config';

comment on column vnpt_dev.send_report_user.user_id is 'id của đối tượng trong bảng users';


create table vnpt_dev.send_report_external_email
(
    id bigserial
        constraint send_report_external_email_pk
            primary key,
    send_report_id int8 not null,
    external_email_id int8 not null
);

comment on column vnpt_dev.send_report_external_email.id is 'id tự sinh khi tạo bản ghi trong CSDL';

comment on column vnpt_dev.send_report_external_email.send_report_id is 'id của đối tượng trong bảng auto_send_report_config';

comment on column vnpt_dev.send_report_external_email.external_email_id is 'id của đối tượng trong bảng external_email_received_report';


