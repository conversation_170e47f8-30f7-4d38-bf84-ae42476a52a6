drop TABLE if exists vnpt_dev.shared_bss;
CREATE TABLE "vnpt_dev"."shared_bss" (
                                         "id" bigserial NOT NULL,
                                         "sub_code" varchar(255),
                                         "time_update" timestamp(6),
                                         "phone_receipt" varchar(255),
                                         "package_code" varchar(255),
                                         "pay_code" varchar(255),
                                         "traffic_share" int8,
                                         "traffic_type" varchar(255),
                                         "package_name" varchar(255),
                                         "traffic_after" int8,
                                         "traffic_before" int8,
                                         "history" varchar(255),
                                         "status" varchar(255),
                                         "user_id" int8,
                                         "name" varchar(255),
                                         "email" varchar(255),
                                         "type" int4,
                                         "transaction_code" varchar(255) NOT NULL,
                                         PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."sub_code" IS 'Mã thuê bao';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."time_update" IS 'Thời gian update';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."phone_receipt" IS 'Số điện thoại nhận';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."package_code" IS 'Mã gói';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."pay_code" IS 'Mã thanh toán';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."traffic_share" IS 'Lưu lượng chia sẻ';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."traffic_type" IS 'Loại lưu lượng';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."package_name" IS 'Tên gói';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."traffic_after" IS 'Lưu lượng after';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."traffic_before" IS 'Lưu lượng before';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."history" IS 'Lịch sử';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."status" IS 'Trạng thái';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."user_id" IS 'Id của user';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."name" IS 'Tên người được chia sẻ';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."email" IS 'Email người được chia sẻ';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."type" IS '0: %, 1: MB';

COMMENT ON COLUMN "vnpt_dev"."shared_bss"."transaction_code" IS 'Mã giao dịch';