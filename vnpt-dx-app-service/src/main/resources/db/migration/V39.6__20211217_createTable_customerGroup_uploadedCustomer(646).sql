-- Create customer_group table
CREATE TABLE "vnpt_dev"."customer_group" (
  "id" bigserial NOT NULL,
  "name" varchar(100) NOT NULL,
  "description" varchar(500),
  "creator_id" int8 NOT NULL,
  "province_id" int8,
  "active_status" int2 DEFAULT 0,
  "archive_status" int2 DEFAULT 0,
  "has_condition" bool DEFAULT false,
  "has_enterprise_list" bool DEFAULT false,
  "has_contact_list" bool DEFAULT false,
  "has_uploaded_customer_list" bool DEFAULT false,
  "has_ignored_list" bool,
  "condition_json" text,
  "lst_condition_enterprise_id" text,
  "lst_condition_contact_id" text,
  "lst_enterprise_id" text,
  "lst_contact_id" text,
  "lst_uploaded_customer_id" text,
  "lst_ignored_enterprise_id" text,
  "created_at" timestamp,
  "modified_at" timestamp,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."customer_group"."id" IS 'ID nhóm khách hàng';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."name" IS 'Tên nhóm khách hàng';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."description" IS 'Mô tả nhóm khách hàng';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."creator_id" IS 'ID user tạo nhóm khách hàng';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."province_id" IS 'ID tỉnh thành của admin khu vực tạo nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."active_status" IS 'Trạng thái hoạt động của nhóm khách hàng (0: Không hoạt động, 1: Hoạt động)';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."archive_status" IS 'Trạng thái lưu trữ của nhóm khách hàng (0: Không lưu trữ, 1: Lưu trữ)';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."has_condition" IS 'Nhóm khách hàng chứa điều kiện tạo nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."has_enterprise_list" IS 'Nhóm khách hàng chứa danh sách doanh nghiệp';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."has_contact_list" IS 'Nhóm khách hàng chứa danh sách liên hệ';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."has_uploaded_customer_list" IS 'Nhóm khách hàng chứa khách hàng tải lên';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."has_ignored_list" IS 'Nhóm khách hàng chứa danh sách bỏ qua';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."condition_json" IS 'Biểu thức json mô tả điều kiện tạo nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."lst_condition_enterprise_id" IS 'Danh sách ID doanh nghiệp được đồng bộ từ điều kiện tạo nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."lst_condition_contact_id" IS 'Danh sách ID liên hệ được đồng bộ từ điều kiện tạo nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."lst_enterprise_id" IS 'Danh sách ID doanh nghiệp được thêm vào nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."lst_contact_id" IS 'Danh sách ID liên hệ được thêm vào nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."lst_uploaded_customer_id" IS 'Danh sách các khách hàng tải lên được thêm vào nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."lst_ignored_enterprise_id" IS 'Danh sách các khách hàng bị bỏ qua khi thêm vào nhóm';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."created_at" IS 'Thời gian tạo';

COMMENT ON COLUMN "vnpt_dev"."customer_group"."modified_at" IS 'Thời gian cập nhật';




-- Create uploaded_customer table

CREATE TABLE "vnpt_dev"."uploaded_customer" (
  "id" bigserial NOT NULL,
  "name" varchar(255) NOT NULL,
  "email" varchar(255) NOT NULL,
  "phone" varchar(255),
  "created_at" timestamp,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."uploaded_customer"."id" IS 'ID khách hàng tải lên';

COMMENT ON COLUMN "vnpt_dev"."uploaded_customer"."name" IS 'Tên doanh nghiệp';

COMMENT ON COLUMN "vnpt_dev"."uploaded_customer"."email" IS 'Email doanh nghiệp';

COMMENT ON COLUMN "vnpt_dev"."uploaded_customer"."phone" IS 'Số điện thoại doanh nghiệp';

COMMENT ON COLUMN "vnpt_dev"."uploaded_customer"."created_at" IS 'Thời gian tạo';