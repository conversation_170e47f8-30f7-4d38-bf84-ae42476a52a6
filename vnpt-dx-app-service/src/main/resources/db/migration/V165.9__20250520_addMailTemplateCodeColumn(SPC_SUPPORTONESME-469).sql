ALTER TABLE vnpt_dev.mail_send_history
ADD COLUMN IF NOT EXISTS mail_template_code varchar(50) NULL;
COMMENT ON COLUMN vnpt_dev.mail_send_history.mail_template_code IS 'Code của mail_template';

COMMENT ON COLUMN vnpt_dev.mail_send_history.object_type IS '<PERSON>ại đối tượng xem/click quảng cáo (0: <PERSON><PERSON><PERSON> nghi<PERSON>, 1: <PERSON><PERSON><PERSON> hệ, 2: User, 3: User_metadata)';

CREATE OR REPLACE FUNCTION vnpt_dev.func_count_object_by_rule_condition(objecttype integer, rulecondition text)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
    DECLARE
mQuery text;
        mResult
int4;
BEGIN
CASE objecttype
WHEN 0
    THEN mQuery = CONCAT('SELECT COUNT(users.id) FROM vnpt_dev.users WHERE affiliate_type IS NULL and users.deleted_flag = 1 and ', rulecondition);
WHEN 1
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.customer_ticket WHERE customer_ticket.deleted_flag = 1 and ', rulecondition);
WHEN 2
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.enterprise WHERE enterprise.deleted_flag = 1 and ', rulecondition);
WHEN 3
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.customer_contact WHERE customer_contact.deleted_flag = 1 and ', rulecondition);
WHEN 4
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and ', rulecondition);
WHEN 7
    THEN mQuery = CONCAT('SELECT COUNT(id) FROM vnpt_dev.users WHERE affiliate_type IS NOT NULL AND users.deleted_flag = 1 and ', rulecondition);
END
CASE;
        RAISE
NOTICE 'mQuery: %', mQuery;
EXECUTE mQuery INTO mResult;
RETURN mResult;
END
$function$
;

CREATE OR REPLACE FUNCTION vnpt_dev.func_list_object_by_condition_query(conditionquery text, objecttype integer)
 RETURNS TABLE(id bigint)
 LANGUAGE plpgsql
AS $function$
DECLARE
mQuery text;
    sQuery text;
BEGIN
CASE objectType
    WHEN 0 THEN
        mQuery = 'SELECT users.id as id FROM vnpt_dev.users WHERE affiliate_type IS NULL and users.deleted_flag = 1 and ';
WHEN 1 THEN
        mQuery = 'SELECT customer_ticket.id as id FROM vnpt_dev.customer_ticket WHERE customer_ticket.deleted_flag = 1 and ';
WHEN 2 THEN
        mQuery = 'SELECT enterprise.id as id FROM vnpt_dev.enterprise WHERE enterprise.deleted_flag = 1 and ';
WHEN 3 THEN
        mQuery = 'SELECT customer_contact.id as id FROM vnpt_dev.customer_contact WHERE customer_contact.deleted_flag = 1 and ';
WHEN 4 THEN
        mQuery = 'SELECT subscriptions.id as id FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and ';
WHEN 7 THEN
        mQuery = 'SELECT users.id as id FROM vnpt_dev.users WHERE affiliate_type is not null and users.deleted_flag = 1 and ';
END CASE;
    sQuery = conditionQuery;
    mQuery = CONCAT(mQuery, sQuery);
    RAISE NOTICE 'mQuery: %', mQuery;
RETURN QUERY EXECUTE mQuery;
END
$function$
;

CREATE OR REPLACE FUNCTION vnpt_dev.func_list_object_assignee_id_by_condition_query(conditionquery text, objecttype integer)
 RETURNS TABLE(assignee_id bigint)
 LANGUAGE plpgsql
AS $function$

DECLARE

    mQuery text;

    sQuery text;

BEGIN

    CASE objectType

        WHEN 0 THEN

            mQuery = 'SELECT users.assignee_id as assignee_id FROM vnpt_dev.users WHERE affiliate_type is null and users.deleted_flag = 1 and';

        WHEN 1 THEN

            mQuery = 'SELECT customer_ticket.assignee_id as assignee_id FROM vnpt_dev.customer_ticket WHERE customer_ticket.deleted_flag = 1 and ';

        WHEN 2 THEN

            mQuery = 'SELECT enterprise.assignee_id as assignee_id FROM vnpt_dev.enterprise WHERE enterprise.deleted_flag = 1 and ';

        WHEN 3 THEN

            mQuery = 'SELECT customer_contact.assignee_id as assignee_id FROM vnpt_dev.customer_contact WHERE customer_contact.deleted_flag = 1 and ';

				WHEN 4 THEN

						mQuery = 'SELECT subscriptions.assignee_id as assignee_id FROM vnpt_dev.subscriptions WHERE subscriptions.deleted_flag = 1 and ';

				WHEN 7 THEN

				    mQuery = 'SELECT users.assignee_id as assignee_id

								FROM vnpt_dev.users WHERE affiliate_type is not null and users.deleted_flag = 1 and ';

        END CASE;

    sQuery = conditionQuery;

    mQuery = CONCAT(mQuery, sQuery);

    RAISE NOTICE 'mQuery: %', mQuery;

    RETURN QUERY EXECUTE mQuery;

END

$function$
;
