-- Add column sub_using_type in view enterprise user classification
DROP VIEW vnpt_dev.view_enterprise_user_classification;
CREATE OR REPLACE VIEW vnpt_dev.view_enterprise_user_classification AS
WITH last_login AS (
SELECT DISTINCT ON (mhistorylogin.user_id) mhistorylogin.user_id,
    mhistorylogin.created_at
    FROM vnpt_dev.history_login mhistorylogin
    ORDER BY mhistorylogin.user_id, mhistorylogin.created_at DESC
), user_subs AS (
SELECT msubscriptions.user_id,
	count(msubscriptions.id) FILTER (WHERE (msubscriptions.status = 1)) AS num_trial,
	count(msubscriptions.id) FILTER (WHERE (msubscriptions.status = 2)) AS num_active,
    count(msubscriptions.id) FILTER (WHERE ((msubscriptions.status = 1) OR (msubscriptions.status = 2))) AS num_active_or_trial,
    count(msubscriptions.id) FILTER (WHERE ((msubscriptions.status = 3) OR (msubscriptions.status = 4))) AS num_cancel_or_finish
    FROM vnpt_dev.subscriptions msubscriptions
    WHERE ((msubscriptions.deleted_flag = 1) AND (msubscriptions.confirm_status = 1))
GROUP BY msubscriptions.user_id
), user_classification AS (
SELECT musers.id AS user_id,
    musers.tin,
    musers.email,
    musers.phone_number,
    musers.customer_type,
    musers.status,
    musers.created_at,
    vusersubs.num_active_or_trial,
    vusersubs.num_cancel_or_finish,
    (vlastlogin.created_at)::date AS last_login,
    CASE
        WHEN vusersubs.num_trial > 0 THEN 1
        WHEN vusersubs.num_active > 0 THEN 2
    END AS sub_using_type,
    CASE
        WHEN (vusersubs.num_active_or_trial > 0) THEN 0
        WHEN ((vusersubs.num_active_or_trial = 0) AND (vusersubs.num_cancel_or_finish > 0) AND ((vlastlogin.created_at)::date IS NOT NULL) AND ((vlastlogin.created_at)::date < ((now() - '90 days'::interval))::date)) THEN 1
        ELSE 2
    END AS type
    FROM ((vnpt_dev.users musers
    LEFT JOIN user_subs vusersubs ON ((musers.id = vusersubs.user_id)))
    LEFT JOIN last_login vlastlogin ON ((musers.id = vlastlogin.user_id)))
    WHERE (musers.deleted_flag = 1)
)
SELECT menterprise.id AS enterprise_id,
       muserclassify.user_id,
       (COALESCE(muserclassify.num_active_or_trial, (0)::bigint) + COALESCE(muserclassify.num_cancel_or_finish, (0)::bigint)) AS num_subs,
       muserclassify.sub_using_type,
       muserclassify.type,
       muserclassify.status,
       muserclassify.created_at
FROM user_classification muserclassify
         LEFT JOIN vnpt_dev.enterprise menterprise ON (
            (muserclassify.customer_type = menterprise.customer_type OR menterprise.customer_type IS NULL )
        AND (muserclassify.email = menterprise.email OR muserclassify.phone_number = menterprise.phone OR muserclassify.tin = menterprise.tin ))
WHERE menterprise.id IS NOT NULL;