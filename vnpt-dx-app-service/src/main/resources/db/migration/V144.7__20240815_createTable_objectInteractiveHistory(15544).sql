DROP TABLE IF EXISTS "vnpt_dev"."object_interactive_history";
CREATE TABLE "vnpt_dev"."object_interactive_history" (
  "id" bigserial,
  "action_type" int2 NOT NULL,
  "object_type" int2 NOT NULL,
  "object_draft_id" int8,
  "object_id" int8,
  "object_multiplan_id" int8,
  "metadata" text,
  "created_at" timestamp DEFAULT NOW(),
  PRIMARY KEY ("id", "action_type", "object_type")
) PARTITION BY LIST (action_type);
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."action_type" IS 'Loại thao tác (1: VIEW)';
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."object_type" IS 'Loại object được thao tác (0: SERVICE, 1: PRICING, 2: COMBO, 3: COMBO_PLAN)';
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."object_draft_id" IS 'ID draft của object được thao tác';
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."object_id" IS 'ID của object được thao tác';
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."object_multiplan_id" IS 'ID multiplan của object được thao tác';
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."metadata" IS 'Metadata của thao tác';
COMMENT ON COLUMN "vnpt_dev"."object_interactive_history"."created_at" IS 'Thời gian thao tác';
COMMENT ON TABLE "vnpt_dev"."object_interactive_history" IS 'Bảng lưu các thông tin thao tác tới object trên hệ thống';

CREATE TABLE object_view_history PARTITION OF object_interactive_history FOR VALUES IN (1) PARTITION BY LIST (object_type);
CREATE TABLE service_view_history PARTITION OF object_view_history FOR VALUES IN (0);
CREATE TABLE pricing_view_history PARTITION OF object_view_history FOR VALUES IN (1);
CREATE TABLE combo_view_history PARTITION OF object_view_history FOR VALUES IN (2);
CREATE TABLE comboplan_view_history PARTITION OF object_view_history FOR VALUES IN (3);
COMMENT ON TABLE "vnpt_dev"."service_reaction" IS 'Bảng lưu các SPDV đã được yêu thích bởi người dùng (khi unlike sẽ xóa khỏi bảng)';
COMMENT ON COLUMN "vnpt_dev"."services"."product_type" IS 'Loại sản phẩm: DEVICE(1), IOT(2), SAAS(3), PAAS(4), IAAS(5), INTERNET(6), MYTV(7), SOFTWARE(8), TELECOMMUNICATION(9)';


DROP TABLE IF EXISTS "vnpt_dev"."subscriptions_extradata";
CREATE TABLE "vnpt_dev"."subscriptions_extradata" (
  "subscription_id" int8 NOT NULL PRIMARY KEY,
  "sim_metadata" jsonb
);

COMMENT ON COLUMN "vnpt_dev"."subscriptions_extradata"."subscription_id" IS 'ID thuê bao';
COMMENT ON COLUMN "vnpt_dev"."subscriptions_extradata"."sim_metadata" IS 'Thông tin metadata ứng với thuê bao mua sim số';
COMMENT ON TABLE "vnpt_dev"."subscriptions_extradata" IS 'Bảng lưu thông tin bổ  của thuê bao';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."created_source_migration" IS 'Nguồn tạo: 0 - OneSME, 1 - Đồng bộ, 4 - Ban KHCN';
COMMENT ON COLUMN "vnpt_dev"."subscriptions"."used_quantity" IS 'Số lượng user trong doanh nghiệp sử dụng thuê bao';
COMMENT ON COLUMN "vnpt_dev"."billings"."created_source_migration" IS 'Nguồn tạo (0-oneSME; 1-Đồng bộ; 4-Ban KHDN)';