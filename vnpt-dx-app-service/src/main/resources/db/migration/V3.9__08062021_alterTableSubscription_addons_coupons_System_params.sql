alter table vnpt_dev.subscription_addons
add column pricing_id int8;
comment on column vnpt_dev.subscription_addons.pricing_id is 'Id của gói dịch vụ';
alter table vnpt_dev.subscription_coupons
add column pricing_id int8;
comment on column vnpt_dev.subscription_coupons.pricing_id is 'Id của gói dịch vụ';

alter table vnpt_dev.system_params
add column payment_type_on int2,
add column payment_date_fail_on int2,
add column payment_type_auto_on int2,
add column payment_request_days varchar(200),
add column unsubscribe_on int2,
add column payment_date_fail_off int2,
add column unsubscribe_off int2,
add column payment_type_off int2;
comment on column vnpt_dev.system_params.payment_type_on is 'Trạng thánh thanh toán online hay online   (online || offline)';
comment on column vnpt_dev.system_params.payment_date_fail_on is '<PERSON><PERSON><PERSON> số ngày cho phép thanh toán không thành công  (10)';
comment on column vnpt_dev.system_params.payment_type_auto_on is '<PERSON><PERSON><PERSON> yêu cầu thanh toán có tự động hoặc chọn ngày(auto || chọn ngày) 1: auto , -1: ko auto';
comment on column vnpt_dev.system_params.payment_request_days is 'Lưu các ngày gửi yêu cầu thanh toán kiểu (1,3,5,7)';
comment on column vnpt_dev.system_params.unsubscribe_on is 'Có tự động hủy đăng ký khi quá hạn hay không  (có || không) 1: có , -1: không';
comment on column vnpt_dev.system_params.payment_date_fail_off is 'Lưu số ngày cho phép thanh toán không thành công  (10)';
comment on column vnpt_dev.system_params.unsubscribe_off is 'Có tự động hủy đăng ký khi quá hạn hay không  (có || không) 1: có , -1: không';
comment on column vnpt_dev.system_params.payment_type_off is 'Trạng thánh thanh toán offline hay online   (online || offline)';

INSERT INTO vnpt_dev.system_params (param_name,param_type,param_value,payment_type_on,payment_date_fail_on,payment_type_auto_on,payment_request_days,unsubscribe_on,payment_date_fail_off,unsubscribe_off,payment_type_off) VALUES
	 ('Cấu hình hóa đơn','BILLING','1',1,1,0,'1,2,3',0,1,1,0);