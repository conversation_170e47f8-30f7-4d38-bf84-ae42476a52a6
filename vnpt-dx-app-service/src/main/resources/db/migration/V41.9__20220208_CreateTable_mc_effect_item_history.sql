/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:48:42
*/


-- ----------------------------
-- Table structure for mc_effect_item_history
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_effect_item_history";
CREATE TABLE "vnpt_dev"."mc_effect_item_history" (
  "id" bigserial NOT NULL,
  "mc_id" int8,
  "effect_item_id" int8,
  "effect_item_set_id" int8,
  "code" int4,
  "value" int4,
  "user_id" int8,
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "created_by" int2,
  "modified_by" int2,
  "status" int2,
  "deleted_flag" int2,
  "activity_id" int8,
  "rule_id" int8,
  "sub_id" int8
)
;
COMMENT ON COLUMN "vnpt_dev"."mc_effect_item_history"."mc_id" IS 'ID của marketing campaign';
COMMENT ON COLUMN "vnpt_dev"."mc_effect_item_history"."effect_item_id" IS 'Index của effect_item trong json object';
COMMENT ON COLUMN "vnpt_dev"."mc_effect_item_history"."effect_item_set_id" IS 'Index của effect_item_set trong json object';
COMMENT ON COLUMN "vnpt_dev"."mc_effect_item_history"."activity_id" IS 'Index của Activity trong json object';
COMMENT ON COLUMN "vnpt_dev"."mc_effect_item_history"."rule_id" IS 'Index của Rule trong json object';

-- ----------------------------
-- Primary Key structure for table mc_effect_item_history
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_effect_item_history" ADD CONSTRAINT "mc_effect_item_history_pkey" PRIMARY KEY ("id");
