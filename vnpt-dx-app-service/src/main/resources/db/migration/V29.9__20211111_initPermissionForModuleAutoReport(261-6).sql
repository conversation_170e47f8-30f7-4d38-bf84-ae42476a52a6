DELETE FROM vnpt_dev.apis WHERE id IN (
    SELECT vnpt_dev.api_permission.api_id FROM vnpt_dev.api_permission JOIN vnpt_dev.permission ON permission.id = api_permission.permission_id
    WHERE vnpt_dev.permission.code IN ('QUAN_LY_CAU_HINH_GUI_BAO_CAO', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS')
);

DELETE FROM vnpt_dev.api_permission WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CAU_HINH_GUI_BAO_CAO', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS'))
);

DELETE FROM vnpt_dev.roles_permissions WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CAU_HINH_GUI_BAO_CAO', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS'))
);

DELETE FROM vnpt_dev.permission_portal WHERE permission_id IN (
    (SELECT id FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CAU_HINH_GUI_BAO_CAO', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS'))
);

DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_CAU_HINH_GUI_BAO_CAO', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE', 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS');

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Quản lý cấu hình gửi báo cáo tự động',
    'QUAN_LY_CAU_HINH_GUI_BAO_CAO',
    -1,
    11000010
);

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Xem cấu hình gửi báo cáo tự động',
    'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW',
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    11000011
);

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Thiết lập thời gian xuất dữ liệu báo cáo',
    'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME',
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    11000012
);

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Tạo, Cập nhật cấu hình gửi báo cáo tự động',
    'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE',
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    11000013
);

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Xóa cấu hình gửi báo cáo tự động',
    'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE',
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    11000014
);

INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Bật tắt báo cáo tự động',
    'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS',
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    11000015
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

-- Add permission vao role
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
(
    (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 2,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 3,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 4,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 5,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE' ORDER BY id DESC LIMIT 1),
    1
),
(
    (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 6,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CHANGE_STATUS' ORDER BY id DESC LIMIT 1),
    1
);

-- Add vao bang apis, api_permission
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/auto-config-report/{id}',
    'ROLE_AUTO_REPORT_GET_ONE',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_GET_ONE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/auto-config-report/search2',
    'ROLE_AUTO_REPORT_SEARCH',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_SEARCH' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/auto-config-report/create',
    'ROLE_AUTO_REPORT_CREATE',
    'POST'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_CREATE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/auto-config-report/update/{id}',
    'ROLE_AUTO_REPORT_UPDATE',
    'PUT'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_UPDATE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/auto-config-report/{id}',
    'ROLE_AUTO_REPORT_DELETE',
    'DELETE'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_DELETE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_DELETE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/system-param',
    'ROLE_AUTO_REPORT_GET_SYSTEM_PARAM',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_GET_SYSTEM_PARAM' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_VIEW' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/system-param/update',
    'ROLE_AUTO_REPORT_UPDATE_SYSTEM_PARAM',
    'POST'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_UPDATE_SYSTEM_PARAM' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_SET_LIMIT_TIME' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_DEV_CREATE_COUPON_2' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_ADMIN_UPDATE_EMAIL_TEMPLATE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/user-portal/department/auto-report',
    'ROLE_AUTO_REPORT_CREATE_DEPARTMENT_GET',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_CREATE_DEPARTMENT_GET' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/user-portal/department/filters',
    'ROLE_AUTO_REPORT_CREATE_DEPARTMENT_FILTER',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_CREATE_DEPARTMENT_FILTER' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/user-portal/department/auto-report/total',
    'ROLE_AUTO_REPORT_CREATE_DEPARTMENT_TOTAL',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_CREATE_DEPARTMENT_TOTAL' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/admin-portal/email-template/auto-report',
    'ROLE_AUTO_REPORT_CREATE_GET_EMAIL_TEMPLATE',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_CREATE_GET_EMAIL_TEMPLATE' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.apis (id, api_path, api_code, method) VALUES
(
    (SELECT id FROM vnpt_dev.apis ORDER BY id DESC LIMIT 1) + 1,
    '/api/portal/generate/key',
    'ROLE_AUTO_REPORT_CREATE_GENERATE_KEY',
    'GET'
 );
INSERT INTO vnpt_dev.api_permission (id, api_id, permission_id) VALUES
(
    (SELECT id FROM vnpt_dev.api_permission ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.apis WHERE api_code = 'ROLE_AUTO_REPORT_CREATE_GENERATE_KEY' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_GUI_BAO_CAO_CREATE' ORDER BY id DESC LIMIT 1)
);

-- Permission xem lịch sử gửi email
INSERT INTO vnpt_dev.permission(id, name, code, parent_id, priority) VALUES
(
    (SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1,
    'Lịch sử gửi email',
    'QUAN_LY_CAU_HINH_1_VIEW_HISTORY_SEND_MAIL',
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_1' ORDER BY id DESC LIMIT 1),
    9999900
);

INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
(
    (SELECT id FROM vnpt_dev.permission_portal ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_1_VIEW_HISTORY_SEND_MAIL' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.portal WHERE name = 'ADMIN' ORDER BY id DESC LIMIT 1)
);

INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT id FROM vnpt_dev.roles_permissions ORDER BY id DESC LIMIT 1) + 1,
    (SELECT id FROM vnpt_dev.role WHERE name = 'FULL_ADMIN' ORDER BY id DESC LIMIT 1),
    (SELECT id FROM vnpt_dev.permission WHERE code = 'QUAN_LY_CAU_HINH_1_VIEW_HISTORY_SEND_MAIL' ORDER BY id DESC LIMIT 1),
    1
);

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;