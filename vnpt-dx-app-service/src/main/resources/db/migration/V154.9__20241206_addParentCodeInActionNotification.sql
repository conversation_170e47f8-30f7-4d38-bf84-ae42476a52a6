
CREATE INDEX IF NOT EXISTS "index_notification_action_id" ON vnpt_dev.notifications USING btree (action_id DESC NULLS LAST);
CREATE INDEX IF NOT EXISTS "index_notification_user_id" ON vnpt_dev.notifications USING btree (user_id DESC NULLS LAST);
CREATE INDEX IF NOT EXISTS "index_action_notification_parent_id" ON vnpt_dev.action_notification USING btree (parent_id DESC NULLS LAST);

-- add PV action notification
DELETE FROM "vnpt_dev"."action_notification" WHERE name IN ('Quản lý phân vùng');
INSERT INTO "vnpt_dev"."action_notification"("action_code", "name", "is_send_email", "is_send_sms", "is_notification", "parent_id", "created_by", "created_at", "modified_by", "modified_at", "receiver", "allow_change_email", "allow_change_sms", "allow_change_notification", "priority_order", "is_send_telegram", "allow_change_telegram") VALUES
    ('PV', 'Quản lý phân vùng', 1, 0, 1, -1, 'system', '2024-12-05 00:00:00', NULL, NULL, null, 'B', 'B', 'B', (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template), 0, 'D');
UPDATE vnpt_dev.action_notification SET parent_id = (SELECT id FROM vnpt_dev.action_notification WHERE action_code = 'PV' and parent_id = -1) WHERE action_code ILIKE ('PVD-%');

UPDATE vnpt_dev.action_notification
SET action_code = 'AC'
WHERE name = 'Quản lý tạo tài khoản và phân quyền';

UPDATE vnpt_dev.action_notification
SET action_code = 'TK'
WHERE name = 'Quản lý phiếu hỗ trợ';

UPDATE vnpt_dev.action_notification
SET action_code = 'EV'
WHERE name = 'Đánh giá dịch vụ';

UPDATE vnpt_dev.action_notification
SET action_code = 'HP'
WHERE name = 'SME Home Page';

UPDATE vnpt_dev.action_notification
SET action_code = 'CP'
WHERE name = 'Quản lý chương trình khuyến mại';

UPDATE vnpt_dev.action_notification
SET action_code = 'CB'
WHERE name = 'Quản lý combo dịch vụ';

UPDATE vnpt_dev.action_notification
SET action_code = 'SCP'
WHERE name = 'Quản lý gói combo dịch vụ';

UPDATE vnpt_dev.action_notification
SET action_code = 'PC'
WHERE name = 'Quản lý gói dịch vụ';

UPDATE vnpt_dev.action_notification
SET action_code = 'SV'
WHERE name = 'Quản lý dịch vụ';

UPDATE vnpt_dev.action_notification
SET action_code = 'SC'
WHERE name = 'Đăng ký subscription';

UPDATE vnpt_dev.action_notification
SET action_code = 'IV'
WHERE name = 'Quản lý hóa đơn';

UPDATE vnpt_dev.action_notification
SET action_code = 'EXP'
WHERE name = 'Báo cáo tự động';

UPDATE vnpt_dev.action_notification
SET action_code = 'TB'
WHERE name = 'Quản lý Topic';

--- xử lý action_id bị null trong bảng notifications
update vnpt_dev.notifications
set action_id = (select id from vnpt_dev.action_notification where action_code = 'PGC-01')
where notifications.action_id is null and screen_id = 'PGC-01';

update vnpt_dev.notifications
set action_id = (select id from vnpt_dev.action_notification where action_code = 'PGC-02')
where notifications.action_id is null and screen_id = 'PGC-02';

update vnpt_dev.notifications
set action_id = (select id from vnpt_dev.action_notification where action_code = 'PGC-03')
where notifications.action_id is null and screen_id = 'PGC-03';

ALTER TABLE vnpt_dev.notifications ALTER COLUMN action_id SET NOT NULL;