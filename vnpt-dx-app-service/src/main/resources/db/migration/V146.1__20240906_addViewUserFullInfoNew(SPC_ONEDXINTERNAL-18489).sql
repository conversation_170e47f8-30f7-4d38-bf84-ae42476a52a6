-- vnpt_dev.report_view_user_full_info source
drop view if exists report_view_user_full_info_new;
CREATE OR REPLACE VIEW vnpt_dev.report_view_user_full_info_new
AS SELECT dt.name AS district_name,
          na.name AS nation_name,
          wa.name AS ward_name,
          sd.name AS street_name,
          p.name AS province_name,
          ba.name AS business_area_name,
          bs.name AS business_area_size_name,
          dp.department_name,
          u.id,
          u.created_at,
          u.created_by,
          u.deleted_flag,
          u.modified_at,
          u.modified_by,
          u.status,
          u.account_expired,
          u.account_locked,
          u.activation_key,
          u.address,
          u.avatar,
          u.birthday,
          u.company,
          u.tin,
          u.cover_image,
          u.credentials_expired,
          u.email,
          u.enabled,
          u.first_name,
          u.gender,
          u.last_name,
          u.password,
          u.password_tmp,
          u.phone_number,
          u.user_name,
          u.website,
          u.district_id,
          u.nation_id,
          u.province_id,
          u.business_area_id,
          u.business_size_id,
          u.parent_id,
          u.user_code,
          u.business_email,
          u.name,
          u.customer_type,
          u.description,
          u.department_id,
          u.sme_user_id,
          u.tech_id,
          u.create_type,
          u.customer_code,
          u.province_code,
          u.ward_id,
          u.street_id,
          u.social_insurance_number,
          u.rep_fullname,
          u.rep_gender,
          u.rep_title,
          u.rep_birthday,
          u.rep_nation_id,
          u.rep_folk_id,
          u.rep_personal_cert_type_id,
          u.rep_personal_cert_number,
          u.rep_personal_cert_date,
          u.rep_personal_cert_place::character varying(50) AS rep_personal_cert_place,
    u.rep_registered_place,
    u.rep_address,
    u.parent_status,
    u.employee_code
   FROM vnpt_dev.users u
              LEFT JOIN vnpt_dev.district dt ON u.district_id = dt.id AND u.province_code::text = dt.province_code::text AND u.province_id = dt.province_id
              LEFT JOIN vnpt_dev.nation na ON u.nation_id = na.id
              LEFT JOIN vnpt_dev.ward wa ON u.ward_id = wa.id AND u.district_id = wa.district_id AND u.province_code::text = wa.province_code::text
              LEFT JOIN vnpt_dev.street_detail sd ON u.street_id = sd.id AND u.province_code::text = sd.province_code::text
              LEFT JOIN vnpt_dev.business_area ba ON u.business_area_id = ba.id
              LEFT JOIN vnpt_dev.business_size bs ON u.business_size_id = bs.id
              LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
              LEFT JOIN vnpt_dev.departments dp ON u.department_id = dp.id;