CREATE TABLE "vnpt_dev"."exception_history" (
  "id" bigserial,
  "service" varchar(50) NOT NULL,
  "created_at" timestamp NOT NULL DEFAULT now(),
  "join_point" text NOT NULL,
  "stack_trace" text NOT NULL,
  PRIMARY KEY ("id")
);

COMMENT ON COLUMN "vnpt_dev"."exception_history"."service" IS 'Tên service phát sinh exception';
COMMENT ON COLUMN "vnpt_dev"."exception_history"."created_at" IS 'Thời gian phát sinh exception';
COMMENT ON COLUMN "vnpt_dev"."exception_history"."join_point" IS 'Vị trí phát sinh exception';
COMMENT ON COLUMN "vnpt_dev"."exception_history"."stack_trace" IS 'Stacktrace của exception';
COMMENT ON TABLE "vnpt_dev"."exception_history" IS 'Bảng lưu lịch sử exception của hệ thống';