/*
 Navicat Premium Data Transfer

 Source Server         : ************_Test
 Source Server Type    : PostgreSQL
 Source Server Version : 120005
 Source Host           : ************:5465
 Source Catalog        : vnpt_dx
 Source Schema         : vnpt_dev

 Target Server Type    : PostgreSQL
 Target Server Version : 120005
 File Encoding         : 65001

 Date: 27/01/2022 14:49:00
*/


-- ----------------------------
-- Table structure for mc_effect_item_relation
-- ----------------------------
DROP TABLE IF EXISTS "vnpt_dev"."mc_effect_item_relation";
CREATE TABLE "vnpt_dev"."mc_effect_item_relation" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
),
  "mc_id" int8,
  "activity_id" int8,
  "effect_item_set_id" int8,
  "effect_item_id" int8,
  "coupon_list" text COLLATE "pg_catalog"."default",
  "mc_list" text COLLATE "pg_catalog"."default",
  "status" int2,
  "created_at" timestamp(6),
  "modified_at" timestamp(6),
  "modified_by" int2,
  "created_by" int2,
  "deleted_flag" int2,
  "activity_rule_id" int8
)
;

-- ----------------------------
-- Primary Key structure for table mc_effect_item_relation
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_effect_item_relation" ADD CONSTRAINT "mc_effect_item_relation_pkey" PRIMARY KEY ("id");
