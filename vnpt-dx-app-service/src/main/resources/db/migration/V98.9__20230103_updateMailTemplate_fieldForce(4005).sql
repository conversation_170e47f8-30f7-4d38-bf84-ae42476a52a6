--==================== set email_type = null cho các template dùng chung ==============
UPDATE vnpt_dev.mail_template
SET email_type = null
WHERE code IN ('TKD-10', 'TKD-12', 'TKD-15', 'TKD-16', 'TKD-17', 'TKD-19', 'TKD-21', 'TKD-23');

--=================== Update priority order của phiếu hỗ trợ ==============
UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-10');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-11');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TICC-11');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-12');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-13');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TICC-13');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-14');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TICC-14');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-15');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-16');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-17');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-19');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TICC-19');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-20');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-21');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-22');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TICC-20');

UPDATE vnpt_dev.mail_template
SET priority_order = (SELECT max(priority_order) + 1 FROM vnpt_dev.mail_template)
WHERE code IN ('TKD-23');

--===================  Edit email_type của Quản lý phân giao ================
UPDATE vnpt_dev.mail_template
SET email_type = 0
WHERE code LIKE 'PGD-%';

UPDATE vnpt_dev.mail_template
SET email_type = 1
WHERE code LIKE 'PGC-%';

--=================== Edit action_notification parent_id của Phiếu hỗ trợ ===================
UPDATE vnpt_dev.action_notification
SET parent_id = (SELECT id FROM vnpt_dev.action_notification WHERE name = 'Quản lý phiếu hỗ trợ' LIMIT 1)
WHERE action_code LIKE 'TKD-%' OR action_code LIKE 'TICC-%';

--=================== Edit tiêu đề của TKD-02 ==============
UPDATE vnpt_dev.mail_template
SET content_html =
'<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body
    style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #FFFFFF;">
  <div class="logo-container"
       style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
    $HEADER
  </div>
  <div class="content-container" style="padding: 40px;">
    <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
      <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_tk.png" alt="Phiếu hỗ trợ">
      <p class="main-title"
         style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Phiếu hỗ trợ mới</p>
    </div>
    <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
      <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$STAFF_NAME</span>,</p>
      <p style="margin: 0;">$USER vừa tạo thành công phiếu hỗ trợ</p>
      <p style="margin: 0;">Tên khách hàng: <span>$USER</span></p>
      <p style="margin: 0;">Tiêu đề: <span>$TITLE_TICKET</span></p>
      <p style="margin: 0;">Loại phiếu hỗ trợ: <span>$SUPPORT_TYPE</span></p>
      <p style="margin: 0;">Nội dung:</p>
      <p style="margin: 0;">$CONTENT_TICKET</p>
      <p style="margin: 0;">Thời gian tạo phiếu: <span>$DATE_TICKET</span></p>
      <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
      <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
    </div>
  </div>
  <div class="footer-container" style="padding: 40px;">
    $FOOTER
  </div>
</div>
</body>
</html>',
content_html_default =
'<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body
    style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #FFFFFF;">
  <div class="logo-container"
       style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
    $HEADER
  </div>
  <div class="content-container" style="padding: 40px;">
    <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
      <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_tk.png" alt="Phiếu hỗ trợ">
      <p class="main-title"
         style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Phiếu hỗ trợ mới</p>
    </div>
    <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
      <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$STAFF_NAME</span>,</p>
      <p style="margin: 0;">$USER vừa tạo thành công phiếu hỗ trợ</p>
      <p style="margin: 0;">Tên khách hàng: <span>$USER</span></p>
      <p style="margin: 0;">Tiêu đề: <span>$TITLE_TICKET</span></p>
      <p style="margin: 0;">Loại phiếu hỗ trợ: <span>$SUPPORT_TYPE</span></p>
      <p style="margin: 0;">Nội dung:</p>
      <p style="margin: 0;">$CONTENT_TICKET</p>
      <p style="margin: 0;">Thời gian tạo phiếu: <span>$DATE_TICKET</span></p>
      <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
      <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
    </div>
  </div>
  <div class="footer-container" style="padding: 40px;">
    $FOOTER
  </div>
</div>
</body>
</html>',
title = 'Phiếu hỗ trợ mới',
title_default = 'Phiếu hỗ trợ mới'
WHERE code = 'TKD-02';

--=================== Edit tiêu đề của TICC-02 ==============
UPDATE vnpt_dev.mail_template
SET content_html =
'<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body
    style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #FFFFFF;">
  <div class="logo-container"
       style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
    $HEADER
  </div>
  <div class="content-container" style="padding: 40px;">
    <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
      <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_tk.png" alt="Phiếu hỗ trợ">
      <p class="main-title"
         style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Phiếu hỗ trợ mới</p>
    </div>
    <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
      <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$STAFF_NAME</span>,</p>
      <p style="margin: 0;">$USER vừa tạo thành công phiếu hỗ trợ</p>
      <p style="margin: 0;">Tên khách hàng: <span>$USER</span></p>
      <p style="margin: 0;">Tiêu đề: <span>$TITLE_TICKET</span></p>
      <p style="margin: 0;">Loại phiếu hỗ trợ: <span>$SUPPORT_TYPE</span></p>
      <p style="margin: 0;">Nội dung:</p>
      <p style="margin: 0;">$CONTENT_TICKET</p>
      <p style="margin: 0;">Thời gian tạo phiếu: <span>$DATE_TICKET</span></p>
      <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
      <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
    </div>
  </div>
  <div class="footer-container" style="padding: 40px;">
    $FOOTER
  </div>
</div>
</body>
</html>',
content_html_default =
'<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body
    style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
<div class="container" style="background-color: #FFFFFF;">
  <div class="logo-container"
       style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
    $HEADER
  </div>
  <div class="content-container" style="padding: 40px;">
    <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
      <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_tk.png" alt="Phiếu hỗ trợ">
      <p class="main-title"
         style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Phiếu hỗ trợ mới</p>
    </div>
    <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
      <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span>$STAFF_NAME</span>,</p>
      <p style="margin: 0;">$USER vừa tạo thành công phiếu hỗ trợ</p>
      <p style="margin: 0;">Tên khách hàng: <span>$USER</span></p>
      <p style="margin: 0;">Tiêu đề: <span>$TITLE_TICKET</span></p>
      <p style="margin: 0;">Loại phiếu hỗ trợ: <span>$SUPPORT_TYPE</span></p>
      <p style="margin: 0;">Nội dung:</p>
      <p style="margin: 0;">$CONTENT_TICKET</p>
      <p style="margin: 0;">Thời gian tạo phiếu: <span>$DATE_TICKET</span></p>
      <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
      <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
    </div>
  </div>
  <div class="footer-container" style="padding: 40px;">
    $FOOTER
  </div>
</div>
</body>
</html>',
title = 'Phiếu hỗ trợ mới',
title_default = 'Phiếu hỗ trợ mới'
WHERE code = 'TICC-02';