-- C<PERSON><PERSON> nhật dữ liệu customer_contact
update vnpt_dev.customer_contact set contact_province_id = id_new
from vnpt_dev.province
where contact_province_id_old = province.id and 
    contact_province_id is null;

update vnpt_dev.customer_contact
    set ward_code = wards.code
from vnpt_dev.wards
where
    ward_code is null and
    customer_contact.province_id = wards.province_id and
    ward_id = wards.id;

update vnpt_dev.customer_contact
    set personal_ward_code = wards.code
from vnpt_dev.wards
where
    personal_ward_code is null and
    personal_province_id = wards.province_id and
    personal_ward_id = wards.id;


-- Cập nhật dữ liệu enterprise
DO $$
DECLARE
    v_batch_size INTEGER := 100000; -- số bản ghi mỗi batch
    v_min_id BIGINT := 0;
    v_max_id BIGINT;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
BEGIN
    -- Tìm ID lớn nhất để làm mốc
    SELECT MAX(id) INTO v_max_id FROM vnpt_dev.enterprise;

    WHILE v_min_id <= v_max_id LOOP
        v_start_time := clock_timestamp();

        update vnpt_dev.enterprise set contact_province_id = id_new
        from vnpt_dev.province
        where contact_province_id_old = province.id and
            contact_province_id is distinct from id_new and
            enterprise.id >= v_min_id and enterprise.id < v_min_id + v_batch_size;

        update vnpt_dev.enterprise
            set ward_code = wards.code
        from vnpt_dev.wards
        where
            ward_code is distinct from wards.code and
            enterprise.province_id = wards.province_id and
            ward_id = wards.id and
            enterprise.id >= v_min_id and enterprise.id < v_min_id + v_batch_size;
                v_min_id := v_min_id + v_batch_size;

        v_end_time := clock_timestamp();
        RAISE NOTICE 'Updated enterprise by batch from ID % to %, duration: % seconds',
                     v_min_id, v_min_id + v_batch_size - 1,
                     EXTRACT(EPOCH FROM (v_end_time - v_start_time));
    END LOOP;
END$$;

