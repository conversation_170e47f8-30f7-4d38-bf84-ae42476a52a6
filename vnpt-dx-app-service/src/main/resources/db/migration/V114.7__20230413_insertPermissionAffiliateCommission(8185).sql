--Thêm permission cho commission affiliate
insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'<PERSON>uản lý hoa hồng affiliate',
	'QUAN_LY_HOA_HONG_AFFILIATE',
	-1,
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'QUAN_LY_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Xem danh sách hoa hồng affiliate',
	'XEM_DANH_SACH_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Xem chi tiết hoa hồng affiliate',
	'XEM_CHI_TIET_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Tạo hoa hồng affiliate',
	'TAO_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'TAO_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Sửa hoa hồng affiliate',
	'SUA_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Xoá hoa hồng affiliate',
	'XOA_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'XOA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission(id,	name, code, parent_id, 	priority)
select 
	(select max(id) from vnpt_dev.permission) + 1,
	'Đổi trạng thái hoa hồng affiliate',
	'DOI_TRANG_THAI_HOA_HONG_AFFILIATE',
	(select id from vnpt_dev.permission where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	(select priority from vnpt_dev.permission order by priority desc limit 1) + 1
where not exists (
	select 1 from vnpt_dev.permission
	where
		code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
);


--Thêm permission portal tương ứng với permission commission affiliate
insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'QUAN_LY_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'TAO_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'TAO_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'SUA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'XOA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'XOA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.permission_portal(id, permission_id, portal_id)
select
	(select max(id) from vnpt_dev.permission_portal) + 1,
	(select id from vnpt_dev.permission	where code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev."permission" per join vnpt_dev.permission_portal per_portal
		on per.id = per_portal.permission_id and per.code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
);

--Thêm role permission tương ứng với permission commission affiliate
--ROLE_FULL_ADMIN
insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'QUAN_LY_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'TAO_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'TAO_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'SUA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'XOA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'XOA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'FULL_ADMIN'),
	(select id from vnpt_dev.permission	where code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'FULL_ADMIN' and per.code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
);


--ROLE_AFFILIATE_DAILY
insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'QUAN_LY_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'QUAN_LY_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'TAO_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'TAO_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'SUA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'XOA_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'XOA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.roles_permissions(role_id, permission_id, allow_edit)
select
	(select id from vnpt_dev."role" where name = 'ROLE_AFFILIATE_DAILY'),
	(select id from vnpt_dev.permission	where code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'),
	1
where not exists (
	select 1
	from vnpt_dev.roles_permissions role_per join vnpt_dev."role" 
		   on role_per.role_id = vnpt_dev."role".id 
		join vnpt_dev."permission" per on role_per.permission_id = per.id 
	where vnpt_dev."role"."name" = 'ROLE_AFFILIATE_DAILY' and per.code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
);




--Thêm các api hoa hồng affiliate
insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/get-page',
	'XEM_DANH_SACH_HOA_HONG_AFFILIATE',
	'POST'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/detail/{affiliateCommissionId}',
	'XEM_CHI_TIET_HOA_HONG_AFFILIATE',
	'GET'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/change-status',
	'DOI_TRANG_THAI_HOA_HONG_AFFILIATE',
	'GET'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/create',
	'TAO_HOA_HONG_AFFILIATE',
	'POST'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'TAO_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/update',
	'SUA_HOA_HONG_AFFILIATE',
	'PUT'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.apis(id, api_path, api_code, "method")
select
	(select max(id) from vnpt_dev.apis) + 1,
	'/api/admin-portal/affiliate-commission/delete/{affiliateCommissionId}',
	'XOA_HOA_HONG_AFFILIATE',
	'DELETE'
where not exists(
	select 1 from vnpt_dev.apis 
	where api_code = 'XOA_HOA_HONG_AFFILIATE'
);


--Thêm các api permission
insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'XEM_DANH_SACH_HOA_HONG_AFFILIATE'
);


insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'XEM_CHI_TIET_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'DOI_TRANG_THAI_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'TAO_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'TAO_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'TAO_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'SUA_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'SUA_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'SUA_HOA_HONG_AFFILIATE'
);

insert into vnpt_dev.api_permission(id, api_id, permission_portal_id, map_permission_portal, delete_flag)
select
	(select max(id) from vnpt_dev.api_permission) + 1,
	(select id from vnpt_dev.apis where api_code = 'XOA_HOA_HONG_AFFILIATE'),
	(
		select per_portal.id
		from vnpt_dev.permission_portal per_portal join vnpt_dev."permission" per
			on per_portal.permission_id = per.id 
		where per.code = 'XOA_HOA_HONG_AFFILIATE'
	),
	1,
	1
where not exists(
	select 1 from vnpt_dev.api_permission api_per join vnpt_dev.apis api 
		on api_per.api_id = api.id 
	where api.api_code = 'XOA_HOA_HONG_AFFILIATE'
);

refresh materialized view concurrently vnpt_dev.role_permission_api;

