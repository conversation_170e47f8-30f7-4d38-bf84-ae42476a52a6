notify:
  rating:
    service:
      new: "Đánh giá mới|%s đã đánh giá cho dịch vụ %s của bạn."
      update: "Cập nhật đánh giá|%s đã sửa đánh giá cho dịch vụ %s của bạn."
      response:
        reply: "Phản hồi đánh giá|%sNhà cung cấp đã gửi trả lời cho đánh giá của bạn đối với dịch vụ %s."
        update: "Cập nhật phản hồi|%sNhà cung cấp đã chỉnh sửa câu trả lời cho đánh giá của bạn đối với dịch vụ %s."
    comment:
      update: "Cập nhật nhận xét|%sQuản trị viên đã xóa nhận xét của bạn cho dịch vụ %s."

mail:
  rating:
    service:
      new: "<PERSON><PERSON>h gi<PERSON> mới|Xin chào %s,<br><br>Bạn vừa nhận đư<PERSON><PERSON> một đánh giá mới từ %s, %s<br>%s<br>Điểm đánh giá<br>%s<br>%s<br><br>Trân trọng,<br>Đội ngũ phát triển VNPT DX Platform"
      update: "Đánh giá mới|Xin chào %s,<br><br>Bạn vừa nhận được một đánh giá mới từ %s,%s <br>%s<br><br>Điểm đánh giá<br>%s<br>%s<br><p>Trân trọng,<br>Đội ngũ phát triển VNPT DX Platform</p>"
      response:
        reply: "Cập nhật đánh giá|Xin chào %s,<br><br>%s vừa trả lời cho nhận xét của bạn<br>%s<br>
        <div style=\"border: 3px dotted black; padding: 5px 0;margin:5px 0;\">
          %s<br>
          <div style=\"margin-left:30px;\">
            %s
          </div>
        </div>
        <br><p>Trân trọng,<br> Đội ngũ phát triển VNPT DX Platform</p>"
        update: "Cập nhật đánh giá|Xin chào %s,<br><br>%s vừa sửa trả lời cho nhận xét của bạn %s
        <div style=\" padding: 5px 0;margin:5px 0;border-style: dotted;\">
        %s
          <div style=\"margin-left:30px!important;\">
            <del>%s</del><br>%s
          </div>
        </div>
        <p>Trân trọng,<br> Đội ngũ phát triển VNPT DX Platform</p>"
    comment:
      update: "Cập nhật đánh giá|Xin chào %s,<br><br>Quản trị viên vừa xóa nhận xét của bạn<br>%s<br>
      <div style=\" padding: 5px 0;margin:5px 0;border-style: dotted;\">
        %s
      </div>
      <br><p>Trân trọng,<br> Đội ngũ phát triển VNPT DX Platform</p>"

  EV04: "Cập nhật đánh giá|Xin chào %s,<br><br>%s vừa cập nhật đánh giá cho dịch vụ<br>%s<br><br>Điểm đánh giá<br>%s<br>%s<br><p>Trân trọng,<br>Đội ngũ phát triển VNPT DX Platform</p>"
  EV03: "Cập nhật đánh giá|Xin chào %s,<br><br>Bạn vừa tạo sửa đánh giá cho dịch vụ<br>%s<br><br>Điểm đánh giá<br>%s<br>%s<br><p>Trân trọng,<br>Đội ngũ phát triển VNPT DX Platform</p>"
  EV01: "Đánh giá mới|Xin chào %s,<br><br>Bạn vừa tạo 1 đánh giá cho dịch vụ<br>%s<br>Điểm đánh giá<br>%s%s<br><br>Trân trọng,<br>Đội ngũ phát triển VNPT DX Platform"
  EV06: "Cập nhật đánh giá|Xin chào %s,<br><br>%s vừa trả lời cho một nhận xét<br>%s<br>
  <div style=\"border: 3px dotted black; padding: 5px 0;margin:5px 0;\">
    %s<br>
    <div style=\"margin-left:30px;\">
      %s
    </div>
  </div>
  <br><p>Trân trọng,<br> Đội ngũ phát triển VNPT DX Platform</p>"
  EV08: "Cập nhật đánh giá|Xin chào %s,<br><br>%s vừa sửa trả lời cho một nhận xét<br>%s<br>
  <div style=\"border: 3px dotted black; padding: 5px 0;margin:5px 0;\">
    %s<br>
    <div style=\"margin-left:30px;\">
      <del>%s</del><br>%s
    </div>
  </div>
  <br><p>Trân trọng,<br> Đội ngũ phát triển VNPT DX Platform</p>"

SB01:
  title: "Thuê bao sắp hết hạn <br>"
  email: "
  <div style=\"border: 3px dotted black; padding: 5px 0;margin:5px 0;\">
    Dịch vụ %s - %s %ssẽ hết hạn sau % ngày.
    Quý khách vui lòng gia hạn để tiếp tục sử dụng dịch vụ.<br>
  </div>
  Truy cập <Link> để gia hạn dịch vụ."
  firebase: "Thuê bao sắp hết hạn <br>
  %s - %s sẽ hết hạn sau %s ngày."
SB02:
  title: "Thuê bao sắp hết hạn <br>"
  email: "<div style=\"border: 3px dotted black; padding: 5px 0;margin:5px 0;\">
    Dịch vụ %s - %s đang được sử dụng bởi khách hàng %s
    sẽ hết hạn sau %s ngày. Quý khách vui lòng gia hạn để tiếp tục sử dụng dịch vụ.<br>
  </div>
  Truy cập <Link> để gia hạn dịch vụ."
  firebase: "Thuê bao sắp hết hạn <br><br>
  %s - %s sẽ hết hạn sau %s ngày."
AO01:
  notify:
    content: "Dịch vụ bổ sung %s đã được gửi tới quản trị viên chờ phê duyệt."
    title: "Dịch vụ bổ sung chờ phê duyệt."
AO02:
  notify:
    content: "Dịch vụ bổ sung %s  từ %s đang  chờ phê duyệt."
    title: "Dịch vụ bổ sung chờ phê duyệt."
AO03:
  notify:
    content: "Dịch vụ bổ sung %s đã được phê duyệt bởi quản trị viên."
    title: "Dịch vụ bổ sung được phê duyệt"
AO04:
  notify:
    content: "Dịch vụ bổ sung %s đã bị từ chối bởi quản trị viên."
    title: "Dịch vụ bổ sung bị từ chối"

AO05:
  notify:
    content: "Dịch vụ bổ sung %s được yêu cầu cập nhật bởi quản trị viên
               Truy cập email để xem chi tiết."
    title: "Dịch vụ bổ sung được yêu cầu cập nhật"

SV01:
  notify:
    content: "Dịch vụ %s đã được gửi tới quản trị viên chờ phê duyệt."
    title: "Dịch vụ chờ phê duyệt"

SV02:
  notify:
    content: "Dịch vụ %s từ %s đang chờ phê duyệt"
    title: "Dịch vụ chờ phê duyệt"

SV03:
  notify:
    content: "Dịch vụ %s của bạn đã được phê duyệt bởi quản trị viên"
    title: "Dịch vụ được phê duyệt"

SV04:
  notify:
    content: "Dịch vụ %s của bạn đã bị từ chối bởi quản trị viên"
    title: "Dịch vụ bị từ chối"
CB01:
  notify:
    content: "Combo dịch vụ %s đã được gửi tới quản trị viên chờ phê duyệt."
    title: "Combo dịch vụ chờ phê duyệt"
CB02:
  notify:
    content: "Combo dịch vụ %s từ %s đang chờ phê duyệt."
    title: "Combo dịch vụ chờ phê duyệt"
CB03:
  notify:
    content: "Combo dịch vụ %s đã được phê duyệt bởi quản trị viên."
    title: "Combo dịch vụ được phê duyệt"
CB04:
  notify:
    content: "Combo dịch vụ %s đã bị từ chối bởi quản trị viên."
    title: "Combo dịch vụ bị từ chối"
MIG02:
  notify:
    content: "%s đã được xử lý xong!"
    title: "Đã xử lý tệp đồng bộ"
Invoice: "Điều chỉnh giảm giá trị gói cước cũ do chưa sử dụng hết thời gian gói cước cũ"

TP01:
  notify:
    content: "Topic %s đã được gửi tới quản trị viên chờ phê duyệt."
    title: "Topic chờ phê duyệt"

TP02:
  notify:
    content: "Topic %s từ %s đang chờ phê duyệt"
    title: "Topic chờ phê duyệt"

TP03:
  notify:
    content: "Topic %s của bạn đã được phê duyệt bởi quản trị viên"
    title: "Topic được phê duyệt"

TP04:
  notify:
    content: "Topic %s của bạn đã bị từ chối bởi quản trị viên"
    title: "Topic bị từ chối"

#white-list yêu cầu phân quyền theo portal nhận giá trị theo enum PortalType
#ADMIN(1), DEV(2), SME(3), UNSET(-1)-Tất cả k yêu cầu token, ALL(-2)-Tất cả yêu cầu token
white-list:
  apis:
    - /api/portal/manufacturer|1,2
    - /api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-change-email-account|1,2
    - /api/portal/subscription/update/status/bos|-2
    - /api/portal/subscription/order-item-status|1,2
    - /api/portal/subscription/update/status/bill/bos|1,2
    - /api/admin-portal/affiliate/member/convert|1,2
    - /api/admin-portal/bill-affiliate-commission/get-popup|1,2
    - /api/admin-portal/bill-affiliate-commission/get-combobox|1,2
    - /api/admin-portal/bill-affiliate-commission/update/commission-config-min|1,2
    - /api/admin-portal/bill-affiliate-commission/get/commission-config-min|1,2
    - /api/admin-portal/affiliate-commission/get-combobox/pricing-and-combo-plan|-1
    - /api/admin-portal/affiliate-commission/check-user-condition-role|1,2
    - /api/admin-portal/affiliate/member/check-referral|-1
    - /api/admin-portal/affiliate/member/change-status|1,2
    - /api/admin-portal/affiliate/member/update-profile|-1
    - /api/admin-portal/affiliate/member/resend-info|1,2
    - /api/admin-portal/affiliate/member/register|-1
    - /api/admin-portal/affiliate-link/get-commission-success|-1
    - /api/admin-portal/affiliate-link/check-config-edit-affiliate-link|-1
    - /api/admin-portal/affiliate-link/get-lst-link-assignee|1,2
    - /api/admin-portal/affiliate-link/create-shorten-link|1,2
    - /api/admin-portal/affiliate-commission/check-config-edit-commision-link|-1
    - api/admin-portal/affiliate/member/test-commission|-1
    - /api/portal/attributes/overview/get-attributes-code|-2
    - /api/portal/attributes/overview/|-1
    - /api/portal/variant/price|-1
    - /api/portal/variant/list/{serviceId}|-1
    - /api/portal/variant/all/{serviceId}|-1
    - /api/portal/variant/auto/{serviceId}|-1
    - /api/portal/variant/file-attach|-1
    - /api/portal/variant/{serviceId}/detail/{variantId}|2
    - /api/portal/variant/code|-1
    - /api/portal/variant/auto|-1
    - /api/portal/variant/list-code|-1
    - /api/portal/variant/system-param|-1
    - /api/portal/variant/detail|-1
    - /api/portal/variant/create|2
    - /api/portal/variant/update|2
    - /api/portal/variant/delete|2
    - /api/portal/variant/update-price|2
    - /api/portal/variant/validate-variant-sku|1,2
    - /api/portal/variant/{serviceId}/{variantDraftId}|1,2
    - /api/portal/file/site-map|-1
    - /api/portal/integrate-service|-1
    - /api/portal/unit|1,2
    - /currency|1,2
    - /api/portal/listTax|1,2
    - /api/portal/unit/listUnit|1,2
    - /tree-department|-2
    - /api/portal/listCurrency|1,2
    - /business|-2
    - /api/sme-portal/combos|-1
    - /api/sme-portal/combos/{comboId}/suggestions|-1
    - /api/sme-portal/services|-1
    - /api/sme-portal/services/{serviceId}/suggestions|-1
    - /api/sme-portal/services/favorite|-1
    - /api/sme-portal/services/unified-search/detail/product|-1
    - /api/sme-portal/services/unified-search/detail/pricing|-1
    - /api/sme-portal/services/unified-search/detail/combo|-1
    - /api/sme-portal/services/unified-search/detail/provider|-1
    - /api/sme-portal/services/unified-search/detail/solution-bundling|-1
    - /api/sme-portal/services/multi-sub/check|-1
    - /api/sme-portal/services/category/{id}|-1
    - /api/sme-portal/combos/category|-1
    - /api/sme-portal/combos/category/{id}|-1
    - /api/portal/service/categories/sme|-1
    - /api/sme-portal/system-param/detail|-1
    - /api/admin-portal/categories|1
    - /api/portal/service/categories|-1
    - /api/portal/service/categories/bos/{providerId}|-1
    - /api/portal/service/categories/update-priority|1
    - /api/dev-portal/rating/get-list-province|-2
    - /api/portal/service-recommend|-1
    - /api/portal/services/providers|-1
    - /api/portal/services|-2
    - /api/portal/combos/calculate|-2
    - /api/portal/combos/subscription|1,3
    - /api/admin-portal/subscription/combo/bill-incurred|1
    - /api/dev-portal/subscription/combo/bill-incurred|2
    - /api/portal/combos|1,2
    - /api/portal/rating/service|-1
    - /api/sme-portal/rating/service|-1
    - /api/sme-portal/rating/combo/{id}|-1
    - /api/sme-portal/rating|3
    - /api/portal/notif|-2
    - /api/admin-portal/notif|1
    - /api/admin-portal/page-builder/delete|1
    - /api/dev-portal/notif|2
    - /api/admin-portal/service-suggestion|-2
    - /api/sme-portal/notif|3
    - /api/sme-portal/service-reaction/{id}/{type}|-2
    - /login|-1
    - /swagger-ui|-1
    - /swagger-resources|-1
    - /api/v3|-1
    - /v3/api-docs|-1
    - /swagger-ui.html|-1
    - /resources|-1
    - /api-dev/integrate/|-1
    - /dev-companies|-2
    - /api/sme-portal/subscription/e-contract/create|1,3
    - /api/sme-portal/subscription/service/|-1
    - /api/dev-portal/services/create/application/step1|2
    - /api/customer-contact/dupplicate-detection|1
    - /api/customer-contact/dupplicate-contact-info|1
    - /api/sme-portal/subscription/pricing1/{id}|-1
    - /api/sme-portal/subscription|3
    - /features|1,2
    - /api/portal/pre-order|-1
    - /pre-order|3
    - /file|-2
    - /api/integration/dev-portal/setup/success|-1
    - /api/portal/massoffer/tracking|-1
    - /api/admin-portal/subscription/calculate-old-sub|1
    - /api/admin-portal/history-email|1
    - /api/admin-portal/history-email/subjects|1
    - /api/admin-portal/history-email/receivers|1
    - /api/admin-portal/seo|1
    - /api/sme-portal/seo/detail|-1
    - /api/dev-portal/seo|2
    - /api/sme-portal/seo|1,3
    - /api/portal/seo|1,2
    - /validate-user|-2
    - /api/interest-products/brief|-1
    - /api/portal/action-log/addon|1,2
    - /api/dev-portal/subscription/tin|2
    - /api/sme-portal/seo/find-all|-1
    - /api/integration/portal/subscription/update-quantity|-1
    - /api/sme-portal/address|1,2,3
    - /api/common/combobox/get-data-combobox-admin-combo-plan-list-created-by|1
    - /api/common/combobox/get-data-combobox-admin-combo-plan-list-service|1
    - /api/common/combobox/get-data-combobox-admin-dbdl-import-createby|1
    - /api/common/combobox/get-data-combobox-admin-dbdl-import-email|1
    - /api/common/combobox/get-data-combobox-admin-dbdl-import-sgt|1
    - /api/common/combobox/get-data-combobox-admin-tran-list-mst|1
    - /api/common/combobox/get-data-combobox-admin-tran-list-ndd|1
    - /api/common/combobox/get-data-combobox-admin-tran-list-sctcn|1
    - /api/common/combobox/get-list-admin-email|1
    - /api/common/combobox/get-list-admin-manager-name|1
    - /api/common/combobox/get-list-admin-name|1
    - /api/common/combobox/get-list-admin-phone|1
    - /api/common/combobox/get-list-partition-admin-name|1
    - /api/admin-portal/address-categories|1
    - /api/common/combobox/get-list-provider|-1
    - /api/common/combobox/get-list-target-admin-email|1
    - /api/common/combobox/get-list-users-admin-nvkd-code|1
    - /api/common/combobox/search-provider-full-dev|1
    - /api/common/combobox|-2
    - /api/dev-admin-portal/subscription/tin|1,2
    - /api/admin-portal/account-history|1
    - /api/admin-portal/manual/check-subscription|1
    - /api/portal/subscription/check-exist-items|-2
    - /api/portal/subscription/check-order-qr/{billId}|-1
    - /api/portal/subscription/getSubscriptionsByIds|-2
    - /e-contract|1,2,3
    - /api/sme-portal/service-view|3
    - /api/dev-portal/combos/subscription|1
    - /api/admin-portal/unit|1
    - /api/enterprise/search-by-email|1
    - /api/enterprise/email|-2
    - /api/enterprise/phone|-2
    - /api/enterprise/checkTin|-1
    - /api/enterprise/check-tin-and-user-status|-1
    - /api/enterprise/get-info-by-tin|-2
    - /api/portal/contacts|-1
    - /api/enterprise/createEnterprise|-1
    - /api/enterprise/checkIdentify|-1
    - /api/enterprise|-2
    - /api/admin-portal/crm/enterprise-mgmt/common|-2
    - /api/sme-portal/page-builder|-1
    - /api/admin-portal/builder-template|-2
    - /api/portal/payment/notification-receiver|-1
    - /api/portal/payment/notification-receiver-qr|-1
    - /api/portal/payment/verify|-1
    - /api/portal/payment/create-econtract-draft|3
    - /api/portal/payment/get-econtract-token|3
    - /api/portal/subscription/combo/check-exist-items|-2
    - /api/dev-portal/service-suggestion|2
    - /api/admin-portal/marketing-campaign/list-email-template|-2
    - /api/admin-portal/marketing-campaign/list-coupon-available|-2
    - /api/sme-portal/marketing-campaign/ads-action|-1
    - /api/sme-portal/marketing-campaign/get-list-available-popup|-1
    - /api/sme-portal/marketing-campaign/get-list-active-campaign|-1
    - /api/sme-portal/marketing-campaign/get-list-banner-on-service|-1
    - /api/sme-portal/marketing-campaign/get-campaign-detail|-1
    - /api/admin-portal/import-migration/import-validate|1
    - /api/admin-portal/import-migration/import-excel|1
    - /api/admin-portal/import-migration/get-status|1
    - /api/admin-portal/import-migration/delete-customer|1
    - /api/migration/user/active-imported|-1
    - /api/migration/user/new-password|-1
    - /api/portal/subscription/calculate/tier/pricing|-2
    - /api/portal/subscription/addon/price-before-tax|-2
    - /api/portal/services/checkCanRemoveCustomerType|-1
    - /api/portal/pricing/checkCanRemoveCustomerType|-1
    - /api/tmdt/report|-1
    - /api/collect-info-preorder|-1
    - /api/update-info-preorder|-1
    - /api/admin-portal/shopping-cart/get-spdv-info|1,2
    - /api/sme-portal/shopping-cart/get-spdv-detail|-1
    - /api/sme-portal/shopping-cart/update-cart|-1
    - /api/sme-portal/shopping-cart/get-cart|-1
    - /api/sme-portal/shopping-cart/get-detail|-1
    - /api/sme-portal/shopping-cart/calculate-cart|3
    - /api/sme-portal/shopping-cart/detail-from-brief|3
    - /api/admin-portal/shopping-cart/calculate-cart|1,2
    - /api/admin-portal/shopping-cart/detail-from-brief|1,2
    - /api/admin-portal/subscription/get-all|1
    - /api/dev-portal/subscription/get-all|2
    - /api/sme-portal/subscription/get-all|3
    - /api/sme-portal/billing/get-detail|1,2,3
    - /api/sme-portal/billings/e-invoice|3
    - /api/admin-portal/subscription/re-active|1
    - /api/dev-portal/subscription/re-active|2
    - /api/sme-portal/subscription/re-active|3
    - /api/portal/pricing/get-number-of-cycle|-2
    - /api/portal/pricing/detail/|-1
    - /api/portal/pricing/{pricingId}/addons|-1
    - /api/portal/pricing/top-view|-1
    - /api/portal/pricing/top-selling|-1
    - /api/portal/pricing/personal|-1
    - /api/portal/coupon/enterprise|2
    - /api/portal/coupon/search-service|2
    - /api/portal/coupon/search-pricing|2
    - /api/portal/coupon/search-category|2
    - /api/portal/coupon/search-pricing-plan|2
    - /api/portal/coupon/popup-addons|2
    - /api/sme-portal/shopping-cart/register-from-cart|3
    - /api/sme-portal/shopping-cart/validate-cart|-1
    - /api/admin-portal/shopping-cart/validate-cart|1
    - /api/admin-portal/subscription/get-detail-billing|1
    - /api/dev-portal/subscription/get-detail-billing|2
    - /api/sme-portal/subscription/get-detail-billing|3
    - /api/portal/subscription/pricing/coupon-mc-promotion/{companyId}/{pricingId}|-1
    - /api/portal/subscription/pricing-addon/coupon-mc-promotion|-1
    - /api/portal/subscription/combo-plan/coupon-mc-promotion|-1
    - /api/portal/subscription/combo-plan/coupon-mc-promotion/{companyId}/{comboPlanId}|-1
    - /api/portal/subscription/addon/coupon-mc-promotion|-1
    - /api/portal/subscription/total/coupon-mc-promotion|-1
    - /api/portal/subscription/combo-plan/total/coupon-mc-promotion|-1
    - /api/portal/subscription/bundling/coupons/{companyId}/{packageId}|-1
    - /api/portal/subscription/bundling/coupon-mc-promotion/{companyId}/{packageId}|-1
    - /api/admin-portal/shopping-cart/register-from-cart-admin|1,2
    - /api/sme-portal/billings/{billingCode}/sme-info-update-cart|3
    - /api/sme-portal/billings/sme-info-update-cart|3
    - /api/sme-portal/billings/sme-info-update|3
    - /api/sme-portal/subscription/calculate/new/service|3
    - /api/admin-portal/billings/e-invoice/export/{billingCode}|1
    - /api/dev-portal/billings/e-invoice/export/{billingCode}|2
    - /api/sme-portal/shorten-link/detail|-1
    - /api/admin-portal/tag|1
    - /api/dev-portal/tag|2
    - /api/sme-portal/topic|-1
    - /api/admin-portal/topic|1
    - /api/dev-portal/topic|2
    - /api/admin-portal/crm/assignment-rule|1
    - /api/admin-portal/crm/data-partition|1
    - /api/admin-portal/crm/revenue-target|1
    - /api/admin-portal/action-history|1,2
    - /api/admin-portal/tickets|1
    - /api/sme-portal/tickets|3
    - /api/dev-portal/tickets|2
    - /api/admin-portal/custom-field|1
    - /api/dev-portal/custom-field|2
    - /api/sme-portal/custom-field|3
    - /api/dev-portal/subscription/pricing/bill-incurred-once|-1
    - /api/admin-portal/email-setting|1
    - /api/portal/subscription/test-swap-auto|-1
    - /api/sme-portal/affiliate-link/statistical-click|-1
    - /api/admin-portal/system-param|1
    - /api/dev-portal/system-param|2
    - /api/admin-portal/affiliate-link/get-config|1,2
    - /api/admin-portal/affiliate-link/generate-created-code|1,2
    - /api/admin-portal/affiliate-link/generate-link|1,2
    - /api/admin-portal/affiliate-link/search-created-code|1,2
    - /api/admin-portal/affiliate-link/search-user|1,2
    - /api/admin-portal/affiliate-link/get-statistical|1,2
    - /api/admin-portal/affiliate/dashboard|-2
    - /api/admin-portal/affiliate-commission/info-create|1,2
    - /api/admin-portal/affiliate-commission/get-list-member|1,2
    - /api/admin-portal/affiliate-commission/get-all-member|1,2
    - /api/admin-portal/affiliate-commission/search-commission-code|1,2
    - /api/admin-portal/affiliate-commission/search-commission-name|1,2
    - /api/admin-portal/affiliate-commission/search-product-affiliate|1,2
    - /api/admin-portal/affiliate-commission/search-service-product|1,2
    - /api/admin-portal/affiliate-commission/search-service-product-affiliate|1,2
    - /api/admin-portal/affiliate-commission/combobox-product-link-affiliate|1,2
    - /api/admin-portal/affiliate-commission/search-creater-commission|1,2
    - /api/admin-portal/affiliate-commission/search-member-apply|1,2
    - /api/admin-portal/affiliate-commission/search-product-name|1,2
    - /api/admin-portal/affiliate/member/import-affiliate|1,2
    - /api/admin-portal/affiliate/member/get-affiliate-config|1,2
    - /api/admin-portal/affiliate/member/update-affiliate-config|1,2
    - /api/admin-portal/affiliate/member/check-user-creation-role|1,2
    - /api/dev-portal/reports/export-pricing-service|2
    - /api/dev-portal/reports/statistic-revenue|2
    - /api/dev-portal/reports/export-pricing-service|2
    - /api/dev-portal/reports/statistic-customer|2
    - /api/dev-portal/reports/subscription|2
    - /api/dev-portal/reports/statistic-pricing-service|2
    - /api/dev-portal/reports/combobox|2
    - /api/dev-portal/3rd-party/add-transaction|2
    - /api/dev-portal/3rd-party/bank-transaction|1
    - /api/dev-portal/3rd-party/transaction-type|1
    - /api/dev-portal/3rd-party/export-transaction|1
    - /api/admin-portal/affiliate/dashboard-manager|-2
    - /api/admin-portal/custom-field/get-lst-custom-node|1
    - /api/admin-portal/custom-field/set-lst-custom-node|1
    - /api/dev-portal/custom-field/get-lst-custom-node|2
    - /api/dev-portal/custom-field/set-lst-custom-node|2
    - /api/sme-portal/custom-field/get-lst-custom-node|3
    - /api/dev-portal/order-service/update-status|2
    - /api/admin-portal/order-service/update-status|1
    - /api/portal/services/branding-image|3
    - /api/admin-portal/pricing/update-status|1
    - /api/admin-portal/pricing/multipPlan/{planId}/{displayStatus}|1
    - /api/admin-portal/pricing/order-list/{serviceId}|1
    - /api/admin-portal/rating-review|1
    - /api/portal/attributes/payment|-1
    - /api/portal/subscription/{subId}/online-service/progress|1,2
    - /api/portal/subscription/fees-apply|1,2
    - /api/portal/subscription/delete-apply-fees|1,2
    - /api/dev-portal/pricing/service-filter|2
    - /api/portal/subscription/calculate|-2
    - /api/portal/subscription/{id}/pricing|-2
    - /api/portal/user/file-workplace/update|3
    - /api/portal/subscription/official-joint-screen/cancel-multi-sub|1,2
    - /api/dev-portal/pricing/change-pricing-default/|2
    - /api/admin-portal/pricing/change-pricing-default/|1
    - /api/admin-portal/pricing/delete|1
    - /api/admin-portal/pricing/{id}|1
    - /api/dev-portal/pricing/multipPlan|2
    - /api/permissions/get-tree|1,2
    - /api/bos/homepage|-1
    - /api/bos/provider/information/{userId}|-1
    - /api/bos/provider/seen/{providerId}|-2
    - /api/bos/provider/purchase/{providerId}|-2
    - /api/coupon-set/get-list|1
    - /api/customer-contact/get-personal-detail|1
    - /api/admin-portal/combos/{id}/status/{displayedStatus}|1
    - /api/sme-portal/subscription/pricing1/{id}|-1
    - /api/portal/quotation/{uuid}|-1
    - /api/portal/quotation/update/response|-1
    - /api/portal/quotation/{uuid}/export-pdf|-1
    - /api/portal/quotation/dowload|-1
    - /api/sme-portal/subscription|3
    - /api/portal/user/{supplierId}/coupons|-1
    - api/sme-portal/traffic/wallet/send-otp|3
    - api/admin-portal/traffic/wallet/send-otp|1
    - /api/admin-portal/traffic/transaction/resend|1
    - /api/admin-portal/traffic/combobox/package-name|1
    - /api/sme-portal/traffic/combobox/package-name|3
    - /api/admin-portal/traffic/combobox/wallet-package-name|1
    - /api/sme-portal/traffic/combobox/wallet-package-name|3
    - /api/admin-portal/traffic/combobox/shared-list|1
    - /api/sme-portal/traffic/combobox/shared-list|3
    - /api/admin-portal/traffic/combobox/shared-list-phone|1
    - /api/sme-portal/traffic/combobox/sharing/list-phone|3
    - /api/admin-portal/traffic/combobox/sharing/list-phone|1
    - /api/sme-portal/traffic/combobox/shared-list-phone|3
    - /api/admin-portal/traffic/share/list-detail-shared|1
    - /api/sme-portal/traffic/share/list-detail-shared|3
    - /api/admin-portal/traffic/transaction/info|1
    - /api/admin-portal/traffic/get-config-delete|1
    - /api/admin-portal/traffic/config-delete|1
    - /api/admin-portal/traffic/wallet/detail-history/bss|1
    - /api/sme-portal/traffic/wallet/detail-history/bss|3
    - /api/admin-portal/traffic/wallet/check-participant|1
    - /api/sme-portal/traffic/wallet/check-participant|3
    - /api/admin-portal/traffic/share/check_exist/phone_receipt|1
    - /api/sme-portal/traffic/share/check_exist/phone_receipt|3
    - /api/admin-portal/traffic/share/import/phone_receipt|1
    - /api/sme-portal/traffic/share/import/phone_receipt|3
    - /api/sme-portal/marketing-campaign/promotion/get-list|3
    - /api/sme-portal/subscription/register-promotion|3
    - /api/sme-portal/integration/apigw-khcn/inquire|-1
    - /api/sme-portal/integration/apigw-khcn/pay|-1
    - /api/admin-portal/coupon/{id}/action/{status}|1
    - /api/admin-portal/service-group|1
    - /api/dev-portal/service-group|2
    - /api/sme-portal/service-group/{groupId}|-1
    - /api/sme-portal/service-group/{groupId}/suggestions|-1
    - /api/dev-portal/coupon/{id}/action/{status}|2
    - /api/sme-portal/system-param/evaluation-criteria|-2
    - /api/dev-portal/report|2
    - /api/dev-portal/categories|2
    - /api/sme-portal/services/get-by-category|-1
    - /api/theme/config|1
    - /api/theme/detail|-1
    - /api/theme/current-theme-link-by-action|-1
    - /api/service-group|-1
    - /api/portal/pricing/personal/payment-cycle|-1
    - /api/admin-portal/pricing/payment-cycle|-1
    - /api/portal/pricing/personal/multi-plan|-1
    - /api/portal/pricing/get-all|-1
    - /api/portal/pricing/{pricingId}/detail|-1
    - /api/portal/pricing/mobile/send-otp|-1
    - /api/pricing/import|-1
    - /api/sme-portal/service-reaction|3
    - /api/integration/onebss/subscriptions/register|-1
    - /api/integration/onebss/subscriptions/cancel|-1
    - /api/integration/onebss/subscriptions/reactive|-1
    - /api/integration/onebss/subscriptions/merge|-1
    - /api/integration/onebss/subscriptions/upgrade|-1
    - /api/integration/onebss/subscriptions/renewal|-1
    - /api/integration/onebss/subscriptions/secure-code|-1
    - /api/git-info|1
    - /api/v1/mobile-app/homepage/banner|-1
    - /api/v1/mobile-app/coupons|-1
    - /api/v1/mobile-app/coupons/{couponId}|-1
    - /api/v1/mobile-app/coupons/banner/{id}|1
    - /api/v1/mobile-app/services/suggestions|-1
    - /api/v1/mobile-app/services|-1
    - /api/v1/mobile-app/services/newest|-1
    - /api/v1/mobile-app/services/last-seen|-1
    - /api/v1/mobile-app/services/top-selling|-1
    - /api/v1/mobile-app/services/categories|-1
    - /api/v1/mobile-app/services/coupons/flash-sale|-1
    - /api/v1/mobile-app/services/best-deal|-1
    - /api/v1/mobile-app/services/rating/{id}/{type}|-2
    - /api/v1/mobile-app/notification|3
    - /api/v1/mobile-app/notification/count|3
    - /api/v1/mobile-app/notification/latest|3
    - /{ruleId}/objects/{assigneeId}|1
    - /scan-assignee-interactive|1
    - /change-assignee-interactive|1
    - /api/admin-portal/crm/automation-rule|1
    - /api/admin-portal/page-builder/delete|1
    - /api/customer-group/common/popup/get-lst-contact|1
    - /api/bos/subscriptions|-2
    - /api/admin-portal/subscription/get-list-order|1
    - /api/dev-portal/subscription/get-list-order|2
    - /api/dev-portal/billings/payment|2
    - /api/portal/user/email/validate|-1
    - /api/geography/sync/{provinceId}|1
    - /api/v1/mobile-app/coupons/{couponId}/services|-1
    - /api/v1/mobile-app/coupons/{couponId}/pricings|-1
    - /api/v1/mobile-app/services/reaction|3
    - /api/portal/subscription/payment-methods|-1
    - /api/interest-products/brief|-1
    - /api/customer-contact/duplicate-detection|1
    - /api/customer-contact/duplicate-contact-info|1
    - /api/admin-portal/crm/enterprise-mgmt/details/{enterpriseId}/messages|1
    - /api/admin-portal/crm/enterprise-mgmt/details/{enterpriseId}/interest-products|1
    - /api/portal/subscription/combo-plan/coupons/{companyId}/{comboPlanId}|-2
    - /api/customer-contact/{contactId}/file|1
    - /api/portal/user/dupplicate-detection|1
    - /api/users/exist-by-email|-1
    - /api/admin-portal/crm/enterprise-mgmt/update|1
    - /api/portal/pricing/{pricingId}/addons|-1
    - /api/v1/mobile-app/services/fcm/{userId}/send|-1
    - /api/sme-portal/service-suggestion/{serviceId}|-1
    - /api/sme-portal/subscription/{subscriptionId}/summary|3
    - /api/v1/sme-portal/subscriptions/{subscriptionId}/details|3
    - /api/v1/sme-portal/subscriptions/{subscriptionId}/progress|3
    - /api/v1/products/products|-1
    - /api/v1/products/{productId}|-1
    - /api/sme-portal/shopping-cart/count|-1
    - /api/v1/categories|-1
    - /api/v1/categories/{category_id}|-1
    - /api/v1/categories/{category_id}/sub-categories|-1
    - /api/v1/products/{product_id}|-1
    - /api/v1/products/{productId}/plans/{planId}|-1
    - /api/v1/products/{productId}/plans|-1
    - /api/v1/products/client/{clientId}|-1
    - /api/v1/products/{productId}/stock|-1
    - /api/v1/products|-1
    - /api/v1/webhooks|-1
    - /api/v1/payments|-1
    - /api/v1/webhooks/{webhookId}|-1
    - /api/v1/orders|-1
    - /api/v1/orders/{order_id}/status|-1
    - /api/v1/orders/trigger|1
    - /api/v1/orders/received|-1
    - /api/v1/subscriptions/{subscriptionId}/renew|-1
    - /api/v1/payments/{paymentId}/status|-1
    - /api/v1/orders/{order_id}|-1
    - /api/admin-portal/order-service/get-lst-cbb-am
    - /api/v1/addresses|-1
    - /api/portal/file-attach|-2
    - /api/v1/telco|-1
    - /api/sme-portal/telco|-1
    - /api/admin-portal/services|1
    - /api/admin-portal/services/{serviceId}|1
    - /api/portal/pricing/validate-code|1,2
    - /api/portal/product-solutions|-1
    - /api/admin-portal/product-solutions|-1
    - /api/dev-portal/product-solutions|-1
    - /api/portal/packages|-1
    - /api/admin-portal/packages|-1
    - /api/dev-portal/packages|-1
    - /api/sme-portal/product-solutions/{solutionId}|-1
    - /api/sme-portal/product-solutions/{solutionId}/packages|-1
    - /api/iot-portal/packages/{solutionDraftId}/evaluations|-1
    - /api/portal/product-solutions/pop-up|-1
    - /api/portal/product-solutions/cbb/packages|-1
    - /api/sme-portal/product-solutions/domains|-1
    - /api/sme-portal/product-solutions/domain/{domainId}|-1
    - /api/sme-portal/product-solutions/domains/intro/{domainId}|-1
    - /api/sme-portal/product-solutions/domains/intro|-1
    - /api/portal/packages/created-by|-1
    - /api/portal/packages/get-spdv-bundling|-1
    - /api/portal/packages/cbb/service|-1
    - /api/portal/packages/{packageId}/suggestions|-1
    - /api/portal/packages/calculate-price-info|-1
    - /api/portal/packages/recommended|-1
    - /api/portal/packages/visibility|-1
    - /api/sme-portal/packages|-1
    - /api/sme-portal/packages/{packageId}|-1
    - /api/portal/action-log/solution-bundling/{objectDraftId}|1,2
    - /api/sme-portal/service-view/product-solution|-1
    - /api/iot-portal/packages/{packageId}|-1
    - /api/iot-portal/packages/{packageDraftId}/evaluations|-1
    - /api/iot-portal/packages/{packageId}/suggestions|-1
    - /api/admin-portal/pricing/approve|1
    - /api/sme-portal/product-solutions/{solutionId}/devices|-1
    - /api/sme-portal/product-solutions/{solutionId}/services|-1
    - /api/sme-portal/product-solutions/domains/category/{domainId}|-1
    - /api/sme-portal/product-solutions/{solutionId}/devices
    - /api/sme-portal/product-solutions/{solutionId}/services
    - /api/dev-portal/subscription/cart/{cartCode}/order-overview|2
    - /api/admin-portal/subscription/cart/{cartCode}/order-overview|1
    - /api/sme-portal/subscription/cart/{cartCode}/order-overview|3
    - /api/admin-portal/subscription/{subId}/order-overview|1
    - /api/dev-portal/subscription/{subId}/order-overview|2
    - /api/sme-portal/subscription/{subId}/order-overview|3
    - /api/admin-portal/subscription/detail/{productOrderId}/history|1
    - /api/admin-portal/subscription/detail/{productOrderId}/status/history|1
    - /api/admin-portal/subscription/detail/{sourceReference}/payment/history|1
    - /api/admin-portal/subscription/detail/{sourceReference}/payment-detail/history|1
    - /api/dev-portal/subscription/detail/{productOrderId}/history|2
    - /api/dev-portal/subscription/detail/{productOrderId}/status/history|2
    - /api/dev-portal/subscription/detail/{sourceReference}/payment/history|2
    - /api/dev-portal/subscription/detail/{sourceReference}/payment-detail/history|2
    - /api/admin-portal/subscription/{userId}/order-history|1
    - /api/admin-portal/subscription/{userId}/used-services|1
    - /api/admin-portal/subscription/orders/{productOrderId}/billing|1
    - /api/dev-portal/subscription/orders/{productOrderId}/billing|2
    - /api/admin-portal/state-types|1
    - /api/admin-portal/states|1
    - /api/admin-portal/features|1
    - /api/admin-portal/state-transition|1
    - /api/admin-portal/workflows|1
    - /api/partners|1
    - /api/affiliate-portal/page-builder|-2
inventory-apis:
  event-subscription-registered: /api/transfers/simple-create

apis:
  internal:
    notifications:
      api-webhooks: /notifications/api/v1/webhooks
