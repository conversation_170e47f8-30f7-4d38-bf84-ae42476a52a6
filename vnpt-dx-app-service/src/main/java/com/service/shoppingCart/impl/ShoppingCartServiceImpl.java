package com.service.shoppingCart.impl;

import static com.constant.SubscriptionConstant.GET_CART;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.component.BaseController.ListRequest;
import com.constant.BillConstant;
import com.constant.PackageBundlingConstant;
import com.constant.SubscriptionConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.credit_note.CreditNoteDetailStatusEnum;
import com.constant.enums.marketingCampaign.McPromotionTypeEnum;
import com.constant.enums.marketingCampaign.McRejectCauseEnum;
import com.constant.enums.orders.SmeProgressEnum;
import com.constant.enums.product_variant.VariantStatusEnum;
import com.constant.enums.serviceGroup.ServiceGroupPricingObjectTypeEnum;
import com.constant.enums.services.PopupProductTypeEnum;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.constant.enums.subscription.CouponTypeEnum;
import com.constant.enums.subscription.OrderItemStatusEnum;
import com.constant.enums.subscription.ProgressOrderEnum;
import com.constant.enums.subscription.StatusOrderEnum;
import com.constant.enums.subscription.TriggerModeEnum;
import com.constant.enums.transactionLog.ActivityCodeEnum;
import com.constant.integration.apigwkhcn.ApiGwKHCNConstant;
import com.dto.addons.UnitLimitedDTO;
import com.dto.attributes.LstServiceAttributeDTO;
import com.dto.attributes.LstServiceAttributeITF;
import com.dto.combo.ComboDetailDTO;
import com.dto.combo.detail.IComboPricingDTO;
import com.dto.common.ICommonIdName;
import com.dto.contracts.ResponseBodyEcontractDTO;
import com.dto.contracts.ResponseListEcontractDTO;
import com.dto.contracts.ViewEcontractBodyDTO;
import com.dto.creditNote.CreditNoteCalculateDTO;
import com.dto.einvoice.InvoiceInfoDTO;
import com.dto.file.attach.IFileAttachResponse;
import com.dto.marketingCampaign.smePortal.AppliedPromotionDTO;
import com.dto.marketingCampaign.smePortal.ApplyCampaignInfoDTO;
import com.dto.marketingCampaign.smePortal.ApplyCampaignResponseDTO;
import com.dto.marketingCampaign.smePortal.McAppliedEffectDTO;
import com.dto.marketingCampaign.smePortal.PromotionDTO;
import com.dto.pricing.PricingDetailResDTO;
import com.dto.pricing.PricingDetailResDTO.UnitLimited;
import com.dto.pricing.PricingTaxRes;
import com.dto.product_solustions.CouponPackageDTO;
import com.dto.product_solustions.ISubOrderCouponDetailDTO;
import com.dto.product_solustions.ISubOrderItemAddonDTO;
import com.dto.product_solustions.ISubPackageItemDTO;
import com.dto.product_solustions.ServiceProgressDetailDTO;
import com.dto.product_solustions.SubOrderItemAddonDTO;
import com.dto.product_solustions.SubOrderItemDTO;
import com.dto.product_solustions.SubOrderServiceProgressDetailDTO;
import com.dto.product_variant.AttributesDTO;
import com.dto.product_variant.VariantAttributesValueConvertDTO;
import com.dto.product_variant.VariantResDTO;
import com.dto.product_variant.VariantResponseDTO;
import com.dto.serviceGroup.ServiceGroupTableReqDTO;
import com.dto.serviceGroup.ServiceGroupTableReqDTO.PricingInfo;
import com.dto.serviceGroup.ServiceGroupTableReqDTO.ServiceVariantInfo;
import com.dto.serviceGroup.ServiceGroupTableResDTO;
import com.dto.services.SetupFeeInfoConvertDTO;
import com.dto.shoppingCart.BillingSubResDTO;
import com.dto.shoppingCart.CartErrorDTO;
import com.dto.shoppingCart.GetBillingDetailDTO;
import com.dto.shoppingCart.GetBillingDetailDTO.BillItem;
import com.dto.shoppingCart.GetBillingDetailDTO.BillItem.Item;
import com.dto.shoppingCart.GetBillingDetailDTO.Customer;
import com.dto.shoppingCart.GetBillingDetailDTO.EInvoice;
import com.dto.shoppingCart.GetBillingDetailDTO.Payment;
import com.dto.shoppingCart.GetBillingDetailDTO.Provider;
import com.dto.shoppingCart.GetListSubReqDTO;
import com.dto.shoppingCart.IBillingCodePageDTO;
import com.dto.shoppingCart.IGetBillingDetailDTO;
import com.dto.shoppingCart.IGetSPDVInfoDTO;
import com.dto.shoppingCart.IGetSubDetailBillingDTO;
import com.dto.shoppingCart.IServiceInfo;
import com.dto.shoppingCart.ISubCodePageDTO;
import com.dto.shoppingCart.ISubOrderOverviewDTO;
import com.dto.shoppingCart.ISubscriptionsSaaSResDTO;
import com.dto.shoppingCart.OrderItemStatusDetailDTO;
import com.dto.shoppingCart.ShoppingCartBriefDTO;
import com.dto.shoppingCart.ShoppingCartBriefDTO.AddonBriefDTO;
import com.dto.shoppingCart.ShoppingCartBriefDTO.PricingBriefDTO;
import com.dto.shoppingCart.ShoppingCartBriefDTO.ServiceBriefDTO;
import com.dto.shoppingCart.ShoppingCartBriefDTO.ShoppingCartBriefDTOOld;
import com.dto.shoppingCart.ShoppingCartDetailDTO;
import com.dto.shoppingCart.ShoppingCartDetailDTO.AddonDetailDTO;
import com.dto.shoppingCart.ShoppingCartDetailDTO.ISubVariantDTO;
import com.dto.shoppingCart.ShoppingCartDetailDTO.PricingDetailDTO;
import com.dto.shoppingCart.ShoppingCartDetailDTO.ServiceDetailDTO;
import com.dto.shoppingCart.ShoppingCartDetailDTO.SubPricingDTO;
import com.dto.shoppingCart.SubBillingITFDTO;
import com.dto.shoppingCart.SubOrderOverviewBundlingDetailDTO;
import com.dto.shoppingCart.SubOrderOverviewCustomerDetailDTO;
import com.dto.shoppingCart.SubOrderOverviewDTO;
import com.dto.shoppingCart.SubOrderServiceAndProductDetailDTO;
import com.dto.shoppingCart.SubscriptionsSaaSResDTO;
import com.dto.shoppingCart.SubscriptionsSaaSResDTO.BundlingPackageDTO;
import com.dto.shoppingCart.SubscriptionsSaaSResDTO.ProductsDTO;
import com.dto.shoppingCart.SubscriptionsSaaSResDTO.ProductsDTO.AvatarProductDTO;
import com.dto.shoppingCart.SubscriptionsSaaSResDTO.ProductsDTO.PricingProductDTO;
import com.dto.shoppingCart.SubscriptionsSaaSResDTO.SolutionDTO;
import com.dto.shoppingCart.fomula.ShoppingCartFormulaReqDTO;
import com.dto.shoppingCart.fomula.ShoppingCartFormulaResDTO;
import com.dto.shoppingCart.fomula.ShoppingCartFormulaResDTO.CouponApplyDTO;
import com.dto.shoppingCart.fomula.ShoppingCartFormulaResDTO.FeeApplyDTO;
import com.dto.shoppingCart.fomula.ShoppingCartFormulaResDTO.TaxApplyDTO;
import com.dto.shoppingCart.register.OrderAddressDTO;
import com.dto.subscriptions.ISubscriptionsDTO;
import com.dto.subscriptions.McApplyDTO;
import com.dto.subscriptions.OrderStatusHistoryInfo;
import com.dto.subscriptions.StatusHistoryInfo;
import com.dto.subscriptions.SubCalBaseDevReqDTO;
import com.dto.subscriptions.SubscriptionCalculateDTO;
import com.dto.subscriptions.SubscriptionRangeDTO;
import com.dto.subscriptions.SubscriptionsDTO;
import com.dto.subscriptions.UpdateStatusOrderDeviceDTO;
import com.dto.subscriptions.calculate.CalSetupFeeDeviceReqDTO;
import com.dto.subscriptions.calculate.UnitLimitedNewDTO.UnitLimitedNew;
import com.dto.subscriptions.formula.ShoppingCartCalculateDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaReqDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaReqDTO.FormulaAddon;
import com.dto.subscriptions.formula.SubscriptionFormulaReqDTO.FormulaCustomFee;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.CouponItem;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.DetailSubOrder;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaCoupon;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaObject;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaTax;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.SetupFee;
import com.dto.subscriptions.order_service.ISubOrderStatusHistoryResDTO;
import com.dto.subscriptions.order_service.OrderItemStatusReqDTO;
import com.dto.subscriptions.responseDTO.DetailCustomerSubOrderDTO;
import com.dto.subscriptions.responseDTO.IDetailCustomerSubOrderDTO;
import com.dto.subscriptions.responseDTO.IGetAddonSubDetail;
import com.dto.subscriptions.responseDTO.IGetEcontract;
import com.dto.subscriptions.responseDTO.IGetMcCoupon;
import com.dto.subscriptions.responseDTO.IGetSubDetailCart;
import com.dto.subscriptions.responseDTO.IGetSubHistories;
import com.dto.subscriptions.responseDTO.IListServiceInterestDTO;
import com.dto.subscriptions.responseDTO.IServiceInterestDTO;
import com.dto.subscriptions.responseDTO.ProgressDetailDTO;
import com.dto.subscriptions.responseDTO.ServiceInterestDTO;
import com.dto.subscriptions.responseDTO.SubDetailEcontract;
import com.dto.subscriptions.responseDTO.SubscriptionDetailCartDTO;
import com.entity.addons.Addon;
import com.entity.bills.Bills;
import com.entity.combo.ComboPlan;
import com.entity.coupons.Coupon;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.pricing.PricingPlanDetail;
import com.entity.pricing.PricingTax;
import com.entity.product_solutions.Package;
import com.entity.product_solutions.PackageItems;
import com.entity.product_variant.Variant;
import com.entity.serviceGroup.ServiceGroup;
import com.entity.serviceGroup.ServiceGroupPricing;
import com.entity.serviceGroup.ServiceGroupPricingItem;
import com.entity.services.ServiceDraft;
import com.entity.services.ServiceEntity;
import com.entity.shoppingCart.ShoppingCart;
import com.entity.subscriptions.CustomFee;
import com.entity.subscriptions.ProductOrders;
import com.entity.subscriptions.Subscription;
import com.entity.subscriptions.SubscriptionOrderStatusHistory;
import com.enums.ApproveStatusEnum;
import com.enums.DisplayStatus;
import com.enums.PermissionNameEnum;
import com.enums.PurchaseVersionEnum;
import com.enums.SystemParamType;
import com.enums.product_solutions.ObjectTypeEnum;
import com.exception.ErrorKey;
import com.exception.ErrorKey.EKPackage;
import com.exception.ErrorKey.Services;
import com.exception.Resources;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mapper.EnumFieldMapper;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.DiscountTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.billings.BillStatusEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.crm.CrmPermissionEnum;
import com.onedx.common.constants.enums.enterprise.EnterpriseCreatedSourceEnum;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.constants.enums.migration.MigrationServiceTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.enums.services.ProductClassificationEnum;
import com.onedx.common.constants.enums.subscriptions.BillItemType;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.enums.subscriptions.TotalOrderProgressStatusEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.dto.customFields.GetSPDVInfoDTO;
import com.onedx.common.dto.customFields.GetSPDVInfoDTO.AttributeInfor;
import com.onedx.common.dto.customFields.GetSPDVInfoDTO.AttributeValues;
import com.onedx.common.dto.customFields.GetSPDVInfoDTO.PricingMultiPlanInfo;
import com.onedx.common.dto.customFields.GetSPDVInfoDTO.VariantInfor;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO.CalUnitLimitedCustomDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionOneTimeFee;
import com.onedx.common.entity.subscriptions.SubscriptionSetupFee;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.BaseException;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.MessageKeyConstant.ShoppingCartError;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.utils.GsonMapperUtil;
import com.onedx.common.utils.ObjectMapperUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.addons.AddonRepository;
import com.repository.addons.AddonsTaxRepository;
import com.repository.address.AddressRepository;
import com.repository.bills.BillItemRepository;
import com.repository.bills.BillsRepository;
import com.repository.bills.EInvoiceRepository;
import com.repository.combo.ComboAddonRepository;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboPricingRepository;
import com.repository.combo.ComboRepository;
import com.repository.combo.ComboTaxRepository;
import com.repository.coupons.CouponRepository;
import com.repository.credit_note.CreditNoteRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.orderService.OrderServiceReceiveRepository;
import com.repository.pricing.PricingAddonRepository;
import com.repository.pricing.PricingMultiPlanAddonRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingPlanDetailRepository;
import com.repository.pricing.PricingRepository;
import com.repository.pricing.PricingSetupFeeTaxRepository;
import com.repository.pricing.PricingTaxRepository;
import com.repository.pricing.PricingVariantRepository;
import com.repository.product_solutions.PackageAddonRepository;
import com.repository.product_solutions.PackageItemRepository;
import com.repository.product_solutions.PackageRepository;
import com.repository.product_solutions.ProductSolutionRepository;
import com.repository.product_variant.VariantDraftRepository;
import com.repository.product_variant.VariantRepository;
import com.repository.serviceGroup.ServiceGroupPricingItemRepository;
import com.repository.serviceGroup.ServiceGroupPricingRepository;
import com.repository.serviceGroup.ServiceGroupRepository;
import com.repository.services.ServiceDraftRepository;
import com.repository.services.ServiceRepository;
import com.repository.shoppingCart.ShoppingCartRepository;
import com.repository.subscriptions.CustomFeeRepository;
import com.repository.subscriptions.ProductOrdersRepository;
import com.repository.subscriptions.SubscriptionCouponsRepository;
import com.repository.subscriptions.SubscriptionMetadataRepository;
import com.repository.subscriptions.SubscriptionOrderStatusHistoryRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.subscriptions.SubscriptionSetupFeeRepository;
import com.repository.unitLimited.UnitLimitedRepository;
import com.repository.units.UnitRepository;
import com.repository.users.UserRepository;
import com.service.bills.BillsService;
import com.service.calculator.ContainedTaxListCalculator;
import com.service.calculator.PercentCouponListCalculator;
import com.service.calculator.PriceCalculator;
import com.service.calculator.PriceCouponListCalculator;
import com.service.calculator.SubscriptionCalculationInput;
import com.service.calculator.SubscriptionCalculator;
import com.service.calculator.SubscriptionPlanCalculationInput;
import com.service.calculator.TotalPercentCouponAmountCalculator;
import com.service.calculator.TotalPriceCouponAmountCalculator;
import com.service.contracts.EcontractService;
import com.service.crm.dataPartition.impl.CrmObjectPermissionUtil;
import com.service.marketingCampaign.MarketingCampaignSmeService;
import com.service.multiplePeriod.impl.SubMultiplePeriodImpl;
import com.service.pricing.PricingService;
import com.service.product_orders.ProductOrdersService;
import com.service.product_variant.AttributesService;
import com.service.product_variant.VariantService;
import com.service.serviceGroup.ServiceGroupService;
import com.service.services.ServicesService;
import com.service.shoppingCart.ShoppingCartAdminService;
import com.service.shoppingCart.ShoppingCartSmeService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.subscriptions.SubscriptionHistoryService;
import com.service.subscriptions.SubscriptionMetadataService;
import com.service.subscriptions.SubscriptionValidateService;
import com.service.system.param.SystemParamService;
import com.util.AuthUtil;
import com.util.CalculatorUtil;
import com.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

@Service
@Slf4j
public class ShoppingCartServiceImpl implements ShoppingCartSmeService, ShoppingCartAdminService {

    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private ShoppingCartRepository shoppingCartRepository;
    @Autowired
    private ComboRepository comboRepository;
    @Autowired
    private AddonsTaxRepository addonsTaxRepository;
    @Autowired
    private ComboPricingRepository comboPricingRepository;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private ShoppingCartSmeService shoppingCartSmeService;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private ServicesService servicesService;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private ServiceGroupRepository serviceGroupRepository;
    @Autowired
    private VariantDraftRepository variantDraftRepository;
    @Autowired
    private VariantRepository variantRepository;
    @Autowired
    private ServiceGroupPricingRepository serviceGroupPricingRepository;
    @Autowired
    private ServiceGroupPricingItemRepository serviceGroupPricingItemRepository;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private PricingPlanDetailRepository pricingPlanDetailRepository;
    @Autowired
    private AddonRepository addonRepository;
    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private PricingAddonRepository pricingAddonRepository;
    @Autowired
    private PricingMultiPlanAddonRepository pricingMultiPlanAddonRepository;
    @Autowired
    private ComboAddonRepository comboAddonRepository;
    @Autowired
    private FileAttachRepository fileAttachRepository;
    @Autowired
    private UnitLimitedRepository unitLimitedRepository;
    @Autowired
    private PricingTaxRepository pricingTaxRepository;
    @Autowired
    private SubscriptionMetadataRepository subscriptionMetadataRepository;
    @Autowired
    private SubscriptionOrderStatusHistoryRepository subscriptionOrderStatusHistoryRepository;
    @Autowired
    private ProductOrdersService productOrdersService;
    @Autowired
    private ComboTaxRepository comboTaxRepository;
    @Autowired
    private AddonsTaxRepository addonTaxRepository;
    @Autowired
    private SubMultiplePeriodImpl subMultiplePeriod;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private MarketingCampaignSmeService campaignSmeService;
    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private CreditNoteRepository creditNoteRepository;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private EInvoiceRepository eInvoiceRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CrmObjectPermissionUtil crmObjectPermissionUtil;
    @Autowired
    private BillsRepository billsRepository;
    private BillsService billsService;
    @Autowired
    private VariantService variantService;
    @Autowired
    AttributesService attributesService;
    @Autowired
    private OrderServiceReceiveRepository orderServiceReceiveRepository;
    @Autowired
    private ServiceDraftRepository serviceDraftRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private CustomFeeRepository customFeeRepository;
    @Autowired
    private SubscriptionHistoryService subscriptionHistoryService;
    @Autowired
    private SubscriptionSetupFeeRepository subscriptionSetupFeeRepository;
    @Autowired
    private BillItemRepository billItemRepository;
    @Autowired
    private SubscriptionCouponsRepository subscriptionCouponsRepository;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private ServiceGroupService serviceGroupService;
    @Autowired
    private PricingVariantRepository pricingVariantRepository;
    @Autowired
    private PackageRepository packageRepository;

    @Autowired
    private PackageItemRepository packageItemRepository;

    @Autowired
    private ProductSolutionRepository productSolutionRepository;

    @Autowired
    private PackageAddonRepository packageAddonRepository;

    @Autowired
    private ProductOrdersRepository productOrdersRepository;

    @Autowired
    private PricingSetupFeeTaxRepository pricingSetupFeeTaxRepository;

    @Autowired
    private SubscriptionMetadataService subscriptionMetadataService;

    @Autowired
    private SubscriptionValidateService subscriptionValidateService;

    @Autowired
    public void setBillsService(BillsService billsService) {
        this.billsService = billsService;
    }

    final String [] subscriptionMessage= {"subscription"};

    @Override
    public Page<GetSPDVInfoDTO> getSPDVInfo(String productType, Integer portalType, String value, Long customerId, Integer searchProduct,
        List<CustomerTypeEnum> lstCustomerType, Long categoryId, String paymentCycle, Integer isDevice, Integer serviceType, Integer isTrial,
        Integer searchPricing, Integer serviceOnOsType, List<Long> lstSelectedId, Boolean isProviderVNPTTech,
        Long serviceUniqueId, Long pricingUniqueId, Set<CalculateTypeEnum> calculateTypeEnums, Set<PopupProductTypeEnum> popupProductTypeEnums, Pageable pageable) {
        Long userId = AuthUtil.getCurrentParentId();
        if (userId == null) {
            return null;
        }
        value = SqlUtils.optimizeSearchLike(value);
        List<GetSPDVInfoDTO> response = new ArrayList<>();
        Long providerId = (Objects.equals(portalType, 0)) ? -1L : userId;
        String customerType = lstCustomerType.contains(CustomerTypeEnum.ALL) ?
            "" : String.join(",", lstCustomerType.stream().map(CustomerTypeEnum::getValue).collect(Collectors.toList()));

        // Thông tin khách hàng đã chọn
        User customer = userRepository.findById(ObjectUtil.getOrDefault(customerId, -1L)).orElse(null);
        Long provinceId = Objects.nonNull(customer) ? ObjectUtil.getOrDefault(customer.getProvinceId(), -1L): -1L;
        Long districtId = Objects.nonNull(customer) ? ObjectUtil.getOrDefault(customer.getDistrictId(), -1L) : -1L;
        Long wardId = Objects.nonNull(customer) ? ObjectUtil.getOrDefault(customer.getWardId(), -1L) : -1L;
        String customerTypeEnum =
            Objects.nonNull(customer) ? ObjectUtil.getOrDefault(CustomerTypeEnum.fromValue(customer.getCustomerType()), CustomerTypeEnum.ALL).name() : CustomerTypeEnum.ALL.name();
        // all data
        List<IGetSPDVInfoDTO> lstResult;
        if (isProviderVNPTTech) {
            lstResult = shoppingCartRepository.getSPDVInfoByVNPTTech(productType, value, providerId, searchProduct, customerType, categoryId,
                paymentCycle, isDevice, serviceType, isTrial, searchPricing, serviceOnOsType, lstSelectedId, serviceUniqueId, pricingUniqueId,
                calculateTypeEnums.stream().map(CalculateTypeEnum::getValue).collect(Collectors.toSet()));
        } else {
            lstResult = shoppingCartRepository.getSPDVInfo(productType, value, providerId, searchProduct, customerType, categoryId,
                paymentCycle, isDevice, serviceType, isTrial, searchPricing, serviceOnOsType, lstSelectedId, serviceUniqueId, pricingUniqueId,
                calculateTypeEnums.stream().map(CalculateTypeEnum::getValue).collect(Collectors.toSet()), popupProductTypeEnums.stream().map(PopupProductTypeEnum::getValue).collect(
                    Collectors.toSet()), provinceId, districtId, wardId, customerTypeEnum);
        }
        // get data device service
        Set<Long> lstDeviceServiceId = lstResult.stream()
            .filter(e -> Objects.equals(e.getProductClassification(), ProductClassificationEnum.PHYSICAL.getValue()) ||
                Objects.equals(e.getProductType(), ServiceProductTypeEnum.DEVICE.getValue()))
            .map(IGetSPDVInfoDTO::getServiceId)
            .collect(Collectors.toCollection(LinkedHashSet::new));
        List<VariantResDTO> listVariant = shoppingCartRepository.getAllVariantByServiceId(lstDeviceServiceId);
        List<LstServiceAttributeITF> listAttributes = shoppingCartRepository.getAllAttributeByServiceId(lstDeviceServiceId);
        // Danh sách các pricing_multi_plan mặc định
        Set<Long> setDefaultPMPId = pricingRepository.getAllDefaultPMPIds();
        // Gộp thông tin trả về
        Map<Long, List<IGetSPDVInfoDTO>> mapService = lstResult.stream().collect(Collectors.groupingBy(IGetSPDVInfoDTO::getServiceUniqueId));
        final int start = Math.min((int) pageable.getOffset(), mapService.size());
        final int end = Math.min((start + pageable.getPageSize()), mapService.size());
        for (Entry<Long, List<IGetSPDVInfoDTO>> serviceEntry : new ArrayList<>(mapService.entrySet()).subList(start, end)) {
            GetSPDVInfoDTO serviceDTO = new GetSPDVInfoDTO();
            List<IGetSPDVInfoDTO> iGetSPDVInfoDTOS = serviceEntry.getValue();

            // skip loop if empty items (bundling/pricing)
            if (ObjectUtils.isEmpty(iGetSPDVInfoDTOS)) {
                continue;
            }

            // trường hợp root object là SERVICE_GROUP / PACKAGE -> set giá tổng
            if (!CollectionUtils.isEmpty(iGetSPDVInfoDTOS) && Objects.nonNull(iGetSPDVInfoDTOS.get(0).getCalculateType())) {
                IGetSPDVInfoDTO iGetSPDVInfoDTO = iGetSPDVInfoDTOS.get(0);
                serviceDTO.setServiceId(iGetSPDVInfoDTO.getServiceId());
                serviceDTO.setServiceName(iGetSPDVInfoDTO.getServiceName());
                serviceDTO.setServiceUniqueId(iGetSPDVInfoDTO.getServiceUniqueId());
                serviceDTO.setServiceDraftId(iGetSPDVInfoDTO.getDraftId());
                if (iGetSPDVInfoDTO.getCalculateType().equals(CalculateTypeEnum.SERVICE_GROUP.getValue())) {
                    serviceDTO.setCalculateType(CalculateTypeEnum.SERVICE_GROUP);
                    serviceDTO.setProductType(CalculateTypeEnum.SERVICE_GROUP);
                    serviceDTO.setAvatarUrl(iGetSPDVInfoDTO.getAvatarUrl());
                    serviceDTO.setPrice(serviceGroupService.getGroupTotalTempPrice(iGetSPDVInfoDTO.getServiceId()));
                    response.add(serviceDTO);
                    continue;
                } else if (iGetSPDVInfoDTO.getCalculateType().equals(CalculateTypeEnum.PACKAGE_BUNDLING.getValue()) &&
                    Objects.equals(iGetSPDVInfoDTO.getRootType(), ObjectTypeEnum.PACKAGE.name())) {
                    serviceDTO.setRootType(ObjectTypeEnum.PACKAGE.name());
                    serviceDTO.setCalculateType(CalculateTypeEnum.PACKAGE_BUNDLING);
                    serviceDTO.setProductType(CalculateTypeEnum.PACKAGE_BUNDLING);
                    serviceDTO.setAvatarUrl(iGetSPDVInfoDTO.getAvatarUrl());
                    serviceDTO.setNumChildItems(iGetSPDVInfoDTO.getNumChildItems());
                    serviceDTO.setPrice(packageRepository.getPackagePriceById(serviceDTO.getServiceId()));
                    response.add(serviceDTO);
                    continue;
                }
            }

            Map<Long, List<IGetSPDVInfoDTO>> mapPricing = serviceEntry.getValue().stream()
                .collect(Collectors.groupingBy(IGetSPDVInfoDTO::getPricingUniqueId,
                    LinkedHashMap::new, // Dùng LinkedHashMap để giữ thứ tự
                    Collectors.toList()
                ));
            CalculateTypeEnum productTypeEnum = CalculateTypeEnum.PRICING;
            ServiceProductTypeEnum serviceProductTypeEnum = ServiceProductTypeEnum.SAAS;
            ProductClassificationEnum productClassificationEnum = ProductClassificationEnum.SERVICE;
            // set thông tin các gói cước của dịch vụ
            for (Entry<Long, List<IGetSPDVInfoDTO>> pricingEntry : mapPricing.entrySet()) {
                GetSPDVInfoDTO.PricingInfo pricingDTO = new GetSPDVInfoDTO.PricingInfo();
                for (IGetSPDVInfoDTO pricingItem : pricingEntry.getValue()) {
                    if (Objects.nonNull(pricingItem.getPricingId())) {
                        if (pricingItem.getPreOrderUrl() != null) {
                            serviceDTO.setPreOrderUrl(pricingItem.getPreOrderUrl());
                        }
                        PricingMultiPlanInfo multiPlanDTO = new PricingMultiPlanInfo();
                        multiPlanDTO.setPricingMultiplanId(pricingItem.getPricingMultiPlanId());
                        multiPlanDTO.setMultiPlanDraftId(pricingItem.getMultiPlanDraftId());
                        multiPlanDTO.setPaymentCycle(pricingItem.getPaymentCycle());
                        if (setDefaultPMPId.contains(pricingItem.getPricingMultiPlanId())) {
                            multiPlanDTO.setIsDefaultPMP(true);
                        }
                        // Lấy thông tin pricing
                        pricingDTO.setPricingId(pricingItem.getPricingId());
                        pricingDTO.setPricingDraftId(pricingItem.getPricingDraftId());
                        pricingDTO.setPricingName(pricingItem.getPricingName());
                        pricingDTO.setIsOneTime(pricingItem.getIsOneTime());
                        pricingDTO.setAvatarPath(pricingItem.getPricingAvatarUrl());
                        if (pricingDTO.getLstMultiPlan().stream()
                            .noneMatch(item -> Objects.equals(item.getPricingMultiplanId(), multiPlanDTO.getPricingMultiplanId())) &&
                            Objects.nonNull(multiPlanDTO.getPricingMultiplanId())) {
                            pricingDTO.getLstMultiPlan().add(multiPlanDTO);
                        }

                        if (Objects.nonNull(pricingItem.getPricingDefault())) {
                            pricingDTO.setIsDefault(pricingItem.getPricingDefault().equals(pricingItem.getPricingDraftId()));
                        }
                    }
                    // Lấy thông tin service
                    BeanUtils.copyProperties(pricingItem, serviceDTO);
                    serviceDTO.setServiceDraftId(pricingItem.getDraftId());
                    CalculateTypeEnum calculateType = CalculateTypeEnum.fromValue(pricingItem.getCalculateType());
                    switch (calculateType) {
                        case PRICING:
                            serviceDTO.setCalculateType(CalculateTypeEnum.PRICING);
                            productTypeEnum = CalculateTypeEnum.PRICING;
                            break;
                        case COMBO:
                            serviceDTO.setCalculateType(CalculateTypeEnum.COMBO);
                            productTypeEnum = CalculateTypeEnum.COMBO;
                        case SERVICE_GROUP:
                            break;
                        case PACKAGE_BUNDLING:
                            serviceDTO.setCalculateType(CalculateTypeEnum.PACKAGE_BUNDLING);
                            productTypeEnum = CalculateTypeEnum.PACKAGE_BUNDLING;
                            break;
                        default:
                            break;
                    }
                    productClassificationEnum = ObjectUtil.getOrDefault(ProductClassificationEnum.fromValue(pricingItem.getProductClassification()), ProductClassificationEnum.SERVICE);
                    serviceDTO.setProductClassification(productClassificationEnum);

                    // Nếu là gói bundling thì set thông tin số lượng item bên trong
                    if (Objects.equals(pricingItem.getCalculateType(), CalculateTypeEnum.PACKAGE_BUNDLING.getValue())) {
                        pricingDTO.setNumChildItems(pricingItem.getNumChildItems());
                    }
                }
                if (Objects.nonNull(pricingDTO.getPricingId())) {
                    serviceDTO.getLstPricing().add(pricingDTO);
                }
            }
            // Lấy số sub của user và dịch vụ khi multi sub mode bị tắt
            if (!serviceDTO.getAllowMultiSub()) {
                serviceDTO.setNumSub(shoppingCartRepository.getNumSubOfServiceUser(serviceDTO.getCalculateType() == CalculateTypeEnum.PRICING,
                        serviceDTO.getServiceId(), customerId));
            }
            // Theo pop-up mới của logic sản phẩm hàng hóa -> các sản phẩm có biến thể thì các gói cước sẽ được đi kèm với biến thể (không hiển thị riêng lẻ)
            Map<Long, GetSPDVInfoDTO.PricingInfo> mapResPricing = new LinkedHashMap<>();
            serviceDTO.getLstPricing().forEach(item -> mapResPricing.put(item.getPricingId(), item));
            // set DEVICE info
            if (Objects.equals(productClassificationEnum, ProductClassificationEnum.PHYSICAL)) {
                List<VariantResDTO> getVariant = listVariant.stream()
                    .filter(obj -> serviceDTO.getServiceId().equals(Long.valueOf(obj.getServiceId())))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(getVariant)) {
                    Set<Long> attributeIds = new HashSet<>();
                    List<AttributeValues> attributeValue = new ArrayList<>();
                    getVariant.forEach(detail -> {
                        // kiểm tra nếu biến thể không có gói cước nào -> không hiển thị theo logic spdv hàng hóa vật lý mới
                        if (ObjectUtils.isEmpty(detail.getListPricingId())) {
                            return;
                        }
                        // variant info
                        List<VariantAttributesValueConvertDTO> attributesValue = new Gson().fromJson(detail.getAttributesValue(),
                                new TypeToken<List<VariantAttributesValueConvertDTO>>() {}.getType());

                        VariantInfor variantDetail = new VariantInfor();
                        BeanUtils.copyProperties(detail, variantDetail);
                        variantDetail.setVariantId(detail.getId());
                        variantDetail.setVariantDraftId(detail.getVariantDraftId());
                        variantDetail.setStatus(VariantStatusEnum.stringValueOf(Objects.nonNull(detail.getStatus()) ? detail.getStatus() : VariantStatusEnum.STOCKING.value));
                        variantDetail.setVariantName(detail.getFullName());
                        variantDetail.setDefaultValue(!Objects.isNull(detail.getVariantDefault()) && detail.getVariantDefault() == 1);
                        variantDetail.setPrice(calculateVariantPrice(detail));
                        variantDetail.setLstAttributeId(!CollectionUtils.isEmpty(attributesValue) ?
                                attributesValue.stream()
                                        .filter(e -> !e.getValueName().equals(CharacterConstant.NO_SELECT))
                                        .map(VariantAttributesValueConvertDTO::getAttributesId).collect(Collectors.toList()) :
                                new ArrayList<>());
                        attributeIds.addAll(new LinkedHashSet<>(convertAttributesIdsStringToSet(detail.getAttributesIds())));

                        if (!CollectionUtils.isEmpty(attributesValue)) {
                            attributesValue.stream()
                                    .filter(e -> !e.getValueName().equals(CharacterConstant.NO_SELECT))
                                .forEach(item -> {
                                        AttributeValues variantAttributeValues = new AttributeValues();
                                    variantAttributeValues.setAttributesId(item.getAttributesId());
                                    variantAttributeValues.setUniqueCode(item.getUniqueCode() != null ? item.getUniqueCode() : "");
                                    variantAttributeValues.setValueName(item.getValueName());

                                    variantAttributeValues.setHexCode(item.getHexCode() != null ? item.getHexCode() : "");
                                        variantDetail.getAttributeValues().add(variantAttributeValues);

                                        if (CollectionUtils.isEmpty(attributeValue) ||
                                                (!CollectionUtils.isEmpty(attributeValue) && attributeValue.stream().noneMatch(e ->
                                                        e.getAttributesId().equals(variantAttributeValues.getAttributesId()) &&
                                                                e.getValueName().equals(variantAttributeValues.getValueName()) &&
                                                                e.getHexCode().equals(variantAttributeValues.getHexCode()) &&
                                                                e.getUniqueCode().equals(variantAttributeValues.getUniqueCode())
                                                ))) {
                                            attributeValue.add(variantAttributeValues);
                                        }
                                    });
                        }

                        // Lấy thông tin các giá trị thuộc tính thuộc biến thể mặc định
                        if (variantDetail.getDefaultValue()) {
                            var setDefaultValue = new HashSet<String>();
                            variantDetail.getAttributeValues().forEach(item -> setDefaultValue.add(item.getUniqueId()));
                            attributeValue.forEach(item -> {
                                if (setDefaultValue.contains(item.getUniqueId())) {
                                    item.setDefaultVariantAttributeValue(true);
                                }
                            });
                        }
                        // set các gói cước gắn với biến thể
                        List<GetSPDVInfoDTO.PricingInfo> variantPricingLst = new ArrayList<>();
                        detail.getListPricingId().forEach(pricingId -> {
                            GetSPDVInfoDTO.PricingInfo variantPricing = mapResPricing.get(pricingId);
                            if (Objects.nonNull(variantPricing)) {
                                variantPricingLst.add(variantPricing);
                            }
                        });
                        variantDetail.setPricingList(variantPricingLst);
                        if (ObjectUtils.isNotEmpty(variantPricingLst)) {
                            serviceDTO.getVariant().add(variantDetail);
                        }
                    });

                    // attribute info
                    List<LstServiceAttributeITF> getAttribute = listAttributes.stream().filter(obj ->
                            serviceDTO.getServiceId().equals(obj.getServiceId()) &&
                                    attributeIds.contains(obj.getAttributeId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(getAttribute)) {
                        getAttribute.forEach(detail -> {
                            List<AttributeValues> attributeValuesList = attributeValue.stream()
                                    .filter(e -> e.getAttributesId().equals(detail.getAttributeId()))
                                    .filter(e -> !e.getValueName().equals(CharacterConstant.NO_SELECT))
                                    .collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(attributeValuesList)) {
                                LstServiceAttributeDTO item = new LstServiceAttributeDTO();
                                BeanUtils.copyProperties(detail, item);

                                AttributeInfor attributeDetail = new AttributeInfor();
                                attributeDetail.setAttributeId(item.getAttributeId());
                                attributeDetail.setAttributeName(item.getName());
                                attributeDetail.setNameId(item.getNameId());

                                attributeDetail.setAttributeValues(attributeValue.stream()
                                        .filter(e -> e.getAttributesId().equals(detail.getAttributeId()))
                                        .collect(Collectors.toList()));
                                serviceDTO.getAttribute().add(attributeDetail);
                            }
                        });
                    }
                    productTypeEnum = CalculateTypeEnum.VARIANT;
                    // Không hiển thị các gói cước riêng lẻ khi sản phẩm có biến thể
                    serviceDTO.setLstPricing(Collections.emptyList());
                } else {
                    productTypeEnum = CalculateTypeEnum.DEVICE_NO_VARIANT;
                }
            }
            serviceDTO.setProductType(productTypeEnum);
            if (ObjectUtils.isNotEmpty(serviceDTO.getLstPricing()) || ObjectUtils.isNotEmpty(serviceDTO.getVariant())) {
                response.add(serviceDTO);
            }
        }
        // Điền thông tin giá SPDV
        fillPriceInfo(response);
        // pageable
        return new PageImpl<>(response, pageable, mapService.size());
    }

    private void fillPriceInfo(List<GetSPDVInfoDTO> lstDTO) {
        lstDTO.forEach(serviceDTO -> {
            if (serviceDTO.getCalculateType() == CalculateTypeEnum.PRICING) { // Dịch vụ hoặc Thiết bị
                // Thiết bị không biến thể
                if (Objects.equals(serviceDTO.getProductType(), CalculateTypeEnum.DEVICE_NO_VARIANT)) {
                    serviceDTO.setPrice(servicesService.getServicePreTaxAmount(serviceDTO.getServiceId()));
                }

                // fill giá của pricing
                if (ObjectUtils.isNotEmpty(serviceDTO.getLstPricing())) {
                    // Gói dịch vụ
                    serviceDTO.getLstPricing().forEach(pricingInfo -> {
                        if (pricingInfo.getLstMultiPlan().isEmpty()) {
                            pricingInfo.setPrice(pricingService.getPricingPreTaxAmount(pricingInfo.getPricingId(), null).getPrice());
                        } else {
                            pricingInfo.getLstMultiPlan().forEach(pricingMultiPlanInfo ->
                                    pricingMultiPlanInfo.setPrice(pricingService.getPricingPreTaxAmount(pricingInfo.getPricingId(),
                                        pricingMultiPlanInfo.getPricingMultiplanId()).getPrice())
                                );
                        }
                    });
                } else if (ObjectUtils.isNotEmpty(serviceDTO.getVariant())) { // trường hợp gói cước nằm trong biến thể
                    serviceDTO.getVariant().forEach(variantDetail -> variantDetail.getPricingList().forEach(pricingInfo -> {
                        if (pricingInfo.getLstMultiPlan().isEmpty()) {
                            pricingInfo.setPrice(pricingService.getPricingPreTaxAmount(pricingInfo.getPricingId(), null).getPrice());
                        } else {
                            pricingInfo.getLstMultiPlan().forEach(pricingMultiPlanInfo ->
                                    pricingMultiPlanInfo.setPrice(pricingService.getPricingPreTaxAmount(pricingInfo.getPricingId(),
                                        pricingMultiPlanInfo.getPricingMultiplanId()).getPrice())
                                );
                        }
                    }));
                }

            } else if (serviceDTO.getCalculateType() == CalculateTypeEnum.COMBO) { // Combo
                serviceDTO.getLstPricing()
                    .forEach(pricingInfo -> pricingInfo.setPrice(comboPlanRepository.getPreTaxAmount(pricingInfo.getPricingId())));
            } else if (serviceDTO.getCalculateType() == CalculateTypeEnum.PACKAGE_BUNDLING) {
                // TH package nằm trong các solution <-> root type = SOLUTION
                if (Objects.equals(serviceDTO.getRootType(), ObjectTypeEnum.SOLUTION.name())) {
                    serviceDTO.getLstPricing().forEach(pricingInfo -> pricingInfo.setPrice(packageRepository.getPackagePriceById(pricingInfo.getPricingId())));
                }
            }
        });
    }

    /**
     * Tính giá tiền của biến thể, có áp dụng chiết khấu
     */
    private BigDecimal calculateVariantPrice(VariantResDTO detail) {
        BigDecimal variantPrice = ObjectUtil.getOrDefault(detail.getVariantExtraPrice(), BigDecimal.ZERO);
        if (detail.getDiscount() != null) {
            Date now = new Date();
            // Nếu khuyến mại còn hạn
            if (detail.getDiscountStart() == null || detail.getDiscountStart().compareTo(now) <= 0) {
                if (detail.getDiscountEnd() == null || detail.getDiscountEnd().compareTo(now) >= 0) {
                    if (Objects.equals(detail.getDiscountType(), 1)) { // Khuyến mại số tiền
                        return variantPrice.subtract(ObjectUtil.getOrDefault(detail.getDiscount(), BigDecimal.ZERO));
                    } else {
                        BigDecimal discountValue = variantPrice.multiply(ObjectUtil.getOrDefault(detail.getDiscount(), BigDecimal.ZERO))
                            .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP);
                        return variantPrice.subtract(discountValue);
                    }
                }
            }
        }
        return variantPrice;
    }

    @Override
    public ShoppingCartDetailDTO getSPDVDetail(Boolean isService, Long serviceId, Long pricingId, Long pricingMultiPlanId) {
        PricingBriefDTO pricingBriefDTO = new PricingBriefDTO();
        pricingBriefDTO.setPricingId(pricingId);
        pricingBriefDTO.setPricingMultiPlanId(pricingMultiPlanId);
        ServiceBriefDTO serviceBriefDTO = new ServiceBriefDTO();
        serviceBriefDTO.setIsService(isService);
        serviceBriefDTO.setProductType(Boolean.TRUE.equals(isService) ? CalculateTypeEnum.PRICING : CalculateTypeEnum.COMBO);
        serviceBriefDTO.setServiceId(serviceId);
        serviceBriefDTO.setType(SubscriptionConstant.DETAIL_SUB);
        serviceBriefDTO.getLstPricing().add(pricingBriefDTO);
        ShoppingCartBriefDTO briefDTO = new ShoppingCartBriefDTO();
        briefDTO.getLstProduct().add(serviceBriefDTO);
        return getCartDetailFromBrief(briefDTO);
    }

    @Override
    public Long updateCart(Long cartId, ShoppingCartBriefDTO request) {
        // Loại bỏ các item rác
        request.getLstProduct().removeIf(item -> item == null ||
            (item.getServiceId() == null && !Objects.equals(item.getProductType(), CalculateTypeEnum.PACKAGE_BUNDLING)) ||
            (item.getPackageId() == null && Objects.equals(item.getProductType(), CalculateTypeEnum.PACKAGE_BUNDLING)) ||
            (Objects.isNull(item.getVariantId()) && CollectionUtils.isEmpty(item.getLstPricing()) &&
                Objects.equals(item.getProductType(), CalculateTypeEnum.PRICING)));

        // Loại bỏ các bundling trùng nhau packageId, và solutionId giống nhau
        cleanBundlingCart(request);

        List<Long> lstServiceId = request.getLstProduct().stream().map(ServiceBriefDTO::getServiceId).collect(Collectors.toList()); // nếu set serviceDraft r thì bỏ qua
        Map<Long, ServiceEntity> mapService = serviceRepository.findAllByIdIn(lstServiceId).stream()
            .collect(Collectors.toMap(ServiceEntity::getId, Function.identity()));
        for (ServiceBriefDTO serviceDTO : request.getLstProduct()) {
            ServiceEntity serviceEntity = mapService.get(serviceDTO.getServiceId());
            if (Objects.nonNull(serviceEntity) && Objects.isNull(serviceDTO.getServiceDraftId())) {
                serviceDTO.setServiceDraftId(serviceEntity.getServiceDraftId()); // set phiên bản mới nhất khi thêm vào giỏ hàng
            }
            if (!CollectionUtils.isEmpty(serviceDTO.getLstPricing())) {
                serviceDTO.getLstPricing().removeIf(item -> item == null || item.getPricingId() == null);
                for (PricingBriefDTO pricingDTO : serviceDTO.getLstPricing()) {
                    pricingDTO.getLstAddon().removeIf(item -> item == null || item.getAddonId() == null);
                }
            }
            if (Objects.nonNull(serviceDTO.getType())) {
                serviceDTO.setType(GET_CART);
            }
        }
        //
        Long userId = AuthUtil.getCurrentUserId();
        ShoppingCart shoppingCart;
        if (cartId != null) { // Nếu truyền cartId, cập nhật giỏ hàng qua cartId
            shoppingCart = shoppingCartRepository.findById(cartId).orElse(null);
            if (shoppingCart != null) { // Nếu giỏ hàng đã tồn tại
                // Nếu giỏ hàng có nội dung rỗng, không thuộc về một user nào, thực hiện xóa giỏ hàng đó
                if (shoppingCart.getUserId() == null && request.getLstProduct().isEmpty()) {
                    shoppingCartRepository.delete(shoppingCart);
                    return -1L;
                }
                shoppingCart.setCartContent(ObjectMapperUtil.toJsonString(request));
            } else { // Nếu giỏ hàng chưa tồn tại
                throw exceptionFactory.badRequest(MessageKeyConstant.NOT_FOUND, Resources.SHOPPING_CART, ErrorKey.ID, String.valueOf(cartId));
            }
        } else if (userId != null) { // Nếu không truyền cartId, đồng thời đang đăng nhập, cập nhật giỏ hàng của user
            shoppingCart = shoppingCartRepository.findLastByUserId(userId);
            if (shoppingCart == null) {
                shoppingCart = ShoppingCart.builder()
                    .userId(userId)
                    .cartContent(ObjectMapperUtil.toJsonString(request))
                    .build();
            } else {
                shoppingCart.setCartContent(ObjectMapperUtil.toJsonString(request));
            }
        } else { // Nếu không truyền cartId, đồng thời chưa đăng nhập, tạo mới giỏ hàng
            if (request.getLstProduct().isEmpty()) {
                throw exceptionFactory.badRequest(ShoppingCartError.EMPTY_CONTENT, Resources.SHOPPING_CART,
                    ErrorKey.CART_CONTENT); // Không tạo mới các giỏ hàng rỗng
            }
            shoppingCart = ShoppingCart.builder()
                .cartContent(ObjectMapperUtil.toJsonString(request))
                .build();
        }
        return shoppingCartRepository.save(shoppingCart).getId();
    }

    @Override
    public Integer getCartItemCount(Long cartId) {
        ShoppingCart shoppingCart = getShoppingCartInfo(cartId);
        if (shoppingCart == null || shoppingCart.getCartContent() == null) {
            return 0;
        }
        ShoppingCartBriefDTOOld briefDTO = ObjectMapperUtil.mapping(shoppingCart.getCartContent(), ShoppingCartBriefDTOOld.class);
        if (briefDTO == null || briefDTO.getLstProduct() == null) {
            return 0;
        } else {
            return briefDTO.getLstProduct().size();
        }
    }

    private ShoppingCart getShoppingCartInfo(Long cartId) {
        Long userId = AuthUtil.getCurrentUserId();
        ShoppingCart shoppingCart = null;
        if (cartId != null) { // Nếu truyền cartId, lấy giỏ hàng từ cartId
            shoppingCart = shoppingCartRepository.findById(cartId).orElse(null);
        } else if (userId != null) { // Nếu không truyền cartId, đồng thời đang đăng nhập, lấy giỏ hàng từ user
            shoppingCart = shoppingCartRepository.findLastByUserId(userId);
        }
        return shoppingCart;
    }

    @Override
    public ShoppingCartBriefDTO getCart(Long cartId) {
        ShoppingCart shoppingCart = getShoppingCartInfo(cartId);
        if (shoppingCart == null || shoppingCart.getCartContent() == null) {
            return new ShoppingCartBriefDTO();
        }
        ShoppingCartBriefDTOOld briefDTO = ObjectMapperUtil.mapping(shoppingCart.getCartContent(), ShoppingCartBriefDTOOld.class);
        ShoppingCartBriefDTO briefDTONew = new ShoppingCartBriefDTO();
        if (briefDTO == null || briefDTO.getLstProduct() == null) {
            return new ShoppingCartBriefDTO();
        } else {
            List<ServiceBriefDTO> lstProduct = briefDTO.getLstProduct().stream().map(e -> {
                // Cập nhật variantId nếu đang null
                if (Objects.isNull(e.getVariantId()) && Objects.nonNull(e.getVariantDraftId())) {
                    e.setVariantId(variantDraftRepository.getLatestVariantIdByDraftId(e.getVariantDraftId()));
                }
                ServiceBriefDTO serviceBriefDTO = new ServiceBriefDTO();
                BeanUtils.copyProperties(e, serviceBriefDTO);
                serviceBriefDTO.setType(GET_CART);
                return serviceBriefDTO;
            }).collect(Collectors.toList());
            briefDTONew.setLstProduct(lstProduct);
        }
        // Cập nhật thông tin brief trong giỏ hàng
        refreshCart(briefDTONew);
        return briefDTONew;
    }

    /**
     * Cập nhật thông tin trong brief của giỏ hàng.
     *
     * @implNote Thực hiện các cập nhật sau:
     * <ul>
     *     <li>Loại bỏ các item rác.</li>
     *     <li>Bổ sung index (service/pricing/addon).</li>
     *     <li>Bổ sung thông tin version mới nhất của service/pricing.</li>
     *     <li>Bổ sung addon bắt buộc.</li>
     * </ul>
     */
    private void refreshCart(ShoppingCartBriefDTO briefDTO) {
        if (briefDTO == null) {
            return;
        }
        // Loại bỏ item rác
        briefDTO.getLstProduct().removeIf(item -> item == null ||
            (!item.getProductType().equals(CalculateTypeEnum.PACKAGE_BUNDLING) && (Objects.isNull(item.getServiceId()))) ||
            (item.getProductType().equals(CalculateTypeEnum.PACKAGE_BUNDLING) && (Objects.isNull(item.getPackageId()))) ||
            (item.getProductType().equals(CalculateTypeEnum.PRICING) && Objects.isNull(item.getVariantId()) && ObjectUtils.isEmpty(item.getLstPricing()))
        );

        // Loại bỏ các item cart của bundling có solutionId và packageId giống nhau
        cleanBundlingCart(briefDTO);

        for (ServiceBriefDTO serviceBriefDTO : briefDTO.getLstProduct()) {
            if (Objects.isNull(serviceBriefDTO.getProductType())) {
                serviceBriefDTO.setProductType(CalculateTypeEnum.UNSET);
            }
            serviceBriefDTO.getLstPricing().removeIf(item -> item == null || item.getPricingId() == null); // Loại bỏ item rác
            if (serviceBriefDTO.getIdx() == null) {
                serviceBriefDTO.setIdx(getNextIndex(0));
            }
            // Lấy version service mới nhất
            if (!serviceBriefDTO.getProductType().equals(CalculateTypeEnum.SERVICE_GROUP) && !serviceBriefDTO.getProductType().equals(CalculateTypeEnum.PACKAGE_BUNDLING)  ) {
                getLatestServiceVersion(serviceBriefDTO);
            }
            if (!CollectionUtils.isEmpty(serviceBriefDTO.getLstPricing()) && Objects.nonNull(serviceBriefDTO.getIsService())) {
                for (PricingBriefDTO pricingBriefDTO : serviceBriefDTO.getLstPricing()) {
                    pricingBriefDTO.getLstAddon().removeIf(item -> item == null || item.getAddonId() == null); // Loại bỏ item rác
                    // Lấy version gói mới nhất
                    boolean isInvalidState = getLatestPricingVersion(serviceBriefDTO.getIsService(), pricingBriefDTO);
                    if (isInvalidState) {
                        // Nếu gói không tồn tại hoặc đã bị xóa, bỏ qua việc lấy thông tin addon
                        continue;
                    }
                    //
                    if (pricingBriefDTO.getIdx() == null) {
                        pricingBriefDTO.setIdx(getNextIndex(1));
                    }
                    pricingBriefDTO.setParentIdx(serviceBriefDTO.getIdx());
                    //
                    Long pricingId = pricingBriefDTO.getPricingId();
                    Long pricingMultiPlanId = pricingBriefDTO.getPricingMultiPlanId();
                    /*
                     * Bổ sung thông tin các addon còn thiếu
                     */
                    List<AddonBriefDTO> lstAllAddon;
                    List<AddonBriefDTO> lstCurrentAddon = pricingBriefDTO.getLstAddon();
                    if (Boolean.TRUE.equals(serviceBriefDTO.getIsService())) {
                        if (pricingMultiPlanId == null) {
                            lstAllAddon = pricingAddonRepository.findAllLatestByPricingId(pricingId).stream()
                                    .map(AddonBriefDTO::new).collect(Collectors.toList());
                        } else {
                            lstAllAddon = pricingMultiPlanAddonRepository.findAllLatestByPricingMultiPlanId(pricingMultiPlanId).stream()
                                    .map(AddonBriefDTO::new).collect(Collectors.toList());
                        }
                    } else {
                        lstAllAddon = comboAddonRepository.findAllLatestByComboPlanId(pricingId).stream()
                                .map(AddonBriefDTO::new).collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(lstAllAddon)) { // remove những thằng khác customerType
                        Long currentUserId = AuthUtil.getCurrentUserId();
                        if(currentUserId != null){
                            String customerTypeCurrent = userRepository.getCustomerTypeById(currentUserId);
                            lstAllAddon.removeIf(addon -> !addon.getCustomerTypeCode().contains(customerTypeCurrent));
                        }
                        lstAllAddon.forEach(item -> lstCurrentAddon.stream()
                                .filter(current -> Objects.equals(current.getAddonId(), item.getAddonId()) &&
                                        Objects.equals(current.getAddonMultiPlanId(), item.getAddonMultiPlanId()))
                                .findFirst().ifPresent(current -> {
                                    item.setIdx(current.getIdx());
                                    item.setQuantity(current.getQuantity());
                                    item.setSelected(current.getSelected());
                                }));
                        for (AddonBriefDTO addonBriefDTO : lstAllAddon) {
                            if (addonBriefDTO.getIdx() == null) {
                                addonBriefDTO.setIdx(getNextIndex(2));
                            }
                            addonBriefDTO.setParentIdx(pricingBriefDTO.getIdx());
                        }
                        pricingBriefDTO.setLstAddon(lstAllAddon);
                    }
                }
            }
        }
    }

    /**
     * Loại bỏ các item gói bundling có cùng packageId và solutionId
     */
    private void cleanBundlingCart(ShoppingCartBriefDTO briefDTO) {
        Set<Pair<Long, Long>> bundlingSet = new HashSet<>();
        briefDTO.getLstProduct().removeIf(item -> {
            if (Objects.equals(item.getCalculateType(), CalculateTypeEnum.PACKAGE_BUNDLING)) {
                if (Objects.isNull(item.getPackageId())) {
                    return true;
                }
                Pair<Long, Long> itemPairKey = Pair.of(item.getPackageId(), ObjectUtil.getOrDefault(item.getSolutionId(), -1L));
                // nếu đã có item tương tự với item bundling hiện tại trong giỏ hàng => remove item hiện tại và next loop
                return !bundlingSet.add(itemPairKey);
            } else {
                return false;
            }
        });
    }

    private void getLatestServiceVersion(ServiceBriefDTO serviceBriefDTO) {
        Long oldServiceId = serviceBriefDTO.getServiceId();
        if (Boolean.TRUE.equals(serviceBriefDTO.getIsService())) {
            if (serviceRepository.getServiceDraftId(oldServiceId) != null) {
                serviceBriefDTO.setServiceId(serviceRepository.getLatestServiceId(oldServiceId));
            }
        } else {
            serviceBriefDTO.setServiceId(comboRepository.getLatestComboId(oldServiceId));
        }
    }

    private boolean getLatestPricingVersion(boolean isService, PricingBriefDTO pricingBriefDTO) {
        Long oldPricingId = pricingBriefDTO.getPricingId();
        Long oldPricingMultiPlanId = pricingBriefDTO.getPricingMultiPlanId();
        boolean isInvalidState = false;
        if (isService) {
            Long newPricingId = pricingRepository.getLastestPricingId(oldPricingId);
            if (Objects.isNull(newPricingId)) {
                pricingBriefDTO.setPricingId(oldPricingId);
                isInvalidState = true;
            } else {
                pricingBriefDTO.setPricingId(newPricingId);
            }
            if (oldPricingMultiPlanId != null) {
                Long newPricingMultiPlanId = pricingMultiPlanRepository.getUpgradeVersionPmpId(oldPricingMultiPlanId);
                if (Objects.isNull(newPricingMultiPlanId)) {
                    isInvalidState = false;
                } else {
                    pricingBriefDTO.setPricingMultiPlanId(newPricingMultiPlanId);
                }
            }
        } else {
            pricingBriefDTO.setPricingId(comboPlanRepository.getLatestComboPlanId(oldPricingId));
        }
        return isInvalidState;
    }

    @Override
    public ShoppingCartDetailDTO getDetail(Long cartId) {
        ShoppingCartBriefDTO briefDTO = getCart(cartId);
        ShoppingCartDetailDTO detailDTO = getCartDetailFromBrief(briefDTO);
        List<Long> serviceIds = detailDTO.getLstProduct().stream().map(ServiceDetailDTO::getServiceId).collect(Collectors.toList());
        List<AttributesDTO> listAttributeInServices = attributesService.getListAttributeInServices(serviceIds);
        List<VariantResponseDTO> lstVariantInServices = variantService.getListVariantInServices(serviceIds);
        detailDTO.setLstAttributeInServices(listAttributeInServices);
        detailDTO.setLstVariantInServices(lstVariantInServices);
        // Lưu lại thông tin sau khi lấy chi tiết
        ShoppingCart shoppingCart = null;
        if (cartId == null) {
            Long userId = AuthUtil.getCurrentUserId();
            if (userId != null) {
                shoppingCart = shoppingCartRepository.findLastByUserId(userId);
            }
        } else {
            shoppingCart = shoppingCartRepository.findById(cartId).orElse(null);
        }
        if (shoppingCart != null) {
            shoppingCart.setCartContent(ObjectMapperUtil.toJsonString(detailDTO));
            shoppingCartRepository.save(shoppingCart);
        }
        return detailDTO;
    }

    @Override
    public ShoppingCartDetailDTO getCartDetailFromBrief(ShoppingCartBriefDTO briefDTO) {
        if (briefDTO == null || briefDTO.getLstProduct() == null) {
            return new ShoppingCartDetailDTO();
        }
        // Cập nhật thông tin brief trong giỏ hàng
        refreshCart(briefDTO);
        //
        Map<Long, String> mapUnit = unitRepository.getAllUnit().stream().collect(Collectors.toMap(ICommonIdName::getId, ICommonIdName::getName));
        BigDecimal priceTotal = BigDecimal.ZERO;
        ShoppingCartDetailDTO response = new ShoppingCartDetailDTO();
        for (ServiceBriefDTO serviceBriefDTO : briefDTO.getLstProduct()) {
            ServiceDetailDTO serviceDetailDTO;
            if (serviceBriefDTO.getProductType().equals(CalculateTypeEnum.SERVICE_GROUP)) { // TH NHÓM DỊCH VỤ
                serviceDetailDTO = setDataCartDetailForServiceGroup(serviceBriefDTO.getUserId(), serviceBriefDTO.getServiceId(),
                    response.getLstError(), mapUnit);
            } else if (serviceBriefDTO.getProductType().equals(CalculateTypeEnum.PACKAGE_BUNDLING)) {
                // Xử lý cho case mua gói bundle
                Map<Long, List<AddonBriefDTO>> mapAddon = serviceBriefDTO.getLstPricing().stream()
                    .filter(item -> Objects.nonNull(item.getPackageItemId()))
                    .collect(Collectors.toMap(PricingBriefDTO::getPackageItemId, PricingBriefDTO::getLstAddon));
                serviceDetailDTO = setDataCartForBundling(serviceBriefDTO, response.getLstError(), mapUnit, mapAddon);
            } else {
                if(serviceBriefDTO.getServiceId() == null && serviceBriefDTO.getServiceDraftId() == null && serviceBriefDTO.getVariantId() == null){
                    serviceDetailDTO = null;
                }else{
                    serviceDetailDTO = setDataCartDetail(serviceBriefDTO, response.getLstError(), CalculateTypeEnum.UNSET, mapUnit);
                }
            }
            if(serviceDetailDTO != null) {
                response.getLstProduct().add(serviceDetailDTO);
                serviceDetailDTO.setSelected(serviceBriefDTO.getSelected());
                if (serviceDetailDTO.getSelected()) {
                    priceTotal = priceTotal.add(serviceDetailDTO.getTotalPrice());
                }
            }
        }
        response.setPrice(priceTotal);
        return response;
    }

    private ServiceDetailDTO setDataCartDetailForServiceGroup(Long userId, Long serviceGroupId, List<CartErrorDTO> lstError,
        Map<Long, String> mapUnit) {

        List<ServiceDetailDTO> lstServiceGroupDetailDTO = new ArrayList<>();
        BigDecimal priceByServiceGroup = BigDecimal.ZERO;
        // Thông tin nhóm DV
        ServiceGroup serviceGroup = serviceGroupRepository.findById(serviceGroupId).orElse(null);
        List<ServiceGroupPricing> serviceGroupPricingList = serviceGroupPricingRepository.findAllByServiceGroupId(serviceGroupId);
        List<ServiceGroupPricingItem> serviceGroupPricingItemList = serviceGroupPricingItemRepository.findAllByServiceGroupPricingIdIn(
                serviceGroupPricingList.stream().map(ServiceGroupPricing::getId).collect(Collectors.toList()));
        // validate
        boolean isValidate = Arrays.asList(PortalType.DEV, PortalType.ADMIN).contains(AuthUtil.getPortalOfUserRoles());
        if (isValidate) {
            if (Objects.isNull(serviceGroup) || serviceGroup.getDeletedFlag() == 0 || serviceGroup.getStatus() == 0
                    || CollectionUtils.isEmpty(serviceGroupPricingList)) {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_EXIST_SERVICE, subscriptionMessage, LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.SERVICE_GROUP, MessageKeyConstant.NOT_EXIST_SERVICE);
            } else if (serviceGroup.getStatus() == 0) {
                String message = messageSource.getMessage(MessageKeyConstant.SERVICE_NOT_ACTIVE, subscriptionMessage, LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.SERVICE_GROUP, MessageKeyConstant.SERVICE_NOT_ACTIVE);
            }
            // check multisub
            Integer numSubGroup = shoppingCartRepository.getNumSubOfUserNew(4, serviceGroupId, Objects.nonNull(userId) ? userId : AuthUtil.getCurrentUserId());
            if (Objects.nonNull(numSubGroup) && numSubGroup > 0) {
                String message = messageSource.getMessage(MessageKeyConstant.SERVICE_GROUP_DISABLED_MULTI_SUB, subscriptionMessage,
                        LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICE_GROUP, ErrorKey.SERVICE_GROUP, MessageKeyConstant.SERVICE_GROUP_DISABLED_MULTI_SUB);
            }
        } else if (Objects.isNull(serviceGroup) || serviceGroup.getDeletedFlag() == 0 || serviceGroup.getStatus() == 0
                || CollectionUtils.isEmpty(serviceGroupPricingList)) {
            return null;
        }
        // tạo requestDTO từ mỗi item trong nhóm DV -> truyền vào lấy chi tiết từ HÀM CŨ
        ServiceDetailDTO serviceDetailDTO = new ServiceDetailDTO();
        String idx = getNextIndex(0);
        serviceDetailDTO.setIdx(idx);
        for (ServiceGroupPricing serviceGroupPricing : serviceGroupPricingList) {
            ServiceBriefDTO serviceBriefDTO = new ServiceBriefDTO();
            serviceBriefDTO.setIsService(true);
            serviceBriefDTO.setSelected(true);
            serviceBriefDTO.setProductType(CalculateTypeEnum.PRICING);
            serviceBriefDTO.setCalculateType(CalculateTypeEnum.PRICING);
            serviceBriefDTO.setUserId(userId);
            //
            List<ServiceGroupPricingItem> serviceGroupSubs = serviceGroupPricingItemList.stream()
                    .filter(e -> e.getServiceGroupPricingId().equals(serviceGroupPricing.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(serviceGroupSubs)) continue;
            ServiceGroupPricingItem serviceGroupPricingItemDeviceNoVariant =
                    !CollectionUtils.isEmpty(serviceGroupSubs.stream().filter(e -> e.getObjectType().equals(ServiceGroupPricingObjectTypeEnum.DEVICE_NO_VARIANT.name())).collect(Collectors.toList())) ?
                            serviceGroupSubs.stream().filter(e -> e.getObjectType().equals(ServiceGroupPricingObjectTypeEnum.DEVICE_NO_VARIANT.name())).collect(Collectors.toList()).get(0) :
                            null;
            ServiceGroupPricingItem serviceGroupPricingItemDeviceVariant =
                    !CollectionUtils.isEmpty(serviceGroupSubs.stream().filter(e -> e.getObjectType().equals(ServiceGroupPricingObjectTypeEnum.DEVICE_VARIANT.name())).collect(Collectors.toList())) ?
                            serviceGroupSubs.stream().filter(e -> e.getObjectType().equals(ServiceGroupPricingObjectTypeEnum.DEVICE_VARIANT.name())).collect(Collectors.toList()).get(0) :
                            null;
            ServiceGroupPricingItem serviceGroupPricingItemPricing =
                    !CollectionUtils.isEmpty(serviceGroupSubs.stream().filter(e -> e.getObjectType().equals(ServiceGroupPricingObjectTypeEnum.PRICING.name())).collect(Collectors.toList())) ?
                            serviceGroupSubs.stream().filter(e -> e.getObjectType().equals(ServiceGroupPricingObjectTypeEnum.PRICING.name())).collect(Collectors.toList()).get(0) :
                            null;
            if (Objects.nonNull(serviceGroupPricingItemDeviceNoVariant)) {
                // nếu tồn tại biến thể => bắt buộc mua biến thể
                if (variantDraftRepository.checkExistsVariant(serviceGroupPricingItemDeviceNoVariant.getObjectId())) {
                    String message = messageSource.getMessage(MessageKeyConstant.NOT_EXIST_VARIANT, subscriptionMessage,
                        LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.VARIANT, ErrorKey.SERVICE, MessageKeyConstant.NOT_EXIST_VARIANT);
                }
                serviceBriefDTO.setVariantId(null);
                serviceBriefDTO.setServiceId(serviceGroupPricingItemDeviceNoVariant.getObjectId());
                serviceBriefDTO.setQuantityServiceGroup(Math.toIntExact(serviceGroupPricingItemDeviceNoVariant.getQuantity()));
                serviceBriefDTO.setAmountUpdateServiceGroup(serviceGroupPricingItemDeviceNoVariant.getAmountUpdate());
            }
            if (Objects.nonNull(serviceGroupPricingItemDeviceVariant)) {
                if (isValidate && Objects.isNull(serviceGroupPricingItemDeviceVariant.getObjectDraftId())) {
                    String message = messageSource.getMessage(MessageKeyConstant.SERVICE_GROUP_UNAVAILABLE_DEVICE_PRODUCT, subscriptionMessage,
                            LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.SERVICE_GROUP, ErrorKey.SERVICE_GROUP, MessageKeyConstant.SERVICE_GROUP_UNAVAILABLE_DEVICE_PRODUCT);
                }
                Variant variant = variantRepository.getVariantApprovedByVariantDraftId(serviceGroupPricingItemDeviceVariant.getObjectDraftId());
                if (isValidate && (Objects.isNull(variant) ||
                        (Objects.nonNull(variant.getStatus())
                                && !variant.getStatus().equals(VariantStatusEnum.STOCKING.value)))) {
                    String message = messageSource.getMessage(MessageKeyConstant.SERVICE_GROUP_UNAVAILABLE_DEVICE_PRODUCT, subscriptionMessage,
                            LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.SERVICE_GROUP, ErrorKey.SERVICE_GROUP, MessageKeyConstant.SERVICE_GROUP_UNAVAILABLE_DEVICE_PRODUCT);
                }
                serviceBriefDTO.setVariantId(variant.getId());
                serviceBriefDTO.setServiceId(variant.getServiceId());
                serviceBriefDTO.setQuantityServiceGroup(Math.toIntExact(serviceGroupPricingItemDeviceVariant.getQuantity()));
                serviceBriefDTO.setAmountUpdateServiceGroup(serviceGroupPricingItemDeviceVariant.getAmountUpdate());
            }
            if (Objects.nonNull(serviceGroupPricingItemPricing)) {
                Pricing pricingOld = pricingRepository.findById(serviceGroupPricingItemPricing.getObjectId()).orElse(null);
                if (Objects.nonNull(pricingOld)) {
                    Pricing pricingNewest = pricingRepository.findFirstByPricingDraftIdAndDeletedFlagAndApproveAndStatusOrderByIdDesc(
                            pricingOld.getPricingDraftId(), DeletedFlag.NOT_YET_DELETED.getValue(), ApproveStatusEnum.APPROVED.value, StatusEnum.ACTIVE.value);
                    if (isValidate && Objects.isNull(pricingNewest)) {
                        String message = messageSource.getMessage(MessageKeyConstant.NOT_EXIST_SERVICE, subscriptionMessage,
                                LocaleContextHolder.getLocale());
                        throw new BadRequestException(message, Resources.SERVICES, ErrorKey.SERVICE, MessageKeyConstant.NOT_EXIST_SERVICE);
                    }
                    List<PricingBriefDTO> lstPricing = new ArrayList<>();
                    PricingBriefDTO pricingBriefDTO = new PricingBriefDTO();
                    pricingBriefDTO.setPricingId(pricingNewest.getId());
                    Long pricingMultiPlanId = getPricingMultiPlanNewest(serviceGroupPricingItemPricing.getPricingMultiPlanId());
                    pricingBriefDTO.setPricingMultiPlanId(pricingMultiPlanId);
                    pricingBriefDTO.setQuantityServiceGroup(Math.toIntExact(serviceGroupPricingItemPricing.getQuantity()));
                    pricingBriefDTO.setAmountUpdateServiceGroup(serviceGroupPricingItemPricing.getAmountUpdate());
                    pricingBriefDTO.setSelected(Boolean.TRUE);
                    lstPricing.add(pricingBriefDTO);
                    //
                    serviceBriefDTO.setLstPricing(lstPricing);
                    serviceBriefDTO.setServiceId(pricingNewest.getServiceId());
                }
            }
            // Xử lý theo hàm cũ
            serviceBriefDTO.setIdx(getNextIndex(0));
            serviceBriefDTO.setParentIdx(idx);
            ServiceDetailDTO serviceGroupItemDetailDTO = setDataCartDetail(serviceBriefDTO, lstError, CalculateTypeEnum.SERVICE_GROUP, mapUnit);
            serviceGroupItemDetailDTO.setServiceGroupPricingId(serviceGroupPricing.getId());
            //
            lstServiceGroupDetailDTO.add(serviceGroupItemDetailDTO);
            // add tổng tiền trong sub
            priceByServiceGroup = priceByServiceGroup.add(serviceGroupItemDetailDTO.getTotalPrice());
        }
        serviceDetailDTO.setLstServiceGroupDetailDTO(lstServiceGroupDetailDTO);
        serviceDetailDTO.setTotalPrice(priceByServiceGroup);
        serviceDetailDTO.setProductType(CalculateTypeEnum.SERVICE_GROUP);
        // set info
        IServiceInfo info = null;
        if (Objects.nonNull(serviceGroup) && Objects.nonNull(serviceGroup.getId())) {
            info = shoppingCartRepository.getServiceInfoByServiceGroupId(serviceGroup.getId());
        }
        if (Objects.nonNull(info)) {
            BeanUtils.copyProperties(info, serviceDetailDTO);
            serviceDetailDTO.setPaymentMethods(Collections.singleton(PaymentMethodEnum.fromValue(info.getPaymentMethod())));
        }
        IFileAttachResponse avatar = fileAttachRepository.getAvatarById(serviceGroup.getIconId());
        serviceDetailDTO.setAvatar(Objects.nonNull(avatar) ? avatar : null);
        return serviceDetailDTO;
    }

    /**
     * Hàm set thông tin giỏ hàng cho các gói bundling
     */

    private ServiceDetailDTO setDataCartForBundling(ServiceBriefDTO serviceBriefDTO, List<CartErrorDTO> lstError,
        Map<Long, String> mapUnit, Map<Long, List<AddonBriefDTO>> mapAddon) {
        Long packageId = serviceBriefDTO.getPackageId();
        Long solutionId = serviceBriefDTO.getSolutionId();
        Long userId = serviceBriefDTO.getUserId();
        ServiceDetailDTO rootPackage = new ServiceDetailDTO();
        String rootIndex = "PACKAGE_" + UUID.randomUUID();
        rootPackage.setIdx(rootIndex);
        // Thông tin gói bundling
        Package packageBundling = packageRepository.findByIdAndDeletedFlagAndVisibility(packageId, DeletedFlag.NOT_YET_DELETED.getValue(),
            DisplayStatus.VISIBLE).orElse(null);
        // Lấy thông tin các thành phần (item) trong gói bundling
        List<PackageItems> packageItemList = packageItemRepository.findByPackageId(packageId);

        // validate theo portal
        boolean isValidate = Arrays.asList(PortalType.DEV, PortalType.ADMIN).contains(AuthUtil.getPortalOfUserRoles());
        if (isValidate) {
            if (Objects.isNull(packageBundling) || CollectionUtils.isEmpty(packageItemList)) {
                throw exceptionFactory.resourceNotFound(MessageKeyConstant.NOT_FOUND, Resources.PACKAGE, EKPackage.ID, String.valueOf(packageId));
            }
        } else if (Objects.isNull(packageBundling) || CollectionUtils.isEmpty(packageItemList)) {
            return null;
        }

        // Set thông tin
        rootPackage.setIsBundling(Boolean.TRUE);
        rootPackage.setProductType(CalculateTypeEnum.PACKAGE_BUNDLING);
        rootPackage.setPackageId(packageBundling.getId());
        rootPackage.setPackageDraftId(packageBundling.getDraftId());
        rootPackage.setPackageName(packageBundling.getName());
        rootPackage.setPaymentMethods(packageBundling.getPaymentMethods());
        Set<String> customerTypes = packageBundling.getApplyCondition().getCustomerTypes().stream().filter(Objects::nonNull)
            .map(item -> "\"" + item.getValue() + "\"").collect(Collectors.toSet());
        rootPackage.setCustomerTypeCode(customerTypes.toString());
        // coi bundling như service
        rootPackage.setServiceId(packageBundling.getId());
        rootPackage.setName(packageBundling.getName());
        rootPackage.setPackageAvatarUrl(packageBundling.getIconUrl());

        // thông tin giải pháp tương ứng
        AtomicReference<Long> providerId = new AtomicReference<>(packageBundling.getProviderId());
        if (Objects.nonNull(solutionId)) {
            productSolutionRepository.findByIdAndDeletedFlagAndVisibility(solutionId, DeletedFlag.NOT_YET_DELETED.getValue(),
                DisplayStatus.VISIBLE).ifPresent(solution -> {
                rootPackage.setSolutionId(solution.getId());
                rootPackage.setSolutionDraftId(solution.getDraftId());
                rootPackage.setSolutionName(solution.getName());
                rootPackage.setSolutionAvatarUrl(solution.getAvatarUrl());
                // thông tin nhà cung cấp
                providerId.set(solution.getProviderId());
            });
        }
        // Set thông tin nhà cung cấp
        ICommonIdName providerInfo = userRepository.getProviderInfoById(providerId.get());
        rootPackage.setProviderId(providerId.get());
        rootPackage.setProvider(providerInfo.getName());

        // Map thông tin pricing được lưu trong cart
        Map<Long, PricingBriefDTO> mapCartPricing = new LinkedHashMap<>();
        if (ObjectUtils.isNotEmpty(serviceBriefDTO.getLstPricing())) {
            serviceBriefDTO.getLstPricing().forEach(item -> mapCartPricing.put(item.getPricingId(), item));
        }
        // convert và fill từng item trong package
        boolean isBuyCreatedVersion = Objects.equals(packageBundling.getPurchaseVersion(), PurchaseVersionEnum.CREATED_VERSION);
        List<ServiceDetailDTO> bundlingPackageItems = new ArrayList<>();
        int numDevices = 0;
        for (PackageItems packageItem : packageItemList) {
            if (Objects.isNull(packageItem.getPlanId())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.PRICING_MULTI_PLAN, ErrorKey.ID);
            }
            ServiceBriefDTO serviceBriefItemDTO = new ServiceBriefDTO();
            serviceBriefItemDTO.setIsBundling(true);
            serviceBriefItemDTO.setSelected(true);
            // Loại sản phẩm thành phần trong bundling riêng gói cước hoặc biến thể kèm gói -> PRICING
            serviceBriefItemDTO.setProductType(CalculateTypeEnum.PRICING);
            serviceBriefItemDTO.setCalculateType(CalculateTypeEnum.PRICING);
            serviceBriefItemDTO.setUserId(userId);

            // Set các thông tin của item từ metadata của package (có check logic mua phiên bản)
            Map<String, Object> metadata = packageItem.getMetadata();
            // SPDV hàng hóa
            if (isBuyCreatedVersion) {
                if (metadata.containsKey(PackageBundlingConstant.ITEM_METADATA_SERVICE_ID_KEY)) {
                    serviceBriefItemDTO.setServiceId(((Integer) metadata.get(PackageBundlingConstant.ITEM_METADATA_SERVICE_ID_KEY)).longValue());
                }
                if (metadata.containsKey(PackageBundlingConstant.ITEM_METADATA_SERVICE_DRAFT_ID_KEY)) {
                    serviceBriefItemDTO.setServiceDraftId(
                        ((Integer) metadata.get(PackageBundlingConstant.ITEM_METADATA_SERVICE_DRAFT_ID_KEY)).longValue());
                }
            } else {
                if (metadata.containsKey(PackageBundlingConstant.ITEM_METADATA_SERVICE_ID_KEY)) {
                    serviceBriefItemDTO.setServiceId(serviceRepository.getLatestServiceId(
                        ((Integer) metadata.get(PackageBundlingConstant.ITEM_METADATA_SERVICE_ID_KEY)).longValue()));
                }
                if (metadata.containsKey(PackageBundlingConstant.ITEM_METADATA_SERVICE_DRAFT_ID_KEY)) {
                    serviceBriefItemDTO.setServiceDraftId(serviceRepository.getServiceDraftId(serviceBriefItemDTO.getServiceId()));
                }
            }
            // Biến thể đi kèm
            Object variantIdObj = metadata.get(PackageBundlingConstant.ITEM_METADATA_VARIANT_ID_KEY);
            Long variantId = Objects.nonNull(variantIdObj) ? ((Integer) variantIdObj).longValue() : null;
            serviceBriefItemDTO.setVariantId(variantId);

            // Thông tin gói cước
            PricingBriefDTO pricingBriefDTO = new PricingBriefDTO();
            Object pricingIdObj = metadata.get(PackageBundlingConstant.ITEM_METADATA_PRICING_ID_KEY);
            Long pricingId = Objects.nonNull(pricingIdObj) ? ((Integer) pricingIdObj).longValue() : null;
            pricingBriefDTO.setPricingId(pricingId);
            pricingBriefDTO.setPricingMultiPlanId(packageItem.getPlanId());
            pricingBriefDTO.setQuantity(ObjectUtil.getOrDefault(packageItem.getQuantity(), 0L));
            pricingBriefDTO.setSelected(Boolean.TRUE);
            pricingBriefDTO.setIdx(mapCartPricing.containsKey(pricingId) ? mapCartPricing.get(pricingId).getIdx() : getNextIndex(1));

            // Kiểm tra cấu hình mua phiên bản để set lại dữ liệu gói cước
            if (Objects.nonNull(pricingId)) {
                Pricing pricing = isBuyCreatedVersion
                    ? pricingRepository.findById(pricingId).orElse(null)
                    : pricingRepository.getLatestApprovedPricingByPricingId(pricingId).orElse(null);
                if (isValidate && Objects.isNull(pricing)) {
                    throw exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.Pricing.ID, String.valueOf(pricingId));
                }
                if (Objects.nonNull(pricing)) {
                    pricingBriefDTO.setPricingId(pricing.getId());
                    // Kiểm tra cấu hình mua phiên bản để set lại pricingMultiPlan với plan = unit
                    Long pricingMultiPlanId = isBuyCreatedVersion
                        ? packageItem.getPlanId()
                        : pricingMultiPlanRepository.getLatestMultiPlanIdByOldMultiPlanId(packageItem.getPlanId());
                    PricingMultiPlan pricingMultiPlan = pricingMultiPlanRepository.findById(pricingMultiPlanId).orElse(null);
                    // Set thông tin quantity theo plan mới nhất
                    if (Objects.nonNull(pricingMultiPlan)) {
                        pricingBriefDTO.setPricingMultiPlanId(pricingMultiPlanId);
                        // set lại quantity nếu plan = UNIT hay có quan tâm đế minium_quantity và maximum_quantity
                        if (Objects.equals(pricingMultiPlan.getPricingPlan(), PricingPlanEnum.UNIT.value)) {
                            Long minQuantity = ObjectUtil.getOrDefault(pricingMultiPlan.getMinimumQuantity(), 0L);
                            Long maxQuantity = ObjectUtil.getOrDefault(pricingMultiPlan.getMaximumQuantity(), Long.MAX_VALUE);
                            if (packageItem.getQuantity() < minQuantity) {
                                pricingBriefDTO.setQuantity(minQuantity);
                            } else if (packageItem.getQuantity() > maxQuantity) {
                                pricingBriefDTO.setQuantity(maxQuantity);
                            } else {
                                pricingBriefDTO.setQuantity(packageItem.getQuantity());
                            }
                        }
                    }

                    //Set thông tin addon từ bảng package_item_addons
                    Map<Long, AddonBriefDTO> mapCartPricingAddons = new LinkedHashMap<>();
                    // Set thông tin chỉnh sửa của addon
                    if (mapAddon.containsKey(packageItem.getId())) {
                        mapAddon.get(packageItem.getId()).forEach(addon -> mapCartPricingAddons.put(addon.getAddonId(), addon));
                    }
                    List<AddonBriefDTO> addonList = packageAddonRepository.findByPackageItemId(packageItem.getId()).stream().map(
                        item -> {
                            // nếu thông tin addon đã được sửa/lưu trong cart thì lấy từ cart, nếu không thì trả về addon của item trong bundling
                            if (mapCartPricingAddons.containsKey(item.getAddonId())) {
                                return mapCartPricingAddons.get(item.getAddonId());
                            } else {
                                return AddonBriefDTO.builder()
                                    .idx(getNextIndex(2)) // addon idx
                                    .addonId(item.getAddonId())
                                    .addonMultiPlanId(item.getAddonPlanId())
                                    .quantity(item.getQuantity().intValue())
                                    .selected(Boolean.TRUE)
                                    .required(Boolean.FALSE)
                                    .build();
                            }
                        }).collect(Collectors.toList());
                    pricingBriefDTO.setLstAddon(addonList);
                    serviceBriefItemDTO.setLstPricing(Collections.singletonList(pricingBriefDTO));
                }
            }

            // Kiểm tra cấu hình mua phiên bản và set lại variantId
            if (Objects.nonNull(variantId)) {
                // Kiểm tra cấu hình mua phiên bản để set variantId tương ứng
                Variant variant = isBuyCreatedVersion
                    ? variantRepository.findById(variantId).orElse(null)
                    : variantRepository.getLatestApprovedVariantByVariantId(variantId).orElse(null);
                if (isValidate && Objects.isNull(variant)) {
                    throw exceptionFactory.resourceNotFound(Resources.VARIANT, ErrorKey.Variant.ID, String.valueOf(variantId));
                }
                if (Objects.nonNull(variant)) {
                    serviceBriefItemDTO.setVariantId(variant.getId());
                    serviceBriefItemDTO.setVariantDraftId(variant.getVariantDraftId());
                }
            }

            // check loại hàng hóa vật lý
            if (serviceRepository.existsByIdAndClassification(serviceBriefItemDTO.getServiceId(), ProductClassificationEnum.PHYSICAL)) {
                numDevices++;
            }
            // fill thông tin item vào cart vào cart
            serviceBriefItemDTO.setIdx(getNextIndex(0));
            serviceBriefItemDTO.setParentIdx(rootIndex);
            serviceBriefItemDTO.setIsService(Boolean.TRUE);
            // với các item -> xử lý dạng item đơn lẻ
            ServiceDetailDTO detailItem = setDataCartDetail(serviceBriefItemDTO, lstError, CalculateTypeEnum.PRICING, mapUnit);
            if (Objects.nonNull(detailItem)) {
                detailItem.setPackageItemId(packageItem.getId());
                bundlingPackageItems.add(detailItem);
            }
        }

        // set thông tin các item trong package
        rootPackage.setNumDevices(numDevices);
        rootPackage.setNumServices(packageItemList.size() - numDevices);
        rootPackage.setBundlingPackageItems(bundlingPackageItems);
        // Lấy giá tiền theo cấu hình
        if (Objects.equals(packageBundling.getPurchaseVersion(), PurchaseVersionEnum.CREATED_VERSION)) {
            rootPackage.setTotalPrice(packageBundling.getPrice());
        } else {
            rootPackage.setTotalPrice(rootPackage.getBundlingPackageItems().stream().map(ServiceDetailDTO::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return rootPackage;
    }

    @Override
    public Long getPricingMultiPlanNewest(Long pricingMultiPlanId) {
        if (Objects.isNull(pricingMultiPlanId)) return null;
        while (true) {
            PricingMultiPlan pricingMultiPlanNew = pricingMultiPlanRepository
                    .findByReferenceIdAndDeletedFlag(pricingMultiPlanId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .orElse(null);
            if (Objects.nonNull(pricingMultiPlanNew)) pricingMultiPlanId = pricingMultiPlanNew.getId();
            else break;
        }
        return pricingMultiPlanId;
    }

    @Override
    public List<Long> getLstPricingMultiPlanIdByReferenceId(Long pricingMultiPlanId) {
        List<Long> ids = new ArrayList<>();
        if (Objects.isNull(pricingMultiPlanId)) return new ArrayList<>();
        else ids.add(pricingMultiPlanId);
        // tìm chiều lên
        Long pricingMultiPlanIdNew = pricingMultiPlanId;
        while (true) {
            PricingMultiPlan pricingMultiPlanNew = pricingMultiPlanRepository
                    .findByReferenceIdAndDeletedFlag(pricingMultiPlanIdNew, DeletedFlag.NOT_YET_DELETED.getValue())
                    .orElse(null);
            if (Objects.nonNull(pricingMultiPlanNew) && !ids.contains(pricingMultiPlanNew.getId())) {
                ids.add(pricingMultiPlanNew.getId());
                pricingMultiPlanIdNew = pricingMultiPlanNew.getId();
            } else break;
        }
        // tìm chiều xuống
        Long pricingMultiPlanIdOld = pricingMultiPlanId;
        while (true) {
            PricingMultiPlan pricingMultiPlan = pricingMultiPlanRepository
                    .findById(pricingMultiPlanIdOld)
                    .orElse(null);
            PricingMultiPlan pricingMultiPlanOld;
            if (Objects.nonNull(pricingMultiPlan) && Objects.nonNull(pricingMultiPlan.getReferenceId())) {
                pricingMultiPlanOld = pricingMultiPlanRepository
                        .findById(pricingMultiPlan.getReferenceId())
                        .orElse(null);
            } else break;
            if (Objects.nonNull(pricingMultiPlanOld) && !ids.contains(pricingMultiPlanOld.getId())) {
                ids.add(pricingMultiPlanOld.getId());
                pricingMultiPlanIdOld = pricingMultiPlan.getReferenceId();
            } else break;
        }
        return ids;
    }


    private ServiceDetailDTO setDataCartDetail(ServiceBriefDTO serviceBriefDTO, List<CartErrorDTO> lstError, CalculateTypeEnum calculateTypeEnum,
        Map<Long, String> mapUnit) {
        ServiceDetailDTO serviceDetailDTO;
        try {
            serviceDetailDTO = getServiceDetailInfo(serviceBriefDTO, calculateTypeEnum, lstError);
        } catch (Exception e) {
            log.error("{}: setDataCartDetail error serviceId {}:", e.getClass().getName(), serviceBriefDTO.getServiceId(), e);
            CartErrorDTO errorDTO = createCartError(e);
            errorDTO.setServiceId(serviceBriefDTO.getServiceId());
            errorDTO.setVariantId(serviceBriefDTO.getVariantId());
            lstError.add(errorDTO);
            serviceDetailDTO = null;
        }
        if (Objects.isNull(serviceDetailDTO)) {
            // Đã có lỗi xảy ra trong quá trình tìm kiếm thông tin chi tiết dịch vụ
            return null;
        }
        // validate
        Long userId = Objects.nonNull(serviceBriefDTO.getUserId()) ? serviceBriefDTO.getUserId() : AuthUtil.getCurrentUserId();
        Optional<User> user = userRepository.findUserById(userId);
        String customerTypeUser = user.isPresent() ? user.get().getCustomerType() : ObjectUtil.getOrDefault(serviceBriefDTO.getCustomerType(),
            CustomerTypeEnum.ENTERPRISE.getValue());
        Integer numSub = userId == null ? null : shoppingCartRepository.getNumSubOfServiceUser(true, serviceDetailDTO.getServiceId(), userId);
        boolean isValidate = Arrays.asList(PortalType.DEV, PortalType.ADMIN).contains(AuthUtil.getPortalOfUserRoles()) &&
            (Objects.isNull(serviceBriefDTO.getType()) || !serviceBriefDTO.getType().equals(SubscriptionConstant.DETAIL_SUB));
        if (isValidate) {
            if (Objects.isNull(serviceDetailDTO.getServiceId()) || serviceDetailDTO.getDeletedFlag() == 0) {
                throw exceptionFactory.resourceNotFound(Resources.SERVICES, ErrorKey.ID, String.valueOf(serviceDetailDTO.getServiceId()));
            } else if (serviceDetailDTO.getStatus() == 0) {
                throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_NOT_ACTIVE, Resources.SERVICES, ErrorKey.ID,
                    String.valueOf(serviceDetailDTO.getServiceId()));
            }
            // check đối tượng KH
            if (!serviceDetailDTO.getCustomerTypeCode().contains(customerTypeUser)) {
                throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_NOT_CUSTOMER_IN_SERVICE_GROUP, Resources.SERVICES,
                    ErrorKey.CUSTOMER_TYPE, String.valueOf(serviceDetailDTO.getServiceId()));
            }
            // check multisub
            if (userId != null &&
                    (Objects.isNull(serviceDetailDTO.getAllowMultiSub()) || serviceDetailDTO.getAllowMultiSub().equals(0)) &&
                    Objects.nonNull(numSub) && numSub > 0) {
                throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_DISABLED_MULTI_SUB, Resources.SERVICES, ErrorKey.ID,
                    String.valueOf(serviceDetailDTO.getServiceId()));
            }
        }
        serviceDetailDTO.setNumSub(numSub);
        //
        String idx = Objects.isNull(serviceDetailDTO.getIdx()) ? getNextIndex(0) : serviceDetailDTO.getIdx();
        serviceDetailDTO.setIdx(idx);
        if (Objects.isNull(serviceDetailDTO.getParentIdx()) && Objects.nonNull(serviceBriefDTO.getParentIdx())) {
            serviceDetailDTO.setIdx(serviceBriefDTO.getParentIdx());
        }
        BigDecimal totalPrice = BigDecimal.ZERO;
        //
        if (!CollectionUtils.isEmpty(serviceBriefDTO.getLstPricing())) {
            for (PricingBriefDTO pricingBriefDTO : serviceBriefDTO.getLstPricing()) {
                try {
                    PricingDetailDTO pricingDetailDTO = getPricingDetailInfo(serviceBriefDTO.getIsService(),
                            serviceBriefDTO.getServiceId(), pricingBriefDTO, mapUnit);
                    if (Objects.isNull(pricingDetailDTO.getIdx())) {
                        pricingDetailDTO.setIdx(getNextIndex(1));
                        pricingDetailDTO.setParentIdx(idx);
                    }
                    for (AddonBriefDTO addonBriefDTO : pricingBriefDTO.getLstAddon()) {
                        try {
                            AddonDetailDTO addonDetailDTO = getAddonDetailInfo(addonBriefDTO, mapUnit);
                            if (pricingBriefDTO.getSelected() && addonBriefDTO.getSelected()) {
                                totalPrice = totalPrice.add(addonDetailDTO.getPrice());
                            }
                            if (Objects.isNull(addonDetailDTO.getIdx())) {
                                addonDetailDTO.setIdx(getNextIndex(2));
                                addonDetailDTO.setParentIdx(idx);
                            }
                            // Thêm addon vào chi tiết giỏ hàng
                            pricingDetailDTO.getLstAddon().add(addonDetailDTO);
                        } catch (Exception e) {
                            CartErrorDTO errorDTO = createCartError(e);
                            errorDTO.setServiceId(serviceBriefDTO.getServiceId());
                            errorDTO.setPricingId(pricingBriefDTO.getPricingId());
                            errorDTO.setPricingMultiPlanId(pricingBriefDTO.getPricingMultiPlanId());
                            errorDTO.setAddonId(addonBriefDTO.getAddonId());
                            errorDTO.setAddonMultiPlanId(addonBriefDTO.getAddonMultiPlanId());
                            lstError.add(errorDTO);
                            log.error("{}: serviceId {}, pricingId {}, pricingMultiPlanId {}, addonId {}, addonMultiPlanId {}",
                                    e.getClass().getName(), serviceBriefDTO.getServiceId(), pricingBriefDTO.getPricingId(),
                                pricingBriefDTO.getPricingMultiPlanId(), addonBriefDTO.getAddonId(), addonBriefDTO.getAddonMultiPlanId(), e);
                        }
                    }
                    // TH NHÓM DV -> thêm giá sau thuế, phí thiết lập và thuế
                    if (calculateTypeEnum.equals(CalculateTypeEnum.SERVICE_GROUP)) {
                        FormulaObject formulaObject = calculatePricePricingByQuantityAndAmountAfter(
                                serviceDetailDTO.getServiceId(), pricingDetailDTO.getPricingId(), pricingDetailDTO.getPricingMultiPlanId(),
                                pricingBriefDTO.getAmountUpdateServiceGroup(), pricingBriefDTO.getQuantityServiceGroup(), userId);
                        //
                        pricingDetailDTO.setOriginPrice(formulaObject.getOriginalPrice());
                        pricingDetailDTO.setInputPrice(formulaObject.getInputPrice());
                        pricingDetailDTO.setPrice(formulaObject.getPrice());
                        pricingDetailDTO.setPreTaxAmount(formulaObject.getPreAmountTax());
                        pricingDetailDTO.setFinalAmountAfterTax(formulaObject.getFinalAmountAfterTax());
                        pricingDetailDTO.setSetupFee(formulaObject.getSetupFee());
                        pricingDetailDTO.setTaxes(formulaObject.getTaxes());
                        if (!CollectionUtils.isEmpty(formulaObject.getPlanPriceList())) {
                            pricingDetailDTO.setLstUnitLimited(formulaObject.getPlanPriceList().stream()
                                    .map(e -> {
                                        UnitLimitedDTO unitLimitedDTO = new UnitLimitedDTO();
                                        BeanUtils.copyProperties(e, unitLimitedDTO);
                                        unitLimitedDTO.setPrice(e.getUnitPrice());
                                        return unitLimitedDTO;
                                    })
                                    .collect(Collectors.toList()));
                        }
                    }
                    // thêm tổng tiền
                    if (Boolean.TRUE.equals(pricingBriefDTO.getSelected() && serviceDetailDTO.getStatus() != 0) && !calculateTypeEnum.equals(CalculateTypeEnum.SERVICE_GROUP)) {
                        totalPrice = totalPrice.add(pricingDetailDTO.getPrice());
                        serviceDetailDTO.setSelected(true);
                    }
                    if (Boolean.TRUE.equals(pricingBriefDTO.getSelected() && serviceDetailDTO.getStatus() != 0) && calculateTypeEnum.equals(CalculateTypeEnum.SERVICE_GROUP)) {
                        totalPrice = totalPrice.add(pricingDetailDTO.getFinalAmountAfterTax());
                        serviceDetailDTO.setSelected(true);
                    }
                    // Thêm pricing vào chi tiết giỏ hàng
                    serviceDetailDTO.getLstPricing().add(pricingDetailDTO);
                } catch (Exception e) {
                    CartErrorDTO errorDTO = createCartError(e);
                    errorDTO.setServiceId(serviceBriefDTO.getServiceId());
                    errorDTO.setPricingId(pricingBriefDTO.getPricingId());
                    errorDTO.setPricingMultiPlanId(pricingBriefDTO.getPricingMultiPlanId());
                    lstError.add(errorDTO);
                    log.error("{}: serviceId {}, pricingId {}, pricingMultiPlanId {}",
                            e.getClass().getName(), serviceBriefDTO.getServiceId(), pricingBriefDTO.getPricingId(),
                        pricingBriefDTO.getPricingMultiPlanId(), e);
                }
            }
        }
        // PTTT theo cấu hình khi khởi tạo pricing khi mua 1
        Set<PaymentMethodEnum> paymentMethods =
            Objects.isNull(serviceDetailDTO.getPaymentMethods()) ? new HashSet<>() : new HashSet<>(serviceDetailDTO.getPaymentMethods());
        if (Objects.equals(serviceDetailDTO.getLstPricing().size(), 1) &&
            Objects.nonNull(serviceDetailDTO.getLstPricing().get(0).getPaymentMethods())) {
            paymentMethods.addAll(serviceDetailDTO.getLstPricing().get(0).getPaymentMethods());
        }
        // Nếu có VNPTPAY thì thêm VNPTPAY_QR
        if (paymentMethods.contains(PaymentMethodEnum.VNPTPAY)) {
            paymentMethods.add(PaymentMethodEnum.VNPTPAY_QR);
        }
        serviceDetailDTO.setPaymentMethods(paymentMethods);

        // check đối tượng KH
        serviceDetailDTO.getLstPricing().forEach(pricingDetailDTO -> {
            if (isValidate &&
                    calculateTypeEnum.equals(CalculateTypeEnum.SERVICE_GROUP) &&
                    !pricingDetailDTO.getCustomerTypeCode().contains(customerTypeUser)) {
                String message = messageSource.getMessage(MessageKeyConstant.SERVICE_NOT_CUSTOMER_IN_SERVICE_GROUP,
                        subscriptionMessage, LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICE_GROUP, ErrorKey.SERVICE_GROUP,
                        MessageKeyConstant.SERVICE_NOT_CUSTOMER_IN_SERVICE_GROUP);
            }
        });
        // update chung calculateTypeEnum logic cho giỏ hàng mới; voi gio hang cu thi SL =1
        serviceDetailDTO.setTotalPrice(totalPrice);
        return serviceDetailDTO;
    }

    /**
     * Lấy thông tin chi tiết dịch vụ trong giỏ hàng
     *
     * @param serviceBriefDTO Thông tin định danh dịch vụ trong giỏ hàng
     * @param lstError        Danh sách error (output)
     */
    private ServiceDetailDTO getServiceDetailInfo(ServiceBriefDTO serviceBriefDTO, CalculateTypeEnum calculateTypeEnum,
        List<CartErrorDTO> lstError) {
        if (!Boolean.TRUE.equals(serviceBriefDTO.getIsService())) {
            return getComboDetailInfo(serviceBriefDTO);
        }
        ServiceDetailDTO serviceDetailDTO = new ServiceDetailDTO();
        BeanUtils.copyProperties(serviceBriefDTO, serviceDetailDTO);
        Long serviceId = serviceBriefDTO.getServiceId();
        IServiceInfo info = shoppingCartRepository.getServiceInfo(true, serviceId);
        if (info != null) {
            BeanUtils.copyProperties(info, serviceDetailDTO);
            ServiceProductTypeEnum serviceType = Objects.nonNull(info.getProductType()) ?
                ServiceProductTypeEnum.fromValue(info.getProductType()) : ServiceProductTypeEnum.SAAS;
            serviceDetailDTO.setServiceType(serviceType);
            serviceDetailDTO.setPaymentMethods(Collections.singleton(PaymentMethodEnum.fromValue(info.getPaymentMethod())));
        } else {
            serviceDetailDTO.setStatus(StatusEnum.INACTIVE.value);
            serviceDetailDTO.setDeletedFlag(DeletedFlag.DELETED.getValue());
        }
        // set variant
        VariantResponseDTO variant = Objects.nonNull(serviceBriefDTO.getVariantId())
                ? variantService.getVariantSme(serviceId, serviceBriefDTO.getVariantId())
                : null;

        if (Objects.nonNull(serviceBriefDTO.getVariantId()) && Objects.isNull(variant)) {
            throw exceptionFactory.resourceNotFound(Resources.VARIANT, ErrorKey.ID, String.valueOf(serviceBriefDTO.getVariantId()));
        } else if (Objects.nonNull(variant)){
            variant.setApplyPricing(pricingVariantRepository.findPricingIdsByVariantId(variant.getVariantId(), variant.getServiceId()));

            // Nếu không có sku của biến thể -> sku của hàng hóa
            variant.setSku(StringUtils.isEmpty(variant.getSku()) ? serviceDetailDTO.getSku() : variant.getSku());
        }

        ServiceDraft serviceDraft = serviceDraftRepository.getServiceDraftApproved(serviceId);
        if (Objects.isNull(serviceDraft)) {
            // Các service không có serviceDraft chưa được phê duyệt, nên không tính vào danh sách các item mua được
            throw exceptionFactory.badRequest(MessageKeyConstant.INACTIVE, Resources.SERVICES, ErrorKey.ID, String.valueOf(serviceId));
        }
        serviceDetailDTO.setVariant(variant);
        // set avatar
        IFileAttachResponse avatar;
        if (calculateTypeEnum.equals(CalculateTypeEnum.SERVICE_GROUP) && Objects.nonNull(variant)) {
            avatar = fileAttachRepository.getVariantAvatar(variant.getVariantDraftId());
            if (avatar == null) {
                avatar = fileAttachRepository.getFileAttachDataResponseDto(FileAttachTypeEnum.AVATAR.value, serviceId);
            }
        } else {
            List<Integer> lstAvatarType = new ArrayList<>(
                Arrays.asList(FileAttachTypeEnum.AVATAR_DEVICE.value, FileAttachTypeEnum.AVATAR.value));
            long defaultVariantDraftId = -1L;
            if (Objects.nonNull(variant)) {
                defaultVariantDraftId = ObjectUtil.getOrDefault(variant.getVariantDraftId(), defaultVariantDraftId);
                lstAvatarType.add(FileAttachTypeEnum.VARIANT_AVATAR.value);
            }
            avatar = fileAttachRepository.getFileAttachVariantOrService(lstAvatarType, defaultVariantDraftId, serviceId);
        }
        serviceDetailDTO.setAvatar(avatar);
        //
        ServiceEntity currentService = serviceRepository.findByIdAndDeletedFlag(serviceId, DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        if (Objects.nonNull(currentService) && Objects.isNull(serviceBriefDTO.getServiceDraftId())) {
            // Fill thông tin draftId cho serviceBriefDTO nếu FE ko truyền thông tin
            serviceBriefDTO.setServiceDraftId(currentService.getServiceDraftId());
            serviceDetailDTO.setServiceDraftId(currentService.getServiceDraftId());
        }
        // tinh lai gia priceVariant priceService cho gio hang moi (quantityVariant co the > 1)
        Long quantityVariant = serviceBriefDTO.getQuantityVariant() != null ? serviceBriefDTO.getQuantityVariant() : 1L;
        if (!Objects.equals(calculateTypeEnum, CalculateTypeEnum.SERVICE_GROUP)) {
            if (Objects.nonNull(variant)) {
                int quantity = serviceBriefDTO.getVariant() != null && serviceBriefDTO.getVariant().getQuantity() != null ? serviceBriefDTO.getVariant().getQuantity() : 1;
                serviceDetailDTO.setQuantityVariant((long) quantity);
                variant.setQuantity(quantity);
            } else {
                serviceDetailDTO.setQuantityVariant(quantityVariant);
            }
        }
        if (!calculateTypeEnum.equals(CalculateTypeEnum.SERVICE_GROUP) &&
            !ObjectUtil.getOrDefault(serviceBriefDTO.getIsBundling(), Boolean.FALSE) && Objects.nonNull(currentService)) {
            if (Objects.nonNull(currentService.getServiceDraftId()) && !Objects.equals(currentService.getServiceDraftId(), serviceBriefDTO.getServiceDraftId())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_NOT_LATEST_VERSION, Resources.SERVICES, ErrorKey.DRAFT_ID,
                    String.valueOf(serviceBriefDTO.getServiceDraftId()));
            }
        }

        return serviceDetailDTO;
    }

    public FormulaObject calculatePricePricingByQuantityAndAmountAfter(
            Long serviceId, Long pricingId, Long pricingMultiPlanId, BigDecimal amountUpdate, Integer quantityServiceGroup,
            Long userId) {
        SubscriptionFormulaReqDTO preSubServiceGroup = new SubscriptionFormulaReqDTO();
        SubscriptionFormulaReqDTO.FormulaObject object = new SubscriptionFormulaReqDTO.FormulaObject();
        object.setId(pricingId);
        object.setMultiPlanId(pricingMultiPlanId);
        //
        preSubServiceGroup.setObject(object);
        if (Objects.nonNull(serviceId)) {
            preSubServiceGroup.setServiceId(serviceId);
            ServiceDraft serviceDraft = serviceDraftRepository.getServiceDraftApproved(serviceId);
            preSubServiceGroup.setServiceDraftId(Objects.nonNull(serviceDraft) ? serviceDraft.getId() : null);
        }
        preSubServiceGroup.setCalculateType(CalculateTypeEnum.SERVICE_GROUP);
        preSubServiceGroup.setUserId(userId);
        ShoppingCartCalculateDTO sub = new ShoppingCartCalculateDTO(preSubServiceGroup);
        /* Tạo danh sách tất cả CTKM/CDQC được áp dụng */
        sub.fillPromotion();
        /* Tính tiền bỏ qua khuyến mại */
        SubscriptionCalculationInput input = subscriptionFormula.collectCalculatorInput(sub, -1);
        input.getPlan().setInputPrice(amountUpdate);
        input.getPlan().setPlanQuantity(Long.valueOf(quantityServiceGroup));
        SubscriptionFormulaResDTO formulaResDTO = new SubscriptionCalculator(
                input.getPlan(),
                input.getAddonList(),
                sub.getOtherFeeList())
                .calc();

        formulaResDTO.getObject().setPlanTaxList(input.getPlan().getPlanTaxList());
        return formulaResDTO.getObject();
    }

    /**
     * Lấy thông tin chi tiết combo trong giỏ hàng
     *
     * @param serviceBriefDTO Thông tin định danh combo trong giỏ hàng
     */
    private ServiceDetailDTO getComboDetailInfo(ServiceBriefDTO serviceBriefDTO) {
        ServiceDetailDTO serviceDetailDTO = new ServiceDetailDTO();
        BeanUtils.copyProperties(serviceBriefDTO, serviceDetailDTO);
        Long comboId = serviceBriefDTO.getServiceId();
        IServiceInfo info = shoppingCartRepository.getServiceInfo(false, comboId);
        if (info == null) {
            throw exceptionFactory.resourceNotFound(Resources.COMBO, ErrorKey.ID, String.valueOf(comboId));
        }
        BeanUtils.copyProperties(info, serviceDetailDTO);
        ServiceProductTypeEnum serviceType = Objects.nonNull(info.getProductType()) ?
            ServiceProductTypeEnum.fromValue(info.getProductType()) : ServiceProductTypeEnum.SAAS;
        serviceDetailDTO.setServiceType(serviceType);
        serviceDetailDTO.setPaymentMethods(Collections.singleton(PaymentMethodEnum.fromValue(info.getPaymentMethod())));
        // Avatar
        serviceDetailDTO.setAvatar(fileAttachRepository.getComboAvatar(comboId));
        //
        return serviceDetailDTO;
    }

    /**
     * Lấy thông tin chi tiết gói cước trong giỏ hàng
     *
     * @param isService       Gói cước thuộc về service hay combo
     * @param serviceId       Service/Combo ID chứa gói
     * @param pricingBriefDTO Thông tin định danh gói cước trong giỏ hàng
     */
    private PricingDetailDTO getPricingDetailInfo(Boolean isService, Long serviceId, PricingBriefDTO pricingBriefDTO,
        Map<Long, String> mapUnit) {
        PricingDetailDTO pricingDetailDTO = new PricingDetailDTO();
        BeanUtils.copyProperties(pricingBriefDTO, pricingDetailDTO);
        List<PricingTax> lstPricingTax;
        if (Boolean.TRUE.equals(isService)) {
            Long pricingId = pricingBriefDTO.getPricingId();
            Pricing pricing = pricingRepository.findById(pricingId).orElse(null);
            if (Objects.isNull(pricing) || !Objects.equals(serviceId, pricing.getServiceId())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.PRICING_NOT_BELONG_TO_SERVICE, Resources.PRICING, ErrorKey.ID,
                        String.valueOf(pricingId), String.valueOf(serviceId));
            }
            pricingDetailDTO.setPricingDraftId(pricing.getPricingDraftId());
            pricingDetailDTO.setUnitName(mapUnit.get(pricing.getUnitId()));
            pricingDetailDTO.setName(pricing.getPricingName());
            pricingDetailDTO.setCustomerTypeCode(pricing.getCustomerTypeCode());
            pricingDetailDTO.setIsPrepaid(pricing.getPricingType() != null && pricing.getPricingType() == 0);
            pricingDetailDTO.setDeletedFlag(pricing.getDeletedFlag());
            pricingDetailDTO.setStatus(pricing.getStatus());
            pricingDetailDTO.setAllowChangePrice(Objects.equals(pricing.getHasChangePrice(), 1));
            pricingDetailDTO.setIsOneTime(pricing.getIsOneTime());
            if (pricing.getPricingType() != null) {
                pricingDetailDTO.setPricingType(EnumFieldMapper.convertPricingTypeEnumToDTO(pricing.getPricingType()).name());
            }
            // Lấy thông tin urlPreOrder
            Set<ComboDetailDTO> urlPreOrder = subscriptionRepository.getPricingAndUrlPreOrder(pricing.getId())
                    .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
            pricingDetailDTO.setUrlPreOrder(urlPreOrder);
            if (pricingBriefDTO.getPricingMultiPlanId() == null) {
                pricingDetailDTO.setPricingPlan(pricing.getPricingPlan());
                pricingDetailDTO.setPaymentCycle(getPaymentCycle(pricing.getPaymentCycle(), pricing.getCycleType()));
                pricingDetailDTO.setNumCycle(pricing.getNumberOfCycles());
                pricingDetailDTO.setNumFree(pricing.getFreeQuantity() != null ? pricing.getFreeQuantity().intValue() : null);
                pricingDetailDTO.setPreTaxAmount(pricing.getPrice() != null ? pricing.getPrice() : BigDecimal.ZERO);
                pricingDetailDTO.setLstUnitLimited(unitLimitedRepository.findByPricingId(pricingId).stream()
                        .filter(item -> item.getSubscriptionSetupFeeId() == null) // ID khác null trong trường hợp admin/dev sửa giá gói khi đăng kí hộ
                        .map(UnitLimitedDTO::new).collect(Collectors.toList()));
            } else {
                Long pricingMultiPlanId = pricingBriefDTO.getPricingMultiPlanId();
                PricingMultiPlan multiPlan = pricingMultiPlanRepository.findById(pricingMultiPlanId).orElse(null);
                if (Objects.isNull(multiPlan) || !Objects.equals(multiPlan.getPricingId(), pricingId)) {
                    throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_PRICING_MULTI_PLAN_ID, Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                            String.valueOf(pricingMultiPlanId), String.valueOf(pricingId));
                }
                if (pricing.getStatus() == StatusEnum.ACTIVE.value) { // Nếu pricing là active, gán status theo multiplan
                    pricingDetailDTO.setStatus(multiPlan.getDisplayStatus());
                }
                pricingDetailDTO.setPricingPlan(multiPlan.getPricingPlan());
                pricingDetailDTO.setUnitName(mapUnit.get(multiPlan.getUnitId()));
                pricingDetailDTO.setNumPaymentCycle(multiPlan.getPaymentCycle());
                pricingDetailDTO.setPaymentCycle(getPaymentCycle(multiPlan.getPaymentCycle(), multiPlan.getCircleType()));
                pricingDetailDTO.setNumCycle(multiPlan.getNumberOfCycle());
                pricingDetailDTO.setNumFree(multiPlan.getFreeQuantity());
                pricingDetailDTO.setPreTaxAmount(multiPlan.getPrice() != null ? multiPlan.getPrice() : BigDecimal.ZERO);
                pricingDetailDTO.setLstUnitLimited(pricingPlanDetailRepository.findByPricingMultiPlanIdOrderByUnitFromAsc(pricingMultiPlanId)
                        .orElse(new ArrayList<>()).stream()
                        .filter(item -> item.getSubscriptionSetupFeeId() == null) // ID khác null trong trường hợp admin/dev sửa giá gói khi đăng kí hộ
                        .map(UnitLimitedDTO::new).collect(Collectors.toList()));
                if (multiPlan.getPricingPlan() == PricingPlanEnum.UNIT.value || multiPlan.getPricingPlan() == PricingPlanEnum.FLAT_RATE.value) {
                    pricingDetailDTO.setMinimumQuantity(multiPlan.getMinimumQuantity());
                    pricingDetailDTO.setMaximumQuantity(multiPlan.getMaximumQuantity());
                } else {
                    pricingDetailDTO.setMinimumQuantity(minUnitFrom(pricingDetailDTO.getLstUnitLimited()));
                    pricingDetailDTO.setMaximumQuantity(maxUnitTo(pricingDetailDTO.getLstUnitLimited()));
                }

                // Kiểm tra vi phạm minQuantity, maxQuantity
                Long minQuantity = pricingDetailDTO.getMinimumQuantity();
                Long maxQuantity = pricingDetailDTO.getMaximumQuantity();
                if (minQuantity != null && minQuantity != -1L && pricingDetailDTO.getQuantity() < minQuantity) {
                    pricingDetailDTO.setQuantity(minQuantity.intValue());
                }
                if (maxQuantity != null && maxQuantity != -1L && pricingDetailDTO.getQuantity() > maxQuantity) {
                    pricingDetailDTO.setQuantity(maxQuantity.intValue());
                }
            }
            lstPricingTax = pricingTaxRepository.findAllByPricingId(pricingDetailDTO.getPricingId());
        } else {
            Long comboPlanId = pricingBriefDTO.getPricingId();
            ComboPlan comboPlan = comboPlanRepository.findById(comboPlanId).orElse(null);
            if (Objects.isNull(comboPlan) || !Objects.equals(serviceId, comboPlan.getComboId())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.PRICING_NOT_BELONG_TO_SERVICE, Resources.PRICING, ErrorKey.ID,
                        String.valueOf(comboPlanId), String.valueOf(serviceId));
            }
            pricingDetailDTO.setName(comboPlan.getComboName());
            pricingDetailDTO.setIsPrepaid(comboPlan.getComboPlanType() != null && comboPlan.getComboPlanType() == 0);
            pricingDetailDTO.setDeletedFlag(comboPlan.getDeletedFlag());
            pricingDetailDTO.setStatus(comboPlan.getStatus());
            pricingDetailDTO.setAllowChangePrice(Objects.equals(comboPlan.getHasChangePrice(), 1));
            pricingDetailDTO.setPricingPlan(PricingPlanEnum.FLAT_RATE.value);
            if (comboPlan.getComboPlanType() != null) {
                pricingDetailDTO.setPricingType(EnumFieldMapper.convertPricingTypeEnumToDTO(comboPlan.getComboPlanType()).name());
            }
            pricingDetailDTO.setPaymentCycle(getPaymentCycle(comboPlan.getPaymentCycle(), comboPlan.getCycleType()));
            pricingDetailDTO.setNumCycle(comboPlan.getNumberOfCycles());
            pricingDetailDTO.setNumFree(comboPlan.getFreeQuantity() != null ? comboPlan.getFreeQuantity().intValue() : null);
            pricingDetailDTO.setPreTaxAmount(comboPlan.getPrice() != null ? comboPlan.getPrice() : BigDecimal.ZERO);
            pricingDetailDTO.setLstSubPricing(getListSubPricing(pricingBriefDTO));
            pricingDetailDTO.setLstSubVariant(getListSubVariant(pricingBriefDTO));
            pricingDetailDTO.setAvatar(fileAttachRepository.getPathByObjectTypeAndObjectId(FileAttachTypeEnum.COMBO_PLAN_THUMBNAIL.getValue(),
                comboPlanId));
            pricingDetailDTO.setPaymentMethods(comboPlan.getListPaymentMethod().stream().map(PaymentMethodEnum::fromValue)
                .collect(Collectors.toSet()));
            // Lấy thông tin urlPreOrderSet<ComboDetailDTO> urlPreOrder;
            Set<ComboDetailDTO> urlPreOrder = comboPricingRepository.findPricingByComboPlanId(comboPlan.getId())
                    .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
            pricingDetailDTO.setUrlPreOrder(urlPreOrder);
            lstPricingTax = comboTaxRepository.findByIdComboPlan(comboPlanId).stream().map(PricingTax::new).collect(Collectors.toList());

            // nếu combo có thiết bị: set serviceType = DEVICE
            if (Objects.isNull(pricingDetailDTO.getHasDevice()) &&
                (Objects.nonNull(pricingDetailDTO.getLstSubPricing()) &&
                    pricingDetailDTO.getLstSubPricing().stream()
                        .anyMatch(item -> Objects.equals(item.getServiceProductType(), ServiceProductTypeEnum.DEVICE))) ||
                (Objects.nonNull(pricingDetailDTO.getLstSubVariant()) &&
                    pricingDetailDTO.getLstSubVariant().stream()
                        .anyMatch(item -> Objects.equals(item.getServiceProductType(), ServiceProductTypeEnum.DEVICE.value)))) {
                pricingDetailDTO.setHasDevice(Boolean.TRUE);
            }
        }
        // Nếu là pricing/combo plan loại FLAT_RATE, gán số lượng về 1
        PricingPlanEnum pricingPlanEnum = PricingPlanEnum.valueOf(pricingDetailDTO.getPricingPlan());
        pricingDetailDTO.setQuantity(ObjectUtil.getOrDefault(pricingBriefDTO.getQuantityServiceGroup(), pricingDetailDTO.getQuantity()));
        // Tính toán lại giá cả trước thuế
        BigDecimal preTaxAmount = ObjectUtil.getOrDefault(pricingBriefDTO.getAmountUpdateServiceGroup(), pricingDetailDTO.getPreTaxAmount());
        if (preTaxAmount != null) {
            pricingDetailDTO.setPreTaxAmount(new ContainedTaxListCalculator(lstPricingTax, preTaxAmount).getPreTaxAmount());
        }

        //Cập nhật lại lstUnitLimited trước thuế
        if (pricingDetailDTO.getLstUnitLimited() != null) {
            pricingDetailDTO.getLstUnitLimited().stream().filter(unit -> unit.getPrice() != null)
                    .forEach(unit -> unit.setPrice(new ContainedTaxListCalculator(lstPricingTax, unit.getPrice()).getPreTaxAmount()));
        }

        // Tính toán giá theo số lượng
        pricingDetailDTO.setPrice(PriceCalculator.calculatorWithoutTax(pricingPlanEnum,
                pricingDetailDTO.getPreTaxAmount(),
                pricingDetailDTO.getQuantity() != null ? new BigDecimal(pricingDetailDTO.getQuantity()) : BigDecimal.ONE,
                pricingDetailDTO.getLstUnitLimited().stream().map(SubscriptionCalculateDTO::new).collect(Collectors.toList()),
                pricingDetailDTO.getNumFree() != null ? new BigDecimal(pricingDetailDTO.getNumFree()) : BigDecimal.ZERO,
                new ArrayList<>()).calcWithoutTax());
        pricingDetailDTO.setMatomoId(pricingBriefDTO.getMatomoId());
        return pricingDetailDTO;
    }

    private Long minUnitFrom(List<UnitLimitedDTO> limitedDTOList) {
        if (limitedDTOList.isEmpty()) {
            return -1L;
        }
        Long min = limitedDTOList.get(0).getUnitFrom();
        for (UnitLimitedDTO unitLimitedDTO : limitedDTOList) {
            if (unitLimitedDTO.getUnitFrom() == null || unitLimitedDTO.getUnitFrom() == PricingPlanEnum.UNSET.value) {
                min = (long) PricingPlanEnum.UNSET.value;
                break;
            } else {
                if (min > unitLimitedDTO.getUnitFrom()) {
                    min = unitLimitedDTO.getUnitFrom();
                }
            }
        }
        return min;
    }

    private Long maxUnitTo(List<UnitLimitedDTO> limitedDTOList) {
        if (limitedDTOList.isEmpty()) {
            return -1L;
        }
        Long max = limitedDTOList.get(0).getUnitTo();
        for (UnitLimitedDTO unitLimitedDTO : limitedDTOList) {
            if (unitLimitedDTO.getUnitTo() == null || unitLimitedDTO.getUnitTo() == PricingPlanEnum.UNSET.value) {
                max = (long) PricingPlanEnum.UNSET.value;
                break;
            } else {
                if (max < unitLimitedDTO.getUnitTo()) {
                    max = unitLimitedDTO.getUnitTo();
                }
            }
        }
        return max;
    }

    private List<SubPricingDTO> getListSubPricing(PricingBriefDTO pricingBriefDTO) {
        LinkedList<IComboPricingDTO> lstSubPricing = comboRepository.getPricingByComboPlanId(pricingBriefDTO.getPricingId());
        return lstSubPricing.stream().map(item -> new SubPricingDTO(
                fileAttachRepository.getFileAttachDataResponseDto(FileAttachTypeEnum.AVATAR.value, item.getServiceId()),
                item.getServiceName(), item.getPricingName(), item.getQuantity(), item.getFreeQuantity(), item.getPaymentCycle(),
                item.getCycleType(), item.getNumberOfCycles(), item.getNumCycle(), item.getProviderName(), item.getPrice(), item.getIsOneTime(),
                ServiceProductTypeEnum.fromValue(item.getServiceProductType())))
            .collect(Collectors.toList());
    }

    private List<ISubVariantDTO> getListSubVariant(PricingBriefDTO pricingBriefDTO) {
        return comboRepository.getSubVariantInfo(pricingBriefDTO.getPricingId());
    }

    /**
     * Lấy thông tin chi tiết addon trong giỏ hàng
     *
     * @param addonBriefDTO Thông tin định danh addon trong giỏ hàng
     */
    private AddonDetailDTO getAddonDetailInfo(AddonBriefDTO addonBriefDTO, Map<Long, String> mapUnit) {
        AddonDetailDTO addonDetailDTO = new AddonDetailDTO();
        BeanUtils.copyProperties(addonBriefDTO, addonDetailDTO);
        Long addonId = addonBriefDTO.getAddonId();
        Long addonMultiPlanId = addonBriefDTO.getAddonMultiPlanId();
        Addon addon = addonRepository.findByIdAndDeletedFlag(addonId, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> exceptionFactory.badRequest(MessageKeyConstant.NOT_FOUND, Resources.ADDON, ErrorKey.ID, String.valueOf(addonId)));
        addonDetailDTO.setAddonDraftId(addon.getAddonDraftId());
        addonDetailDTO.setName(addon.getName());
        addonDetailDTO.setOneTime(addon.getBonusType() == 0);
        addonDetailDTO.setDeletedFlag(addon.getDeletedFlag());
        addonDetailDTO.setStatus(addon.getStatus());
        addonDetailDTO.setAllowChangePrice(Objects.equals(addon.getAllowPriceChange(), 1));
        if (Objects.nonNull(addon.getUnitId())) {
            addonDetailDTO.setUnitName(mapUnit.get(addon.getUnitId()));
        }
        if (addonMultiPlanId == null) {
            addonDetailDTO.setPaymentCycle(getPaymentCycle(addon.getBonusValue(), addon.getType()));
            addonDetailDTO.setNumFree(addon.getFreeQuantity() != null ? addon.getFreeQuantity().intValue() : null);
            addonDetailDTO.setPricingPlan(addon.getPricingPlan());
            addonDetailDTO.setPreTaxAmount(addon.getPrice());
            addonDetailDTO.setLstUnitLimited(unitLimitedRepository.findByAddonId(addonBriefDTO.getAddonId()).stream()
                .filter(item -> item.getSubscriptionSetupFeeId() == null) // ID khác null trong trường hợp admin/dev sửa giá gói khi đăng kí hộ
                .map(UnitLimitedDTO::new).collect(Collectors.toList()));
            if (addon.getPricingPlan() == PricingPlanEnum.UNIT.value || addon.getPricingPlan() == PricingPlanEnum.FLAT_RATE.value) {
                addonDetailDTO.setMinimumQuantity(addon.getMinimumQuantity());
                addonDetailDTO.setMaximumQuantity(addon.getMaximumQuantity());
            } else {
                addonDetailDTO.setMinimumQuantity(minUnitFrom(addonDetailDTO.getLstUnitLimited()));
                addonDetailDTO.setMaximumQuantity(maxUnitTo(addonDetailDTO.getLstUnitLimited()));
            }
        } else {
            PricingMultiPlan addonMultiPlan = pricingMultiPlanRepository.getAddonMultiPlanById(addonMultiPlanId);
            if (addonMultiPlan == null || !Objects.equals(addonMultiPlan.getAddonId(), addonId) ||
                addonMultiPlan.getDeletedFlag() == DeletedFlag.DELETED.getValue()) {
                throw exceptionFactory.badRequest(MessageKeyConstant.NOT_FOUND, Resources.ADDON_MULTI_PLAN, ErrorKey.ID,
                    String.valueOf(addonMultiPlanId));
            }
            addonDetailDTO.setPaymentCycle(getPaymentCycle(addonMultiPlan.getPaymentCycle(), addonMultiPlan.getCircleType()));
            addonDetailDTO.setNumFree(addonMultiPlan.getFreeQuantity());
            addonDetailDTO.setPricingPlan(addonMultiPlan.getPricingPlan());
            addonDetailDTO.setPreTaxAmount(addonMultiPlan.getPrice());
            addonDetailDTO.setLstUnitLimited(pricingPlanDetailRepository.findByPricingMultiPlanIdOrderByUnitFromAsc(addonMultiPlanId)
                .orElse(new ArrayList<>()).stream()
                .filter(item -> item.getSubscriptionSetupFeeId() == null) // ID khác null trong trường hợp admin/dev sửa giá gói khi đăng kí hộ
                .map(UnitLimitedDTO::new).collect(Collectors.toList()));
            if (addonMultiPlan.getPricingPlan() == PricingPlanEnum.UNIT.value
                || addonMultiPlan.getPricingPlan() == PricingPlanEnum.FLAT_RATE.value) {
                addonDetailDTO.setMinimumQuantity(addonMultiPlan.getMinimumQuantity());
                addonDetailDTO.setMaximumQuantity(addonMultiPlan.getMaximumQuantity());
            } else {
                addonDetailDTO.setMinimumQuantity(minUnitFrom(addonDetailDTO.getLstUnitLimited()));
                addonDetailDTO.setMaximumQuantity(maxUnitTo(addonDetailDTO.getLstUnitLimited()));
            }
        }

        // Nếu là addon loại FLAT_RATE, gán số lượng về 1
        PricingPlanEnum pricingPlanEnum = PricingPlanEnum.valueOf(addonDetailDTO.getPricingPlan());
        if (pricingPlanEnum == PricingPlanEnum.FLAT_RATE) {
            addonDetailDTO.setQuantity(1);
        } else {
            // Kiểm tra vi phạm minQuantity, maxQuantity
            Long minQuantity = addonDetailDTO.getMinimumQuantity();
            Long maxQuantity = addonDetailDTO.getMaximumQuantity();
            if (minQuantity != null && minQuantity != -1L && addonDetailDTO.getQuantity() < minQuantity) {
                addonDetailDTO.setQuantity(minQuantity.intValue());
            }
            if (maxQuantity != null && maxQuantity != -1L && addonDetailDTO.getQuantity() > maxQuantity) {
                addonDetailDTO.setQuantity(maxQuantity.intValue());
            }
        }

        // Tính toán lại giá cả trước thuế
        List<PricingTax> lstPricingTax = addonTaxRepository.findAllByAddonsId(addonBriefDTO.getAddonId()).stream().map(PricingTax::new)
            .collect(Collectors.toList());
        BigDecimal preTaxAmount = addonDetailDTO.getPreTaxAmount();
        if (preTaxAmount != null) {
            addonDetailDTO.setPreTaxAmount(new ContainedTaxListCalculator(lstPricingTax, preTaxAmount).getPreTaxAmount());
        }

        //Cập nhật lại lstUnitLimited trước thuế
        if (addonDetailDTO.getLstUnitLimited() != null) {
            addonDetailDTO.getLstUnitLimited().stream().filter(unit -> unit.getPrice() != null)
                .forEach(unit -> unit.setPrice(new ContainedTaxListCalculator(lstPricingTax, unit.getPrice()).getPreTaxAmount()));
        }

        // Tính toán lại giá tổng theo số lượng
        addonDetailDTO.setPrice(PriceCalculator.calculatorWithoutTax(pricingPlanEnum,
                addonDetailDTO.getPreTaxAmount(),
                addonBriefDTO.getQuantity() != null ? new BigDecimal(addonBriefDTO.getQuantity()) : BigDecimal.ONE,
                addonDetailDTO.getLstUnitLimited().stream().map(SubscriptionCalculateDTO::new).collect(Collectors.toList()),
                addonDetailDTO.getNumFree() != null ? new BigDecimal(addonDetailDTO.getNumFree()) : BigDecimal.ZERO,
                new ArrayList<>())
            .calcWithoutTax());
        return addonDetailDTO;
    }

    private String getPaymentCycle(Integer paymentCycle, Integer cycleType) {
        if (paymentCycle == null || cycleType == null) {
            return null;
        }
        switch (cycleType) {
            case 0:
                return paymentCycle + " ngày";
            case 1:
                return paymentCycle + " tuần";
            case 2:
                return paymentCycle + " tháng";
            case 3:
                return paymentCycle + " năm";
        }
        return null;
    }

    private String getPaymentCycle(Long paymentCycle, Integer cycleType) {
        if (paymentCycle == null) {
            return null;
        }
        return getPaymentCycle(paymentCycle.intValue(), cycleType);
    }

    @Override
    public Page<SubscriptionsSaaSResDTO> getListSubscription(GetListSubReqDTO dto, PortalType portal, Pageable pageable) {

        // Kiểm tra permission dựa trên user đăng nhập và portal type
        Long userId = -1L;
        Long devId = -1L;
        switch (portal) {
            case SME:
                if(Boolean.TRUE.equals(checkPermissionWithSubsOfCurrentUser(AuthUtil.getCurrentUserId()))) {
                    userId = AuthUtil.getCurrentParentId();
                }else {
                    userId = AuthUtil.getCurrentUserId();
                }
                break;
            case DEV:
                if(Boolean.TRUE.equals(checkPermissionWithSubsOfCurrentUser(AuthUtil.getCurrentUserId()))) {
                    devId = AuthUtil.getCurrentParentId();
                }else {
                    devId = AuthUtil.getCurrentUserId();
                }
                break;
        }
        // Lấy page subCode thỏa mã điều kiện lọc, phân trang, sắp xếp giảm dần theo createdAt
        Page<ISubCodePageDTO> subCodePage = shoppingCartRepository.getSubCode(dto.getSubCode(), dto.getProductId(), dto.getProduct(),
            dto.getCustomer(), dto.getProvider(), dto.getSubStatus(), dto.getSubInstalled(), dto.getProgressStatus(),
            dto.getServiceType(), dto.getCustomerType(), dto.getTaxCode(), dto.getIdentityNo(), dto.getEmail(), dto.getPhone(),
            dto.getRepName(), dto.getFoundingFrom(), dto.getFoundingTo(), dto.getBusinessArea(), dto.getBusinessSize(),
            dto.getCreatedSource(), dto.getPaymentFrom(), dto.getPaymentTo(), dto.getProvinceId(), dto.getProvinceIdLst(),
            dto.getSubCreatedFrom(), dto.getSubCreatedTo(), dto.getPaymentCycle(), dto.getCycleType(), userId, devId,
            AuthUtil.getAdminProvinceId(), dto.getValue(), dto.getSearchID(), dto.getSearchServiceName(), dto.getReferCode(), dto.getKhcnPhone(),
            dto.getSubActionEnum().name(), dto.getServiceOwnerType().name(),
            dto.getPaymentMethod(), dto.getAmDepartments(), dto.getAmCodes(), dto.getAmNames(), dto.getAmPhones(), dto.getAmEmails(), pageable);

        // Tìm kiếm các bản ghi theo sub code đã tìm ở trên
        Set<String> setSubCode = subCodePage.getContent().stream().map(ISubCodePageDTO::getSubCode).collect(Collectors.toSet());
        List<ISubscriptionsSaaSResDTO> lstSubDetail = shoppingCartRepository.getSubscriptionBySubCode(setSubCode);

        Map<String, List<ISubscriptionsSaaSResDTO>> mapSubCodeToBillingDetail = lstSubDetail.stream()
            .collect(Collectors.groupingBy(ISubscriptionsSaaSResDTO::getSubCode));
        return subCodePage.map(item -> convertToSubscriptionDTO(item.getSubCode(), mapSubCodeToBillingDetail.get(item.getSubCode())));
    }

    @Override
    public Page<SubscriptionsSaaSResDTO> getListSubscriptionAdmin(GetListSubReqDTO dto, PortalType portal, Pageable pageable) {
        // Kiểm tra permission dựa trên user đăng nhập và portal type
        Long userId = -1L;
        Long devId = -1L;
        switch (portal) {
            case SME:
                if(Boolean.TRUE.equals(checkPermissionWithSubsOfCurrentUser(AuthUtil.getCurrentUserId()))) {
                    userId = AuthUtil.getCurrentParentId();
                }else {
                    userId = AuthUtil.getCurrentUserId();
                }
                break;
            case DEV:
                if(Boolean.TRUE.equals(checkPermissionWithSubsOfCurrentUser(AuthUtil.getCurrentUserId()))) {
                    devId = AuthUtil.getCurrentParentId();
                }else {
                    devId = AuthUtil.getCurrentUserId();
                }
                break;
        }
        Long currentUserId = AuthUtil.getCurrentUserId();
        BigDecimal[] lstViewablePartitionId = crmObjectPermissionUtil.getPartitionIdByObjectRole(currentUserId, CrmPermissionEnum.VIEW).stream()
                .sorted().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);
        // Check nhân sự là admin tổng
        Boolean isSuperAdmin = AuthUtil.isSuperAdmin();
        // Lấy page subCode thỏa mã điều kiện lọc, phân trang, sắp xếp giảm dần theo createdAt
        Page<ISubCodePageDTO> subCodePage = shoppingCartRepository.getSubCodeAdmin(dto.getValue(), dto.getSearchID(),
            dto.getSearchServiceName(), dto.getSearchCustomerName(), dto.getSearchTaxCode(), dto.getSearchIdentityNo(),
            dto.getSearchEmail(), dto.getSearchUserCode(), dto.getProvider(), dto.getSubStatus(), dto.getSubInstalled(), dto.getProgressStatus(),
            dto.getServiceType(), dto.getCustomerType(), dto.getTaxCode(), dto.getIdentityNo(), dto.getEmail(),
            dto.getPhone(), dto.getRepName(), dto.getFoundingFrom(), dto.getFoundingTo(), dto.getBusinessArea(),
            dto.getBusinessSize(), dto.getCreatedSource(), dto.getPaymentFrom(), dto.getPaymentTo(), dto.getProvinceId(),
            dto.getProvinceIdLst(), dto.getSubCreatedFrom(), dto.getSubCreatedTo(), dto.getPaymentCycle(), dto.getCycleType(),
            userId, devId, dto.getServiceOwner(), currentUserId, lstViewablePartitionId,
            dto.getDhsxCode(), isSuperAdmin, dto.getReferCode(), dto.getKhcnPhone(), dto.getPaymentMethod(),
            dto.getAmDepartments(), dto.getAmCodes(), dto.getAmNames(), dto.getAmPhones(), dto.getAmEmails(), pageable);

        // Tìm kiếm các bản ghi theo sub code đã tìm ở trên
        Set<String> setSubCode = subCodePage.getContent().stream().map(ISubCodePageDTO::getSubCode).collect(Collectors.toSet());
        List<ISubscriptionsSaaSResDTO> lstSubDetail = shoppingCartRepository.getSubscriptionBySubCodeAdmin(setSubCode);

        var mapSubCodeToBillingDetail = lstSubDetail.stream().collect(Collectors.groupingBy(ISubscriptionsSaaSResDTO::getSubCode));
        return subCodePage.map(item -> convertToSubscriptionDTO(item.getSubCode(), mapSubCodeToBillingDetail.get(item.getSubCode())));
    }

    private Boolean checkPermissionWithSubsOfCurrentUser (Long currentUserId) {
        List<String> roles = userRepository.getRole(currentUserId);
        if (!roles.isEmpty()) {
            List<String> permissionName = userRepository.getPermissionByRoleName(roles);
            return !permissionName.isEmpty() &&
                (permissionName.contains(PermissionNameEnum.MANAGE_SUBSCRIPTION.getValue()) ||
                    permissionName.contains(PermissionNameEnum.VIEW_LIST_SUBSCRIPTION.getValue())
                );
        }
        return false;
    }

    private SubscriptionsSaaSResDTO convertToSubscriptionDTO(String subCode, List<ISubscriptionsSaaSResDTO> lstSubDetail) {
        SubscriptionsSaaSResDTO resDTO = new SubscriptionsSaaSResDTO();
        List<String> lstPackageNames = new ArrayList<>();
        if (lstSubDetail.isEmpty()) {
            return resDTO;
        }
        ISubscriptionsSaaSResDTO saaSResDTO = lstSubDetail.get(0);
        // Lấy thông tin thuê bao
        resDTO.setSubCode(subCode);
        BeanUtils.copyProperties(saaSResDTO, resDTO);

        Map<Long, List<ISubscriptionsSaaSResDTO>> subscriptionAddonResITFDTOListDistinct = lstSubDetail.stream()
            .collect(Collectors.groupingBy(ISubscriptionsSaaSResDTO::getSubId));
        Set<Entry<Long, List<ISubscriptionsSaaSResDTO>>> entrySetPricing = subscriptionAddonResITFDTOListDistinct.entrySet();

        double subPrice = 0.0;
        for (Entry<Long, List<ISubscriptionsSaaSResDTO>> entryPricing : entrySetPricing) {
            ProductsDTO productsDTO = new ProductsDTO();
            AvatarProductDTO avatarProductDTO = new AvatarProductDTO();
            ISubscriptionsSaaSResDTO serviceInfo = entryPricing.getValue().get(0);
            // Lấy thông tin Product
            productsDTO.setServiceName(serviceInfo.getServiceName());
            productsDTO.setSimSubCode(serviceInfo.getSimSubCode());
            productsDTO.setServiceOwner(serviceInfo.getServiceOwner());
            productsDTO.setIsCombo(serviceInfo.getIsCombo());
            productsDTO.setProvider(serviceInfo.getProvider());
            productsDTO.setNameVariant(serviceInfo.getNameVariant());
            productsDTO.setQuantityVariant(serviceInfo.getQuantityVariant());
            productsDTO.setIsOnlyService(serviceInfo.getIsOnlyService());
            productsDTO.setProductClassification(serviceInfo.getProductClassification());
            // Lấy thông tin avatar
            BeanUtils.copyProperties(serviceInfo, avatarProductDTO);
            avatarProductDTO.setId(serviceInfo.getFileAttachId());
            productsDTO.setAvatar(avatarProductDTO);
            Double productPrice = 0.0;
            for (ISubscriptionsSaaSResDTO subPricing : entryPricing.getValue()) {
                // Lấy thông tin Pricing
                PricingProductDTO pricingProductDTO = new PricingProductDTO();
                BeanUtils.copyProperties(subPricing, pricingProductDTO);
                productsDTO.getLstPricing().add(pricingProductDTO);
                productPrice += subPricing.getTotalAmount();
                //Thêm tên package của pricing
                if (Objects.nonNull(subPricing.getPackageName()) && !lstPackageNames.contains(subPricing.getPackageName())) {
                    lstPackageNames.add(subPricing.getPackageName());
                    BundlingPackageDTO bundlingPackageDTO = new BundlingPackageDTO();
                    bundlingPackageDTO.setPackageName(subPricing.getPackageName());
                    bundlingPackageDTO.setPackageProvider(subPricing.getPackageProvider());
                    bundlingPackageDTO.setAvatarUrl(subPricing.getPackageAvatarUrl());

                    resDTO.getLstPackage().add(bundlingPackageDTO);

                }
            }
            productsDTO.setPrice(productPrice);
            productsDTO.setPriceVariant(serviceInfo.getPriceVariant());
            resDTO.getLstProduct().add(productsDTO);
            subPrice += productPrice;
        }
        //Set thông tin giải pháp và gói bundling
        if (Objects.nonNull(saaSResDTO.getSolutionName())){
            SolutionDTO solutionPackageDTO = new SolutionDTO();

            solutionPackageDTO.setSolutionName(saaSResDTO.getSolutionName());
            solutionPackageDTO.setAvatarUrl(saaSResDTO.getSolutionAvatarUrl());
            solutionPackageDTO.setSolutionProvider(saaSResDTO.getSolutionProvider());
            solutionPackageDTO.setLstPackageNames(lstPackageNames);

            resDTO.setSolution(solutionPackageDTO);

        }

        resDTO.setPrice(subPrice);
        return resDTO;
    }

    @Override
    public Page<BillingSubResDTO> getListBilling(String billOrSubCode, String taxCode, String identityNo, String subCode, Integer serviceType, String billingCode,
        Integer paymentStatus, String serviceName, String customerName, String customerType, Long subId, PortalType portal, Pageable pageable) {
        Long userId = -1L;
        Long devId = -1L;
        Long currentUserId = AuthUtil.getCurrentUserId();
        switch (portal) {
            case SME:
                if (Boolean.TRUE.equals(checkPermissionWithBillingOfCurrentUser(currentUserId))) {
                    userId = AuthUtil.getCurrentParentId();
                } else {
                    userId = currentUserId;
                }
                break;
            case ADMIN:
            case ONE_BSS:
            case UNSET:
            case ALL:
                break;
            case DEV:
                if (Boolean.TRUE.equals(checkPermissionWithBillingOfCurrentUser(currentUserId))) {
                    devId = AuthUtil.getCurrentParentId();
                } else {
                    devId = currentUserId;
                }
                break;
        }
        //Sme, Dev thì hàm AuthUtil.getAdminProvinceId() luôn ra -1. Sử dung biến adminProvinceId để đỡ gọi hàm AuthUtil.getAdminProvinceId()
        Long adminProvinceId = -1L; 
        // Lấy page billingCode thỏa mã điều kiện lọc, phân trang, sắp xếp
        Page<IBillingCodePageDTO> subBillPage = shoppingCartRepository.getListBilling(billOrSubCode, userId, devId, taxCode, identityNo,
            subCode, serviceType, billingCode, paymentStatus, serviceName, customerName, customerType, adminProvinceId, pageable);

        // Tìm kiếm các bản ghi theo billing code đã tìm ở trên
        List<SubBillingITFDTO> lstBillingSub = shoppingCartRepository.getBillingSubByBillingCode(subBillPage.getContent().stream()
            .map(IBillingCodePageDTO::getBillingCode).collect(Collectors.toList()), subId);
        Map<String, List<SubBillingITFDTO>> billingSubscriptionListDistinct = lstBillingSub.stream()
            .collect(Collectors.groupingBy(SubBillingITFDTO::getBillingCode));
        return subBillPage.map(item -> convertToBillingDTO(item.getBillingCode(), billingSubscriptionListDistinct.get(item.getBillingCode())));
    }

    @Override
    public Page<BillingSubResDTO> getListBillingAdmin(String billOrSubCode, String taxCode, String identityNo, String subCode, Integer serviceType, String value,
        Integer searchBillingCode, Integer searchCustomerName, Integer paymentStatus, String serviceName, String customerType, Long subId, PortalType portal, String serviceOwner, Pageable pageable) {
        Long userId = -1L;
        Long devId = -1L;
        Long currentUserId = AuthUtil.getCurrentUserId();
        switch (portal) {
            case SME:
                if(Boolean.TRUE.equals(checkPermissionWithBillingOfCurrentUser(currentUserId))) {
                    userId = AuthUtil.getCurrentParentId();
                }else {
                    userId = currentUserId;
                }
                break;
            case DEV:
            if(Boolean.TRUE.equals(checkPermissionWithBillingOfCurrentUser(currentUserId))) {
                devId = AuthUtil.getCurrentParentId();
            }else {
                devId = currentUserId;
            }
            break;
        }
        BigDecimal[] lstViewablePartitionId = crmObjectPermissionUtil.getPartitionIdByObjectRole(currentUserId, CrmPermissionEnum.VIEW).stream()
                .sorted().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);
        // Check nhân sự là admin tổng
        Boolean isSuperAdmin = AuthUtil.isSuperAdmin();
        // Lấy page billingCode thỏa mã điều kiện lọc, phân trang, sắp xếp
        Page<IBillingCodePageDTO> subBillPage = shoppingCartRepository.getListBillingAdmin(billOrSubCode, userId, devId, taxCode, identityNo,
            subCode, serviceType, value, searchBillingCode, searchCustomerName, paymentStatus, serviceName, customerType, AuthUtil.getAdminProvinceId(),serviceOwner,
                currentUserId, lstViewablePartitionId, isSuperAdmin, pageable);

        // Tìm kiếm các bản ghi theo billing code đã tìm ở trên
        List<SubBillingITFDTO> lstBillingSub = shoppingCartRepository.getBillingSubByBillingCode(subBillPage.getContent().stream()
            .map(IBillingCodePageDTO::getBillingCode).collect(Collectors.toList()), subId);
        Map<String, List<SubBillingITFDTO>> billingSubscriptionListDistinct = lstBillingSub.stream()
            .collect(Collectors.groupingBy(SubBillingITFDTO::getBillingCode));
        return subBillPage.map(item -> convertToBillingDTO(item.getBillingCode(), billingSubscriptionListDistinct.get(item.getBillingCode())));
    }

    private Boolean checkPermissionWithBillingOfCurrentUser (Long currentUserId) {
        List<String> roles = userRepository.getRole(currentUserId);
        if (!roles.isEmpty()) {
            List<String> permissionName = userRepository.getPermissionByRoleName(roles);
            return !permissionName.isEmpty() &&
                (permissionName.contains(PermissionNameEnum.MANAGE_BILLING.getValue()) ||
                    permissionName.contains(PermissionNameEnum.VIEW_LIST_BILLING.getValue())
                );
        }
        return false;
    }

    private BillingSubResDTO convertToBillingDTO(String billingCode, List<SubBillingITFDTO> lstBillingSub) {
        BillingSubResDTO resDTO = new BillingSubResDTO();
        if (Objects.isNull(lstBillingSub) || lstBillingSub.isEmpty()) {
            return resDTO;
        }
        resDTO.setBillingCode(billingCode);
        BeanUtils.copyProperties(lstBillingSub.get(0), resDTO);

        Map<String, List<SubBillingITFDTO>> billingSubListDistinct = lstBillingSub.stream()
            .collect(Collectors.groupingBy(SubBillingITFDTO::getBillingCode));
        Set<Entry<String, List<SubBillingITFDTO>>> entrySetPricing = billingSubListDistinct.entrySet();
        double billAmount = 0.0;
        for (Entry<String, List<SubBillingITFDTO>> entryPricing : entrySetPricing) {
            double amount = 0.0;
            for (SubBillingITFDTO itfdto : entryPricing.getValue()) {
                amount += itfdto.getPrice();
                resDTO.getBillingId().add(itfdto.getBillingId());
            }
            billAmount += amount;
        }
        resDTO.setPrice(billAmount);
        return resDTO;

    }

    @Override
    public GetBillingDetailDTO getBillingDetail(PortalType portal, String billingCode) {
        // Kiểm tra permission dựa trên user đăng nhập và portal type
        Long userId = -1L;
        switch (portal) {
            case SME:
                if (Boolean.TRUE.equals(checkPermissionWithBillingDetailOfCurrentUser(AuthUtil.getCurrentUserId()))) {
                    userId = AuthUtil.getCurrentParentId();
                }else {
                    userId = AuthUtil.getCurrentUserId();
                }
                break;
            case DEV:
                //kiểm tra billing dịch vụ nào có provider_id = devId ko
                if (Boolean.FALSE.equals(checkPermissionWithBillingDetailOfCurrentUser(AuthUtil.getCurrentUserId()))) {
                    throw exceptionFactory.badRequest(MessageKeyConstant.PERMISSION_DENIED, Resources.DETAIL_BILLING, ErrorKey.BILL_CODE,
                        String.valueOf(billingCode));
                }
                break;
        }

        List<IGetBillingDetailDTO> lstDetail;
        MigrationServiceTypeEnum migrationServiceType = MigrationServiceTypeEnum.valueOf(subscriptionRepository.getCategoriesMigrationIdByBillingCode(billingCode));
        if (!Objects.equals(migrationServiceType, MigrationServiceTypeEnum.UNSET)) {
            // TH là hóa đơn của dv ban KHCN
            String subEmail = subscriptionRepository.getSubCustomerEmailByBillingCode(billingCode);
            if (Objects.equals(subEmail, ApiGwKHCNConstant.EMAIL_KHCN_VANG_LAI)) {
                // TH KH vang lai --> lay thong tin KH trong bang billing + sub
                lstDetail = shoppingCartRepository.getBanKHCNVisitingCustomerBillingDetail(userId, billingCode, AuthUtil.getAdminProvinceId());
            } else {
                lstDetail = shoppingCartRepository.getBanKHCNBillingDetail(userId, billingCode, AuthUtil.getAdminProvinceId());
            }
        } else {
            lstDetail = shoppingCartRepository.getBillingDetail(userId, billingCode, AuthUtil.getAdminProvinceId());
        }

        if (lstDetail.isEmpty()) {
            return new GetBillingDetailDTO();
        }
        return convertToBillingDetailDTO(lstDetail, migrationServiceType);
    }

    private Boolean checkPermissionWithBillingDetailOfCurrentUser (Long currentUserId) {
        List<String> roles = userRepository.getRole(currentUserId);
        if (!roles.isEmpty()) {
            List<String> permissionName = userRepository.getPermissionByRoleName(roles);
            return !permissionName.isEmpty() &&
                (permissionName.contains(PermissionNameEnum.MANAGE_BILLING.getValue()) ||
                    permissionName.contains(PermissionNameEnum.VIEW_DETAIL_BILLING.getValue())
                );
        }
        return false;
    }

    private GetBillingDetailDTO convertToBillingDetailDTO(List<IGetBillingDetailDTO> detailDTO, MigrationServiceTypeEnum migrationServiceType) {
        GetBillingDetailDTO result = new GetBillingDetailDTO();
        IGetBillingDetailDTO billDetailInfo = detailDTO.get(0);
        //lấy thông tin provider
        Provider provider = new Provider();
        BeanUtils.copyProperties(billDetailInfo, provider);
        if (Objects.isNull(provider.getProviderAddress())) {
            provider.setProviderAddress(addressRepository.getAddressByUserId(billDetailInfo.getProviderId()));
        }
        result.setProvider(provider);

        //lấy thông tin customer
        Customer customer = new Customer();
        String orderAddressesStr = productOrdersRepository.getProductOrderAddressByBillingId(billDetailInfo.getBillingId());
        if (Objects.nonNull(orderAddressesStr)) {
            OrderAddressDTO orderAddressDTO = GsonMapperUtil.fromJson(orderAddressesStr, OrderAddressDTO.class);
            if (Objects.nonNull(orderAddressDTO)) {
                customer.setCustomerName(orderAddressDTO.getSmeName());
                customer.setCustomerLastName(orderAddressDTO.getLastName());
                customer.setCustomerFirstName(orderAddressDTO.getFirstName());
                customer.setCustomerTaxCode(orderAddressDTO.getTin());
                customer.setIdentityNo(orderAddressDTO.getRepPersonalCertNumber());
                customer.setCustomerAddress(orderAddressDTO.getAddress());
                customer.setAddressType(orderAddressDTO.getTypeAddress());
                customer.setCompanyName(orderAddressDTO.getSmeName());

                customer.setCustomerProvinceId(orderAddressDTO.getProvinceId());
                customer.setCustomerProvinceCode(orderAddressDTO.getProvinceCode());
                customer.setCustomerProvinceName(orderAddressDTO.getProvinceName());

                customer.setCustomerDistrictId(orderAddressDTO.getDistrictId());
                customer.setCustomerDistrictCode(orderAddressDTO.getDistrictCode());
                customer.setCustomerDistrictName(orderAddressDTO.getDistrictName());

                customer.setCustomerWardId(orderAddressDTO.getWardId());
                customer.setCustomerWardCode(orderAddressDTO.getWardCode());
                customer.setCustomerWardName(orderAddressDTO.getWardName());

                customer.setCustomerStreetId(orderAddressDTO.getStreetId());
                customer.setCustomerStreetName(orderAddressDTO.getStreetName());
            }
        } else {
            BeanUtils.copyProperties(billDetailInfo, customer);
        }
        result.setCustomer(customer);

        //lấy thông tin payment
        Payment payment = new Payment();
        BeanUtils.copyProperties(billDetailInfo, payment);
        payment.setNumberOfDayExportBill(getBillExpiredDate(billDetailInfo.getPaymentDate()));
        result.setPayment(payment);
        result.setCalculateTypeEnum(Objects.nonNull(billDetailInfo.getSubGroupCode()) ? CalculateTypeEnum.SERVICE_GROUP : CalculateTypeEnum.UNSET);

        Map<String, List<IGetBillingDetailDTO>> billingServiceDistinct = detailDTO.stream()
            .collect(Collectors.groupingBy(IGetBillingDetailDTO::getGroupByService));
        Set<Entry<String, List<IGetBillingDetailDTO>>> entrySet = billingServiceDistinct.entrySet();

        long indexBillItem = 0L;
        double totalAfterTax = 0L;
        List<Long> billingId = new ArrayList<>();
        for (Entry<String, List<IGetBillingDetailDTO>> set : entrySet) {
            BillItem billItem = new BillItem();
            IGetBillingDetailDTO billingDetailDTO = set.getValue().get(0);
            //thông tin về billItem
            BeanUtils.copyProperties(billingDetailDTO, billItem);
            billItem.setNo(++indexBillItem);
            double priceOfBillItem = 0L;
            if (!Objects.equals(MigrationServiceTypeEnum.UNSET, migrationServiceType)) {
                // dv ban KHCN
                //lấy thông tin về các dịch vụ bill_item
                for (IGetBillingDetailDTO billDTO : set.getValue()) {
                    Item item = new Item();
                    item.setItemId(billDTO.getBillItemId());
                    item.setItemName(billDTO.getItemName());
                    item.setPrice(billDTO.getItemPrice());
                    billItem.getLstBillItem().add(item);
                    priceOfBillItem += billDTO.getItemPrice();
                    totalAfterTax += billDTO.getItemPrice();
                    billItem.setBillingId(billDTO.getBillingId());
                    billingId.add(billDTO.getBillingId());
                }

            } else {
                List<IGetBillingDetailDTO> listPricing = set.getValue().stream().filter((i) -> !Arrays.asList(BillItemType.VARIANT.getValue(),
                        BillItemType.FEE_VARIANT.getValue(), BillItemType.ONLY_SERVICE.getValue())
                    .contains(i.getObjectType())).collect(Collectors.toList());
                List<IGetBillingDetailDTO> listVariant = set.getValue().stream().filter((i) -> Arrays.asList(BillItemType.VARIANT.getValue(),
                        BillItemType.ONLY_SERVICE.getValue(), BillItemType.FEE_VARIANT.getValue())
                    .contains(i.getObjectType())).collect(Collectors.toList());
                //lấy thông tin về các dịch vụ bill_item
                for (IGetBillingDetailDTO billDTO : listPricing) {
                    Item item = new Item();
                    item.setItemId(billDTO.getBillItemId());
                    item.setItemName(billDTO.getItemName());
                    item.setPrice(billDTO.getItemPrice());
                    billItem.getLstBillItem().add(item);
                    priceOfBillItem += billDTO.getItemPrice();
                    totalAfterTax += billDTO.getItemPrice();
                    billItem.setBillingId(billDTO.getBillingId());
                    billingId.add(billDTO.getBillingId());
                }
                double priceVariant = listVariant.stream().map(IGetBillingDetailDTO::getItemPrice).reduce(0.0, Double::sum);
                IGetBillingDetailDTO itemVariant = listVariant.stream().filter(i -> Objects.equals((BillItemType.VARIANT.getValue()), i.getObjectType())).findFirst().orElse(null);
                billItem.setPriceVariant(priceVariant);
                billItem.setVariantName(Objects.nonNull(itemVariant) ? itemVariant.getItemName() : null);
                if (!listVariant.isEmpty()) {
                    billItem.setIsBuyService(true);
                }
            }

            billItem.setPrice(priceOfBillItem);
            result.getBillItem().add(billItem);
        }
        result.setTotalAfterTaxAmount(totalAfterTax);
        //tổng tiền thanh toán sau cùng với billing
        result.setTotalAfterTaxFinalAmount(shoppingCartRepository.getTotalAmountAfterAdjustmentByBillingCode(billDetailInfo.getBillingCode()));

        //lấy thông tin creditNote
        if (!billingId.isEmpty()) {
            List<CreditNoteCalculateDTO> creditNoteApply = creditNoteRepository.getAllCreditNoteByListId(billingId,BillConstant.CREDIT_APPLY);
            List<CreditNoteCalculateDTO> creditNoteNew = creditNoteRepository.getAllCreditNoteByListId(billingId,BillConstant.CREDIT_NEW);

            result.setCreditNoteApplies(creditNoteApply);
            result.setCreditNoteNews(creditNoteNew);
        }

        //lấy thông tin nguồn tạo của billing
        result.setCreatedSourceMigration(CreatedSourceMigrationEnum.valueOf(billDetailInfo.getCreatedSourceMigration()));

        //lấy thông tin invoice
        List<InvoiceInfoDTO> invoiceList;
        //nếu hóa đơn từ giỏ hàng thì lấy bằng billingCode còn lại lấy bằng billingId
        if (Boolean.TRUE.equals(billDetailInfo.getIsCart()) && Boolean.TRUE.equals(!billDetailInfo.getIsOnlyService())) {
            invoiceList = eInvoiceRepository.getInvoiceByBillingCode(result.getPayment().getBillingCode());
        } else {
            invoiceList = eInvoiceRepository.getInvoiceByBillingId(result.getBillItem().get(0).getBillingId());
        }

        if (!invoiceList.isEmpty()) {
            for (InvoiceInfoDTO invoiceInfoDTO : invoiceList) {
                EInvoice eInvoice = new EInvoice();
                BeanUtils.copyProperties(invoiceInfoDTO, eInvoice);
                result.getEInvoice().add(eInvoice);
            }
        }
        Bills billDb = billsRepository.findById(billDetailInfo.getBillingId()).orElseThrow(null);
        result.setBilling(billsService.getBillInfo(billDb));
        //one time fee
        result.setOnceTimeFee(getOnceTimeFee(billDb.getSubscriptionsId()));
        return result;
    }

    public Integer getBillExpiredDate(Date paymentDate) {
        Optional<SystemParam> systemParam = systemParamService.findOptionalByParamType(SystemParamType.EXPORT_BILLING);
        int day = systemParam.isPresent() ? systemParam.get().getNumberOfDayExportBill() : 3;
        if (paymentDate != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(paymentDate);
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            c.set(Calendar.MILLISECOND, 0);
            paymentDate = c.getTime();
            Integer oldMonth = c.get(Calendar.MONTH);
            c.add(Calendar.DATE, day);
            Integer newMonth = c.get(Calendar.MONTH);
            if (!oldMonth.equals(newMonth)) {
                c.add(Calendar.DATE, -day);
                c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                Date lastDayOfMonth = c.getTime();
                long diff = lastDayOfMonth.getTime() - paymentDate.getTime();
                TimeUnit time = TimeUnit.DAYS;
                long days = time.convert(diff, TimeUnit.MILLISECONDS) + 1L;
                day = (int) days;
            }
        }
        return day;
    }


    @Override
    public Page<IGetSubDetailBillingDTO> getLstOrderBilling(Long productOrderId, PortalType portal, Pageable pageable) {
        // Kiểm tra permission dựa trên user đăng nhập và portal type
        Long userId = -1L;
        Long devId = -1L;
        switch (portal) {
            case SME:
                userId = AuthUtil.getCurrentUserId();
                break;
            case DEV:
                devId = AuthUtil.getCurrentUserId();
                break;
            case ADMIN:
                // Check view permission for admin/am
                Set<Long> lstSubId = subscriptionRepository.getLstSubIdByProductOrderId(productOrderId);
                crmObjectPermissionUtil.checkViewPermissionForLstObjectId(CrmObjectTypeEnum.SUBSCRIPTION, lstSubId);
        }

        return shoppingCartRepository.getLstOrderBillingByProductOrderId(productOrderId, userId, devId, AuthUtil.getAdminProvinceId(), pageable);
    }

    @Override
    public Page<IGetSubDetailBillingDTO> getBillingOnSubscriptionDetail(Long subId, PortalType portal, Pageable pageable) {
        // Kiểm tra permission dựa trên user đăng nhập và portal type
        Long userId = -1L;
        Long devId = -1L;
        switch (portal) {
            case SME:
                userId = AuthUtil.getCurrentUserId();
                break;
            case DEV:
                devId = AuthUtil.getCurrentUserId();
                break;
            case ADMIN:
                // Check view permission for admin/am
                crmObjectPermissionUtil.checkViewPermission(CrmObjectTypeEnum.SUBSCRIPTION, subId);
        }
        return shoppingCartRepository.getBillingOnSubscriptionDetail(subId, userId, devId, AuthUtil.getAdminProvinceId(), pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public ShoppingCartFormulaResDTO calculateShoppingCartAdmin(ShoppingCartFormulaReqDTO request, CreatedSourceMigrationEnum createdSource) {
        if (request.getUserId() == null) {
            throw exceptionFactory.badRequest(ShoppingCartError.INVALID_USER_ID, Resources.SUBSCRIPTION, ErrorKey.USER_ID);
        }
        return calculateShoppingCart(request, createdSource);
    }

    @Override
    @Transactional(readOnly = true)
    public ShoppingCartFormulaResDTO calculateShoppingCartSME(ShoppingCartFormulaReqDTO request, CreatedSourceMigrationEnum createdSource) {
        if (request.getUserId() == null) {
            request.setUserId(AuthUtil.getCurrentUserId());
        }
        return calculateShoppingCart(request, createdSource);
    }

    @Override
    public List<SubscriptionsDTO> getListSubscriptionEvaluation(String name, Long categoriesId, Long createdBy) {
        String createSubSpdv = ActivityCodeEnum.UPDATE_PRICING_SPDV.value;
        name = StringUtils.isEmpty(name) ? StringUtils.EMPTY
                : SqlUtils.optimizeSearchLike(name);
        List<ISubscriptionsDTO> iSubscriptionsList =
            shoppingCartRepository.getListSubscriptionEvaluation(name, createdBy, AuthUtil.getCurrentParentId(), createSubSpdv);
        List<SubscriptionsDTO> subscriptionsDTOs;
        if (categoriesId == -1) {
            subscriptionsDTOs = iSubscriptionsList.stream().map(iSubscriptionsDTO -> {
                SubscriptionsDTO subscriptionsDTO = new SubscriptionsDTO();
                BeanUtils.copyProperties(iSubscriptionsDTO, subscriptionsDTO);
                subscriptionsDTO.setCategoriesId(StringUtil.convertStringArrToListLong(iSubscriptionsDTO.getCategoriesId()));
                if (Objects.isNull(iSubscriptionsDTO.getEvaluationDeadline())){
                    if (Objects.equals(iSubscriptionsDTO.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
                        subscriptionsDTO.setEvaluationDeadline(orderServiceReceiveRepository.getLogCompleteByOrderServiceIdByCreateSourceDHSXKD(Long.parseLong(iSubscriptionsDTO.getSubId())));
                    } else {
                        subscriptionsDTO.setEvaluationDeadline(orderServiceReceiveRepository.getLogCompleteByOrderServiceId(Long.parseLong(iSubscriptionsDTO.getSubId())));
                    }
                }
                return subscriptionsDTO;
            }).collect(Collectors.toList());
        } else {
            subscriptionsDTOs = iSubscriptionsList.stream().map(iSubscriptionsDTO -> {
                SubscriptionsDTO subscriptionsDTO = new SubscriptionsDTO();
                BeanUtils.copyProperties(iSubscriptionsDTO, subscriptionsDTO);
                subscriptionsDTO.setCategoriesId(StringUtil.convertStringArrToListLong(iSubscriptionsDTO.getCategoriesId()));
                if (Objects.isNull(iSubscriptionsDTO.getEvaluationDeadline())){
                    if (Objects.equals(iSubscriptionsDTO.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
                        subscriptionsDTO.setEvaluationDeadline(orderServiceReceiveRepository.getLogCompleteByOrderServiceIdByCreateSourceDHSXKD(Long.parseLong(iSubscriptionsDTO.getSubId())));
                    } else {
                        subscriptionsDTO.setEvaluationDeadline(orderServiceReceiveRepository.getLogCompleteByOrderServiceId(Long.parseLong(iSubscriptionsDTO.getSubId())));
                    }
                }
                return subscriptionsDTO;
            }).filter(subscriptionsDTO -> subscriptionsDTO.getCategoriesId().contains(categoriesId)).collect(Collectors.toList());
        }
        return subscriptionsDTOs;
    }

    public CalSetupFeeDeviceReqDTO getCalSetupFeeDeviceReqDTO(Long serviceId, Long serviceDraftId, BigDecimal inputSetupFeePriceDevice) {
        if (Objects.isNull(serviceId) && Objects.isNull(serviceDraftId)) {
            return null;
        }
        CalSetupFeeDeviceReqDTO res = new CalSetupFeeDeviceReqDTO();
        ServiceDraft serviceDraft;
        if (Objects.nonNull(serviceDraftId)) { // nếu là chi tiết -> lấy thông tin đã mua
            serviceDraft = serviceDraftRepository.findById(serviceDraftId).orElse(null);
        } else { // mua mới
            serviceDraft = serviceDraftRepository.getServiceDraftApproved(serviceId);
        }
        if (Objects.nonNull(serviceDraft) && Objects.nonNull(serviceDraft.getProductType()) &&
            Objects.equals(serviceDraft.getProductType(), ServiceProductTypeEnum.DEVICE)) {
            res.setInputSetupFeePriceDevice(Objects.nonNull(inputSetupFeePriceDevice) ? inputSetupFeePriceDevice : BigDecimal.ZERO);
            SetupFeeInfoConvertDTO setupFee = serviceDraft.getSetupFee();
            if (Objects.nonNull(setupFee)) {
                res.setSetupFeePriceDevice(Objects.nonNull(setupFee.getPrice()) ? setupFee.getPrice() : BigDecimal.ZERO);
                res.setTaxList(!CollectionUtils.isEmpty(setupFee.getTaxList()) ? setupFee.getTaxList().stream().map(e -> {
                    PricingTax pricingTax = new PricingTax();
                    pricingTax.setPercent(e.getValue().doubleValue());
                    pricingTax.setTaxName(e.getType());
                    pricingTax.setHasTax(setupFee.getHasTax().value);
                    return pricingTax;
                }).collect(Collectors.toList()) : null);
            }
        } else {
            res = null;
        }
        return res;
    }

    @Override
    public SubOrderServiceAndProductDetailDTO getSubDetailTabServiceAndProduct(Long subId, PortalType portalType) {
        SubOrderServiceAndProductDetailDTO result = new SubOrderServiceAndProductDetailDTO();

        // Thông tin chung của sub:
        ISubOrderOverviewDTO commonInfo = subscriptionRepository.getCommonInfoSubOrderOverview(subId);
        // check quyền được xem
        shoppingCartSmeService.checkPermission(portalType, Collections.singleton(commonInfo.getServiceProviderId()), AuthUtil.getCurrentParentId(), commonInfo.getCustomerId());

        BeanUtils.copyProperties(commonInfo, result);

        // thông tin thành phần
        List<SubOrderItemDTO> lstAllServiceAndProduct = getItemBySubId(subId, commonInfo);
        // Dịch vụ
        result.setLstService(lstAllServiceAndProduct.stream()
            .filter(d -> Objects.equals(d.getClassification(), ProductClassificationEnum.SERVICE.getValue()) ||
                Objects.equals(d.getClassification(), ProductClassificationEnum.DIGITAL.getValue()))
            .collect(Collectors.toList()));
        // Sim số: pending TODO
        // Hàng hóa vật lý
        result.setLstDevice(lstAllServiceAndProduct.stream()
            .filter(d -> Objects.equals(d.getClassification(), ProductClassificationEnum.PHYSICAL.getValue()))
            .collect(Collectors.toList()));

        result.setServiceQuantity(result.getLstService().size());
        result.setDeviceQuantity(result.getLstDevice().size());

        // CTKM tổng:
        result.setCouponList(getLstSubOrderCouponTotalBySubId(subId));

        // Set thông tin tiến trình tổng của đơn hàng order
        ProductOrders productOrder =  productOrdersRepository.findFirstBySubscriptionIdOrderByIdDesc(subId).orElse(null);
        if (Objects.nonNull(productOrder) && Objects.nonNull(productOrder.getTotalOrderProgress())) {
            result.setTotalProgressStatus(productOrder.getTotalOrderProgress().getCode());
            result.setTotalProgressName(productOrder.getTotalOrderProgress().getDescription());
            result.setTotalProgressStatusEnum(productOrder.getTotalOrderProgress());
        }

        return result;
    }

    @Override
    public SubOrderServiceAndProductDetailDTO getCartSubDetailTabServiceAndProduct(String cartCode, PortalType portalType) {
        SubOrderServiceAndProductDetailDTO result = new SubOrderServiceAndProductDetailDTO();

        // Thông tin chung của sub:
        ISubOrderOverviewDTO commonInfo = subscriptionRepository.getCommonInfoCartSubOrderOverview(cartCode);
        // check quyền được xem
        // SME chỉ thấy được sub của chính SME đó
        // ADMIN thấy tất cả sub
        // Dev thấy được sub của gói bundling dev đó tạo (kể cả bundling có chứa thành phần của các dev khác)
        shoppingCartSmeService.checkPermission(portalType, Collections.singleton(commonInfo.getPackageProviderId()), AuthUtil.getCurrentParentId(), commonInfo.getCustomerId());

        BeanUtils.copyProperties(commonInfo, result);

        // thông tin thành phần
        List<SubOrderItemDTO> lstAllServiceAndProduct = getItemByCartCode(cartCode, commonInfo);

        // Dịch vụ
        result.setLstService(lstAllServiceAndProduct.stream()
            .filter(d -> Objects.equals(d.getClassification(), ProductClassificationEnum.SERVICE.getValue()) ||
                Objects.equals(d.getClassification(), ProductClassificationEnum.DIGITAL.getValue()))
            .collect(Collectors.toList()));
        // Sim số: pending TODO
        // Hàng hóa vật lý
        result.setLstDevice(lstAllServiceAndProduct.stream()
            .filter(d -> Objects.equals(d.getClassification(), ProductClassificationEnum.PHYSICAL.getValue()))
            .collect(Collectors.toList()));

        result.setServiceQuantity(result.getLstService().size());
        result.setDeviceQuantity(result.getLstDevice().size());

        // CTKM tổng:
        result.setCouponList(getSubOrderCouponTotalByCartCode(cartCode));

        // Set thông tin tiến trình tổng của đơn hàng order
        ProductOrders productOrder =  productOrdersRepository.findFirstByCartCodeOrderByIdDesc(cartCode).orElse(null);
        if (Objects.nonNull(productOrder) && Objects.nonNull(productOrder.getTotalOrderProgress())) {
            result.setTotalProgressStatus(productOrder.getTotalOrderProgress().getCode());
            result.setTotalProgressName(productOrder.getTotalOrderProgress().getDescription());
            result.setTotalProgressStatusEnum(productOrder.getTotalOrderProgress());
        }

        return result;
    }

    @Override
    public SubOrderOverviewDTO getOrderOverview(Long subId, PortalType portalType) {
        SubOrderOverviewDTO dto = new SubOrderOverviewDTO();
        ISubOrderOverviewDTO data = subscriptionRepository.getCommonInfoSubOrderOverview(subId);
        // check quyền được xem
        shoppingCartSmeService.checkPermission(portalType, Collections.singleton(data.getServiceProviderId()), AuthUtil.getCurrentParentId(), data.getCustomerId());

        dto.setIsCart(false);
        // Thông tin khách hàng
        setOrderOverviewCustomerInfo(data, dto);
        // Thông tin đơn hàng
        dto.setTotalProgressStatusEnum(TotalOrderProgressStatusEnum.fromCode(data.getOrderProgress()));
        dto.setTotalProgressStatus(Objects.nonNull(dto.getTotalProgressStatusEnum()) ? dto.getTotalProgressStatusEnum().getCode() : TotalOrderProgressStatusEnum.UNSET.getCode());
        dto.setTotalProgressName(Objects.nonNull(dto.getTotalProgressStatusEnum()) ? dto.getTotalProgressStatusEnum().getDescription() : "");

        // sau khi thanh toán sinh 1 sub -> tức là mua 1 hàng hóa vật lý
        List<SubOrderItemDTO> lstDeviceDetail = getItemBySubId(subId, null);
        dto.setLstDeviceDetail(lstDeviceDetail);

        dto.setCouponList(getLstSubOrderCouponTotalBySubId(subId));
        return dto;
    }

    @Override
    public SubOrderOverviewDTO getCartOrderOverview(String cartCode, PortalType portalType) {
        SubOrderOverviewDTO dto = new SubOrderOverviewDTO();

        ISubOrderOverviewDTO data = subscriptionRepository.getCommonInfoCartSubOrderOverview(cartCode);

        // check quyền được xem
        // SME chỉ thấy được sub của chính SME đó
        // ADMIN thấy tất cả sub
        // Dev thấy được sub của gói bundling dev đó tạo (kể cả bundling có chứa thành phần của các dev khác)
        shoppingCartSmeService.checkPermission(portalType, Collections.singleton(data.getPackageProviderId()), AuthUtil.getCurrentParentId(), data.getCustomerId());

        dto.setCode(data.getCode());
        dto.setIsCart(data.getIsCart());
        dto.setTotalProgressStatusEnum(TotalOrderProgressStatusEnum.fromCode(data.getOrderProgress()));
        dto.setTotalProgressStatus(Objects.nonNull(dto.getTotalProgressStatusEnum()) ? dto.getTotalProgressStatusEnum().getCode() : TotalOrderProgressStatusEnum.UNSET.getCode());
        dto.setTotalProgressName(Objects.nonNull(dto.getTotalProgressStatusEnum()) ? dto.getTotalProgressStatusEnum().getDescription() : "");

        // Thông tin khách hàng
        setOrderOverviewCustomerInfo(data, dto);

        // Thông tin đơn hàng
        List<SubOrderItemDTO> lstPackageSubItemDetail = getItemByCartCode(cartCode, null);
        if (Objects.nonNull(data.getPackageId()) || Objects.nonNull(data.getSolutionId())) {
            // TH1: đơn hàng gói bunlding
            SubOrderOverviewBundlingDetailDTO bundlingDetail = new SubOrderOverviewBundlingDetailDTO();
            BeanUtils.copyProperties(data, bundlingDetail);
            // lấy danh sách thành phần trong bundling
            bundlingDetail.setLstItemDetail(lstPackageSubItemDetail);
            bundlingDetail.setServiceQuantity((int) lstPackageSubItemDetail.stream()
                .filter(d -> Objects.equals(d.getClassification(), ProductClassificationEnum.SERVICE.getValue()) ||
                    Objects.equals(d.getClassification(), ProductClassificationEnum.DIGITAL.getValue())).count());
            bundlingDetail.setDeviceQuantity((int) lstPackageSubItemDetail.stream()
                .filter(d -> Objects.equals(d.getClassification(), ProductClassificationEnum.PHYSICAL.getValue())).count());
            dto.setBundlingDetail(bundlingDetail);
        } else {
            // TH2: đơn hàng Hàng hóa vật lý
            dto.setLstDeviceDetail(lstPackageSubItemDetail);
        }
        dto.setCouponList(getSubOrderCouponTotalByCartCode(cartCode));

        return dto;
    }

    private static void setOrderOverviewCustomerInfo(ISubOrderOverviewDTO data, SubOrderOverviewDTO dto) {
        SubOrderOverviewCustomerDetailDTO customerDetail = new SubOrderOverviewCustomerDetailDTO();
        BeanUtils.copyProperties(data, customerDetail);
        OrderAddressDTO billAddress = GsonMapperUtil.fromJson(data.getAddress(), OrderAddressDTO.class);
        if (Objects.nonNull(billAddress)) {
            String finalAddress = billAddress.getAddress();
            if (StringUtils.isBlank(finalAddress)) {
                finalAddress = Stream.of(billAddress.getStreetName(), billAddress.getWardName(), billAddress.getDistrictName(), billAddress.getProvinceName())
                    .filter(s -> s != null && !s.trim().isEmpty())
                    .map(String::trim)
                    .collect(Collectors.joining(", "));
            }
            customerDetail.setAddress(finalAddress);
        }
        customerDetail.setCustomerType(CustomerTypeEnum.fromValue(data.getCustomerType()));
        dto.setCustomerDetail(customerDetail);
    }

    private List<SubOrderItemDTO> getItemByCartCode(String cartCode, ISubOrderOverviewDTO commonInfo) {
        List<ISubPackageItemDTO> lstItemData = packageItemRepository.getSubPackageItemDetailByCartCode(cartCode);

        // Thuế + phí của từng thành phần
        Set<Long> lstPricingId = lstItemData.stream()
            .map(ISubPackageItemDTO::getPricingId).collect(Collectors.toSet());
        List<PricingTaxRes> lstPricingTax = pricingTaxRepository.getPricingTaxByIds(lstPricingId);
        List<PricingTaxRes> lstPricingSetUpFeeTax = pricingSetupFeeTaxRepository.getListPricingSetupFeeTax(lstPricingId);
        List<ISubOrderItemAddonDTO> lstAddonData = packageItemRepository.getSubAddonItemByCartCode(cartCode);
        // Phí nhanh
        List<CustomFee> lstAllSubOtherFee = customFeeRepository.findAllBySubscriptionIdIn(lstItemData.stream()
            .map(ISubPackageItemDTO::getSubId).collect(Collectors.toSet()));
        // danh sach coupon addon:
        List<Long> lstAddonId = lstAddonData.stream().map(ISubOrderItemAddonDTO::getId).collect(Collectors.toList());
        List<ISubOrderCouponDetailDTO> lstAllCouponAddon = addonRepository.getSubOrderAddonCouponByCartCode(cartCode, lstAddonId);
        List<PricingTaxRes> lstAllAddonTax = addonsTaxRepository.getAddonTax(lstAddonId);
        List<PricingTaxRes> lstAllAddonSetupFeeTax = addonsTaxRepository.getAddonSetupFeeTaxByIds(lstAddonId);
        // danh sach coupon của pricing
        List<ISubOrderCouponDetailDTO> lstAllPricingCoupon = subscriptionRepository.getSubOrderPrivateCouponByCartCode(cartCode);
        return getLstSubOrderItem(lstItemData, lstAddonData, lstAllAddonTax, lstAllAddonSetupFeeTax,
            lstAllCouponAddon, lstAllPricingCoupon, lstPricingTax, lstPricingSetUpFeeTax, lstAllSubOtherFee, commonInfo);
    }

    private List<SubOrderItemDTO> getItemBySubId(Long subId, ISubOrderOverviewDTO commonInfo) {
        List<ISubPackageItemDTO> lstItemData = packageItemRepository.getSubPackageItemDetailBySubId(subId);
        // Thuế + phí của từng thành phần
        Set<Long> lstPricingId = lstItemData.stream()
            .map(ISubPackageItemDTO::getPricingId).collect(Collectors.toSet());
        List<PricingTaxRes> lstPricingTax = pricingTaxRepository.getPricingTaxByIds(lstPricingId);
        List<PricingTaxRes> lstPricingSetUpFeeTax = pricingSetupFeeTaxRepository.getListPricingSetupFeeTax(lstPricingId);
        // Phí nhanh
        List<CustomFee> lstAllSubOtherFee = customFeeRepository.findAllBySubscriptionId(subId);

        // ADDON
        List<ISubOrderItemAddonDTO> lstAddonData = packageItemRepository.getSubAddonItemBySubId(subId);
        // danh sach coupon addon:
        List<Long> lstAddonId = lstAddonData.stream().map(ISubOrderItemAddonDTO::getId).collect(Collectors.toList());
        List<ISubOrderCouponDetailDTO> lstAllCouponAddon = addonRepository.getSubOrderAddonCouponBySubId(subId, lstAddonId);
        List<PricingTaxRes> lstAllAddonTax = addonsTaxRepository.getAddonTax(lstAddonId);
        List<PricingTaxRes> lstAllAddonSetupFeeTax = addonsTaxRepository.getAddonSetupFeeTaxByIds(lstAddonId);
        // danh sach coupon của pricing
        List<ISubOrderCouponDetailDTO> lstAllPricingCoupon = subscriptionRepository.getSubOrderPrivateCouponBySubId(subId);
        return getLstSubOrderItem(lstItemData, lstAddonData, lstAllAddonTax, lstAllAddonSetupFeeTax,
            lstAllCouponAddon, lstAllPricingCoupon, lstPricingTax, lstPricingSetUpFeeTax, lstAllSubOtherFee, commonInfo);
    }

    private List<CouponPackageDTO> getSubOrderCouponTotalByCartCode(String cartCode) {
        List<ISubOrderCouponDetailDTO> lstTotalCoupon = subscriptionRepository.getSubOrderTotalCouponByCartCode(cartCode);
        return getLstSubOrderCouponTotal(lstTotalCoupon);
    }

    private List<CouponPackageDTO> getLstSubOrderCouponTotalBySubId(Long subId) {
        List<ISubOrderCouponDetailDTO> lstTotalCoupon = subscriptionRepository.getSubOrderTotalCouponBySubId(subId);
        return getLstSubOrderCouponTotal(lstTotalCoupon);
    }

    private static List<CouponPackageDTO> getLstSubOrderCouponTotal(List<ISubOrderCouponDetailDTO> lstTotalCoupon) {
        return lstTotalCoupon.stream().map(c -> {
            CouponPackageDTO coupon = new CouponPackageDTO();
            coupon.setCouponType(CouponTypeEnum.ADDON);
            coupon.setObjectName(c.getCouponName());

            CouponItem couponItem = new CouponItem();
            couponItem.setCouponName(c.getCouponName());
            couponItem.setId(c.getCouponId());
            couponItem.setPrice(c.getDiscountValue());
            coupon.setLstCouponItem(Collections.singletonList(couponItem));
            return coupon;
        }).collect(Collectors.toList());
    }

    private List<SubOrderItemDTO> getLstSubOrderItem(List<ISubPackageItemDTO> lstItemData, List<ISubOrderItemAddonDTO> lstAddonData,
        List<PricingTaxRes> lstAllAddonTax, List<PricingTaxRes> lstAllAddonSetupFeeTax, List<ISubOrderCouponDetailDTO> lstAllCouponAddon,
        List<ISubOrderCouponDetailDTO> lstAllPricingCoupon, List<PricingTaxRes> lstAllPricingTax, List<PricingTaxRes> lstAllPricingSetUpFee,
        List<CustomFee> lstAllSubOtherFee, ISubOrderOverviewDTO shippingInfo) {

        // Timeline trạng thái của all item
        List<ISubOrderStatusHistoryResDTO> allSubStatusTimeline = subscriptionOrderStatusHistoryRepository.getTimelineSubOrderStatusHistory(
            lstItemData.stream().map(ISubPackageItemDTO::getSubId).collect(Collectors.toSet()));

        return lstItemData.stream()
            .map(d -> {
                SubOrderItemDTO dto = new SubOrderItemDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setOnOsType(OnOsTypeEnum.valueOf(d.getOnOsType()));
                // Tiến trình
                SubOrderServiceProgressDetailDTO progressDetail = new SubOrderServiceProgressDetailDTO();
                if (Objects.nonNull(shippingInfo)) {
                    BeanUtils.copyProperties(shippingInfo, progressDetail);
                }
                progressDetail.setFinalProgress(OrderItemStatusEnum.fromType(d.getOrderItemStatus()));

                // timeline
                List<ServiceProgressDetailDTO> timeLine = allSubStatusTimeline.stream()
                    .filter(timeline -> Objects.equals(d.getSubId(), timeline.getSubId()))
                    .map(timeline -> {
                        ServiceProgressDetailDTO result = new ServiceProgressDetailDTO();
                        OrderItemStatusEnum curStatus = timeline.getNextStatus();
                        result.setProgressNumber(curStatus.getStatus());
                        result.setTime(timeline.getCreatedAt());
                        result.setProgress(curStatus);
                        result.setTriggerType(timeline.getTriggerType());
                        return result;
                    }).collect(Collectors.toList());
                progressDetail.setLstProgressDetail(timeLine);

                dto.setProgressDetail(progressDetail);

                // Thuế + phí của thành phần
                List<PricingTaxRes> lstPricingTax = lstAllPricingTax.stream()
                    .filter(t -> Objects.equals(t.getObjectId(), d.getPricingId())).collect(Collectors.toList());
                List<PricingTaxRes> lstPricingSetUpFee = lstAllPricingSetUpFee.stream()
                    .filter(t -> Objects.equals(t.getObjectId(), d.getPricingId())).collect(Collectors.toList());
                // Phí nhanh
                List<CustomFee> lstOtherFee = lstAllSubOtherFee.stream()
                    .filter(t -> Objects.equals(t.getSubscriptionId(), d.getSubId())).collect(Collectors.toList());
                dto.setTaxFeeDetail(servicesService.getTaxFee(lstPricingTax, d.getAmount(), lstPricingSetUpFee, lstOtherFee,
                    Objects.equals(d.getClassification(), ProductClassificationEnum.PHYSICAL.getValue()), d.getServiceId(), d.getServiceName(), d.getPricingName()));

                // coupon của thành phần
                dto.setCouponList(lstAllPricingCoupon.stream()
                    .filter(c -> Objects.equals(c.getPricingId(), d.getPricingId()))
                    .map(c -> {
                        CouponPackageDTO coupon = new CouponPackageDTO();
                        coupon.setCouponType(CouponTypeEnum.PRICING);
                        coupon.setObjectName(dto.getPricingName());
                        CouponItem couponItem = new CouponItem();
                        couponItem.setCouponName(c.getCouponName());
                        couponItem.setId(c.getCouponId());
                        couponItem.setPrice(c.getDiscountValue());
                        coupon.setLstCouponItem(Collections.singletonList(couponItem));
                        return coupon;
                }).collect(Collectors.toList()));

                // ADDON
                List<SubOrderItemAddonDTO> addons = lstAddonData.stream()
                    .filter(a -> Objects.equals(d.getPricingId(), a.getPricingId()))
                    .map(a -> {
                        SubOrderItemAddonDTO addon = new SubOrderItemAddonDTO();
                        BeanUtils.copyProperties(a, addon);

                        // thuế + phí thiết lập của addon
                        List<PricingTaxRes> lstAddonTax = lstAllAddonTax.stream()
                            .filter(tax -> Objects.equals(tax.getAddonId(), a.getId())).collect(Collectors.toList());
                        List<PricingTaxRes> lstAddonSetupFeeTax = lstAllAddonSetupFeeTax.stream()
                            .filter(tax -> Objects.equals(tax.getAddonId(), a.getId())).collect(Collectors.toList());
                        addon.setTaxFeeDetail(servicesService.getTaxFee(lstAddonTax, addon.getOriginPrice(), lstAddonSetupFeeTax,
                            null, false, addon.getId(), addon.getAddonName(), null));

                        // coupon của addon
                        List<CouponPackageDTO> couponAddonList = lstAllCouponAddon.stream()
                            .filter(couponData -> Objects.equals(couponData.getAddonId(), a.getId()))
                            .map(couponData -> {
                                CouponPackageDTO coupon = new CouponPackageDTO();
                                coupon.setCouponType(CouponTypeEnum.ADDON);
                                coupon.setObjectName(a.getAddonName());

                                CouponItem couponItem = new CouponItem();
                                couponItem.setCouponName(couponData.getCouponName());
                                couponItem.setId(couponData.getCouponId());
                                if (Objects.equals(DiscountTypeEnum.PRICE.value, couponData.getDiscountType())) {// số tiền
                                    couponItem.setPrice(couponData.getDiscountValue());
                                } else { // Phần trăm
                                    // tính số tiền được chiết khấu
                                    couponItem.setPrice(a.getOriginPrice()
                                        .multiply(couponData.getDiscountValue())
                                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                                }
                                coupon.setLstCouponItem(Collections.singletonList(couponItem));
                                return coupon;
                            }).collect(Collectors.toList());
                        addon.setCouponList(couponAddonList);
                        return addon;
                    }).collect(Collectors.toList());
                dto.setAddons(addons);

                // Thông tin trạng thái của item theo bộ trạng thái mới (thông tin từ bảng subscription_metadata)
                OrderItemStatusEnum itemStatus = OrderItemStatusEnum.fromType(d.getOrderItemStatus());
                if (!Objects.equals(itemStatus, OrderItemStatusEnum.NONE_STATUS)) {
                    dto.setItemType(d.getItemType());
                    dto.setOrderItemStatus(itemStatus.getStatus());
                    dto.setOrderItemStatusCode(itemStatus);
                    dto.setOrderItemStatusName(itemStatus.getDescription());
                }
                // TODO: cần fill thời gian chuyển đến trạng thái hiện tại dựa vào history
                dto.setAllOrderItemStatusSteps(OrderItemStatusEnum.getAllStatusListByItemType(d.getItemType()).stream()
                    .map(item -> new OrderItemStatusDetailDTO(item.getStatus(), item.getCode(), item.getDescription(), null))
                    .collect(Collectors.toList()));

                return dto;
            }).collect(Collectors.toList());
    }

    @Override
    public List<ServiceGroupTableResDTO> getServiceGroupTableInfo(List<ServiceGroupTableReqDTO> req, Boolean isPopup) {
        Long userId = AuthUtil.getCurrentParentId();
        // Thông tin thuế - phí thiết bị/biến thể
        // Set lưu thông tin các service đã duyệt
        Set<Long> serviceIdSet = new HashSet<>();
        List<ServiceGroupTableResDTO> lstResponse = new ArrayList<>();
        for (ServiceGroupTableReqDTO item : req) {
            //Validate multisub service
            Long serviceId = (item.getServiceVariantInfo() != null && item.getServiceVariantInfo().getServiceId() != null)
                ? item.getServiceVariantInfo().getServiceId()
                : (Objects.nonNull(item.getPricingInfo()) ? item.getPricingInfo().getServiceId() : null);
            if (Objects.isNull(serviceId)) {
                throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.SERVICES, ErrorKey.ID);
            }
            boolean isAllowMultiSub = serviceRepository.isAllowServiceMultiSub(serviceId);
            if(!isAllowMultiSub){
                if (Boolean.TRUE.equals(isPopup) && serviceIdSet.contains(serviceId)) {
                    throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_EXIST_DISABLE_MULTI_SUB, ErrorKey.SERVICE, Services.ID);
                }
                serviceIdSet.add(serviceId);
            }

            ServiceGroupTableResDTO res = new ServiceGroupTableResDTO();
            if (Objects.nonNull(item.getServiceVariantInfo())) {
                ServiceVariantInfo va = item.getServiceVariantInfo();
                VariantResponseDTO variantResponseDTO = new VariantResponseDTO();
                variantResponseDTO.setServiceId(va.getServiceId());
                variantResponseDTO.setServiceDraftId(va.getServiceDraftId());
                variantResponseDTO.setServiceName(va.getServiceName());
                variantResponseDTO.setVariantName(va.getVariantName());
                variantResponseDTO.setUpdateQuantity(va.getUpdateQuantity());
                variantResponseDTO.setAvatarUrl(va.getAvatarUrl());
                variantResponseDTO.setVariantId(va.getVariantId());
                variantResponseDTO.setVariantDraftId(va.getVariantDraftId());
                variantResponseDTO.setFullName(String.join(" - ", va.getServiceName(), va.getVariantName()));
                variantResponseDTO.setTemporaryPrice(va.getUpdatePrice().multiply(va.getUpdateQuantity()));
                res.setVariantResponseDTO(variantResponseDTO);
            }

            // Thông tin thuế - phí của pricing
            BigDecimal freeQuantity = BigDecimal.ZERO;

            if (Objects.nonNull(item.getPricingInfo())) {
                List<SubscriptionCalculateDTO> planPriceList;
                PricingInfo pri = item.getPricingInfo();
                BigDecimal updatePrice = (BigDecimal.ZERO.compareTo(pri.getUpdatePrice()) > 0) ? BigDecimal.ZERO : pri.getUpdatePrice();
                FormulaObject formulaObject = calculatePricePricingByQuantityAndAmountAfter(pri.getServiceId(),
                    pri.getPricingId(), pri.getPricingMultiPlanId(), updatePrice, pri.getUpdateQuantity(), userId);
                formulaObject.setOriginalPrice(pri.getOriginalPrice());
                formulaObject.setPricingDraftId(pri.getPricingDraftId());
                formulaObject.setServiceDraftId(pri.getServiceDraftId());

                if (Objects.isNull(pri.getPricingMultiPlanId())) {
                    var pricing = pricingRepository.findByIdAndDeletedFlag(pri.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue())
                        .orElse(null);
                    if (Objects.nonNull(pricing)) {
                        freeQuantity = BigDecimal.valueOf(ObjectUtil.getOrDefault(pricing.getFreeQuantity(), 0L));
                        formulaObject.setPricingPlan(pricing.getPricingPlan());
                    }
                } else {
                    var pricingMultiPlan = pricingMultiPlanRepository.findById(pri.getPricingMultiPlanId())
                        .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING_PLAN, ErrorKey.ID,
                            String.valueOf(pri.getPricingMultiPlanId())));
                    if (Objects.nonNull(pricingMultiPlan)) {
                        planPriceList = pricingPlanDetailRepository.findAllByPricingMultiPlanIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromDesc(
                            pricingMultiPlan.getId()).stream().map(SubscriptionCalculateDTO::new).collect(Collectors.toList());
                        freeQuantity = BigDecimal.valueOf(ObjectUtil.getOrDefault(pricingMultiPlan.getFreeQuantity(), 0));
                        formulaObject.setIsOneTime(pri.getIsOneTime()); // 1 gói thuê bao có min, max quantity.
                        formulaObject.setPricingPlan(pricingMultiPlan.getPricingPlan()); // 0 : Không cho thay đổi số lượng khi tạo nhóm
                        int planEnum = pricingMultiPlan.getPricingPlan();
                        // Bậc thang, khối lượng
                        if (Arrays.asList(PricingPlanEnum.VOLUME.value, PricingPlanEnum.STAIR_STEP.value).contains(planEnum)) {
                            for (SubscriptionCalculateDTO planPrice : planPriceList) {
                                Long unitFrom = planPrice.getUnitFrom();
                                Long unitTo = planPrice.getUnitTo();
                                if (pri.getUpdateQuantity() >= unitFrom && (pri.getUpdateQuantity() <= unitTo || Objects.equals(unitTo, -1L))) {
                                    formulaObject.setOriginalPrice(getOriginalPrice(planPrice.getUnitPrice(), formulaObject.getPlanTaxList()));
                                    break;
                                }
                            }
                        } else if (!planPriceList.isEmpty() && Objects.equals(pricingMultiPlan.getPricingPlan(),
                            PricingPlanEnum.TIER.value)) {// lũy kế
                            BigDecimal originalPrice = getPerPrice(planPriceList, formulaObject.getPlanPriceList());
                            formulaObject.setOriginalPrice(getOriginalPrice(originalPrice, formulaObject.getPlanTaxList()));
                        } else {
                            formulaObject.setOriginalPrice(pri.getOriginalPrice());
                        }
                    }
                }
                formulaObject.setUpdatePrice(pri.getUpdatePrice());
                formulaObject.setFreeQuantity(freeQuantity);
                formulaObject.setServiceName(pri.getServiceName());
                formulaObject.setPricingName(pri.getPricingName());
                formulaObject.setServiceId(pri.getServiceId());
                formulaObject.setAvatarUrl(pri.getAvatarUrl());
                formulaObject.setMaxQuantity(ObjectUtil.getOrDefault(pri.getMaxQuantity(), 1L));
                formulaObject.setMinQuantity(ObjectUtil.getOrDefault(pri.getMinQuantity(), 1L));
                //Tính tổng tiền thuế
                BigDecimal totalTax = BigDecimal.ZERO;
                for (FormulaTax tax : formulaObject.getTaxes()) {
                    totalTax = totalTax.add(tax.getPrice());

                }
                BigDecimal finalPrice = formulaObject.getFinalAmountAfterTax()
                    .add(ObjectUtil.getOrDefault(formulaObject.getSetupFee().getFinalAmountAfterTax(), BigDecimal.ZERO));
                formulaObject.setFinalPrice(finalPrice);
                res.setFormulaObject(formulaObject);
            }
            lstResponse.add(res);
        }
        return lstResponse;
    }

    @Override
    public SubscriptionDetailCartDTO getDetailSubscriptionCart(String cartCode, PortalType portalType) {
        Long userId = AuthUtil.getCurrentParentId();

        SubscriptionDetailCartDTO response = new SubscriptionDetailCartDTO();
        List<IGetSubDetailCart> subDetailCart = subscriptionRepository.getDetailSubOrderByCart(cartCode);

        PortalType portal = AuthUtil.getPortalOfUserRoles();
        if (PortalType.DEV.equals(portal)) { // dev chỉ xem đc sản phẩm thuộc NCC của n
            subDetailCart = subDetailCart.stream().filter(item -> item.getProviderId().equals(userId)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(subDetailCart)) {
            return null;
        }
        Integer sizeBillPaid = (int) subDetailCart.stream().filter(i -> Objects.equals(BillStatusEnum.PAID.value, i.getBillingStatus())).count();
        IGetSubDetailCart iGetSubDetailCart = subDetailCart.get(0);

        checkPermission(portalType, subDetailCart.stream().map(IGetSubDetailCart::getProviderId).collect(Collectors.toSet()), userId, iGetSubDetailCart.getUserId());
        response.setConfirmPayment(sizeBillPaid.equals(subDetailCart.size()));
        response.setProductOrderId(iGetSubDetailCart.getProductOrderId());
        response.setAddress(iGetSubDetailCart.getAddress());
        response.setPhone(iGetSubDetailCart.getPhone());
        response.setCustomerName(iGetSubDetailCart.getCustomerName());
        response.setCreatedSource(iGetSubDetailCart.getCreatedSource());
        response.setEmployeeCode(iGetSubDetailCart.getEmployeeCode());
        response.setCreatedAt(iGetSubDetailCart.getCreatedAt());
        response.setNotes(iGetSubDetailCart.getNotes());
        response.setOrderProgress(iGetSubDetailCart.getOrderProgress());
        IGetSubDetailCart subCancelFinish = subDetailCart.stream().filter(i -> Objects.nonNull(i.getCancelTime()))
            .max(Comparator.comparing(IGetSubDetailCart::getCancelTime)).orElse(null);
        // ĐH mua từ giỏ hàng mặc định là OFF vs trả sau
        // Move sang oneSME mua qua cart -> Onl
        response.setPricingType(PricingTypeEnum.PREPAY);
        if (Objects.nonNull(subCancelFinish)) {
            response.setCancelTime(DateFormatUtils.format(subCancelFinish.getCancelTime(), "HH:mm dd-MM-yyyy"));
        }
        response.setBillingId(iGetSubDetailCart.getBillingId());
        response.setBillingCode(iGetSubDetailCart.getBillingCode());
        response.setUpdateReason(iGetSubDetailCart.getUpdateReason());
        response.setMessage(iGetSubDetailCart.getMessage());
        response.setBillAddress(billsRepository.getBillingDetailAddress(iGetSubDetailCart.getBillingId()));
        response.setPaymentMethod(PaymentMethodEnum.valueOf(iGetSubDetailCart.getPaymentMethod()));
        ShoppingCartFormulaResDTO cartFormulaResDTO = new ShoppingCartFormulaResDTO();
        // Thêm thông tin tổng hợp tất cả các phí
        FeeApplyDTO mergedFeeObject = new FeeApplyDTO();
        mergedFeeObject.setTotalAmount(BigDecimal.ZERO);
        mergedFeeObject.setLstItem(new ArrayList<>());

        // Thêm thông tin tổng hợp tất cả các thuế
        TaxApplyDTO mergedTaxObject = new TaxApplyDTO();
        mergedTaxObject.setTotalAmount(BigDecimal.ZERO);
        mergedTaxObject.setLstItem(new ArrayList<>());

        // Thêm thông tin tổng hợp tất cả các km
        CouponApplyDTO mergedCouponObject = new CouponApplyDTO();
        mergedCouponObject.setTotalAmount(BigDecimal.ZERO);
        mergedCouponObject.setLstItem(new ArrayList<>());

        for (IGetSubDetailCart subDetail : subDetailCart) { // lấy thông tinh tính toán chi tiết sub
            SubscriptionFormulaResDTO formulaResDTO;
            // Phí nhanh
            List<SubscriptionFormulaReqDTO.FormulaCustomFee> otherFeeList = customFeeRepository.findAllBySubscriptionId(subDetail.getId())
                .stream()
                .map(x -> new SubscriptionFormulaReqDTO.FormulaCustomFee(x.getId(), x.getName(), x.getPrice())).collect(Collectors.toList());
            formulaResDTO = getDetailSubOrder(subDetail, otherFeeList);
            // Phí nhanh
            formulaResDTO.setOtherFeeList(
                otherFeeList.stream().map(x -> new SubscriptionFormulaResDTO.CustomFee(x.getId(), x.getName(), x.getPrice()))
                    .collect(Collectors.toList()));
            formulaResDTO.setQuantityVariant(subDetail.getQuantityVariant());
            formulaResDTO.setSubId(subDetail.getId());
            formulaResDTO.setServiceName(subDetail.getServiceName());
            cartFormulaResDTO.getLstSubscriptionResponse().add(formulaResDTO);
            // tổng hợp thuế, phí, KM
            mergeFeeTaxCoupon(formulaResDTO, mergedFeeObject, mergedTaxObject, mergedCouponObject);
        }
        // Gán thuế, phí, KM đã gộp vào cart
        cartFormulaResDTO.setFeeObject(mergedFeeObject);
        cartFormulaResDTO.setTaxObject(mergedTaxObject);
        cartFormulaResDTO.setCouponObject(mergedCouponObject);
        // tổng tiền đơn hàng
        sumTotalAmountSub(cartFormulaResDTO, cartFormulaResDTO.getLstSubscriptionResponse());
        Map<Long, IGetSubDetailCart> map = subDetailCart.stream().collect(Collectors.toMap(IGetSubDetailCart::getId, Function.identity()));
        cartFormulaResDTO.setTotalCoupons(subscriptionRepository.getSubTotalCoupons(map.keySet()));
        for (SubscriptionFormulaResDTO subDetail : cartFormulaResDTO.getLstSubscriptionResponse()) {
            IGetSubDetailCart item = map.get(subDetail.getSubId());
            SubscriptionFormulaResDTO.FormulaObject object = subDetail.getObject();
            if (Objects.nonNull(object)) {
                object.setPricingPlan(item.getPricingPlan());
                object.setCycleType(item.getCycleType());
                object.setIsOneTime(item.getIsOneTime());
                object.setNumberOfCycles(item.getNumberOfCycles());
                object.setPricingPlan(item.getPricingPlan());
                object.setFreeQuantity(BigDecimal.valueOf(ObjectUtil.getOrDefault(item.getFreeQuantity(), 0L).longValue()));
                object.setUnitName(item.getUnitName());
                object.setPaymentCycle(item.getPaymentCycle());
                object.setOwner(item.getOwner());
            }
            if (Objects.nonNull(item)) {
                DetailSubOrder detailSubOrder = new DetailSubOrder();
                BeanUtils.copyProperties(item, detailSubOrder);
                List<SubPricingDTO> lstSubPricing = new ArrayList<>();
                List<ISubVariantDTO> lstSubVariant = new ArrayList<>();
                // Avatar dịch vụ/combo
                IFileAttachResponse avatar;
                if (Boolean.TRUE.equals(item.getIsService())) {
                    List<Integer> lstObjectType = ((Objects.equals(item.getProductType(), ServiceProductTypeEnum.DEVICE.value)))
                        ? new ArrayList<>(Arrays.asList(FileAttachTypeEnum.AVATAR_DEVICE.value, FileAttachTypeEnum.VARIANT_AVATAR.getValue(),
                        FileAttachTypeEnum.AVATAR.value)) : Collections.singletonList(FileAttachTypeEnum.AVATAR.value);
                    Long variantId = Objects.nonNull(item.getVariantId()) ? item.getVariantId() : -1L;
                    avatar = fileAttachRepository.getFileAttachVariantOrService(lstObjectType, variantId, item.getServiceId());
                    if (Objects.nonNull(object)) {
                        object.setAvatar(fileAttachRepository.getPathByObjectTypeAndObjectId(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(),
                            object.getId()));
                    }
                } else {
                    Long comboPlanId = item.getComboPlanId();
                    avatar = fileAttachRepository.getComboAvatar(ObjectUtil.getOrDefault(item.getServiceId(), -1L));
                    if (Objects.nonNull(comboPlanId)) {
                        lstSubPricing = getListSubPricing(comboPlanId);
                        lstSubVariant = comboRepository.getSubVariantInfo(comboPlanId);
                        avatar = fileAttachRepository.getComboPlanAvatar(comboPlanId, FileAttachTypeEnum.COMBO_PLAN_THUMBNAIL.value);
                    }

                }
                detailSubOrder.setAvatar(avatar);
                detailSubOrder.setLstSubPricing(lstSubPricing);
                detailSubOrder.setLstSubVariant(lstSubVariant);
                if (Objects.nonNull(item.getOs3rdStatus()) && Objects.nonNull(StatusOrderEnum.valueOf(item.getOs3rdStatus()))) {
                    detailSubOrder.setProcessStatus(StatusOrderEnum.valueOf(item.getOs3rdStatus()).toString());
                }
                subDetail.setDetailSubOrder(detailSubOrder);
            }

        }
        response.setShoppingCartFormulaResDTO(cartFormulaResDTO);

        boolean allOrdersCancelled = subDetailCart.stream()
            .filter(e -> Objects.nonNull(e.getProcessId()))
            .allMatch(e -> Objects.equals(e.getProcessId(), (long) ProgressOrderEnum.CANCELLED.status));

        // Nếu không phải tất cả đơn hàng đều bị hủy, loại bỏ các đơn hàng đã hủy
        if (!allOrdersCancelled) {
            subDetailCart.removeIf(e -> Objects.nonNull(e.getProcessId()) &&
                e.getProcessId() == (long) ProgressOrderEnum.CANCELLED.status);
        }

        // Tìm đơn hàng có trạng thái tiến trình cao nhất
        subDetailCart.stream()
            .filter(e -> Objects.nonNull(e.getProcessId()))
            .max(Comparator.comparing(IGetSubDetailCart::getProcessId))
            .ifPresent(progress -> {
                response.setProcessStatusId(progress.getProcessId());
                response.setProcessStatusName(progress.getProcessStatus());
                response.setPaymentMethod(PaymentMethodEnum.valueOf(progress.getPaymentMethod()));
            });


        // lịch sử hoạt động của đơn hàng
        List<IGetSubHistories> historyList;
        if (!Objects.equals(PortalType.DEV, portal)) {
            historyList = subscriptionRepository.getSubHistoriesByCart(cartCode);
        } else {
            historyList = subscriptionRepository.getSubHistoriesByCartByDEV(cartCode, userRepository.getListChildUserIdByParentId(userId));
        }
        response.setHistories(historyList);
        // Loại bỏ type không làm cập nhật tiến trình
        List<IGetSubHistories> histories = historyList.stream().filter(
                i -> !Objects.equals(i.getContentType(), SubscriptionHistoryConstant.ContentType.RECEIVED_ORDER) &&
                    !Objects.equals(i.getContentType(), SubscriptionHistoryConstant.ContentType.POSTPAID_CONFIRM_PAYMENT))
            .collect(Collectors.toList());

        setProgressDetail(histories, response);
        response.setOrderCode(cartCode);

        // Check tòan bộ tiến trình có lắp đặt.
        List<SubscriptionFormulaResDTO> lstSubscriptionFormulaResDTO = cartFormulaResDTO.getLstSubscriptionResponse();
        for (SubscriptionFormulaResDTO item : lstSubscriptionFormulaResDTO) {
            if (Objects.nonNull(item.getDetailSubOrder())) {
                if (Objects.nonNull(item.getDetailSubOrder().getInstallationConfiguration())) {
                    response.setIsInstalled(true);
                    break;
                }
            }
        }
        return response;
    }

    private static void mergeFeeTaxCoupon(SubscriptionFormulaResDTO formulaResDTO, FeeApplyDTO mergedFeeObject, TaxApplyDTO mergedTaxObject,
        CouponApplyDTO mergedCouponObject) {
        // Tổng hợp feeObject
        FeeApplyDTO feeObject = formulaResDTO.getFeeObject();
        if (feeObject != null) {
            // Gộp lstItem
            if (feeObject.getLstItem() != null) {
                mergedFeeObject.getLstItem().addAll(feeObject.getLstItem());
            }

            // Gộp totalAmount
            if (feeObject.getTotalAmount() != null) {
                mergedFeeObject.setTotalAmount(
                    mergedFeeObject.getTotalAmount().add(feeObject.getTotalAmount())
                );
            }
        }

        // Tổng hợp taxObject
        TaxApplyDTO taxObject = formulaResDTO.getTaxObject();
        if (taxObject != null) {
            // Gộp lstItem
            if (taxObject.getLstItem() != null) {
                mergedTaxObject.getLstItem().addAll(taxObject.getLstItem());
            }

            // Gộp totalAmount
            if (taxObject.getTotalAmount() != null) {
                mergedTaxObject.setTotalAmount(
                    mergedTaxObject.getTotalAmount().add(taxObject.getTotalAmount())
                );
            }
        }

        // Tổng hợp couponObject
        CouponApplyDTO couponObject = formulaResDTO.getCouponObject();
        if (couponObject != null) {
            // Gộp lstItem
            if (couponObject.getLstItem() != null) {
                mergedCouponObject.getLstItem().addAll(couponObject.getLstItem());
            }

            // Gộp totalAmount
            if (couponObject.getTotalAmount() != null) {
                mergedCouponObject.setTotalAmount(
                    mergedCouponObject.getTotalAmount().add(couponObject.getTotalAmount())
                );
            }
        }
    }

    /**
     * check quyền user đăng nhập
     */
    @Override
    public void checkPermission(PortalType portalType, Set<Long> providerIds, Long userId, Long subUserId) {
        boolean checkPermission = true;
        if (PortalType.ADMIN.equals(portalType) &&
            !AuthUtil.checkUserRoles(
                Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN_PROVINCE.getValue()))) {
            checkPermission = false;
        }
        if (PortalType.DEV.equals(portalType) &&
            AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(),
                RoleType.DEVELOPER_BUSINESS.getValue(), RoleType.DEVELOPER_OPERATOR.getValue(), RoleType.CUSTOMER_SUPPORT.getValue()))) {
            if (!providerIds.contains(userId)) {
                checkPermission = false;
            }
        }
        if (PortalType.SME.equals(portalType) &&
            AuthUtil.checkUserRoles(Arrays.asList(RoleType.SME.getValue(), RoleType.EMPLOYEE.getValue()))) {
            if (!userId.equals(subUserId)) {
                checkPermission = false;
            }
        }
        if (!checkPermission) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
    }

    @Override
    public SubscriptionFormulaResDTO getDetailSubOrder(IGetSubDetailCart subDetail, List<FormulaCustomFee> otherFeeList) {
        Long subscriptionId = subDetail.getId();
        boolean isCombo = Objects.nonNull(subDetail.getComboPlanId());
        SubscriptionFormulaResDTO formulaResDTO;
        List<Long> couponPricing;
        List<SubscriptionPricingAddonDTO.CalUnitLimitedCustomDTO> unitLimitedList = new ArrayList<>();
        List<IGetMcCoupon> lstMcPrivate = new ArrayList<>();
        ComboPlan comboPlan = null;
        if (!isCombo) {
            couponPricing = subscriptionRepository.getCouponInPricing(subscriptionId);
            lstMcPrivate = subscriptionRepository.getCouponMcInPricing(subscriptionId);
            unitLimitedList = getUnitLimitedList(subDetail);
        } else {
            comboPlan = comboPlanRepository.findByIdAndDeletedFlag(subDetail.getComboPlanId(), 1).orElse(null);
            couponPricing = subscriptionRepository.getCouponInCombo(subscriptionId);
            lstMcPrivate = subscriptionRepository.getCouponMcInCombo(subscriptionId);
        }

        // custom fee
        List<SubscriptionSetupFee> customSetupFee = subscriptionSetupFeeRepository.findBySubscriptionId(subscriptionId);

        // request
        SubscriptionFormulaReqDTO formulaReqDTO = new SubscriptionFormulaReqDTO();

        //danh sach addon
        List<IGetMcCoupon> lstMcAddon = subscriptionRepository.getCouponMcInAddon(subscriptionId);
        Map<Long, List<IGetMcCoupon>> mapMc = new HashMap<>();
        if (!CollectionUtils.isEmpty(lstMcAddon)) {
            mapMc = lstMcAddon.stream().collect(Collectors.groupingBy(IGetMcCoupon::getAddonId));
        }
        List<IGetAddonSubDetail> addonPricing = subscriptionRepository.getDetailAddonInSub(subscriptionId);
        List<Long> addonIds = new ArrayList<>();
        List<FormulaAddon> formulaAddons = new ArrayList<>();
        addonPricing.forEach(item -> {
            if (!addonIds.contains(item.getId())) {
                addonIds.add(item.getId());
                FormulaAddon formulaAddon = new FormulaAddon();
                BeanUtils.copyProperties(item, formulaAddon);
                SubscriptionSetupFee customPriceSetupFeeAddon = customSetupFee.stream().filter(i -> Objects.equals(i.getAddonId(), item.getId()))
                    .findFirst().orElse(null);
                if (Objects.nonNull(customPriceSetupFeeAddon)) {
                    formulaAddon.setPrice(
                        Objects.nonNull(customPriceSetupFeeAddon.getPrice()) ? customPriceSetupFeeAddon.getPrice() : item.getPrice());
                    formulaAddon.setSetupFee(
                        Objects.nonNull(customPriceSetupFeeAddon.getSetupFee()) ? customPriceSetupFeeAddon.getSetupFee() : null);
                }
                formulaAddons.add(formulaAddon);
            }
        });
        Map<Long, List<IGetMcCoupon>> finalMapMc = mapMc;
        formulaAddons.forEach(addon -> {
            List<Long> couponIds = addonPricing.stream()
                .filter(i -> Objects.nonNull(i.getCouponId()) && Objects.equals(addon.getId(), i.getId()))
                .collect(Collectors.toList())
                .stream().map(IGetAddonSubDetail::getCouponId).collect(Collectors.toList());
            List<IGetMcCoupon> mcCoupons = finalMapMc.get(addon.getId());
            if (Objects.nonNull(mcCoupons)) {
                List<McApplyDTO> lstAddon = mcCoupons.stream().map(McApplyDTO::new).collect(Collectors.toList());
                addon.setLstMcAddon(lstAddon);
            }
            addon.setCouponIds(couponIds);
        });
        formulaAddons.forEach(addon -> {
            // lay danh sach unitlimited list cua addon
            List<CalUnitLimitedCustomDTO> unitLimitedListAddon = new ArrayList<>();
            List<SubscriptionRangeDTO> rageOfAddons = subscriptionRepository.getAddonRangePrice(addon.getId(), subscriptionId);
            //Xóa phần tử chưa được sửa giá nếu phần tử đó có sửa giá
            rageOfAddons.removeIf(range -> !range.getIsEdited() &&
                rageOfAddons.stream().anyMatch(r -> r.getIsEdited() && Objects.equals(r.getUnitFrom(), range.getUnitFrom())));

            //Lấy danh sách thuế
            List<PricingTaxRes> addonTaxes = pricingTaxRepository.getAddonTax(addon.getId());

            if (!org.springframework.util.CollectionUtils.isEmpty(rageOfAddons)) {
                rageOfAddons.forEach(au -> {
                    CalUnitLimitedCustomDTO unitLimited = new CalUnitLimitedCustomDTO();
                    unitLimited.setUnitTo(Objects.nonNull(au.getUnitTo()) ? (long) au.getUnitTo() : null);
                    unitLimited.setUnitFrom(Objects.nonNull(au.getUnitFrom()) ? (long) au.getUnitFrom() : null);
                    unitLimited.setPrice(au.getIsEdited() ? au.getPrice() : subscriptionFormula.priceBeforeTax(au.getPrice(), addonTaxes));
                    unitLimitedListAddon.add(unitLimited);
                });
            }
            if (Objects.nonNull(addon.getAddonMultiPlanId()) && addon.getAddonMultiPlanId() != -1L) {
                unitLimitedListAddon.clear(); // Only return unit limited entries from matching multiplan id
                // lay danh sach unitlimited list cua addon trong bang pricingMultiPlan detail
                List<SubscriptionRangeDTO> rageOfAddonsMultiPlan = subscriptionRepository
                    .getAddonMultiPlanRange(addon.getAddonMultiPlanId(), subDetail.getId());
                //Xóa phần tử chưa được sửa giá nếu phần tử đó có sửa giá
                rageOfAddonsMultiPlan.removeIf(range -> !range.getIsEdited() &&
                    rageOfAddonsMultiPlan.stream().anyMatch(r -> r.getIsEdited() && r.getUnitFrom().equals(range.getUnitFrom())));

                if (!org.springframework.util.CollectionUtils.isEmpty(rageOfAddonsMultiPlan)) {
                    rageOfAddonsMultiPlan.forEach(au -> {
                        CalUnitLimitedCustomDTO unitLimited = new CalUnitLimitedCustomDTO();
                        unitLimited.setUnitTo(Objects.nonNull(au.getUnitTo()) ? (long) au.getUnitTo() : null);
                        unitLimited.setUnitFrom(Objects.nonNull(au.getUnitFrom()) ? (long) au.getUnitFrom() : null);
                        unitLimited.setPrice(au.getIsEdited() ? au.getPrice() : subscriptionFormula.priceBeforeTax(au.getPrice(), addonTaxes));
                        unitLimitedListAddon.add(unitLimited);
                    });
                }
            }

            SubscriptionSetupFee customPriceSetupFeeAddon = customSetupFee.stream().filter(i -> Objects.equals(i.getAddonId(), addon.getId()))
                .findFirst().orElse(null);
            if (Objects.nonNull(customPriceSetupFeeAddon)) {
                List<SubscriptionRangeDTO> customRageOfAddons = subscriptionRepository.getAddonRangePriceCustom(
                    customPriceSetupFeeAddon.getId());
                if (!org.springframework.util.CollectionUtils.isEmpty(customRageOfAddons)) {
                    unitLimitedListAddon.clear(); // Only return unit limited entries from matching subscriptionSetupFeeId
                    customRageOfAddons.forEach(au -> {
                        CalUnitLimitedCustomDTO unitLimited = new CalUnitLimitedCustomDTO();
                        unitLimited.setUnitTo(Objects.nonNull(au.getUnitTo()) ? (long) au.getUnitTo() : null);
                        unitLimited.setUnitFrom(Objects.nonNull(au.getUnitFrom()) ? (long) au.getUnitFrom() : null);
                        unitLimited.setPrice(au.getIsEdited() ? au.getPrice() : subscriptionFormula.priceBeforeTax(au.getPrice(), addonTaxes));
                        unitLimitedListAddon.add(unitLimited);
                    });
                }
            }
            addon.setUnitLimitedList(unitLimitedListAddon);
        });
        formulaReqDTO.setAddons(formulaAddons);

        // pricing
        SubscriptionFormulaReqDTO.FormulaObject formulaObject = new SubscriptionFormulaReqDTO.FormulaObject();
        formulaObject.setCouponIds(couponPricing);
        formulaObject.setId(isCombo ? subDetail.getComboPlanId() : subDetail.getPricingId());
        formulaObject.setMultiPlanId(subDetail.getPricingMultiPlanId());
        formulaObject.setQuantity(subDetail.getQuantity());
        formulaObject.setLstMcPrivate(lstMcPrivate.stream().map(McApplyDTO::new).collect(Collectors.toList()));
        formulaObject.setSetupFee(billItemRepository.getAmountSetupFee(subDetail.getBillingId(), BillItemType.SETUP_FEE.getValue(),
            isCombo ? subDetail.getComboPlanId() : subDetail.getPricingId()));
        SubscriptionSetupFee customPriceSetupFee = new SubscriptionSetupFee();
        if (isCombo) {
            customPriceSetupFee = customSetupFee.stream().filter(i -> Objects.equals(i.getComboPlanId(), subDetail.getComboPlanId())).findFirst()
                .orElse(null);
        } else {
            customPriceSetupFee = customSetupFee.stream().filter(i -> Objects.equals(i.getPricingId(), subDetail.getPricingId())).findFirst()
                .orElse(null);
        }
        if (Objects.nonNull(customPriceSetupFee)) {
            formulaObject.setSetupFee(customPriceSetupFee.getSetupFee());
            formulaObject.setPrice(customPriceSetupFee.getPrice());
        }

        formulaReqDTO.setObject(formulaObject);

        // km tong
        formulaReqDTO.setCoupons(subscriptionCouponsRepository.getListInvoiceCouponBySubscriptionId(subscriptionId));
        // km tong mc
        List<McApplyDTO> lstMcInvoice = subscriptionRepository.getCouponMcInTotal(subscriptionId).stream().map(McApplyDTO::new)
            .collect(Collectors.toList());
        formulaReqDTO.setLstMcInvoice(lstMcInvoice);
        formulaReqDTO.setVariantId(subDetail.getVariantId());
        formulaReqDTO.setServiceId(subDetail.getServiceId());
        formulaReqDTO.setQuantityVariant(subDetail.getQuantityVariant());
        formulaReqDTO.setSubscriptionId(subDetail.getId());

        formulaReqDTO.setCalculateType(isCombo ? CalculateTypeEnum.COMBO : CalculateTypeEnum.PRICING);
        formulaReqDTO.setOtherFeeList(otherFeeList);
        // tính tiền
        formulaResDTO = subscriptionFormula.calculate(formulaReqDTO);
        formulaResDTO.setIsService(isCombo ? Boolean.FALSE : Boolean.TRUE);
        formulaResDTO.setServiceId(isCombo && Objects.nonNull(comboPlan) ? comboPlan.getComboId() : subDetail.getServiceId());

        Map<Long, FormulaAddon> mapAddon = formulaAddons.stream().collect(Collectors.toMap(FormulaAddon::getId, Function.identity()));
        if (Objects.nonNull(formulaResDTO.getObject())) {
            formulaResDTO.getObject().setUnitLimitedList(unitLimitedList);
            formulaResDTO.getAddonShopCart().forEach(item -> {
                FormulaAddon addon = mapAddon.get(item.getId());
                if (Objects.nonNull(addon)) {
                    item.setUnitLimitedList(addon.getUnitLimitedList());
                    item.setCycleType(addon.getCycleType());
                    item.setPrice(addon.getPrice());
                    item.setPaymentCycle(addon.getPaymentCycle());
                    item.setFreeQuantity(BigDecimal.valueOf(ObjectUtil.getOrDefault(addon.getFreeQuantity(), 0L).longValue()));
                    item.setUnitName(addon.getUnitName());
                    item.setPricingPlan(addon.getPricingPlan());
                    item.setUnitLimitedList(addon.getUnitLimitedList());
                }
            });
        }
        return formulaResDTO;
    }

    private List<SubscriptionPricingAddonDTO.CalUnitLimitedCustomDTO> getUnitLimitedList(IGetSubDetailCart subDetail) {
        Pricing pricing = pricingService.findByIdAndDeletedFlag(subDetail.getPricingId(), 1);
        // Danh sách CTKM của gói dịch vụ trong thuê bao đã chọn
        PricingMultiPlan pricingMultiPlan = null;
        Optional<PricingMultiPlan> pricingMultiPlanOpt = pricingMultiPlanRepository.findMaxPricingMultiPlanById
            (Objects.nonNull(subDetail.getPricingMultiPlanId()) ? subDetail.getPricingMultiPlanId() : -1L, pricing.getId());
        if (pricingMultiPlanOpt.isPresent()) {
            pricingMultiPlan = pricingMultiPlanOpt.get();
        }
        List<UnitLimitedNew> limitedNews = subscriptionFormula.getPricingPriceBeforeTax(pricing, subDetail.getId(), pricingMultiPlan);
        return limitedNews.stream().map(item -> {
            SubscriptionPricingAddonDTO.CalUnitLimitedCustomDTO result = new SubscriptionPricingAddonDTO.CalUnitLimitedCustomDTO();
            BeanUtils.copyProperties(item, result);
            return result;
        }).collect(Collectors.toList());
    }

    private List<ViewEcontractBodyDTO> getListEcontract(String identityNo) {
        ListRequest listRequest = new ListRequest(100000, 0, "");
        ResponseListEcontractDTO responseList = econtractService.callApiGetListEcontract(false, null, null, null, identityNo, listRequest);
        List<ResponseBodyEcontractDTO> dataRes = responseList.getObject().getData();
        List<ViewEcontractBodyDTO> data = new ArrayList<>();
        dataRes.forEach(x -> {
            ViewEcontractBodyDTO viewEcontractBodyDTO = new ViewEcontractBodyDTO();
            try {
                String[] ids = x.getOrderId().split("_");
                Long subscripitonId = Long.valueOf(ids[(ids.length - 1)]);
                // Áp dụng phân quyền field force
                viewEcontractBodyDTO.setSubscriptionId(subscripitonId);

            } catch (Exception e) {
                log.error("--=====not found Subscription: ===== {}", e.getMessage(), e);
            }
            viewEcontractBodyDTO.setContractName(x.getContractName());
            viewEcontractBodyDTO.setContractNumber(x.getContractNumber());
            viewEcontractBodyDTO.setStatus(x.getStatus());
            viewEcontractBodyDTO.setSdt(x.getSdt());
            viewEcontractBodyDTO.setTenToChuc(x.getTenToChuc());
            data.add(viewEcontractBodyDTO);
        });
        return data;
    }

    private static void setProgressDetail(List<IGetSubHistories> historyList, SubscriptionDetailCartDTO response) {
        Map<Integer, IGetSubHistories> map = historyList.stream().collect(Collectors.toMap(IGetSubHistories::getContentType, Function.identity(),
            BinaryOperator.maxBy(Comparator.comparing(IGetSubHistories::getCreatedAt))));
        List<ProgressDetailDTO> listProgress = new ArrayList<>();
        Set<Integer> list = new HashSet<>();
        historyList.forEach(item -> {
            IGetSubHistories iGetSubHistories = map.get(item.getContentType());
            if (list.contains(item.getContentType())) {
                return;
            }
            if (Objects.nonNull(iGetSubHistories)) {
                list.add(item.getContentType());
                ProgressDetailDTO progressDetailDTO = new ProgressDetailDTO();
                progressDetailDTO.setTimeUpdate(iGetSubHistories.getCreatedAt());
                if (iGetSubHistories.getContentType() == 50) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.ORDERED);
                } else if (iGetSubHistories.getContentType() == 51) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.CONFIRM_PAYMENT);
                } else if (iGetSubHistories.getContentType() == 52) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.PREPARE);
                } else if (iGetSubHistories.getContentType() == 53 || iGetSubHistories.getContentType() == 58
                    || iGetSubHistories.getContentType() == 59) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.SHIPPING);
                } else if (iGetSubHistories.getContentType() == 54) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.INSTALL);
                } else if (iGetSubHistories.getContentType() == 55) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.SUCCESS);
                } else if (iGetSubHistories.getContentType() == 56 || iGetSubHistories.getContentType() == 57) {
                    progressDetailDTO.setProgressOrder(ProgressOrderEnum.CANCELLED);
                }
                listProgress.add(progressDetailDTO);
            }
        });

        response.setProgressDetails(listProgress);
    }

    private void sumTotalAmountSub(ShoppingCartFormulaResDTO formulaResDTO, List<SubscriptionFormulaResDTO> lstSubscriptionResponse) {
        // Tính tổng tiền cả đơn hàng
        formulaResDTO.setTotalAmountPreTaxFinal(lstSubscriptionResponse.stream().map(SubscriptionFormulaResDTO::getTotalAmountPreTaxFinal)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        formulaResDTO.setTotalAmountPreTaxFeeFinal(lstSubscriptionResponse.stream().map(SubscriptionFormulaResDTO::getTotalAmountPreTaxFee)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        formulaResDTO.setTotalAmountAfterRefund(lstSubscriptionResponse.stream().map(SubscriptionFormulaResDTO::getTotalAmountAfterRefund)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        formulaResDTO.setTotalAmountAfterTaxFinal(lstSubscriptionResponse.stream().map(SubscriptionFormulaResDTO::getTotalAmountAfterTaxFinal)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        formulaResDTO.setShippingFee(lstSubscriptionResponse.stream()
            .map(item -> item.getShippingFee() != null ? item.getShippingFee().getFinalAmountAfterTax() : BigDecimal.ZERO)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    private List<SubPricingDTO> getListSubPricing(Long pricingId) {
        LinkedList<IComboPricingDTO> lstSubPricing = comboRepository.getPricingByComboPlanId(pricingId);
        return lstSubPricing.stream().map(item -> new SubPricingDTO(
                fileAttachRepository.getFileAttachPricingResponseDto(item.getServiceId(), item.getId()),
                item.getServiceName(), item.getPricingName(), item.getQuantity(), item.getFreeQuantity(), item.getPaymentCycle(),
                item.getCycleType(), item.getNumberOfCycles(),
                item.getNumCycle(), item.getProviderName(), item.getPrice(), item.getIsOneTime(),
                ServiceProductTypeEnum.fromValue(item.getServiceProductType())))
            .collect(Collectors.toList());
    }

    @Override
    public SubscriptionDetailCartDTO getDetailSubscriptionById(Long subscriptionId, PortalType portalType) {
        Long userId = AuthUtil.getCurrentParentId();
        SubscriptionDetailCartDTO response = new SubscriptionDetailCartDTO();
        IGetSubDetailCart subDetail = subscriptionRepository.getDetailSubOrderById(subscriptionId);
        if (Objects.isNull(subDetail)) {
            return null;
        }
        checkPermission(portalType, Collections.singleton(subDetail.getProviderId()), userId, subDetail.getUserId());
        response.setAddress(subDetail.getAddress());
        response.setPhone(subDetail.getPhone());
        response.setCustomerName(subDetail.getCustomerName());
        response.setPaymentMethod(PaymentMethodEnum.valueOf(subDetail.getPaymentMethod()));
        if (Objects.nonNull(subDetail.getCancelTime())) {
            response.setCancelTime(DateFormatUtils.format(subDetail.getCancelTime(), "HH:mm dd-MM-yyyy"));
        }
        response.setProductOrderId(subDetail.getProductOrderId());
        response.setBillingId(subDetail.getBillingId());
        response.setBillingCode(subDetail.getBillingCode());
        response.setUpdateReason(subDetail.getUpdateReason());
        response.setMessage(subDetail.getMessage());
        response.setCreatedSource(subDetail.getCreatedSource());
        response.setEmployeeCode(subDetail.getEmployeeCode());
        response.setCreatedAt(subDetail.getCreatedAt());
        response.setNotes(subDetail.getNotes());
        response.setOrderProgress(subDetail.getOrderProgress());
        response.setBillAddress(billsRepository.getBillingDetailAddress(subDetail.getBillingId()));
        response.setConfirmPayment(Objects.equals(BillStatusEnum.PAID.value, subDetail.getBillingStatus()));
        response.setPricingType(PricingTypeEnum.valueOf(subDetail.getPricingType()));
        SubscriptionFormulaResDTO formulaResDTO;
        // Phí nhanh
        List<SubscriptionFormulaReqDTO.FormulaCustomFee> otherFeeList = customFeeRepository.findAllBySubscriptionId(subscriptionId).stream()
            .map(x -> new SubscriptionFormulaReqDTO.FormulaCustomFee(x.getId(), x.getName(), x.getPrice())).collect(Collectors.toList());
        formulaResDTO = getDetailSubOrder(subDetail, otherFeeList);
        // Phí nhanh
        formulaResDTO.setOtherFeeList(customFeeRepository.findAllBySubscriptionId(subscriptionId).stream()
            .map(x -> new SubscriptionFormulaResDTO.CustomFee(x.getId(), x.getName(), x.getPrice())).collect(Collectors.toList()));
        formulaResDTO.setQuantityVariant(subDetail.getQuantityVariant());
        formulaResDTO.setServiceName(subDetail.getServiceName());
        ShoppingCartFormulaResDTO shoppingCartFormulaResDTO = new ShoppingCartFormulaResDTO();
        List<SubscriptionFormulaResDTO> lstSubscriptionResponse = new ArrayList<>(Collections.singletonList(formulaResDTO));
        shoppingCartFormulaResDTO.setLstSubscriptionResponse(lstSubscriptionResponse);
        shoppingCartFormulaResDTO.setTotalCoupons(subscriptionRepository.getSubTotalCoupons(Collections.singleton(subscriptionId)));

        // tinh tong tien don hang
        sumTotalAmountSub(shoppingCartFormulaResDTO, lstSubscriptionResponse);
        response.setShoppingCartFormulaResDTO(shoppingCartFormulaResDTO);
        SubscriptionFormulaResDTO.FormulaObject object = formulaResDTO.getObject();
        if (Objects.nonNull(object)) {
            object.setPricingPlan(subDetail.getPricingPlan());
            object.setCycleType(subDetail.getCycleType());
            object.setNumberOfCycles(subDetail.getNumberOfCycles());
            object.setPricingPlan(subDetail.getPricingPlan());
            object.setFreeQuantity(BigDecimal.valueOf(ObjectUtil.getOrDefault(subDetail.getFreeQuantity(), 0L).longValue()));
            object.setUnitName(subDetail.getUnitName());
            object.setIsOneTime(subDetail.getIsOneTime());
            object.setPaymentCycle(subDetail.getPaymentCycle());
            object.setOwner(subDetail.getOwner());
            object.setAvatar(
                fileAttachRepository.getPathByObjectTypeAndObjectId(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(), object.getId()));
        }

        // Avatar dịch vụ/combo
        DetailSubOrder detailSubOrder = new DetailSubOrder();
        BeanUtils.copyProperties(subDetail, detailSubOrder);
        IFileAttachResponse avatar;
        List<SubPricingDTO> lstSubPricing = new ArrayList<>();
        List<ISubVariantDTO> lstSubVariant = new ArrayList<>();
        if (subDetail.getIsService()) {
            List<Integer> lstObjectType = ((Objects.equals(subDetail.getProductType(), ServiceProductTypeEnum.DEVICE.value)))
                ? new ArrayList<>(Arrays.asList(FileAttachTypeEnum.AVATAR_DEVICE.value, FileAttachTypeEnum.VARIANT_AVATAR.getValue(),
                FileAttachTypeEnum.AVATAR.value)) : Collections.singletonList(FileAttachTypeEnum.AVATAR.value);
            Long variantId = Objects.nonNull(subDetail.getVariantId()) ? subDetail.getVariantId() : -1L;
            avatar = fileAttachRepository.getFileAttachVariantOrService(lstObjectType, variantId, subDetail.getServiceId());
        } else {
            avatar = fileAttachRepository.getComboAvatar(ObjectUtil.getOrDefault(subDetail.getServiceId(), -1L));
            Long comboPlanId = subDetail.getComboPlanId();
            if (Objects.nonNull(comboPlanId)) {
                lstSubPricing = getListSubPricing(comboPlanId);
                lstSubVariant = comboRepository.getSubVariantInfo(comboPlanId);
                avatar = fileAttachRepository.getComboPlanAvatar(comboPlanId, FileAttachTypeEnum.COMBO_PLAN_THUMBNAIL.value);
            }

        }
        detailSubOrder.setAvatar(avatar);
        detailSubOrder.setLstSubVariant(lstSubVariant);
        detailSubOrder.setLstSubPricing(lstSubPricing);
        if (Objects.nonNull(subDetail.getOs3rdStatus()) && Objects.nonNull(StatusOrderEnum.valueOf(subDetail.getOs3rdStatus()))) {
            detailSubOrder.setProcessStatus(StatusOrderEnum.valueOf(subDetail.getOs3rdStatus()).toString());
        }
        formulaResDTO.setDetailSubOrder(detailSubOrder);

        response.setProcessStatusId(subDetail.getProcessId());
        response.setProcessStatusName(subDetail.getProcessStatus());

        // lịch sử hoạt động của đơn hàng
        List<IGetSubHistories> historyList = subscriptionRepository.getSubHistoriesById(subscriptionId);
        response.setHistories(historyList);
        // Loại bỏ type không làm cập nhật tiến trình
        List<IGetSubHistories> histories = historyList.stream()
            .filter(i -> !Objects.equals(i.getContentType(), SubscriptionHistoryConstant.ContentType.RECEIVED_ORDER))
            .collect(Collectors.toList());
        setProgressDetail(histories, response);

        // Chọn tiến tình có lắp đặt
        if (Objects.nonNull(detailSubOrder.getInstallationConfiguration()) && Objects.equals(detailSubOrder.getInstallationConfiguration(), 1)) {
            response.setIsInstalled(true);
        }
        response.setOrderCode("ID" .concat(String.format("%08d", subscriptionId)));
        return response;
    }

    @Override
    public DetailCustomerSubOrderDTO getDetailSubscriptionCustomerById(Long subscriptionId, PortalType portalType) {
        DetailCustomerSubOrderDTO res = new DetailCustomerSubOrderDTO();
        IDetailCustomerSubOrderDTO customerDetail = subscriptionRepository.getDetailCustomerSubOrderById(subscriptionId);
        convertDetailCustomerSub(customerDetail, res);
        return res;
    }

    @Override
    public DetailCustomerSubOrderDTO getDetailSubscriptionCustomerByCartCode(String cartCode, PortalType portalType) {
        DetailCustomerSubOrderDTO res = new DetailCustomerSubOrderDTO();
        IDetailCustomerSubOrderDTO customerDetail = subscriptionRepository.getDetailCustomerSubOrderByCartCode(cartCode);
        convertDetailCustomerSub(customerDetail, res);
        return res;
    }

    @Override
    public Page<ServiceInterestDTO> getPageServiceInterestByUserId(Long userId, Pageable pageable) {
        Page<IListServiceInterestDTO> pageServiceId = subscriptionRepository.getListServiceInterest(userId, pageable);

        // Gom id theo type + ghi nhận thứ tự gốc
        Map<String, List<Long>> idsByType = new HashMap<>();
        List<Long> orderedIds = new ArrayList<>();          // giữ thứ tự gốc

        for (IListServiceInterestDTO dto : pageServiceId.getContent()) {
            idsByType
                .computeIfAbsent(dto.getType(), k -> new ArrayList<>())
                .add(dto.getServiceId());
            orderedIds.add(dto.getServiceId());  // thứ tự hiển thị
        }

        Map<Long, ServiceInterestDTO> dtoById = new HashMap<>();

        for (Entry<String, List<Long>> entry : idsByType.entrySet()) {
            ObjectTypeEnum type = ObjectTypeEnum.valueOf(entry.getKey());
            List<Long> ids  = entry.getValue();

            switch (type) {
                case SERVICE:
                    List<IServiceInterestDTO> services = serviceRepository.getPricingVariantDefaultByServiceIdIn(ids);
                    for (IServiceInterestDTO service : services) {
                        ServiceInterestDTO dto = new ServiceInterestDTO();
                        BeanUtils.copyProperties(service, dto);
                        dto.setType(ObjectTypeEnum.SERVICE.name());

                        // Tính giá sau thuế service
                        Long pricingId = service.getPricingId();
                        if (Objects.nonNull(pricingId)) {
                            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(pricingId);
                            PricingMultiPlan pricingMultiPlan = pricingMultiPlanRepository.getPricingMultiPlanDefaultByPricingId(pricingId)
                                .orElse(null);
                            if (Objects.nonNull(pricingMultiPlan)) {
                                PricingPlanEnum pricingPlan = PricingPlanEnum.valueOf(pricingMultiPlan.getPricingPlan());
                                List<PricingPlanDetail> lstPlanDetail = pricingPlanDetailRepository.findByPricingMultiPlanIdOrderByUnitFromAsc(
                                        pricingMultiPlan.getId())
                                    .orElse(new ArrayList<>());
                                List<PricingDetailResDTO.UnitLimited> lstUnitLimited = subMultiplePeriod.getUnitLimitedByPricingId(lstPlanDetail,
                                    taxes);
                                // Tính giá chỉ từ (số tiền sau thue cần để mua gói với số lượng tối thiểu)
                                Integer minQuantity = getMinQuantity(pricingPlan, pricingMultiPlan.getMinimumQuantity(), lstUnitLimited);
                                dto.setPrice(PriceCalculator.calculatorWithLstTax(pricingPlan,
                                    pricingMultiPlan.getPrice(),
                                    BigDecimal.valueOf(minQuantity),
                                    lstPlanDetail.stream().map(SubscriptionCalculateDTO::new).collect(Collectors.toList()),
                                    BigDecimal.valueOf(ObjectUtil.getOrDefault(pricingMultiPlan.getFreeQuantity(), 0)),
                                    taxes.stream().map(PricingTax::new).collect(Collectors.toList())).calcWithTax());
                            }
                        }
                        dtoById.put(service.getId(), dto);
                    }
                    break;

                case COMBO:
                    List<IServiceInterestDTO> combos = subscriptionRepository.getListComboInterested(ids);
                    for (IServiceInterestDTO c : combos) {
                        ServiceInterestDTO dto = new ServiceInterestDTO();
                        BeanUtils.copyProperties(c, dto);
                        dto.setType(ObjectTypeEnum.COMBO.name());

                        // Tính giá sau thuế combo
                        List<PricingTaxRes> taxes = comboTaxRepository.getComboTax(c.getPricingId());
                        dto.setPrice(subscriptionFormula.priceAfterTax(c.getPricingPrice(), taxes));

                        dtoById.put(c.getId(), dto);
                    }
                    break;

                case SOLUTION:
                    List<IServiceInterestDTO> solutions = subscriptionRepository.getListSolutionInterested(ids);
                    for (IServiceInterestDTO solution : solutions) {
                        ServiceInterestDTO dto = new ServiceInterestDTO();
                        BeanUtils.copyProperties(solution, dto);
                        dto.setPrice(solution.getPricingPrice());
                        dto.setType(ObjectTypeEnum.SOLUTION.name());

                        dtoById.put(solution.getId(), dto);
                    }
                    break;
                case PACKAGE:
                    List<IServiceInterestDTO> packages = subscriptionRepository.getListPackageInterested(ids);
                    for (IServiceInterestDTO p : packages) {
                        ServiceInterestDTO dto = new ServiceInterestDTO();
                        BeanUtils.copyProperties(p, dto);
                        dto.setPrice(p.getPricingPrice());
                        dto.setType(ObjectTypeEnum.PACKAGE.name());

                        dtoById.put(p.getId(), dto);
                    }
                    break;

                default:

                    break;
            }
        }

        // Lắp lại theo đúng thứ tự
        List<ServiceInterestDTO> resultList = new ArrayList<>(orderedIds.size());
        for (Long id : orderedIds) {
            ServiceInterestDTO dto = dtoById.get(id);
            if (dto != null) {
                resultList.add(dto);
            }
        }

        return new PageImpl<>(resultList, pageable, pageServiceId.getTotalElements());
    }

    private Integer getMinQuantity(PricingPlanEnum planEnum, Long minQuantity, List<UnitLimited> lstUnitLimited) {
        switch (planEnum) {
            case FLAT_RATE:
                return 1;
            case UNIT:
                return (minQuantity == null || minQuantity < 0) ? 1 : minQuantity.intValue();
            default:
                return lstUnitLimited.stream().map(UnitLimited::getUnitFrom).min(Long::compareTo).orElse(1L).intValue();
        }
    }

    private static void convertDetailCustomerSub(IDetailCustomerSubOrderDTO customerDetail, DetailCustomerSubOrderDTO res) {
        BeanUtils.copyProperties(customerDetail, res);
        res.setCustomerType(CustomerTypeEnum.getValueOf(customerDetail.getCustomerType()));
        res.setCreatedSource(EnterpriseCreatedSourceEnum.getValueOf(customerDetail.getCreatedSource()));
        res.setStatus(StatusEnum.valueOf(customerDetail.getStatus()));
        res.setAddress(GsonMapperUtil.fromJson(customerDetail.getOrderAddresses(), Object.class));
    }

    @Override
    public void updateStatusOrder(UpdateStatusOrderDeviceDTO updateStatusOrderDeviceDTO) {
        PortalType portal = AuthUtil.getPortalOfUserRoles();
        String updateReason = updateStatusOrderDeviceDTO.getUpdateReason();
        boolean isCart = Objects.nonNull(updateStatusOrderDeviceDTO.getCartCode());

        // Lấy thông tin đơn hàng
        SubscriptionDetailCartDTO detailCartDTO = getOrderDetails(updateStatusOrderDeviceDTO, portal);
        if (!isCart) {
            handleSingleOrderStatusUpdate(updateStatusOrderDeviceDTO, updateReason);
        } else { // Xử lý case mua từ giỏ hàng.
            List<Long> subIds = new ArrayList<>();
            boolean isUpdateBill = false;
            // Xác định nội dung lịch sử và loại lịch sử
            StatusHistoryInfo historyInfo = determineStatusHistoryInfo(updateStatusOrderDeviceDTO.getStatusOrder());
            String histContent = historyInfo.getContent();
            int histType = historyInfo.getType();

            // Lấy danh sách subscription
            List<Subscription> subscriptionList = fetchSubscriptionList(updateStatusOrderDeviceDTO.getCartCode(), portal);

            LocalDateTime finalCancelTime = LocalDateTime.now();
            if (Objects.nonNull(updateStatusOrderDeviceDTO.getSubscriptionId())) {
                // SME chỉ update 1 sub
                Subscription subscription = subscriptionList.stream()
                    .filter(item -> item.getId().equals(updateStatusOrderDeviceDTO.getSubscriptionId())).findFirst().orElse(null);

                // Case đã nhận được hàng onl
                if (Objects.nonNull(subscription) && updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.FINISHED)) {
                    String nameService;
                    if (Objects.nonNull(subscription.getServiceId())) {
                        ServiceEntity service = serviceRepository.getServiceById(subscription.getServiceId());
                        nameService = service.getServiceName();
                    } else {
                        nameService = serviceRepository.getNameServiceByComboPlanId(ObjectUtil.getOrDefault(subscription.getComboPlanId(), -1L));
                    }
                    histContent = String.format(SubscriptionHistoryConstant.FINISHED_SERVICE, nameService);
                    histType = SubscriptionHistoryConstant.ContentType.FINISHED_SUB;

                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
                    subscription.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                    subscriptionRepository.save(subscription);

                    // Update order_item_status, trigger MANUAL
                    if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                        subscriptionMetadataService.updateOrderItemStatus(
                            new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                TriggerModeEnum.MANUAL));
                    }

                    int lstSubSameStatusSize = (int) subscriptionList.stream()
                        .filter(item -> Objects.equals(item.getOs3rdStatus(), (long) StatusOrderEnum.FINISHED.status))
                        .count();
                    if (Objects.equals(lstSubSameStatusSize, subscriptionList.size())) {
                        String histContentFinish = SubscriptionHistoryConstant.FINISHED_SUB;
                        Integer histTypeFinish = SubscriptionHistoryConstant.ContentType.FINISHED_SUB;
                        subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContentFinish,
                            histTypeFinish);
                    }
                }

                // Case đã nhận được hàng off/trả sau
                if (Objects.nonNull(subscription) && updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.RECEIVED_POSTPAID)) {
                    String nameService;
                    if (Objects.nonNull(subscription.getServiceId())) {
                        ServiceEntity service = serviceRepository.getServiceById(subscription.getServiceId());
                        nameService = service.getServiceName();
                    } else {
                        nameService = serviceRepository.getNameServiceByComboPlanId(ObjectUtil.getOrDefault(subscription.getComboPlanId(), -1L));
                    }
                    histContent = String.format(SubscriptionHistoryConstant.FINISHED_SERVICE, nameService);
                    histType = SubscriptionHistoryConstant.ContentType.RECEIVED_ORDER;

                    subscriptionHistoryService.addSubscriptionHistoryAndSubIdAndCartCode(updateStatusOrderDeviceDTO.getCartCode(),
                        subscription.getId(),
                        histContent, histType);
                }

                // Case đóng hàng
                if (Objects.nonNull(subscription) && updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.PREPARING)) {
                    String serviceName;
                    if (Objects.nonNull(subscription.getServiceId())) {
                        ServiceEntity service = serviceRepository.getServiceById(subscription.getServiceId());
                        serviceName = service.getServiceName();
                    } else {
                        serviceName = serviceRepository.getNameServiceByComboPlanId(ObjectUtil.getOrDefault(subscription.getComboPlanId(), -1L));
                    }
                    histContent = String.format(SubscriptionHistoryConstant.PACKING_ITEM_SUBSCRIPTION, serviceName);
                    histType = SubscriptionHistoryConstant.ContentType.PACKING_SUBSCRIPTION;

                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
                    subscription.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                    subscriptionRepository.save(subscription);

                    // Update order_item_status, trigger MANUAL
                    if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                        subscriptionMetadataService.updateOrderItemStatus(
                            new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                TriggerModeEnum.MANUAL));
                    }

                    int size = (int) subscriptionList.stream()
                        .filter(item -> item.getOs3rdStatus().equals((long) StatusOrderEnum.PREPARING.status))
                        .count();
                    if (Objects.equals(size, subscriptionList.size())) {
                        String histContentFinish = SubscriptionHistoryConstant.PACKING_SUBSCRIPTION;
                        Integer histTypeFinish = SubscriptionHistoryConstant.ContentType.PACKING_SUBSCRIPTION;
                        subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContentFinish,
                            histTypeFinish);
                    }
                    isUpdateBill = true;
                }
            }
            if (Objects.isNull(updateStatusOrderDeviceDTO.getSubscriptionId()) && !Objects.equals(updateStatusOrderDeviceDTO.getStatusOrder(),
                StatusOrderEnum.CANCELLED)
                && !Objects.equals(updateStatusOrderDeviceDTO.getStatusOrder(), StatusOrderEnum.RECEIVED)
                && !Objects.equals(updateStatusOrderDeviceDTO.getStatusOrder(), StatusOrderEnum.PREPARING_POSTPAID)
                && !Objects.equals(updateStatusOrderDeviceDTO.getStatusOrder(), (StatusOrderEnum.CONFIRM_PAYMENT_POSTPAID))
                && !Objects.equals(updateStatusOrderDeviceDTO.getStatusOrder(), (StatusOrderEnum.INSTALLING))
                && !Objects.equals(updateStatusOrderDeviceDTO.getStatusOrder(), (StatusOrderEnum.INSTALLED_SUCCESS))
            ) {
                // case giao cho DVVC gửi ở bên dưới.
                // case trả sau, onl/off
                subscriptionList.forEach(item -> {
                    subIds.add(item.getId());
                    // Hủy thì ko update trạng thái
                    int itemStatus = Math.toIntExact(item.getOs3rdStatus());
                    if (!Objects.equals(itemStatus, StatusOrderEnum.CANCELLED.status)) {
                        item.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                    }
                });
                subscriptionRepository.saveAll(subscriptionList);

                // Update order_item_status, trigger MANUAL
                subscriptionList.forEach(subscription -> {
                    if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                        subscriptionMetadataService.updateOrderItemStatus(
                            new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                TriggerModeEnum.MANUAL));
                    }
                });

                subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
            }
            // case trả sau, onl/off - Bàn giao cho DVVC rồi xác nhận thanh toán
            if (Objects.nonNull(updateStatusOrderDeviceDTO.getCartCode()) && updateStatusOrderDeviceDTO.getStatusOrder()
                .equals(StatusOrderEnum.PREPARING_POSTPAID)) {
                if (Objects.nonNull(detailCartDTO) && detailCartDTO.getProcessStatusId() == 4) {
                    histContent = SubscriptionHistoryConstant.POSTPAID_CONFIRM_PAYMENT_ONE_PROVIDER;
                    // kiểm tra đơn hàng từ giỏ hàng của 1 hay nhiều nhà cung cấp
                    if (subscriptionRepository.countProviderInCartOrder(updateStatusOrderDeviceDTO.getCartCode()) > 1) {
                        // nếu đơn giỏ hàng có nhiều NCC và dev update => log với tên những sản phẩm của dev login
                        if (PortalType.DEV.equals(portal)) {
                            Set<Long> updatedSubIds = subscriptionList.stream().map(Subscription::getId).collect(Collectors.toSet());
                            List<String> lstProductName = subscriptionRepository.getListProductNameBySubIdsIn(updatedSubIds).stream().filter(
                                ObjectUtils::isNotEmpty).collect(Collectors.toList());
                            histContent = String.format(SubscriptionHistoryConstant.POSTPAID_CONFIRM_PAYMENT_MANY_PROVIDERS,
                                String.join(", ", lstProductName));
                        }
                    }
                    histType = SubscriptionHistoryConstant.ContentType.POSTPAID_CONFIRM_PAYMENT;
                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
                    subscriptionList.forEach(item -> subIds.add(item.getId()));
                    isUpdateBill = true;
                }
            }

            // Xác nhận thành toán trả sau/off => Tiến trình hoàn thành
            if (Objects.nonNull(updateStatusOrderDeviceDTO.getCartCode()) && updateStatusOrderDeviceDTO.getStatusOrder()
                .equals(StatusOrderEnum.CONFIRM_PAYMENT_POSTPAID)) {
                // Lưu xác nhận thanh toán trước
                String content = SubscriptionHistoryConstant.POSTPAID_CONFIRM_PAYMENT_ONE_PROVIDER;
                // Kiểm tra đơn hàng từ giỏ hàng của 1 hay nhiều nhà cung cấp
                if (subscriptionRepository.countProviderInCartOrder(updateStatusOrderDeviceDTO.getCartCode()) > 1) {
                    // Nếu đơn giỏ hàng có nhiều NCC và dev update => log với tên những sản phẩm của dev login
                    if (PortalType.DEV.equals(portal)) {
                        Set<Long> updatedSubIds = subscriptionList.stream().map(Subscription::getId).collect(Collectors.toSet());
                        List<String> lstProductName = subscriptionRepository.getListProductNameBySubIdsIn(updatedSubIds).stream().filter(
                            ObjectUtils::isNotEmpty).collect(Collectors.toList());
                        content = String.format(SubscriptionHistoryConstant.POSTPAID_CONFIRM_PAYMENT_MANY_PROVIDERS,
                            String.join(", ", lstProductName));
                    }
                }
                int type = SubscriptionHistoryConstant.ContentType.RECEIVED_ORDER; // loại bỏ log lúc lấy tiến trình
                subscriptionHistoryService.addSubscriptionHistoryAndSubIdAndCartCode(updateStatusOrderDeviceDTO.getCartCode(),
                    updateStatusOrderDeviceDTO.getSubscriptionId(), content, type);

                // Log đơn hàng hoàn thành
                histContent = SubscriptionHistoryConstant.FINISHED_SUB;
                histType = SubscriptionHistoryConstant.ContentType.FINISHED_SUB;
                subscriptionList.forEach(item -> {
                    subIds.add(item.getId());
                    // Hủy thì ko update trạng thái
                    int itemStatus = Math.toIntExact(item.getOs3rdStatus());
                    if (!Objects.equals(itemStatus, StatusOrderEnum.CANCELLED.status)) {
                        item.setOs3rdStatus((long) StatusOrderEnum.FINISHED.status);
                    }
                });
                subscriptionRepository.saveAll(subscriptionList);

                // Update order_item_status, trigger MANUAL
                subscriptionList.forEach(subscription -> {
                    if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                        subscriptionMetadataService.updateOrderItemStatus(
                            new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                TriggerModeEnum.MANUAL));
                    }
                });

                subscriptionHistoryService.addSubscriptionHistoryAndSubIdAndCartCode(updateStatusOrderDeviceDTO.getCartCode(),
                    updateStatusOrderDeviceDTO.getSubscriptionId(), histContent, histType);
                isUpdateBill = true;
            }

            // Hủy đơn admin/bos
            if (updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.CANCELLED) && !subscriptionList.isEmpty()) {
                if (!portal.equals(PortalType.DEV)) {
                    subscriptionList.forEach(item -> {
                        item.setStatus(SubscriptionStatusEnum.CANCELED.value);
                        item.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                        item.setCancelledTime(finalCancelTime);
                        item.setUpdateReason(updateReason);
                    });
                    subscriptionRepository.saveAll(subscriptionList);

                    // Update order_item_status, trigger MANUAL
                    subscriptionList.forEach(subscription -> {
                        if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                            subscriptionMetadataService.updateOrderItemStatus(
                                new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                    TriggerModeEnum.MANUAL));
                        }
                    });

                    String content = SubscriptionHistoryConstant.ORDER_CANCELLED;
                    histType = SubscriptionHistoryConstant.ContentType.CANCEL_ORDER_DEV;
                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), content, histType);
                } else {
                    // Case hủy nhiều sản phẩm trên dev
                    List<Subscription> lstSub = new ArrayList<>();
                    for (Subscription sub : subscriptionList) {
                        // Phục vụ ghi log hiển thị trên Admin/Bos
                        String serviceName;
                        if (Objects.nonNull(sub.getServiceId())) {
                            ServiceEntity service = serviceRepository.getServiceById(sub.getServiceId());
                            serviceName = service.getServiceName();
                        } else {
                            serviceName = serviceRepository.getNameServiceByComboPlanId(ObjectUtil.getOrDefault(sub.getComboPlanId(), -1L));
                        }
                        histContent = String.format(SubscriptionHistoryConstant.ORDER_ITEM_CANCELLED, serviceName);
                        histType = SubscriptionHistoryConstant.ContentType.CANCEL_ORDER_DEV;
                        sub.setStatus(SubscriptionStatusEnum.CANCELED.value);
                        sub.setCancelledTime(LocalDateTime.now());
                        sub.setUpdateReason(updateReason);
                        sub.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                        lstSub.add(sub);
                        subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
                    }
                    subscriptionRepository.saveAll(lstSub);

                    // Update order_item_status, trigger MANUAL
                    lstSub.forEach(subscription -> {
                        if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                            subscriptionMetadataService.updateOrderItemStatus(
                                new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                    TriggerModeEnum.MANUAL));
                        }
                    });

                    // Lưu log hủy đơn bên DEV
                    String content = SubscriptionHistoryConstant.ORDER_CANCELLED;
                    histType = SubscriptionHistoryConstant.ContentType.CANCEL_ORDER;
                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), content, histType);
                }
            }

            //  Case giao đơn vị vận chuyển admin/bos
            if (updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.RECEIVED) && !subscriptionList.isEmpty()) {
                if (!portal.equals(PortalType.DEV)) {
                    subscriptionList.forEach(item -> {
                        item.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                    });
                    subscriptionRepository.saveAll(subscriptionList);

                    // Update order_item_status, trigger MANUAL
                    subscriptionList.forEach(subscription -> {
                        if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                            subscriptionMetadataService.updateOrderItemStatus(
                                new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                    TriggerModeEnum.MANUAL));
                        }
                    });

                    String content = SubscriptionHistoryConstant.SHIPPING_SUCCESS;
                    histType = SubscriptionHistoryConstant.ContentType.SHIPPING_SUCCESS;
                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), content, histType);
                } else {
                    // case bàn giao đơn vị vận chuyển  trên dev
                    List<Subscription> lstSub = new ArrayList<>();
                    for (Subscription sub : subscriptionList) {
                        sub.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                        lstSub.add(sub);
                    }
                    subscriptionRepository.saveAll(lstSub);

                    // Update order_item_status, trigger MANUAL
                    lstSub.forEach(subscription -> {
                        if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                            subscriptionMetadataService.updateOrderItemStatus(
                                new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                    TriggerModeEnum.MANUAL));
                        }
                    });

                    histContent = SubscriptionHistoryConstant.SHIPPING_SUCCESS;
                    histType = SubscriptionHistoryConstant.ContentType.SHIPPING_SUCCESS_DEV;
                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
                }
            }

            // Case lắp đặt - thi công/thi công -> TH ĐH có cả sub không lắp đặt
            if (updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.INSTALLING)
                || updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.INSTALLED_SUCCESS)) {
                List<Subscription> subscriptions = new ArrayList<>();
                if (Objects.nonNull(detailCartDTO)) {
                    List<SubscriptionFormulaResDTO> lstSubscriptionResponse = detailCartDTO.getShoppingCartFormulaResDTO()
                        .getLstSubscriptionResponse();
                    // lấy thông tin subId có lắp đặt để update
                    List<DetailSubOrder> lstDetailSubOrderInstalled = lstSubscriptionResponse.stream()
                        .map(SubscriptionFormulaResDTO::getDetailSubOrder)
                        .filter(item -> item.getInstallationConfiguration().equals(1))
                        .collect(Collectors.toList());
                    if (!lstDetailSubOrderInstalled.isEmpty()) {
                        Set<Long> lstSubId = lstDetailSubOrderInstalled.stream().map(DetailSubOrder::getId).collect(Collectors.toSet());
                        subscriptions = subscriptionRepository.findAllByIdIn(lstSubId);
                    }

                    List<Subscription> lstSub = new ArrayList<>();
                    for (Subscription sub : subscriptions) {
                        sub.setOs3rdStatus((long) updateStatusOrderDeviceDTO.getStatusOrder().status);
                        lstSub.add(sub);
                    }
                    subscriptionRepository.saveAll(lstSub);

                    // Update order_item_status, trigger MANUAL
                    lstSub.forEach(subscription -> {
                        if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                            subscriptionMetadataService.updateOrderItemStatus(
                                new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                                    TriggerModeEnum.MANUAL));
                        }
                    });


                    if (updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.INSTALLING)) {
                        histContent = SubscriptionHistoryConstant.ORDER_INSTALLING;
                        histType = SubscriptionHistoryConstant.ContentType.ORDER_INSTALLING;
                    } else if (updateStatusOrderDeviceDTO.getStatusOrder().equals(StatusOrderEnum.INSTALLED_SUCCESS)) {
                        histContent = SubscriptionHistoryConstant.ORDER_INSTALLED;
                        histType = SubscriptionHistoryConstant.ContentType.ORDER_INSTALLED;
                    }
                    subscriptionHistoryService.addSubscriptionHistory(updateStatusOrderDeviceDTO.getCartCode(), histContent, histType);
                }
            }
            if (isUpdateBill) {
                billsRepository.updateStatusBySub(subIds, new Date());
            }
        }
    }

    @Transactional
    @Override
    public void updateOrderItemStatus(OrderItemStatusReqDTO reqDTO) {
        PortalType portal = AuthUtil.getPortalOfUserRoles();
        Long userId = AuthUtil.getCurrentParentId();
        Long subscriptionId = reqDTO.getSubscriptionId();
        OrderItemStatusEnum newStatus = reqDTO.getStatus();
        IGetSubDetailCart subDetail = subscriptionRepository.getDetailSubOrderById(subscriptionId);
        checkPermissionAdminDev(portal, Collections.singleton(subDetail.getProviderId()), userId);
        Integer currentStatus = subscriptionMetadataRepository.getOrderItemStatus(subscriptionId);
        if (!isPreviousStatus(currentStatus, newStatus)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.SUBSCRIPTION_CAN_NOT_UPDATE, Resources.SUBSCRIPTION, ErrorKey.STATUS,
                String.valueOf(subscriptionId));
        }
        subscriptionMetadataRepository.updateOrderItemStatus(subscriptionId, newStatus.getStatus());
        saveOrderStatusHistory(subscriptionId, currentStatus, newStatus, reqDTO.getTriggerMode().name(), userId);
        // Cập nhật tiến trình tổng của đơn hàng
        Long productOrderId = subscriptionMetadataRepository.getProductOrderIdBySubId(subscriptionId);
        productOrdersService.updateOrderStatus(productOrderId);
    }

    private void saveOrderStatusHistory(Long subscriptionId, Integer orderItemStatus, OrderItemStatusEnum status, String triggerType, Long userId) {
        SubscriptionOrderStatusHistory statusHistory = SubscriptionOrderStatusHistory.builder()
            .subscriptionId(subscriptionId)
            .previousStatus(orderItemStatus)
            .nextStatus(status.getStatus())
            .triggerType(triggerType)
            .userId(userId)
            .userDisplayName(Objects.nonNull(AuthUtil.getCurrentUser()) ? AuthUtil.getCurrentUser().getDisplayName() : null)
            .createdAt(new Date())
            .build();
        subscriptionOrderStatusHistoryRepository.save(statusHistory);
    }

    /**
     * Kiểm tra trạng thái của đơn hàng có phải là trạng thái trước của trạng thái truyền vào
     */
    public boolean isPreviousStatus(Integer orderItemStatus, OrderItemStatusEnum targetStatus) {
        if (Objects.isNull(orderItemStatus) || Objects.isNull(targetStatus)) {
            return false;
        }

        return orderItemStatus.equals(targetStatus.getPreviousStatus());
    }

    public void checkPermissionAdminDev(PortalType portalType, Set<Long> providerIds, Long userId) {
        boolean checkPermission = !PortalType.ADMIN.equals(portalType) ||
            AuthUtil.checkUserRoles(
                Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN_PROVINCE.getValue()));
        if (PortalType.DEV.equals(portalType) &&
            AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(),
                RoleType.DEVELOPER_BUSINESS.getValue(), RoleType.DEVELOPER_OPERATOR.getValue(), RoleType.CUSTOMER_SUPPORT.getValue()))) {
            if (!providerIds.contains(userId)) {
                checkPermission = false;
            }
        }
        if (!checkPermission) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
    }
    /**
     * Lấy thông tin chi tiết đơn hàng
     */
    private SubscriptionDetailCartDTO getOrderDetails(UpdateStatusOrderDeviceDTO dto, PortalType portal) {
        if (Objects.nonNull(dto.getCartCode())) {
            return getDetailSubscriptionCart(dto.getCartCode(), portal);
        } else {
            return getDetailSubscriptionById(dto.getSubscriptionId(), portal);
        }
    }

    /**
     * Xử lý cập nhật trạng thái cho đơn hàng đơn lẻ
     */
    private void handleSingleOrderStatusUpdate(UpdateStatusOrderDeviceDTO dto, String updateReason) {
        // Tìm đơn hàng
        Optional<Subscription> subscriptionOpt = subscriptionRepository.findById(dto.getSubscriptionId());
        if (!subscriptionOpt.isPresent()) {
            String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, new String[]{"Subscription"},
                LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.SUBSCRIPTION, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        }

        Subscription subscription = subscriptionOpt.get();
        List<Long> subIds = Collections.singletonList(subscription.getId());

        // Xác định nội dung lịch sử và loại lịch sử
        OrderStatusHistoryInfo historyInfo = getHistoryInfoForSingleOrder(dto.getStatusOrder(), subscription, updateReason);

        // Cập nhật trạng thái đơn hàng
        if (!dto.getStatusOrder().equals(StatusOrderEnum.RECEIVED_POSTPAID)) {
            subscription.setOs3rdStatus((long) dto.getStatusOrder().status);
            subscriptionRepository.save(subscription);

            // Update order_item_status, trigger MANUAL
            if (Objects.nonNull(subscription.getOs3rdStatus()) && !Objects.equals(subscription.getOs3rdStatus(), SmeProgressEnum.PAID.getValue())) {
                subscriptionMetadataService.updateOrderItemStatus(
                    new OrderItemStatusReqDTO(subscription.getId(), OrderItemStatusEnum.fromType(subscription.getOs3rdStatus().intValue()),
                        TriggerModeEnum.MANUAL));
            }
        }

        // Lưu lịch sử
        subscriptionHistoryService.addSubscriptionHistory(dto.getSubscriptionId(),
            historyInfo.getContent(), historyInfo.getType());

        // Cập nhật hóa đơn nếu cần
        if (historyInfo.isUpdateBill()) {
            billsRepository.updateStatusBySub(subIds, new Date());
        }
    }


    /**
     * Lấy thông tin lịch sử cho đơn hàng đơn lẻ
     */
    private OrderStatusHistoryInfo getHistoryInfoForSingleOrder(StatusOrderEnum status, Subscription subscription, String updateReason) {
        String histContent;
        int histType;
        boolean isUpdateBill = false;

        switch (status) {
            case WAITING_SHIPPING:
                histContent = SubscriptionHistoryConstant.WAITING_FOR_SHIPPING;
                histType = SubscriptionHistoryConstant.ContentType.WAITING_FOR_SHIPPING;
                break;
            case DELIVERING:
                histContent = SubscriptionHistoryConstant.DELIVERING_TO_YOU;
                histType = SubscriptionHistoryConstant.ContentType.DELIVERING_TO_YOU;
                break;
            case DELIVERED:
                histContent = SubscriptionHistoryConstant.DELIVERED_SUCCESS;
                histType = SubscriptionHistoryConstant.ContentType.DELIVERED_SUCCESS;
                break;
            case INSTALLING:
                histContent = SubscriptionHistoryConstant.ORDER_INSTALLING;
                histType = SubscriptionHistoryConstant.ContentType.ORDER_INSTALLING;
                break;
            case INSTALLED_SUCCESS:
                histContent = SubscriptionHistoryConstant.ORDER_INSTALLED;
                histType = SubscriptionHistoryConstant.ContentType.ORDER_INSTALLED;
                break;
            case RECEIVED:
                histContent = SubscriptionHistoryConstant.SHIPPING_SUCCESS;
                histType = SubscriptionHistoryConstant.ContentType.SHIPPING_SUCCESS;
                break;
            case PREPARING: // case trả trước + off
                // Lưu xác nhận thanh toán thành công trước khi đóng hàng
                subscriptionHistoryService.addSubscriptionHistory(subscription.getId(),
                    SubscriptionHistoryConstant.PAYMENT_SUCCESS,
                    SubscriptionHistoryConstant.ContentType.PAYMENT_SUCCESS);

                histContent = SubscriptionHistoryConstant.PACKING_SUBSCRIPTION;
                histType = SubscriptionHistoryConstant.ContentType.PACKING_SUBSCRIPTION;
                isUpdateBill = true;
                break;
            case CANCELLED:
                histContent = SubscriptionHistoryConstant.ORDER_CANCELLED;
                histType = SubscriptionHistoryConstant.ContentType.CANCEL_ORDER;
                subscription.setStatus(SubscriptionStatusEnum.CANCELED.value);
                subscription.setCancelledTime(LocalDateTime.now());
                subscription.setUpdateReason(updateReason);
                break;
            case PREPARING_POSTPAID:
                histContent = SubscriptionHistoryConstant.PAYMENT_SUCCESS;
                histType = SubscriptionHistoryConstant.ContentType.PAYMENT_SUCCESS_BY_CASH;
                isUpdateBill = true;
                break;
            case FINISHED :
                histContent = SubscriptionHistoryConstant.FINISHED_SUB;
                histType = SubscriptionHistoryConstant.ContentType.FINISHED_SUB;
                break;
            case CONFIRM_PAYMENT_POSTPAID:
                // Lưu xác nhận thanh toán trước
                subscriptionHistoryService.addSubscriptionHistory(subscription.getId(),
                    SubscriptionHistoryConstant.POSTPAID_CONFIRM_PAYMENT_ONE_PROVIDER,
                    SubscriptionHistoryConstant.ContentType.RECEIVED_ORDER);

                // Lưu xác nhận thanh toán thành công trước khi đơn hoàn thành
                subscriptionHistoryService.addSubscriptionHistory(subscription.getId(),
                    SubscriptionHistoryConstant.PAYMENT_SUCCESS,
                    SubscriptionHistoryConstant.ContentType.PAYMENT_SUCCESS);

                histContent = SubscriptionHistoryConstant.FINISHED_SUB;
                histType = SubscriptionHistoryConstant.ContentType.FINISHED_SUB;
                isUpdateBill = true;
                break;
            case RECEIVED_POSTPAID:
                histContent = SubscriptionHistoryConstant.RECEIVED_ORDER;
                histType = SubscriptionHistoryConstant.ContentType.RECEIVED_ORDER;
                break;
            default:
                histContent = CharacterConstant.BLANK;
                histType = SubscriptionHistoryConstant.ContentType.ORDER_DEVICE;
                break;
        }

        return new OrderStatusHistoryInfo(histContent, histType, isUpdateBill);
    }


    /**
     * Xác định thông tin lịch sử dựa trên trạng thái
     */
    private StatusHistoryInfo determineStatusHistoryInfo(StatusOrderEnum status) {
        String content;
        int type = SubscriptionHistoryConstant.ContentType.ORDER_DEVICE;

        switch (status) {
            case WAITING_SHIPPING:
                content = SubscriptionHistoryConstant.WAITING_FOR_SHIPPING;
                type = SubscriptionHistoryConstant.ContentType.WAITING_FOR_SHIPPING;
                break;
            case DELIVERING:
                content = SubscriptionHistoryConstant.DELIVERING_TO_YOU;
                type = SubscriptionHistoryConstant.ContentType.DELIVERING_TO_YOU;
                break;
            case DELIVERED:
                content = SubscriptionHistoryConstant.DELIVERED_SUCCESS;
                type = SubscriptionHistoryConstant.ContentType.DELIVERED_SUCCESS;
                break;
            case INSTALLING:
                content = SubscriptionHistoryConstant.ORDER_INSTALLING;
                type = SubscriptionHistoryConstant.ContentType.ORDER_INSTALLING;
                break;
            case INSTALLED_SUCCESS:
                content = SubscriptionHistoryConstant.ORDER_INSTALLED;
                type = SubscriptionHistoryConstant.ContentType.ORDER_INSTALLED;
                break;
            default:
                content = CharacterConstant.BLANK;
                break;
        }

        return new StatusHistoryInfo(content, type);
    }

    /**
     * Lấy danh sách subscription
     */
    private List<Subscription> fetchSubscriptionList(String cartCode, PortalType portal) {
        if (PortalType.DEV.equals(portal)) {
            Long userId = AuthUtil.getCurrentParentId();
            return subscriptionRepository.findAllSubByProvider(cartCode, userId);
        } else {
            return subscriptionRepository.findAllByCartCode(cartCode);
        }
    }

    @Override
    public SubDetailEcontract getDetailEcontractSubscriptionById(Long id) {
        SubDetailEcontract response = new SubDetailEcontract();
        IGetEcontract iGetEcontract = subscriptionRepository.getEcontractDTOById(id);
        if (Objects.nonNull(iGetEcontract)) {
            // danh sách hợp đồng điện tử
            List<ViewEcontractBodyDTO> lstEcontract = getListEcontract(iGetEcontract.getIdentityNo());
            response.setLstEcontract(lstEcontract.stream().filter(e -> Objects.equals(id, e.getSubscriptionId())).collect(Collectors.toList()));

            // danh sách hóa đơn điện tử
            List<InvoiceInfoDTO> invoiceList = eInvoiceRepository.getInvoiceByBillingId(iGetEcontract.getBillingId());
            if (!invoiceList.isEmpty()) {
                for (InvoiceInfoDTO invoiceInfoDTO : invoiceList) {
                    GetBillingDetailDTO.EInvoice eInvoice = new GetBillingDetailDTO.EInvoice();
                    BeanUtils.copyProperties(invoiceInfoDTO, eInvoice);
                    response.getEInvoice().add(eInvoice);
                }
            }
        }
        return response;
    }

    @Override
    public SubDetailEcontract getDetailEcontractSubscriptionByCart(String cartCode) {
        SubDetailEcontract response = new SubDetailEcontract();
        IGetEcontract iGetEcontract = subscriptionRepository.getEcontractDTOByCart(cartCode);
        if (Objects.nonNull(iGetEcontract)) {
            List<Long> subIds = Arrays.stream(iGetEcontract.getLstSubId().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
            // danh sách hợp đồng điện tử
            List<ViewEcontractBodyDTO> lstEcontract = getListEcontract(iGetEcontract.getIdentityNo());
            response.setLstEcontract(lstEcontract.stream().filter(e -> subIds.contains(e.getSubscriptionId())).collect(Collectors.toList()));

            // danh sách hóa đơn điện tử
            List<InvoiceInfoDTO> invoiceList = eInvoiceRepository.getInvoiceByBillingCode(iGetEcontract.getBillingCode());
            if (!invoiceList.isEmpty()) {
                for (InvoiceInfoDTO invoiceInfoDTO : invoiceList) {
                    GetBillingDetailDTO.EInvoice eInvoice = new GetBillingDetailDTO.EInvoice();
                    BeanUtils.copyProperties(invoiceInfoDTO, eInvoice);
                    response.getEInvoice().add(eInvoice);
                }
            }
        }
        return response;
    }

    @Override
    public void updateStatusBillOrder(List<Long> subIds) {
        billsRepository.updateStatusBySub(subIds, new Date());
    }

    private BigDecimal getOriginalPrice(BigDecimal originalPrice, List<PricingTax> planTaxList) {
        if (planTaxList.isEmpty()) return originalPrice;
        ContainedTaxListCalculator calculator = new ContainedTaxListCalculator(planTaxList, originalPrice);
        return calculator.getPreTaxAmount();
    }


    /**
     * lấy khoảng giá theo số lượng lũy kế
     */
    private BigDecimal getPerPrice(List<SubscriptionCalculateDTO> originPlanPriceList, List<SubscriptionCalculateDTO> planPriceList) {
        for (int i = planPriceList.size() - 1; i >= 0; i--) {
            if (planPriceList.get(i).getQuantity().equals(0L)) {
                return originPlanPriceList.get(i + 1).getUnitPrice();
            }
        }
        return originPlanPriceList.get(0).getUnitPrice();
    }

    public ShoppingCartFormulaResDTO calculateShoppingCart(ShoppingCartFormulaReqDTO request, CreatedSourceMigrationEnum createdSource) {
        List<Long> pricingIds = new ArrayList<>();
        request.getLstSubscription().stream().filter(sub -> sub.getCalculateType().equals(CalculateTypeEnum.PRICING))
            .forEach(preSub -> pricingIds.add(preSub.getObject().getId()));

        List<Long> comboIds = new ArrayList<>();
        request.getLstSubscription().stream().filter(sub -> sub.getCalculateType().equals(CalculateTypeEnum.COMBO))
            .forEach(preSub -> comboIds.add(preSub.getObject().getId()));

        List<Long> addonIds = new ArrayList<>();
        request.getLstSubscription().forEach(sub -> sub.getAddons().forEach(addon -> addonIds.add(addon.getId())));

        // ONEBSS: vẫn cho mua service nếu service / pricing / addon tắt hoạt động
        if (Objects.equals(createdSource, CreatedSourceMigrationEnum.ONE_BSS)) {
            // Validate pricing
            List<Long> lstServiceId = pricingRepository.getListServiceIds(pricingIds);
            if (serviceRepository.existsByIdInAndDeletedFlag(lstServiceId, DeletedFlag.DELETED.getValue()) ||
                pricingRepository.existsByIdInAndDeletedFlag(pricingIds, DeletedFlag.DELETED.getValue())) {
                throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_PRICING, Resources.SERVICES, ErrorKey.SERVICE);
            }
            // Validate addon
            if (addonRepository.existsByIdInAndDeletedFlag(addonIds, DeletedFlag.DELETED.getValue())) {
                throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_ADDON, Resources.ADDON, ErrorKey.ADD_ON);
            }
        } else { // ONESME : luồng cũ (ko cho mua service / combo / pricing / addon tắt hoạt động)
            validateStatusAndDeletedFlag(pricingIds, comboIds, addonIds);
        }

        // Khai báo cho creditNote Lưu lại list creditNote trước khi tính toán
        List<CreditNoteCalculateDTO> creditNoteListOfCart = creditNoteRepository.getAllCreditNoteByUserIdAndStatus(request.getUserId(),
                CreditNoteDetailStatusEnum.ADJUSTED.code, SubscriptionConstant.REGISTER, -1L);
        // Tạo danh sách creditNote để đưa vào tính toán
        List<CreditNoteCalculateDTO> lstCreditNoteTemp = creditNoteListOfCart.stream()
                .map(x -> new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                        x.getAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId()))
                .collect(Collectors.toList());
        // calculate
        ShoppingCartFormulaResDTO response = new ShoppingCartFormulaResDTO();
        List<SubscriptionFormulaResDTO> lstSubscriptionResponse = new ArrayList<>();
        Map<SubscriptionFormulaReqDTO, SubscriptionFormulaResDTO> mapSubResBySubReq = new HashMap<>();
        for (SubscriptionFormulaReqDTO preSub : request.getLstSubscription()) {
            SubscriptionFormulaResDTO formulaResDTO;
            if (CalculateTypeEnum.SERVICE_GROUP.equals(preSub.getCalculateType())) { // TH NHÓM DỊCH VỤ
                formulaResDTO = subscriptionFormula.calculateServiceGroup(preSub, request, lstCreditNoteTemp);
                formulaResDTO.setServiceId(preSub.getServiceId());
                formulaResDTO.setIdx(preSub.getIdx());
                formulaResDTO.setParentIdx(preSub.getParentIdx());
                formulaResDTO.setProductType(preSub.getProductType());
            } else {
                /* Khuyến mại tổng hóa đơn */
                request.getLstMcInvoice().forEach(mcInfo -> preSub.getLstMcInvoice().add(mcInfo));
                // convert to DTO
                preSub.setUserId(request.getUserId());
                formulaResDTO = calculateSub(preSub, lstCreditNoteTemp);
            }

            String serviceType = preSub.getServiceType();
            formulaResDTO.setServiceType(serviceType);
            mapSubResBySubReq.put(preSub, formulaResDTO);
            lstSubscriptionResponse.add(formulaResDTO);
        }

        response.setLstSubscriptionResponse(lstSubscriptionResponse);
        // Tính tiền áp dụng khuyến mãi tổng hóa đơn
        List<FormulaObject> objects = lstSubscriptionResponse.stream().map(SubscriptionFormulaResDTO::getObject).collect(Collectors.toList());
        List<FormulaObject> addons = new ArrayList<>();
        lstSubscriptionResponse.forEach(item -> addons.addAll(item.getAddonShopCart()));
        BigDecimal billAmount = lstSubscriptionResponse.stream().map(SubscriptionFormulaResDTO::getTotalAmountPreTaxFinal)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<Long> couponInvoiceIds = new ArrayList<>();
        Set<Long> couponReqIds = new HashSet<>(request.getCoupons());
        // thêm coupon bundling(tạm thời logic giống coupon tổng)
        if (Objects.nonNull(request.getPackageCoupons())) {
            couponReqIds.addAll(request.getPackageCoupons());
        }
        List<PromotionDTO> lstPromotionRejects = new ArrayList<>();
        String systemParamCoupon = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
        int maxCouponTotalBill = !Strings.isBlank(systemParamCoupon) ? Integer.parseInt(systemParamCoupon) : 0;
        boolean isNotLimited = Objects.equals(maxCouponTotalBill, -1);
        for (Long couponId : couponReqIds) {
            // Số khuyến mãi tổng hóa đơn theo couponId
            int couponTotalBill = couponRepository.countCouponTypeTotalBill(Collections.singletonList(couponId));
            // Check khuyến mãi có thỏa mãn và số lượng khuyến mãi theo tổng hóa đơn được áp dụng tối đa theo cấu hình
            if (validateCouponInvoiceBeforeApply(couponId, request.getPackageId(), request.getSolutionId(), billAmount) &&
                (couponTotalBill == 0 || isNotLimited || maxCouponTotalBill > 0)) {
                couponInvoiceIds.add(couponId);
                // Số lượng khuyến mãi tổng còn lại được áp dụng(Không tính bundling)
                if (!isNotLimited) {
                    maxCouponTotalBill = maxCouponTotalBill - couponTotalBill;
                }
                continue;
            }
            PromotionDTO promotionDTO = new PromotionDTO(couponId);
            promotionDTO.setApplicable(false);
            lstPromotionRejects.add(promotionDTO);
        }
        // Set list khuyến mãi reject
        lstSubscriptionResponse.forEach(item -> item.setLstRejectPromotion(lstPromotionRejects));
        // Tinh lại tiền addon áp dụng khuyến mãi tổng hóa đơn
        List<Coupon> couponInvoices = subscriptionFormula.collectInvoiceCouponList(couponInvoiceIds);
        if (Objects.nonNull(couponInvoices)) {
            couponInvoices.sort((c1, c2) -> c2.getDiscountValue().compareTo(c1.getDiscountValue()));
            // Gộp coupon cùng type
            Map<Integer, List<Coupon>> mapCoupon = couponInvoices.stream().collect(Collectors.groupingBy(Coupon::getDiscountType));
            calculateObjectApplyCouponInvoice(objects, mapCoupon);
            // Sắp xếp lại addon thuế từ cao -> thấp
            addons.sort((a1, a2) -> a2.getTotalTax().compareTo(a1.getTotalTax()));
            calculateObjectApplyCouponInvoice(addons, mapCoupon);
        }
        // Tính tổng tiền của thuê bao sau KM, sau CR
        request.getLstSubscription().forEach(preSub -> {
            SubscriptionFormulaResDTO formulaResDTO = mapSubResBySubReq.get(preSub);
            ShoppingCartCalculateDTO sub = new ShoppingCartCalculateDTO(preSub);
            formulaResDTO.setTotalAmountPreTaxFinal(
                calcTotalFinalPreTaxAmount(formulaResDTO.getObject(), formulaResDTO.getAddonShopCart(), formulaResDTO));
            formulaResDTO.setTotalAmountAfterTaxFinal(
                calcTotalFinalAfterTaxAmount(formulaResDTO.getObject(), formulaResDTO.getAddonShopCart(), formulaResDTO));
            // Tính lại tổng tiền sau CR
            calculateAfterRefundShoppingCart(formulaResDTO, sub, lstCreditNoteTemp);
            // tính toán thông tin thanh toán kì tới
            subscriptionFormula.calculateNextPaymentAmount(null, formulaResDTO);
            subscriptionFormula.convertTaxFeePaymentOne(formulaResDTO, null);
            // Tính lại tổng tiền thuế phí
            CalculatorUtil.summaryFeeTaxCoupon(formulaResDTO.getObject(), preSub.getServiceType(), formulaResDTO, preSub,
                response.getFeeObject(), response.getTaxObject(), response.getCouponObject());
        });

        // Cập nhật lại tiền remaining cho danh sách creditNote trước khi tính toán dự vào id
        List<CreditNoteCalculateDTO> creditNoteListNewOfCart = creditNoteListOfCart.stream().map(x -> {
            Optional<CreditNoteCalculateDTO> crTemp = lstCreditNoteTemp.stream().filter(c -> Objects.equals(c.getId(), x.getId())).findFirst();
            return crTemp.map(
                            creditNoteCalculateDTO -> new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(),
                                    x.getTaxValue(),
                                    x.getAmountRefund(), creditNoteCalculateDTO.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(),
                                    x.getSubscriptionId()))
                    .orElseGet(() -> new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                            x.getAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId()));
        }).collect(Collectors.toList());
        response.setCreditNoteList(creditNoteListOfCart);
        response.setCreditNoteListNew(creditNoteListNewOfCart.stream()
                .collect(Collectors.groupingBy(CreditNoteCalculateDTO::getTaxValue, Collectors.toList())));
        // Tính tổng tiền cả đơn hàng
        sumTotalAmountSub(response, lstSubscriptionResponse);
        return response;
    }

    private void validateStatusAndDeletedFlag(List<Long> pricingIds, List<Long> comboIds, List<Long> addonIds) {
        // Validate pricing
        List<Long> pricingErrors = validateStatus(pricingIds, CalculateTypeEnum.PRICING);
        if (!pricingErrors.isEmpty()) {
            if (new HashSet<>(pricingIds).containsAll(pricingErrors)) {
                throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_PRICING, pricingErrors.toString(), ErrorKey.PRICING);
            }
            throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_PRICING, pricingErrors.toString(), ErrorKey.SERVICE);
        }
        // Validate combo_plan
        List<Long> comboErrors = validateStatus(comboIds, CalculateTypeEnum.COMBO);
        if (!comboErrors.isEmpty()) {
            if (new HashSet<>(comboIds).containsAll(comboErrors)) {
                throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_COMBO_PLAN, comboErrors.toString(), ErrorKey.COMBO_PLAN);
            }
            throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_COMBO_PLAN, comboErrors.toString(), ErrorKey.COMBO);
        }
        // Validate addon
        if (!validateStatus(addonIds, CalculateTypeEnum.ADDON).isEmpty()) {
            throw exceptionFactory.badRequest(ShoppingCartError.INVALID_STATUS_ADDON, validateStatus(addonIds, CalculateTypeEnum.ADDON).toString(),
                ErrorKey.ADD_ON);
        }
    }

    @Override
    public SubscriptionFormulaResDTO calculateSub(SubscriptionFormulaReqDTO preSub, List<CreditNoteCalculateDTO> lstCreditNoteTemp) {
        SubscriptionFormulaResDTO formulaResDTO;
        ShoppingCartCalculateDTO sub = new ShoppingCartCalculateDTO(preSub);
        /* Tạo danh sách tất cả CTKM/CDQC được áp dụng */
        sub.fillPromotion();
        /* Tính tiền bỏ qua khuyến mại */
        SubscriptionCalculationInput input = subscriptionFormula.collectCalculatorInput(sub, -1);
        SubscriptionPlanCalculationInput planCalc = input.getPlan();
        if (Objects.nonNull(preSub.getObject().getInputPrice())) planCalc.setInputPrice(preSub.getObject().getInputPrice());
        List<SubscriptionPlanCalculationInput> lstAddonCalc = input.getAddonList();
        formulaResDTO = new SubscriptionCalculator(planCalc, lstAddonCalc, sub.getOtherFeeList()).calc();
        /* Tính tiền ứng với từng khuyến mại */
        List<McAppliedEffectDTO> lstTotalEffect = new ArrayList<>();
        List<AppliedPromotionDTO> appliedPromotionDTOList = new LinkedList<>();
        List<PromotionDTO> promotions = sub.getLstPromotion();
        for (int index = 0; index < promotions.size(); index++) {
            // Kiểm tra coupon có thỏa mãn điều kiện sử dụng (trong cấu hình của coupon)
            PromotionDTO promotionDTO = promotions.get(index);
            if (promotionDTO.getType() == McPromotionTypeEnum.COUPON) {
                Coupon coupon = validateCouponBeforeApply(promotionDTO.getCouponId(), formulaResDTO);
                if (coupon == null) {
                    promotionDTO.setApplicable(false);
                    continue;
                }
            }
            // Kiểm tra coupon/mc có thỏa mãn điều kiện của CDQC
            ApplyCampaignInfoDTO applyCampaignInfoDTO = new ApplyCampaignInfoDTO(sub, formulaResDTO, appliedPromotionDTOList, promotionDTO);
            ApplyCampaignResponseDTO applyMcResponse = campaignSmeService.applyPromotion(applyCampaignInfoDTO);
            if (applyMcResponse == null || !applyMcResponse.isApplicable()) {
                promotionDTO.setApplicable(false);
                promotionDTO.setCause(applyMcResponse == null ? McRejectCauseEnum.UNKNOWN : applyMcResponse.getCause());
                continue;
            }
            lstTotalEffect.addAll(McAppliedEffectDTO.convert(applyMcResponse));

            // Cập nhật danh sách coupon dần vào input
            input = subscriptionFormula.collectCalculatorInput(sub, index);
            /* Cập nhật lại calculator của pricing/combo với danh sách campaign effect */
            planCalc = input.getPlan();
            planCalc.updateMcEffect(lstTotalEffect, sub.getCalculateType());
            /* Cập nhật lại calculator của addon với danh sách campaign effect */
            lstAddonCalc = input.getAddonList();
            lstAddonCalc.forEach(addonCalc -> addonCalc.updateMcEffect(lstTotalEffect, CalculateTypeEnum.ADDON));
            formulaResDTO = new SubscriptionCalculator(planCalc, lstAddonCalc, sub.getOtherFeeList()).calc();
            // Cập nhật appliedPromotionDTOList sử dụng cho lần sau
            appliedPromotionDTOList.add(new AppliedPromotionDTO(applyMcResponse));
        }
        // Thêm thông tin khuyến mại lần mua sau hoặc tặng sản phẩm
        subscriptionFormula.updateFormulaObject(lstTotalEffect, formulaResDTO.getObject(), false);
        formulaResDTO.getAddonShopCart().forEach(addon -> subscriptionFormula.updateFormulaObject(lstTotalEffect, addon, true));
        formulaResDTO.setAddons(new ArrayList<>());
        // Thêm thông tin các CTKM/CDQC bị reject
        formulaResDTO.setLstRejectPromotion(sub.getLstPromotion().stream().filter(item -> !item.isApplicable())
                .collect(Collectors.toList()));
        formulaResDTO.setIsService(Objects.equals(preSub.getCalculateType(), CalculateTypeEnum.PRICING));
        formulaResDTO.setIdx(preSub.getIdx());
        formulaResDTO.setServiceId(preSub.getServiceId());
        formulaResDTO.setParentIdx(preSub.getParentIdx());
        formulaResDTO.setProductType(preSub.getProductType());
        formulaResDTO.setServiceDraftId(preSub.getServiceDraftId());
        formulaResDTO.setVariantId(preSub.getVariantId());
        formulaResDTO.setVariantDraftId(preSub.getVariantDraftId());
        formulaResDTO.setServiceName(input.getPlan().getServiceName());
        return formulaResDTO;
    }

    /**
     * Tính tiền objects áp dụng khuyến mại hóa đơn
     */
    private void calculateObjectApplyCouponInvoice(List<FormulaObject> objects, Map<Integer, List<Coupon>> mapCoupon) {
        // KM - tiền mặt
        List<Coupon> couponPrices = mapCoupon.get(DiscountTypeEnum.PRICE.value);
        List<Coupon> couponPercents = mapCoupon.get(DiscountTypeEnum.PERCENT.value);
        for (FormulaObject object : objects) {
            // Nếu chỉnh sửa sub thì lấy khuyến mãi đã sử dụng lúc đăng ký
            if (Objects.nonNull(couponPrices)) {
                if (object.isCheckRegisteredSub()) {
                    Map<Long, FormulaCoupon> usedCouponPriceTotalMap = object.getUsedCouponPriceTotalMap();
                    for (Coupon coupon : couponPrices) {
                        FormulaCoupon formulaCoupon = usedCouponPriceTotalMap.get(coupon.getId());
                        if (formulaCoupon != null) {
                            object.getInvoiceCouponPrices().add(formulaCoupon);
                        }
                    }
                } else {
                    // Tính lại khuyến mãi theo tỷ lệ
                    if (object.getRatio().compareTo(BigDecimal.ONE) != 0 && Objects.nonNull(object.getFirstChanged())) {
                        couponPrices.forEach(
                            i -> i.setDiscountValue(i.getDiscountValue().multiply(object.getRatio()).setScale(0, RoundingMode.HALF_UP)));
                    }
                    object.setInvoiceCouponPrices(
                        new PriceCouponListCalculator(new AtomicLong(object.getFinalAmountPreTax().longValue()), couponPrices).calc());
                }

                // Cập nhật lại số tiền tối đa khuyến mãi(Chưa xử lý trường hợp theo tỷ lẹ)
                couponPrices.forEach(item -> {
                        FormulaCoupon formulaCoupon = object.getInvoiceCouponPrices().stream()
                            .filter(couponApply -> Objects.equals(item.getId(), couponApply.getId())).findFirst().orElse(null);
                        if (Objects.nonNull(formulaCoupon)) {
                            item.setDiscountValue(item.getDiscountValue().subtract(formulaCoupon.getPrice()));
                        }
                    }
                );
            }
            // Tính tiền trước thuế sau khuyến mãi riêng và khuyến mãi tổng hóa đơn theo số tiền
            BigDecimal pretaxAmount = object.getFinalAmountPreTax()
                .subtract(new TotalPriceCouponAmountCalculator(object.getInvoiceCouponPrices()).calc());
            if (Objects.nonNull(couponPercents)) {
                if (object.isCheckRegisteredSub()) {
                    Map<Long, FormulaCoupon> usedCouponPriceTotalMap = object.getUsedCouponPriceTotalMap();
                    for (Coupon coupon : couponPercents) {
                        FormulaCoupon formulaCoupon = usedCouponPriceTotalMap.get(coupon.getId());
                        if (formulaCoupon != null) {
                            object.getInvoiceCouponPercents().add(formulaCoupon);
                        }
                    }
                } else {
                    object.setInvoiceCouponPercents(
                        new PercentCouponListCalculator(couponPercents, pretaxAmount, new AtomicLong(pretaxAmount.longValue())).calc());
                }
                couponPercents.forEach(item -> {
                    if (Objects.nonNull(item)) {
                        FormulaCoupon percentCoupon = object.getInvoiceCouponPercents().stream()
                            .filter(couponApply -> Objects.equals(item.getId(), couponApply.getId())).findFirst().orElse(null);
                        if (Objects.nonNull(percentCoupon) && Objects.nonNull(item.getDiscountAmount())) {
                            BigDecimal maxAmount = item.getDiscountAmount().subtract(percentCoupon.getPrice());
                            maxAmount = BigDecimal.ZERO.compareTo(maxAmount) > 0 ? BigDecimal.ZERO : maxAmount;
                            item.setDiscountAmount(maxAmount);
                        }
                    }
                });
            }
            // Tính tiền trước thuế sau tất cả khuyến mãi
            BigDecimal finalPretaxAmount = pretaxAmount.subtract(
                new TotalPercentCouponAmountCalculator(object.getInvoiceCouponPercents()).calc());
            object.setFinalAmountPreTax(finalPretaxAmount);
            object.setFinalAmountAfterTax(
                finalPretaxAmount.add(finalPretaxAmount.multiply(object.getTotalTax())).setScale(0, RoundingMode.HALF_UP));
            object.getTaxes().forEach(tax -> tax.setPrice(finalPretaxAmount.multiply(tax.getPercent()).setScale(0, RoundingMode.HALF_UP)));
        }
    }

    /**
     * Tổng tiền thuê bao trước thuế trừ tất cả KM
     */
    private BigDecimal calcTotalFinalPreTaxAmount(FormulaObject object, List<FormulaObject> addonList, SubscriptionFormulaResDTO formulaResDTO) {
        BigDecimal planAmount = object.getFinalAmountPreTax();
        BigDecimal totalAddonsAmount = addonList.stream().map(FormulaObject::getFinalAmountPreTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        return planAmount.add(formulaResDTO.getPlanSetupFeePreTaxAmount()).add(totalAddonsAmount)
            .add(formulaResDTO.getTotalAddonSetupFeesPreTaxAmount()).add(formulaResDTO.getTotalCustomFeesAmount());
    }

    /**
     * Tính tổng tiền thuê bao sau thuế
     */
    private BigDecimal calcTotalFinalAfterTaxAmount(FormulaObject object, List<FormulaObject> addonList,
        SubscriptionFormulaResDTO formulaResDTO) {
        BigDecimal planAmount = object.getFinalAmountAfterTax();
        BigDecimal planSetupFeeAfterTaxAmount =
            Objects.nonNull(object.getLstSetupFeePricing()) ? object.getLstSetupFeePricing().stream()
                .map(SetupFee::getFinalAmountAfterTax).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
        BigDecimal totalAddonsAmount = addonList.stream().map(FormulaObject::getFinalAmountAfterTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAddonSetupFeesAfterTaxAmount = addonList.stream().map(FormulaObject::getSetupFee)
            .filter(Objects::nonNull).map(SetupFee::getFinalAmountAfterTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        return planAmount.add(planSetupFeeAfterTaxAmount).add(totalAddonsAmount)
            .add(totalAddonSetupFeesAfterTaxAmount).add(formulaResDTO.getTotalCustomFeesAmount());
    }

    private List<Long> validateStatus(List<Long> ids, CalculateTypeEnum type) {
        if (Objects.equals(CalculateTypeEnum.PRICING.getValue(), type.getValue())) {
            if (serviceRepository.findLongDeletedOrTurnOff(pricingRepository.getListServiceIds(ids)).isEmpty()) {
                return pricingRepository.findLongDeletedOrTurnOff(ids);
            }
            return serviceRepository.findLongDeletedOrTurnOff(pricingRepository.getListServiceIds(ids));
        } else if (Objects.equals(CalculateTypeEnum.COMBO.getValue(), type.getValue())) {
            if (comboRepository.findLongDeletedOrTurnOff(comboPlanRepository.getListComboIds(ids)).isEmpty()) {
                return comboPlanRepository.findLongDeletedOrTurnOff(ids);
            }
            return comboRepository.findLongDeletedOrTurnOff(comboPlanRepository.getListComboIds(ids));
        } else {
            return addonRepository.findLongDeletedOrTurnOff(ids);
        }
    }

    /**
     * Tính tiền creditnote cho shopping Cart
     * Mỗi sub phải lưu lại được list creditNote có được trước khi tính tiền
     * Sau khi tính phải có list creditNote mới từ list creditNote trước tính toán
     * Phải cập nhật lại biến creditNoteList để làm input cho sub tiếp theo
     */
    private List<CreditNoteCalculateDTO> calculateAfterRefundShoppingCart(SubscriptionFormulaResDTO res, SubCalBaseDevReqDTO request,
        List<CreditNoteCalculateDTO> creditNoteList) {
        //Khởi tạo đầu vào của refund sub
        List<CreditNoteCalculateDTO> lstCreditNoteCurrentForSub = creditNoteList.stream().map(x->{
            if (x.getAmountRefund().compareTo(x.getRemainingAmountRefund())>0){
                return new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                    x.getRemainingAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId());
            }else {
                return new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                    x.getAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId());
            }
        }).collect(Collectors.toList());

        creditNoteList = lstCreditNoteCurrentForSub.stream()
            .map(x-> new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                x.getAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId()))
            .collect(Collectors.toList());

        res.setTotalAmountAfterRefund(res.getTotalAmountAfterTaxFinal());
        AtomicReference<BigDecimal> totalRefund = new AtomicReference<>(BigDecimal.ZERO);

        // Set lại totalRefund sau mỗi lần trừ tiền
        for(CreditNoteCalculateDTO credit: creditNoteList) {
            BigDecimal taxValue = res.getObject().getTaxes().stream().map(FormulaTax::getPercent).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (credit.getTaxValue().compareTo(taxValue) == 0 &&
                credit.getRemainingAmountRefund().compareTo(BigDecimal.ZERO) > 0 &&
                res.getObject().getAmountIncurred().compareTo(BigDecimal.ZERO) > 0) {
                subscriptionFormula.calculateAmountRefund(totalRefund, credit, res.getObject().getAmountIncurred(), res.getObject(), null, null,
                    request.getType());
            }
            subscriptionFormula.calSetupFeeRefund(res.getObject().getSetupFee(), totalRefund, credit, request.getType());
            if (!CollectionUtils.isEmpty(res.getAddonShopCart())) {
                res.getAddonShopCart().stream()
                    .sorted(
                        Comparator.comparing(c -> c.getTaxes().stream().map(FormulaTax::getPercent).reduce(BigDecimal.ZERO, BigDecimal::add)))
                    .forEach(i -> {
                        BigDecimal taxValueAddon = i.getTaxes().stream().map(FormulaTax::getPercent).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (credit.getTaxValue().compareTo(taxValueAddon) == 0 &&
                            credit.getRemainingAmountRefund().compareTo(BigDecimal.ZERO) > 0 &&
                            i.getAmountIncurred().compareTo(BigDecimal.ZERO) > 0) {
                            subscriptionFormula.calculateAmountRefund(totalRefund, credit, i.getAmountIncurred(), i, null, null,
                                request.getType());
                        }
                        subscriptionFormula.calSetupFeeRefund(i.getSetupFee(), totalRefund, credit, request.getType());
                    });
            }
        }
        List<CreditNoteCalculateDTO> lstCreditNoteCurrentForCart = creditNoteList.stream()
            .map(x-> new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                x.getAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId()))
            .collect(Collectors.toList());

        Map<BigDecimal, List<CreditNoteCalculateDTO>> lstCreditNewForSub = lstCreditNoteCurrentForCart.stream()
            .collect(Collectors.groupingBy(CreditNoteCalculateDTO::getTaxValue, Collectors.toList()));

        res.setTotalAmountAfterRefund(res.getTotalAmountAfterTaxFinal().subtract(totalRefund.get()));
        res.setCreditNoteListNew(lstCreditNewForSub);
        res.setCreditNoteList(lstCreditNoteCurrentForSub);

        return creditNoteList.stream()
            .map(x-> new CreditNoteCalculateDTO(x.getId(), x.getCode(), x.getName(), x.getTaxId(), x.getTaxName(), x.getTaxValue(),
                x.getAmountRefund(), x.getRemainingAmountRefund(), x.getType(), x.getSubApplyId(), x.getSubscriptionId()))
            .collect(Collectors.toList());
    }

    /**
     * Kiểm tra coupon có thỏa mãn điều kiện sử dụng
     */
    private Coupon validateCouponBeforeApply(Long couponId, SubscriptionFormulaResDTO currentBillInfo) {
        // Kiểm tra điều kiện số tiền hóa đơn (chưa tính khuyến mại và thuế)
        Optional<Coupon> couponOpt = couponRepository.findByIdAndDeletedFlag(couponId, DeletedFlag.NOT_YET_DELETED.getValue());
        if (couponOpt.isPresent()) {
            BigDecimal billAmount = currentBillInfo.getBillAmountWithoutDiscountTax();
            // số tiền tối thiểu áp dụng coupon
            BigDecimal minimumAmount = new BigDecimal(couponOpt.get().getMinimumAmount());
            // billAmount <  minimumAmount => không áp dụng CTKM và ngược lại áp dụng được
            if (billAmount.compareTo(minimumAmount) >= 0) {
                return couponOpt.get();
            }
        }
        return null;
    }

    /**
     * Kiểm tra coupon tổng hóa đơn có thỏa mãn điều kiện sử dụng
     */
    private boolean validateCouponInvoiceBeforeApply(Long couponId, Long packageId, Long solutionId, BigDecimal billAmount) {
        // Kiểm tra điều kiện số tiền hóa đơn (chưa tính khuyến mại và thuế)
        Optional<Coupon> couponOpt = couponRepository.findByIdAndDeletedFlag(couponId, DeletedFlag.NOT_YET_DELETED.getValue());
        if (couponOpt.isPresent() && Objects.equals(couponOpt.get().getPromotionType(), PromotionTypeEnum.DISCOUNT.value) &&
            couponRepository.checkCouponApplyEnterpriseType(couponId, AuthUtil.getCurrentUserId()) &&
            couponRepository.checkCouponApplyTotalBillType(couponId, ObjectUtil.getOrDefault(packageId, -1L),
                ObjectUtil.getOrDefault(solutionId, -1L))
        ) {
            // số tiền tối thiểu áp dụng coupon
            BigDecimal minimumAmount = new BigDecimal(couponOpt.get().getMinimumAmount());
            // billAmount <  minimumAmount => không áp dụng CTKM và ngược lại áp dụng được
            return billAmount.compareTo(minimumAmount) >= 0;
        }
        return false;
    }

    private synchronized String getNextIndex(int type) {
        switch (type) {
            case 0:
                return "S_" + UUID.randomUUID();
            case 1:
                return "P_" + UUID.randomUUID();
            default:
                return "A_" + UUID.randomUUID();
        }
    }

    public Set<Long> convertAttributesIdsStringToSet(String o) {
        ObjectMapper mapper = new ObjectMapper();
        Set<Long> res = new HashSet<>();
        if (StringUtils.isNotBlank(o)) {
            try {
                TypeReference<Set<Long>> typeRef = new TypeReference<Set<Long>>() {
                };
                res = mapper.readValue(o, typeRef);
            } catch (IOException e) {
                res = null;
            }
        }
        return res;
    }

    /**
     * Lay danh sach phi mot lan cua subscription
     */
    private List<SubscriptionOneTimeFee> getOnceTimeFee(Long id) {
        List<SubscriptionOneTimeFee> result = new ArrayList<>();
        customFeeRepository.findAllBySubscriptionId(id).forEach(c -> {
            SubscriptionOneTimeFee oneTime = new SubscriptionOneTimeFee();
            oneTime.setId(c.getId());
            oneTime.setName(c.getName());
            oneTime.setDateFee(c.getFeeDate());
            oneTime.setPrice(c.getPrice());
            oneTime.setDescription(c.getDescription());
            result.add(oneTime);
        });
        return result;
    }

    /**
     * Tạo mô tả lỗi shopping-cart từ exception
     */
    private CartErrorDTO createCartError(Exception e) {
        if (e instanceof BaseException) {
            BaseException baseException = (BaseException) e;
            return CartErrorDTO.builder()
                .errorCode(baseException.getErrorCode())
                .errorMessage(baseException.getTitle())
                .build();
        } else {
            return CartErrorDTO.builder()
                .errorClass(e.getClass().getSimpleName())
                .errorMessage(e.toString())
                .build();
        }
    }
}