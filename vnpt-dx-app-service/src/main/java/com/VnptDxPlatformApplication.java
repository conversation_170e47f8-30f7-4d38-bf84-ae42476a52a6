package com;

import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.common.CorsFilter;
import com.monitor.starting.ApplicationFailureAnalyzer;
import com.monitor.starting.ApplicationSuccessAnalyzer;
import com.scheduled.batch.task.EventTask;

@SpringBootApplication(exclude = {RedisRepositoriesAutoConfiguration.class})
@Import({CorsFilter.class})
@EnableMongoRepositories({"com.repository.api.log", "com.repository.crm.assignmentRule.mongodb"})
@EnableJpaAuditing
@EnableScheduling
@EnableAsync
@EnableCaching
@EnableConfigurationProperties
public class VnptDxPlatformApplication {

    @Autowired
    private EventTask eventTask;


    public static void main(String[] args) {
//        SpringApplication.run(VnptDxPlatformApplication.class, args);
        new SpringApplicationBuilder(VnptDxPlatformApplication.class).
            listeners(new ApplicationFailureAnalyzer(), new ApplicationSuccessAnalyzer()).
            run(args);
    }

    @PostConstruct
    public void init() {
        eventTask.processUnprocessedEvents();
    }
}
