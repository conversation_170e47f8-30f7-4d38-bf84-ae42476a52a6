package orderManagerment;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import com.VnptDxPlatformApplication;
import com.dto.shoppingCart.register.ExtraOrderRequestDTO;
import com.dto.shoppingCart.register.OrderAddressDTO;
import com.dto.shoppingCart.register.OrderPaymentPolicyDTO;
import com.entity.subscriptions.ProductOrders;
import com.onedx.common.constants.enums.delivery.DeliveryMethodEnum;
import com.repository.subscriptions.ProductOrdersRepository;
import com.service.product_orders.ProductOrdersService;

@SpringBootTest(classes = VnptDxPlatformApplication.class)
//@AutoConfigureTestDatabase(replace = Replace.NONE) // dùng DB thật (có thể là H2, PostgreSQL...)
@ActiveProfiles("db68") // dùng cấu hình H2 hoặc PostgreSQL test
@Transactional
public class ProductOrdersServiceImplTestDB {

    @Autowired
    private ProductOrdersService productOrdersService;

    @Autowired
    private ProductOrdersRepository productOrdersRepository;

    @Test
    void saveProductOrders_shouldPersistToDatabase() {
        // Given: mock ExtraOrderRequestDTO
        ExtraOrderRequestDTO dto = new ExtraOrderRequestDTO();
        dto.setAssigneeId(123L);
        dto.setHasInstallation(true);
        dto.setPreferredInstallationDate(new Date());
        OrderPaymentPolicyDTO orderPaymentPolicyDTO = new OrderPaymentPolicyDTO();
        orderPaymentPolicyDTO.setType(1);
        dto.setPaymentPolicy(orderPaymentPolicyDTO);
        dto.setDeliveryMethod(DeliveryMethodEnum.EXPRESS);
        dto.setNotes("Khách hàng ưu tiên");
        dto.setEmployeeCode("EMP001");

        OrderAddressDTO address = new OrderAddressDTO();
        address.setAddress("123 ABC");
        address.setType(0);
        OrderAddressDTO addressOne = new OrderAddressDTO();
        addressOne.setAddress("123345 ABC");
        addressOne.setType(1);
        List<OrderAddressDTO> lstAddress = new ArrayList<>();
        lstAddress.add(address);
        lstAddress.add(addressOne);
        dto.setLstAddress(lstAddress);

        // When
        ProductOrders savedOrder = productOrdersService.saveProductOrders(dto);

        // Then
        Assertions.assertNotNull(savedOrder);
        Assertions.assertNotNull(savedOrder.getId());

        // Truy vấn lại từ DB để kiểm tra
        ProductOrders orderInDb = productOrdersRepository.findById(savedOrder.getId()).orElse(null);
        Assertions.assertNotNull(orderInDb);
        Assertions.assertEquals("EMP001", orderInDb.getEmployeeCode());
        Assertions.assertEquals("Khách hàng ưu tiên", orderInDb.getNotes());
    }

}
