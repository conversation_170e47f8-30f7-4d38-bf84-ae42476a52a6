package orderManagerment;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dto.shoppingCart.register.ExtraOrderRequestDTO;
import com.dto.shoppingCart.register.OrderAddressDTO;
import com.dto.shoppingCart.register.OrderPaymentPolicyDTO;
import com.entity.subscriptions.ProductOrders;
import com.onedx.common.constants.enums.delivery.DeliveryMethodEnum;
import com.onedx.common.constants.enums.subscriptions.TotalOrderProgressStatusEnum;
import com.onedx.common.utils.ObjectMapperUtil;
import com.repository.subscriptions.ProductOrdersRepository;
import com.service.product_orders.impl.ProductOrdersServiceImpl;

@ExtendWith(MockitoExtension.class)
class ProductOrdersServiceImplTest {

    @InjectMocks
    private ProductOrdersServiceImpl productOrdersService;

    @Mock
    private ProductOrdersRepository productOrdersRepository;

    @Test
    void testSaveProductOrders_shouldSaveAndReturnEntity() {
        // Given: mock ExtraOrderRequestDTO
        ExtraOrderRequestDTO dto = new ExtraOrderRequestDTO();
        dto.setAssigneeId(123L);
        dto.setHasInstallation(true);
        dto.setPreferredInstallationDate(new Date());
        OrderPaymentPolicyDTO orderPaymentPolicyDTO = new OrderPaymentPolicyDTO();
        orderPaymentPolicyDTO.setType(1);
        dto.setPaymentPolicy(orderPaymentPolicyDTO);
        dto.setDeliveryMethod(DeliveryMethodEnum.EXPRESS);
        dto.setNotes("Khách hàng ưu tiên");
        dto.setEmployeeCode("EMP001");

        OrderAddressDTO address = new OrderAddressDTO();
        address.setAddress("123 ABC");
        address.setType(0);
        OrderAddressDTO addressOne = new OrderAddressDTO();
        addressOne.setAddress("123345 ABC");
        addressOne.setType(1);
        List<OrderAddressDTO> lstAddress = new ArrayList<>();
        lstAddress.add(address);
        lstAddress.add(addressOne);
        dto.setLstAddress(lstAddress);

        // Mock productOrdersRepository.save()
        ArgumentCaptor<ProductOrders> orderCaptor = ArgumentCaptor.forClass(ProductOrders.class);
        ProductOrders savedOrder = new ProductOrders();
        savedOrder.setId(999L); // giả lập kết quả trả về
        Mockito.when(productOrdersRepository.save(any(ProductOrders.class)))
            .thenReturn(savedOrder);

        // When
        ProductOrders result = productOrdersService.saveProductOrders(dto);

        // Then
        Mockito.verify(productOrdersRepository).save(orderCaptor.capture());
        ProductOrders orderSaved = orderCaptor.getValue();

        assertNotNull(result);
        assertEquals(999L, result.getId());
        assertEquals(dto.getAssigneeId(), orderSaved.getAssigneeId());
        assertEquals(dto.getDeliveryMethod(), orderSaved.getDeliveryMethod());
        assertEquals(dto.getNotes(), orderSaved.getNotes());
        assertEquals(dto.getEmployeeCode(), orderSaved.getEmployeeCode());
        assertEquals(TotalOrderProgressStatusEnum.RECEIVED, orderSaved.getTotalOrderProgress());

        // Kiểm tra chuỗi JSON
        String expectedPaymentJson = ObjectMapperUtil.toJsonString(dto.getPaymentPolicy());
        assertEquals(expectedPaymentJson, orderSaved.getPaymentPolicy());
    }
}