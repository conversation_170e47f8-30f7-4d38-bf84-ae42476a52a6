FROM openjdk:8-jdk-alpine

COPY envsubst envsubst
RUN chmod +x envsubst && \
    mv envsubst /usr/local/bin
RUN mkdir config
COPY envsubst-multi.sh envsubst-multi.sh 
RUN chmod +x envsubst-multi.sh 


ARG JAR_FILE=target/*.jar
ARG CONFIG_FOLDER=src/main/resources/config/* config/*
COPY ${JAR_FILE} app.jar
COPY ${CONFIG_FOLDER} config-draft/

CMD ["/bin/sh", "-c", "./envsubst-multi.sh config-draft config && java -jar /app.jar"]