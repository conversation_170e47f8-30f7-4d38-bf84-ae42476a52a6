@startuml
skinparam defaultFontName Verdana
title Dev đăng ký dịch vụ cho sme
hide footbox
autonumber "<b>[0]"



actor Dev
participant DevPortal as "DevPortal"
participant SubscriptionService as "SubscriptionService"

'participant ServicesService as "ServicesService"
'participant PricingService as "PricingService"
'participant AddonService as "AddonService"
'participant ComboService as "ComboService"
'participant CouponService as "CouponService"
'participant PaymentService as "PaymentService"
participant IntegrationService

participant Saa<PERSON> as SaaS

Dev -> DevPortal: danh sách khách hàng
DevPortal <-> SubscriptionService: danh sách khách hàng
note left
    api/portal/subscription/sme?page=0&size=50&portalType=DEV
end note
Dev -> DevPortal: chọn khách hàng
Dev -> DevPortal: danh sách gói dịch vụ
DevPortal <-> SubscriptionService: danh sách dịch vụ
note left
    api/portal/subscription/pricing?page=0&size=50&portalType=DEV
    &osService=YES
end note
Dev -> DevPortal: chọn gói dịch vụ
DevPortal <-> SubscriptionService: chi tiết gói dịch vụ
note left
  api/dev-portal/subscription/pricing/{pricingId}?pricingPeriodId=
end note
Dev -> DevPortal: chọn addon/coupon...
DevPortal <-> SubscriptionService: Tính lại tiền
note left
    api/sme-portal/subscription/calculate/new/pricing/{pricingId}
end note
Dev -> DevPortal: tạo đơn hàng
DevPortal <-> SubscriptionService: tạo đơn hàng
note left
    api/portal/subscription/pricing
end note
SubscriptionService -> IntegrationService: cài đặt dịch vụ
note right
    func: transactionOneSME()
    actionType: CREATE_SUBSCRIPTION
end note
IntegrationService <-> SaaS: cài đặt dịch vụ
@enduml