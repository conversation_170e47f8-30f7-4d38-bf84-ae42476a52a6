@startuml
skinparam defaultFontName Verdana
title Sme đăng ký dịch vụ
hide footbox
autonumber "<b>[0]"



actor Sme
participant Sme<PERSON><PERSON><PERSON> as "SmePortal"
participant SubscriptionService as "SubscriptionService"

'participant ServicesService as "ServicesService"
'participant PricingService as "PricingService"
'participant AddonService as "AddonService"
'participant ComboService as "ComboService"
'participant CouponService as "CouponService"
participant PaymentService as "PaymentService"
participant IntegrationService
participant VNPTPay as "VNPTPay"

participant SaaS as SaaS

Sme -> SmePortal: chọn gó<PERSON> cước
SmePortal <-> SubscriptionService: chi tiết gói dịch vụ
note left
    api/sme-portal/subscription/pricing/{pricingId}
end note
SmePortal <-> SubscriptionService: chi tiết dịch vụ
note left
    api/sme-portal/subscription/service/{serviceId}/basic/{pricingId}
end note
SmePortal <-> SubscriptionService: <PERSON><PERSON>h tiền bước 1 khi đăng ký
note left
    api/sme-portal/subscription/calculate/new/pricing/{pricingId}
end note
Sme -> SmePortal: chọn addon/coupon...
SmePortal <-> SubscriptionService: Tính lại tiền
note left
    api/sme-portal/subscription/calculate/new/pricing/{pricingId}
end note
Sme -> SmePortal: sang bước 2
Sme -> SmePortal: sang bước 3
Sme -> SmePortal: sang bước 4, xác nhận thanh toán
SmePortal <-> SubscriptionService: kiểm tra thông tin đăng ký
note left
    api/portal/subscription/check-exist-items
end note


alt trường hợp thánh toán bằng VNPT Pay
SmePortal -> SubscriptionService: đăng ký dịch vụ
note left
    api/sme-portal/subscription?ipAddress=
end note
SmePortal <-> VNPTPay: redirect
VNPTPay -> PaymentService: thông báo thanh toán
note left
    api/portal/payment/notification-receiver
end note
PaymentService -> IntegrationService: cài đặt dịch vụ
note left
    func: transactionOneSME()
    actionType: CREATE_SUBSCRIPTION
end note
end
alt trường hợp không thanh toán qua VNPT
SmePortal -> SubscriptionService: đăng ký dịch vụ
note left
    api/sme-portal/subscription?ipAddress=
end note
SubscriptionService -> IntegrationService: cài đặt dịch vụ
end
IntegrationService <-> SaaS: cài đặt dịch vụ
@enduml